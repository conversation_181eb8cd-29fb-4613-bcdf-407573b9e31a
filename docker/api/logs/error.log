2025-05-27 18:32:11 | ERROR    | app.agents.api.routes:get_owned_agents:1061 - 获取用户拥有的智能体失败: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
Traceback (most recent call last):

  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
                 │    │     │                    └ <Request [b'GET']>
                 │    │     └ <function AsyncConnectionPool.handle_async_request at 0x73d3b121fce0>
                 │    └ <AsyncConnectionPool [Requests: 0 active, 0 queued | Connections: 0 active, 0 idle]>
                 └ <httpx.AsyncHTTPTransport object at 0x73d356ee7080>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
                     │          └ <function AsyncHTTPConnection.handle_async_request at 0x73d3b121efc0>
                     └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 101, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
                   │    │        └ <Request [b'GET']>
                   │    └ <function AsyncHTTPConnection._connect at 0x73d3b121f060>
                   └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 156, in _connect
    stream = await stream.start_tls(**kwargs)
                   │      │           └ {'ssl_context': <ssl.SSLContext object at 0x73d356edd250>, 'server_hostname': 'hia.ailoveworld.cn', 'timeout': 5.0}
                   │      └ <function AnyIOStream.start_tls at 0x73d3b122e8e0>
                   └ <httpcore._backends.anyio.AnyIOStream object at 0x73d356d17f50>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_backends/anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
         │              └ {<class 'TimeoutError'>: <class 'httpcore.ConnectTimeout'>, <class 'anyio.BrokenResourceError'>: <class 'httpcore.ConnectErro...
         └ <function map_exceptions at 0x73d3b1352a20>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)')
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_exceptions at 0x73d356eabb50>
    └ <contextlib._GeneratorContextManager object at 0x73d356d17ec0>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
          └ <class 'httpcore.ConnectError'>

httpcore.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 6
               │     └ 9
               └ <function _main at 0x73d3b32be980>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 6
           │    └ <function BaseProcess._bootstrap at 0x73d3b33dcc20>
           └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x73d3b33dc180>
    └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x73d3b304fe60>, 'target': <bound method Process.target of <uvicorn.supervisors.m...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
    │    └ <function subprocess_started at 0x73d3b26759e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Process.target of <uvicorn.supervisors.multiprocess.Process object at 0x73d3b2722f00>>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/supervisors/multiprocess.py", line 63, in target
    return self.real_target(sockets)
           │    │           └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │    └ <bound method Server.run of <uvicorn.server.Server object at 0x73d3b2722f90>>
           └ <uvicorn.supervisors.multiprocess.Process object at 0x73d3b2722f00>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x73d3b2674f40>
           │       │   └ <uvicorn.server.Server object at 0x73d3b2722f90>
           │       └ <function run at 0x73d3b2fbf420>
           └ <module 'asyncio' from '/usr/local/lib/python3.12/asyncio/__init__.py'>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x73d3b2578900>
           │      └ <function Runner.run at 0x73d3b2f9aac0>
           └ <asyncio.runners.Runner object at 0x73d3b3276090>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.12/site-packages/uvicorn/server.py:70> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x73d3b2648110>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x73d3b3276090>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x73d356e06000>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d356ee7d...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d356e...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │    └ <fastapi.applications.FastAPI object at 0x73d3b18ea660>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x73d356e06000>
  File "/usr/local/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d356ee7d...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d356e...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 409, in _sentry_patched_asgi_app
    return await middleware(scope, receive, send)
                 │          │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d356ee7d...
                 │          │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d356e...
                 │          └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x73d356eefbf0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 158, in _run_asgi3
    return await self._run_app(scope, receive, send, asgi_version=3)
                 │    │        │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d356ee7d...
                 │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d356e...
                 │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │    └ <function SentryAsgiMiddleware._run_app at 0x73d3a814fec0>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x73d356eefbf0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 255, in _run_app
    return await self.app(
                 │    └ <member 'app' of 'SentryAsgiMiddleware' objects>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x73d356eefbf0>
  File "/usr/local/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function SentryAsgiMiddleware._run_app.<locals>._sentry_wrapped_send at 0x73d356e2c540>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d356e...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x73d356e1fe90>
          └ <fastapi.applications.FastAPI object at 0x73d3b18ea660>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x73d356ea1c60>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │        └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x73d356e1fe90>
                 └ <function ServerErrorMiddleware.__call__ at 0x73d3b075fe20>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x73d356ea07c0>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x73d356e1fd40>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x73d356e1fe90>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 298, in _sentry_exceptionmiddleware_call
    await old_call(self, scope, receive, send)
          │        │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x73d356ea07c0>
          │        │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
          │        │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x73d356e1fd40>
          └ <function _enable_span_for_middleware.<locals>._create_span_call at 0x73d356f76700>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x73d356f768e0>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x73d356e1fd40>
                 └ <function ExceptionMiddleware.__call__ at 0x73d3b07984a0>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x73d356f768e0>
          │                            │    │    │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x73d356d14e60>
          │                            │    └ <fastapi.routing.APIRouter object at 0x73d3a8d6f890>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x73d356e1fd40>
          └ <function wrap_app_handling_exceptions at 0x73d3b073dd00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d356f76b60>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x73d3a8d6f890>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d356f76b60>
          │    │                │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x73d3a8d6f890>>
          └ <fastapi.routing.APIRouter object at 0x73d3a8d6f890>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d356f76b60>
          │     │      │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │     └ <function Route.handle at 0x73d3b073f1a0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d356f76b60>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x73d3570934c0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d356f76b60>
          │                            │    │        │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x73d356d17c80>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x73d356f76c00>
          └ <function wrap_app_handling_exceptions at 0x73d3b073dd00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d356efea20>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d356ea0720>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x73d356f76c00>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x73d356d17c80>
                     └ <function patch_get_request_handler.<locals>._sentry_get_request_handler.<locals>._sentry_app at 0x73d3571aef20>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/fastapi.py", line 143, in _sentry_app
    return await old_app(*args, **kwargs)
                 │        │       └ {}
                 │        └ (<starlette.requests.Request object at 0x73d356d17c80>,)
                 └ <function get_request_handler.<locals>.app at 0x73d3571ac040>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x73d3b073d6c0>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': User(username='test_by_wzz', password='$2b$10$LzhNsBGw3F6RCs3Q7F29X...ZaOlXV1CPMnAWeZQ9r3vk5FzCGlJu', create...
                 │         └ <function get_owned_agents at 0x73d359d70cc0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[Dependant(path_p...

> File "/app/app/agents/api/routes.py", line 1037, in get_owned_agents
    keys = await billing_client.keys.list_available_keys(
                 │              │    └ <function Keys.list_available_keys at 0x73d3a1557d80>
                 │              └ <billing.keys.Keys object at 0x73d356ee6a20>
                 └ <billing.HkJingXiuBilling object at 0x73d356ee7ce0>

  File "/usr/local/lib/python3.12/site-packages/billing/keys/__init__.py", line 61, in list_available_keys
    response = await self.client.get("/keys/available", params=params)
                     │    │      │                             └ {'user_id': '149', 'service_code': 'AGC'}
                     │    │      └ <function HttpClient.get at 0x73d3a1556a20>
                     │    └ <billing.http.HttpClient object at 0x73d356ee6e40>
                     └ <billing.keys.Keys object at 0x73d356ee6a20>
  File "/usr/local/lib/python3.12/site-packages/billing/http.py", line 38, in get
    response = await client.get(
                     │      └ <function AsyncClient.get at 0x73d3a8004400>
                     └ <httpx.AsyncClient object at 0x73d356ee7140>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
                 │    └ <function AsyncClient.request at 0x73d3a8003f60>
                 └ <httpx.AsyncClient object at 0x73d356ee7140>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
                 │    │    │             │                      └ <httpx._client.UseClientDefault object at 0x73d3a83d70b0>
                 │    │    │             └ <httpx._client.UseClientDefault object at 0x73d3a83d70b0>
                 │    │    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                 │    └ <function _install_httpx_async_client.<locals>.send at 0x73d3a2521620>
                 └ <httpx.AsyncClient object at 0x73d356ee7140>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/httpx.py", line 142, in send
    rv = await real_send(self, request, **kwargs)
               │         │     │          └ {'auth': <httpx._client.UseClientDefault object at 0x73d3a83d70b0>, 'follow_redirects': <httpx._client.UseClientDefault objec...
               │         │     └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
               │         └ <httpx.AsyncClient object at 0x73d356ee7140>
               └ <function AsyncClient.send at 0x73d3a8004180>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
                     │    └ <function AsyncClient._send_handling_auth at 0x73d3a8004220>
                     └ <httpx.AsyncClient object at 0x73d356ee7140>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
                     │    └ <function AsyncClient._send_handling_redirects at 0x73d3a80042c0>
                     └ <httpx.AsyncClient object at 0x73d356ee7140>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
                     │    │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │    └ <function AsyncClient._send_single_request at 0x73d3a8004360>
                     └ <httpx.AsyncClient object at 0x73d356ee7140>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
                     │         │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │         └ <function AsyncHTTPTransport.handle_async_request at 0x73d3a8000fe0>
                     └ <httpx.AsyncHTTPTransport object at 0x73d356ee7080>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         └ <function map_httpcore_exceptions at 0x73d3a80005e0>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ConnectError(SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'))
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_httpcore_exceptions at 0x73d356d20940>
    └ <contextlib._GeneratorContextManager object at 0x73d356d17b30>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
          │          └ '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'
          └ <class 'httpx.ConnectError'>

httpx.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
2025-05-27 18:32:12 | ERROR    | app.agents.api.routes:get_owned_agents:1061 - 获取用户拥有的智能体失败: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
Traceback (most recent call last):

  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
                 │    │     │                    └ <Request [b'GET']>
                 │    │     └ <function AsyncConnectionPool.handle_async_request at 0x73d3b121fce0>
                 │    └ <AsyncConnectionPool [Requests: 0 active, 0 queued | Connections: 0 active, 0 idle]>
                 └ <httpx.AsyncHTTPTransport object at 0x73d3563c7530>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
                     │          └ <function AsyncHTTPConnection.handle_async_request at 0x73d3b121efc0>
                     └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 101, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
                   │    │        └ <Request [b'GET']>
                   │    └ <function AsyncHTTPConnection._connect at 0x73d3b121f060>
                   └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 156, in _connect
    stream = await stream.start_tls(**kwargs)
                   │      │           └ {'ssl_context': <ssl.SSLContext object at 0x73d3563c9a50>, 'server_hostname': 'hia.ailoveworld.cn', 'timeout': 5.0}
                   │      └ <function AnyIOStream.start_tls at 0x73d3b122e8e0>
                   └ <httpcore._backends.anyio.AnyIOStream object at 0x73d3563c7800>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_backends/anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
         │              └ {<class 'TimeoutError'>: <class 'httpcore.ConnectTimeout'>, <class 'anyio.BrokenResourceError'>: <class 'httpcore.ConnectErro...
         └ <function map_exceptions at 0x73d3b1352a20>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)')
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_exceptions at 0x73d356d0f970>
    └ <contextlib._GeneratorContextManager object at 0x73d3563c7e30>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
          └ <class 'httpcore.ConnectError'>

httpcore.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 6
               │     └ 9
               └ <function _main at 0x73d3b32be980>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 6
           │    └ <function BaseProcess._bootstrap at 0x73d3b33dcc20>
           └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x73d3b33dc180>
    └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x73d3b304fe60>, 'target': <bound method Process.target of <uvicorn.supervisors.m...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
    │    └ <function subprocess_started at 0x73d3b26759e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Process.target of <uvicorn.supervisors.multiprocess.Process object at 0x73d3b2722f00>>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/supervisors/multiprocess.py", line 63, in target
    return self.real_target(sockets)
           │    │           └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │    └ <bound method Server.run of <uvicorn.server.Server object at 0x73d3b2722f90>>
           └ <uvicorn.supervisors.multiprocess.Process object at 0x73d3b2722f00>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x73d3b2674f40>
           │       │   └ <uvicorn.server.Server object at 0x73d3b2722f90>
           │       └ <function run at 0x73d3b2fbf420>
           └ <module 'asyncio' from '/usr/local/lib/python3.12/asyncio/__init__.py'>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x73d3b2578900>
           │      └ <function Runner.run at 0x73d3b2f9aac0>
           └ <asyncio.runners.Runner object at 0x73d3b3276090>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.12/site-packages/uvicorn/server.py:70> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x73d3b2648110>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x73d3b3276090>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x73d356e06000>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d3559b14...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d3559...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │    └ <fastapi.applications.FastAPI object at 0x73d3b18ea660>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x73d356e06000>
  File "/usr/local/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d3559b14...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d3559...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 409, in _sentry_patched_asgi_app
    return await middleware(scope, receive, send)
                 │          │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d3559b14...
                 │          │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d3559...
                 │          └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x73d355963420>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 158, in _run_asgi3
    return await self._run_app(scope, receive, send, asgi_version=3)
                 │    │        │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d3559b14...
                 │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d3559...
                 │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │    └ <function SentryAsgiMiddleware._run_app at 0x73d3a814fec0>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x73d355963420>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 255, in _run_app
    return await self.app(
                 │    └ <member 'app' of 'SentryAsgiMiddleware' objects>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x73d355963420>
  File "/usr/local/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function SentryAsgiMiddleware._run_app.<locals>._sentry_wrapped_send at 0x73d356e2cfe0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x73d3559...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x73d356e1fe90>
          └ <fastapi.applications.FastAPI object at 0x73d3b18ea660>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x73d355920540>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │        └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x73d356e1fe90>
                 └ <function ServerErrorMiddleware.__call__ at 0x73d3b075fe20>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x73d355920720>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x73d356e1fd40>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x73d356e1fe90>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 298, in _sentry_exceptionmiddleware_call
    await old_call(self, scope, receive, send)
          │        │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x73d355920720>
          │        │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
          │        │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x73d356e1fd40>
          └ <function _enable_span_for_middleware.<locals>._create_span_call at 0x73d356f76700>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x73d355920180>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x73d356e1fd40>
                 └ <function ExceptionMiddleware.__call__ at 0x73d3b07984a0>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x73d355920180>
          │                            │    │    │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x73d3559b1100>
          │                            │    └ <fastapi.routing.APIRouter object at 0x73d3a8d6f890>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x73d356e1fd40>
          └ <function wrap_app_handling_exceptions at 0x73d3b073dd00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d355920860>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x73d3a8d6f890>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d355920860>
          │    │                │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x73d3a8d6f890>>
          └ <fastapi.routing.APIRouter object at 0x73d3a8d6f890>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d355920860>
          │     │      │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │     └ <function Route.handle at 0x73d3b073f1a0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d355920860>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x73d3570934c0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d355920860>
          │                            │    │        │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x73d3559b0d40>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x73d3559216c0>
          └ <function wrap_app_handling_exceptions at 0x73d3b073dd00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x73d3559207c0>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x73d3b137b060>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x73d3559216c0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x73d3559b0d40>
                     └ <function patch_get_request_handler.<locals>._sentry_get_request_handler.<locals>._sentry_app at 0x73d3571aef20>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/fastapi.py", line 143, in _sentry_app
    return await old_app(*args, **kwargs)
                 │        │       └ {}
                 │        └ (<starlette.requests.Request object at 0x73d3559b0d40>,)
                 └ <function get_request_handler.<locals>.app at 0x73d3571ac040>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x73d3b073d6c0>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': User(username='test_by_wzz', password='$2b$10$LzhNsBGw3F6RCs3Q7F29X...ZaOlXV1CPMnAWeZQ9r3vk5FzCGlJu', create...
                 │         └ <function get_owned_agents at 0x73d359d70cc0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[Dependant(path_p...

> File "/app/app/agents/api/routes.py", line 1037, in get_owned_agents
    keys = await billing_client.keys.list_available_keys(
                 │              │    └ <function Keys.list_available_keys at 0x73d3a1557d80>
                 │              └ <billing.keys.Keys object at 0x73d356d17680>
                 └ <billing.HkJingXiuBilling object at 0x73d356d17bf0>

  File "/usr/local/lib/python3.12/site-packages/billing/keys/__init__.py", line 61, in list_available_keys
    response = await self.client.get("/keys/available", params=params)
                     │    │      │                             └ {'user_id': '149', 'service_code': 'AGC'}
                     │    │      └ <function HttpClient.get at 0x73d3a1556a20>
                     │    └ <billing.http.HttpClient object at 0x73d356d17710>
                     └ <billing.keys.Keys object at 0x73d356d17680>
  File "/usr/local/lib/python3.12/site-packages/billing/http.py", line 38, in get
    response = await client.get(
                     │      └ <function AsyncClient.get at 0x73d3a8004400>
                     └ <httpx.AsyncClient object at 0x73d3559b13a0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
                 │    └ <function AsyncClient.request at 0x73d3a8003f60>
                 └ <httpx.AsyncClient object at 0x73d3559b13a0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
                 │    │    │             │                      └ <httpx._client.UseClientDefault object at 0x73d3a83d70b0>
                 │    │    │             └ <httpx._client.UseClientDefault object at 0x73d3a83d70b0>
                 │    │    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                 │    └ <function _install_httpx_async_client.<locals>.send at 0x73d3a2521620>
                 └ <httpx.AsyncClient object at 0x73d3559b13a0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/httpx.py", line 142, in send
    rv = await real_send(self, request, **kwargs)
               │         │     │          └ {'auth': <httpx._client.UseClientDefault object at 0x73d3a83d70b0>, 'follow_redirects': <httpx._client.UseClientDefault objec...
               │         │     └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
               │         └ <httpx.AsyncClient object at 0x73d3559b13a0>
               └ <function AsyncClient.send at 0x73d3a8004180>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
                     │    └ <function AsyncClient._send_handling_auth at 0x73d3a8004220>
                     └ <httpx.AsyncClient object at 0x73d3559b13a0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
                     │    └ <function AsyncClient._send_handling_redirects at 0x73d3a80042c0>
                     └ <httpx.AsyncClient object at 0x73d3559b13a0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
                     │    │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │    └ <function AsyncClient._send_single_request at 0x73d3a8004360>
                     └ <httpx.AsyncClient object at 0x73d3559b13a0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
                     │         │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │         └ <function AsyncHTTPTransport.handle_async_request at 0x73d3a8000fe0>
                     └ <httpx.AsyncHTTPTransport object at 0x73d3563c7530>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         └ <function map_httpcore_exceptions at 0x73d3a80005e0>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ConnectError(SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'))
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_httpcore_exceptions at 0x73d356d23040>
    └ <contextlib._GeneratorContextManager object at 0x73d3563c4e60>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
          │          └ '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'
          └ <class 'httpx.ConnectError'>

httpx.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
2025-05-27 18:42:09 | ERROR    | app.agents.api.routes:get_owned_agents:1061 - 获取用户拥有的智能体失败: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
Traceback (most recent call last):

  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
                 │    │     │                    └ <Request [b'GET']>
                 │    │     └ <function AsyncConnectionPool.handle_async_request at 0x7561d208bd80>
                 │    └ <AsyncConnectionPool [Requests: 0 active, 0 queued | Connections: 0 active, 0 idle]>
                 └ <httpx.AsyncHTTPTransport object at 0x756177cdd280>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
                     │          └ <function AsyncHTTPConnection.handle_async_request at 0x7561d208b060>
                     └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 101, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
                   │    │        └ <Request [b'GET']>
                   │    └ <function AsyncHTTPConnection._connect at 0x7561d208b100>
                   └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 156, in _connect
    stream = await stream.start_tls(**kwargs)
                   │      │           └ {'ssl_context': <ssl.SSLContext object at 0x756177c9f5d0>, 'server_hostname': 'hia.ailoveworld.cn', 'timeout': 5.0}
                   │      └ <function AnyIOStream.start_tls at 0x7561d2096980>
                   └ <httpcore._backends.anyio.AnyIOStream object at 0x756177cddb80>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_backends/anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
         │              └ {<class 'TimeoutError'>: <class 'httpcore.ConnectTimeout'>, <class 'anyio.BrokenResourceError'>: <class 'httpcore.ConnectErro...
         └ <function map_exceptions at 0x7561d21c2ac0>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)')
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_exceptions at 0x756177cec9a0>
    └ <contextlib._GeneratorContextManager object at 0x756177cddac0>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
          └ <class 'httpcore.ConnectError'>

httpcore.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 4
               │     └ 33
               └ <function _main at 0x7561d4142980>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 4
           │    └ <function BaseProcess._bootstrap at 0x7561d4264c20>
           └ <SpawnProcess name='SpawnProcess-7' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7561d4264180>
    └ <SpawnProcess name='SpawnProcess-7' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7561d361fb90>, 'target': <bound method Process.target of <uvicorn.supervisors.m...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-7' parent=7 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-7' parent=7 started>
    │    └ <function subprocess_started at 0x7561d34f59e0>
    └ <SpawnProcess name='SpawnProcess-7' parent=7 started>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Process.target of <uvicorn.supervisors.multiprocess.Process object at 0x7561d359ed20>>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/supervisors/multiprocess.py", line 63, in target
    return self.real_target(sockets)
           │    │           └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │    └ <bound method Server.run of <uvicorn.server.Server object at 0x7561d359ede0>>
           └ <uvicorn.supervisors.multiprocess.Process object at 0x7561d359ed20>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7561d34f4f40>
           │       │   └ <uvicorn.server.Server object at 0x7561d359ede0>
           │       └ <function run at 0x7561d3e4b420>
           └ <module 'asyncio' from '/usr/local/lib/python3.12/asyncio/__init__.py'>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7561d33ec900>
           │      └ <function Runner.run at 0x7561d3e26ac0>
           └ <asyncio.runners.Runner object at 0x7561d359f1d0>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.12/site-packages/uvicorn/server.py:70> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7561d34c0110>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7561d359f1d0>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7561d361cb90>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x756177c019...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x756177c...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │    └ <fastapi.applications.FastAPI object at 0x7561d271e990>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7561d361cb90>
  File "/usr/local/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x756177c019...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x756177c...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 409, in _sentry_patched_asgi_app
    return await middleware(scope, receive, send)
                 │          │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x756177c019...
                 │          │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x756177c...
                 │          └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x756177c15cb0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 158, in _run_asgi3
    return await self._run_app(scope, receive, send, asgi_version=3)
                 │    │        │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x756177c019...
                 │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x756177c...
                 │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │    └ <function SentryAsgiMiddleware._run_app at 0x7561c9743ec0>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x756177c15cb0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 255, in _run_app
    return await self.app(
                 │    └ <member 'app' of 'SentryAsgiMiddleware' objects>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x756177c15cb0>
  File "/usr/local/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function SentryAsgiMiddleware._run_app.<locals>._sentry_wrapped_send at 0x756177d3b380>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x756177c...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x756177de3a70>
          └ <fastapi.applications.FastAPI object at 0x7561d271e990>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x756177d3ad40>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │        └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x756177de3a70>
                 └ <function ServerErrorMiddleware.__call__ at 0x7561d15e3e20>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x756177d3ab60>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x756177de3bf0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x756177de3a70>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 298, in _sentry_exceptionmiddleware_call
    await old_call(self, scope, receive, send)
          │        │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x756177d3ab60>
          │        │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
          │        │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x756177de3bf0>
          └ <function _enable_span_for_middleware.<locals>._create_span_call at 0x756177d3a700>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x756177df0220>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
                 │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x756177de3bf0>
                 └ <function ExceptionMiddleware.__call__ at 0x7561d161c4a0>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x756177df0220>
          │                            │    │    │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x756177cdc4a0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x7561cb564170>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x756177de3bf0>
          └ <function wrap_app_handling_exceptions at 0x7561d15c1d00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x756177df04a0>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x7561cb564170>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x756177df04a0>
          │    │                │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x7561cb564170>>
          └ <fastapi.routing.APIRouter object at 0x7561cb564170>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x756177df04a0>
          │     │      │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │     └ <function Route.handle at 0x7561d15c31a0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x756177df04a0>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x7561781574c0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x756177df04a0>
          │                            │    │        │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x756177cdc380>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x756177df0400>
          └ <function wrap_app_handling_exceptions at 0x7561d15c1d00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x756177c57ec0>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x756177d3aa20>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.5', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x756177df0400>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x756177cdc380>
                     └ <function patch_get_request_handler.<locals>._sentry_get_request_handler.<locals>._sentry_app at 0x75617826ef20>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/fastapi.py", line 143, in _sentry_app
    return await old_app(*args, **kwargs)
                 │        │       └ {}
                 │        └ (<starlette.requests.Request object at 0x756177cdc380>,)
                 └ <function get_request_handler.<locals>.app at 0x75617826c040>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x7561d15c16c0>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': User(username='test_by_wzz', password='$2b$10$LzhNsBGw3F6RCs3Q7F29X...ZaOlXV1CPMnAWeZQ9r3vk5FzCGlJu', create...
                 │         └ <function get_owned_agents at 0x75617b134cc0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[Dependant(path_p...

> File "/app/app/agents/api/routes.py", line 1037, in get_owned_agents
    keys = await billing_client.keys.list_available_keys(
                 │              │    └ <function Keys.list_available_keys at 0x7561c2217d80>
                 │              └ <billing.keys.Keys object at 0x756177cdcd40>
                 └ <billing.HkJingXiuBilling object at 0x756177cdcbf0>

  File "/usr/local/lib/python3.12/site-packages/billing/keys/__init__.py", line 61, in list_available_keys
    response = await self.client.get("/keys/available", params=params)
                     │    │      │                             └ {'user_id': '149', 'service_code': 'AGC'}
                     │    │      └ <function HttpClient.get at 0x7561c2216a20>
                     │    └ <billing.http.HttpClient object at 0x756177cdc950>
                     └ <billing.keys.Keys object at 0x756177cdcd40>
  File "/usr/local/lib/python3.12/site-packages/billing/http.py", line 38, in get
    response = await client.get(
                     │      └ <function AsyncClient.get at 0x7561c97f8400>
                     └ <httpx.AsyncClient object at 0x756177cdcdd0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
                 │    └ <function AsyncClient.request at 0x7561c97f7f60>
                 └ <httpx.AsyncClient object at 0x756177cdcdd0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
                 │    │    │             │                      └ <httpx._client.UseClientDefault object at 0x7561cb562900>
                 │    │    │             └ <httpx._client.UseClientDefault object at 0x7561cb562900>
                 │    │    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                 │    └ <function _install_httpx_async_client.<locals>.send at 0x7561c31d5620>
                 └ <httpx.AsyncClient object at 0x756177cdcdd0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/httpx.py", line 142, in send
    rv = await real_send(self, request, **kwargs)
               │         │     │          └ {'auth': <httpx._client.UseClientDefault object at 0x7561cb562900>, 'follow_redirects': <httpx._client.UseClientDefault objec...
               │         │     └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
               │         └ <httpx.AsyncClient object at 0x756177cdcdd0>
               └ <function AsyncClient.send at 0x7561c97f8180>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
                     │    └ <function AsyncClient._send_handling_auth at 0x7561c97f8220>
                     └ <httpx.AsyncClient object at 0x756177cdcdd0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
                     │    └ <function AsyncClient._send_handling_redirects at 0x7561c97f82c0>
                     └ <httpx.AsyncClient object at 0x756177cdcdd0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
                     │    │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │    └ <function AsyncClient._send_single_request at 0x7561c97f8360>
                     └ <httpx.AsyncClient object at 0x756177cdcdd0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
                     │         │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │         └ <function AsyncHTTPTransport.handle_async_request at 0x7561c97f4fe0>
                     └ <httpx.AsyncHTTPTransport object at 0x756177cdd280>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         └ <function map_httpcore_exceptions at 0x7561c97f45e0>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ConnectError(SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'))
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_httpcore_exceptions at 0x756177cd9540>
    └ <contextlib._GeneratorContextManager object at 0x756177cdd850>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
          │          └ '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'
          └ <class 'httpx.ConnectError'>

httpx.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
2025-05-28 10:02:07 | ERROR    | app.agents.api.routes:get_owned_agents:1061 - 获取用户拥有的智能体失败: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
Traceback (most recent call last):

  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
                 │    │     │                    └ <Request [b'GET']>
                 │    │     └ <function AsyncConnectionPool.handle_async_request at 0x7df19cac7ce0>
                 │    └ <AsyncConnectionPool [Requests: 0 active, 0 queued | Connections: 0 active, 0 idle]>
                 └ <httpx.AsyncHTTPTransport object at 0x7df1407ff260>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
                     │          └ <function AsyncHTTPConnection.handle_async_request at 0x7df19cac6fc0>
                     └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 101, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
                   │    │        └ <Request [b'GET']>
                   │    └ <function AsyncHTTPConnection._connect at 0x7df19cac7060>
                   └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 156, in _connect
    stream = await stream.start_tls(**kwargs)
                   │      │           └ {'ssl_context': <ssl.SSLContext object at 0x7df141b148d0>, 'server_hostname': 'hia.ailoveworld.cn', 'timeout': 5.0}
                   │      └ <function AnyIOStream.start_tls at 0x7df19cad68e0>
                   └ <httpcore._backends.anyio.AnyIOStream object at 0x7df1407fcf20>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_backends/anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
         │              └ {<class 'TimeoutError'>: <class 'httpcore.ConnectTimeout'>, <class 'anyio.BrokenResourceError'>: <class 'httpcore.ConnectErro...
         └ <function map_exceptions at 0x7df19cbf2a20>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)')
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_exceptions at 0x7df1407cbb50>
    └ <contextlib._GeneratorContextManager object at 0x7df1407fd5b0>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
          └ <class 'httpcore.ConnectError'>

httpcore.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 4
               │     └ 29
               └ <function _main at 0x7df19eb76980>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 4
           │    └ <function BaseProcess._bootstrap at 0x7df19ec94c20>
           └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7df19ec94180>
    └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7df19e900410>, 'target': <bound method Process.target of <uvicorn.supervisors.m...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
    │    └ <function subprocess_started at 0x7df19df319e0>
    └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Process.target of <uvicorn.supervisors.multiprocess.Process object at 0x7df19dfe3080>>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/supervisors/multiprocess.py", line 63, in target
    return self.real_target(sockets)
           │    │           └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │    └ <bound method Server.run of <uvicorn.server.Server object at 0x7df19dfe3110>>
           └ <uvicorn.supervisors.multiprocess.Process object at 0x7df19dfe3080>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7df19df30f40>
           │       │   └ <uvicorn.server.Server object at 0x7df19dfe3110>
           │       └ <function run at 0x7df19e87b420>
           └ <module 'asyncio' from '/usr/local/lib/python3.12/asyncio/__init__.py'>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7df19de38900>
           │      └ <function Runner.run at 0x7df19e856ac0>
           └ <asyncio.runners.Runner object at 0x7df19ece2270>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.12/site-packages/uvicorn/server.py:70> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7df19df08110>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7df19ece2270>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7df19e90cb30>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425fe6...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │    └ <fastapi.applications.FastAPI object at 0x7df19d1369c0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7df19e90cb30>
  File "/usr/local/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425fe6...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 409, in _sentry_patched_asgi_app
    return await middleware(scope, receive, send)
                 │          │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425fe6...
                 │          │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425...
                 │          └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7df1411573d0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 158, in _run_asgi3
    return await self._run_app(scope, receive, send, asgi_version=3)
                 │    │        │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425fe6...
                 │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425...
                 │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │    └ <function SentryAsgiMiddleware._run_app at 0x7df194443ec0>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7df1411573d0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 255, in _run_app
    return await self.app(
                 │    └ <member 'app' of 'SentryAsgiMiddleware' objects>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7df1411573d0>
  File "/usr/local/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function SentryAsgiMiddleware._run_app.<locals>._sentry_wrapped_send at 0x7df141b88d60>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7df1426dbb60>
          └ <fastapi.applications.FastAPI object at 0x7df19d1369c0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7df141b89300>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │        └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7df1426dbb60>
                 └ <function ServerErrorMiddleware.__call__ at 0x7df197fc3e20>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7df141b893a0>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7df1426dbb60>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 298, in _sentry_exceptionmiddleware_call
    await old_call(self, scope, receive, send)
          │        │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7df141b893a0>
          │        │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
          │        │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
          └ <function _enable_span_for_middleware.<locals>._create_span_call at 0x7df14262e700>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7df19cc23100>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
                 └ <function ExceptionMiddleware.__call__ at 0x7df197ffc4a0>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7df19cc23100>
          │                            │    │    │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x7df1425ffb00>
          │                            │    └ <fastapi.routing.APIRouter object at 0x7df1947645f0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
          └ <function wrap_app_handling_exceptions at 0x7df197fa1d00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425daca0>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x7df1947645f0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425daca0>
          │    │                │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x7df1947645f0>>
          └ <fastapi.routing.APIRouter object at 0x7df1947645f0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425daca0>
          │     │      │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │     └ <function Route.handle at 0x7df197fa31a0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425daca0>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x7df14274b4c0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425daca0>
          │                            │    │        │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x7df1425ffad0>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x7df1425da7a0>
          └ <function wrap_app_handling_exceptions at 0x7df197fa1d00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425db420>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df141b89260>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x7df1425da7a0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x7df1425ffad0>
                     └ <function patch_get_request_handler.<locals>._sentry_get_request_handler.<locals>._sentry_app at 0x7df142b62f20>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/fastapi.py", line 143, in _sentry_app
    return await old_app(*args, **kwargs)
                 │        │       └ {}
                 │        └ (<starlette.requests.Request object at 0x7df1425ffad0>,)
                 └ <function get_request_handler.<locals>.app at 0x7df142b60040>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x7df197fa16c0>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': User(username='test_by_wzz', password='$2b$10$LzhNsBGw3F6RCs3Q7F29X...ZaOlXV1CPMnAWeZQ9r3vk5FzCGlJu', create...
                 │         └ <function get_owned_agents at 0x7df145d28cc0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[Dependant(path_p...

> File "/app/app/agents/api/routes.py", line 1037, in get_owned_agents
    keys = await billing_client.keys.list_available_keys(
                 │              │    └ <function Keys.list_available_keys at 0x7df18d017d80>
                 │              └ <billing.keys.Keys object at 0x7df1425fe330>
                 └ <billing.HkJingXiuBilling object at 0x7df1426dacc0>

  File "/usr/local/lib/python3.12/site-packages/billing/keys/__init__.py", line 61, in list_available_keys
    response = await self.client.get("/keys/available", params=params)
                     │    │      │                             └ {'user_id': '149', 'service_code': 'AGC'}
                     │    │      └ <function HttpClient.get at 0x7df18d016a20>
                     │    └ <billing.http.HttpClient object at 0x7df1425fede0>
                     └ <billing.keys.Keys object at 0x7df1425fe330>
  File "/usr/local/lib/python3.12/site-packages/billing/http.py", line 38, in get
    response = await client.get(
                     │      └ <function AsyncClient.get at 0x7df1944f8400>
                     └ <httpx.AsyncClient object at 0x7df1425fd790>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
                 │    └ <function AsyncClient.request at 0x7df1944f7f60>
                 └ <httpx.AsyncClient object at 0x7df1425fd790>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
                 │    │    │             │                      └ <httpx._client.UseClientDefault object at 0x7df194543290>
                 │    │    │             └ <httpx._client.UseClientDefault object at 0x7df194543290>
                 │    │    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                 │    └ <function _install_httpx_async_client.<locals>.send at 0x7df18dfd5620>
                 └ <httpx.AsyncClient object at 0x7df1425fd790>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/httpx.py", line 142, in send
    rv = await real_send(self, request, **kwargs)
               │         │     │          └ {'auth': <httpx._client.UseClientDefault object at 0x7df194543290>, 'follow_redirects': <httpx._client.UseClientDefault objec...
               │         │     └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
               │         └ <httpx.AsyncClient object at 0x7df1425fd790>
               └ <function AsyncClient.send at 0x7df1944f8180>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
                     │    └ <function AsyncClient._send_handling_auth at 0x7df1944f8220>
                     └ <httpx.AsyncClient object at 0x7df1425fd790>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
                     │    └ <function AsyncClient._send_handling_redirects at 0x7df1944f82c0>
                     └ <httpx.AsyncClient object at 0x7df1425fd790>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
                     │    │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │    └ <function AsyncClient._send_single_request at 0x7df1944f8360>
                     └ <httpx.AsyncClient object at 0x7df1425fd790>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
                     │         │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │         └ <function AsyncHTTPTransport.handle_async_request at 0x7df1944f4fe0>
                     └ <httpx.AsyncHTTPTransport object at 0x7df1407ff260>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         └ <function map_httpcore_exceptions at 0x7df1944f45e0>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ConnectError(SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'))
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_httpcore_exceptions at 0x7df1407e9040>
    └ <contextlib._GeneratorContextManager object at 0x7df1407ff0e0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
          │          └ '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'
          └ <class 'httpx.ConnectError'>

httpx.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
2025-05-28 10:02:08 | ERROR    | app.agents.api.routes:get_owned_agents:1061 - 获取用户拥有的智能体失败: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
Traceback (most recent call last):

  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
                 │    │     │                    └ <Request [b'GET']>
                 │    │     └ <function AsyncConnectionPool.handle_async_request at 0x7df19cac7ce0>
                 │    └ <AsyncConnectionPool [Requests: 0 active, 0 queued | Connections: 0 active, 0 idle]>
                 └ <httpx.AsyncHTTPTransport object at 0x7df1407fddf0>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
                     │          └ <function AsyncHTTPConnection.handle_async_request at 0x7df19cac6fc0>
                     └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 101, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
                   │    │        └ <Request [b'GET']>
                   │    └ <function AsyncHTTPConnection._connect at 0x7df19cac7060>
                   └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 156, in _connect
    stream = await stream.start_tls(**kwargs)
                   │      │           └ {'ssl_context': <ssl.SSLContext object at 0x7df140609cd0>, 'server_hostname': 'hia.ailoveworld.cn', 'timeout': 5.0}
                   │      └ <function AnyIOStream.start_tls at 0x7df19cad68e0>
                   └ <httpcore._backends.anyio.AnyIOStream object at 0x7df140632d50>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_backends/anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
         │              └ {<class 'TimeoutError'>: <class 'httpcore.ConnectTimeout'>, <class 'anyio.BrokenResourceError'>: <class 'httpcore.ConnectErro...
         └ <function map_exceptions at 0x7df19cbf2a20>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)')
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_exceptions at 0x7df140678e50>
    └ <contextlib._GeneratorContextManager object at 0x7df140632e10>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
          └ <class 'httpcore.ConnectError'>

httpcore.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 4
               │     └ 29
               └ <function _main at 0x7df19eb76980>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 4
           │    └ <function BaseProcess._bootstrap at 0x7df19ec94c20>
           └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7df19ec94180>
    └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7df19e900410>, 'target': <bound method Process.target of <uvicorn.supervisors.m...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
    │    └ <function subprocess_started at 0x7df19df319e0>
    └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Process.target of <uvicorn.supervisors.multiprocess.Process object at 0x7df19dfe3080>>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/supervisors/multiprocess.py", line 63, in target
    return self.real_target(sockets)
           │    │           └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │    └ <bound method Server.run of <uvicorn.server.Server object at 0x7df19dfe3110>>
           └ <uvicorn.supervisors.multiprocess.Process object at 0x7df19dfe3080>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7df19df30f40>
           │       │   └ <uvicorn.server.Server object at 0x7df19dfe3110>
           │       └ <function run at 0x7df19e87b420>
           └ <module 'asyncio' from '/usr/local/lib/python3.12/asyncio/__init__.py'>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7df19de38900>
           │      └ <function Runner.run at 0x7df19e856ac0>
           └ <asyncio.runners.Runner object at 0x7df19ece2270>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.12/site-packages/uvicorn/server.py:70> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7df19df08110>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7df19ece2270>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7df19e90cb30>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1407ff2...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1407...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │    └ <fastapi.applications.FastAPI object at 0x7df19d1369c0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7df19e90cb30>
  File "/usr/local/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1407ff2...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1407...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 409, in _sentry_patched_asgi_app
    return await middleware(scope, receive, send)
                 │          │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1407ff2...
                 │          │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1407...
                 │          └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7df14061bc90>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 158, in _run_asgi3
    return await self._run_app(scope, receive, send, asgi_version=3)
                 │    │        │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1407ff2...
                 │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1407...
                 │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │    └ <function SentryAsgiMiddleware._run_app at 0x7df194443ec0>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7df14061bc90>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 255, in _run_app
    return await self.app(
                 │    └ <member 'app' of 'SentryAsgiMiddleware' objects>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7df14061bc90>
  File "/usr/local/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function SentryAsgiMiddleware._run_app.<locals>._sentry_wrapped_send at 0x7df1425dbd80>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1407...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7df1426dbb60>
          └ <fastapi.applications.FastAPI object at 0x7df19d1369c0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7df1425db880>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │        └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7df1426dbb60>
                 └ <function ServerErrorMiddleware.__call__ at 0x7df197fc3e20>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7df1425db9c0>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7df1426dbb60>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 298, in _sentry_exceptionmiddleware_call
    await old_call(self, scope, receive, send)
          │        │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7df1425db9c0>
          │        │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
          │        │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
          └ <function _enable_span_for_middleware.<locals>._create_span_call at 0x7df14262e700>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7df1425db600>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
                 └ <function ExceptionMiddleware.__call__ at 0x7df197ffc4a0>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7df1425db600>
          │                            │    │    │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x7df1407fffe0>
          │                            │    └ <fastapi.routing.APIRouter object at 0x7df1947645f0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
          └ <function wrap_app_handling_exceptions at 0x7df197fa1d00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425d9760>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x7df1947645f0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425d9760>
          │    │                │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x7df1947645f0>>
          └ <fastapi.routing.APIRouter object at 0x7df1947645f0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425d9760>
          │     │      │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │     └ <function Route.handle at 0x7df197fa31a0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425d9760>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x7df14274b4c0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425d9760>
          │                            │    │        │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x7df1407fd850>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x7df1425d9ee0>
          └ <function wrap_app_handling_exceptions at 0x7df197fa1d00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df1425da520>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df1425dade0>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x7df1425d9ee0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x7df1407fd850>
                     └ <function patch_get_request_handler.<locals>._sentry_get_request_handler.<locals>._sentry_app at 0x7df142b62f20>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/fastapi.py", line 143, in _sentry_app
    return await old_app(*args, **kwargs)
                 │        │       └ {}
                 │        └ (<starlette.requests.Request object at 0x7df1407fd850>,)
                 └ <function get_request_handler.<locals>.app at 0x7df142b60040>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x7df197fa16c0>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': User(username='test_by_wzz', password='$2b$10$LzhNsBGw3F6RCs3Q7F29X...ZaOlXV1CPMnAWeZQ9r3vk5FzCGlJu', create...
                 │         └ <function get_owned_agents at 0x7df145d28cc0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[Dependant(path_p...

> File "/app/app/agents/api/routes.py", line 1037, in get_owned_agents
    keys = await billing_client.keys.list_available_keys(
                 │              │    └ <function Keys.list_available_keys at 0x7df18d017d80>
                 │              └ <billing.keys.Keys object at 0x7df1407fd790>
                 └ <billing.HkJingXiuBilling object at 0x7df1425fee40>

  File "/usr/local/lib/python3.12/site-packages/billing/keys/__init__.py", line 61, in list_available_keys
    response = await self.client.get("/keys/available", params=params)
                     │    │      │                             └ {'user_id': '149', 'service_code': 'AGC'}
                     │    │      └ <function HttpClient.get at 0x7df18d016a20>
                     │    └ <billing.http.HttpClient object at 0x7df1425fef90>
                     └ <billing.keys.Keys object at 0x7df1407fd790>
  File "/usr/local/lib/python3.12/site-packages/billing/http.py", line 38, in get
    response = await client.get(
                     │      └ <function AsyncClient.get at 0x7df1944f8400>
                     └ <httpx.AsyncClient object at 0x7df1407fe720>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
                 │    └ <function AsyncClient.request at 0x7df1944f7f60>
                 └ <httpx.AsyncClient object at 0x7df1407fe720>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
                 │    │    │             │                      └ <httpx._client.UseClientDefault object at 0x7df194543290>
                 │    │    │             └ <httpx._client.UseClientDefault object at 0x7df194543290>
                 │    │    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                 │    └ <function _install_httpx_async_client.<locals>.send at 0x7df18dfd5620>
                 └ <httpx.AsyncClient object at 0x7df1407fe720>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/httpx.py", line 142, in send
    rv = await real_send(self, request, **kwargs)
               │         │     │          └ {'auth': <httpx._client.UseClientDefault object at 0x7df194543290>, 'follow_redirects': <httpx._client.UseClientDefault objec...
               │         │     └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
               │         └ <httpx.AsyncClient object at 0x7df1407fe720>
               └ <function AsyncClient.send at 0x7df1944f8180>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
                     │    └ <function AsyncClient._send_handling_auth at 0x7df1944f8220>
                     └ <httpx.AsyncClient object at 0x7df1407fe720>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
                     │    └ <function AsyncClient._send_handling_redirects at 0x7df1944f82c0>
                     └ <httpx.AsyncClient object at 0x7df1407fe720>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
                     │    │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │    └ <function AsyncClient._send_single_request at 0x7df1944f8360>
                     └ <httpx.AsyncClient object at 0x7df1407fe720>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
                     │         │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │         └ <function AsyncHTTPTransport.handle_async_request at 0x7df1944f4fe0>
                     └ <httpx.AsyncHTTPTransport object at 0x7df1407fddf0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         └ <function map_httpcore_exceptions at 0x7df1944f45e0>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ConnectError(SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'))
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_httpcore_exceptions at 0x7df1425c7b40>
    └ <contextlib._GeneratorContextManager object at 0x7df1406323f0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
          │          └ '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'
          └ <class 'httpx.ConnectError'>

httpx.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
2025-05-28 10:02:10 | ERROR    | app.agents.api.routes:get_owned_agents:1061 - 获取用户拥有的智能体失败: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
Traceback (most recent call last):

  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
                 │    │     │                    └ <Request [b'GET']>
                 │    │     └ <function AsyncConnectionPool.handle_async_request at 0x7df19cac7ce0>
                 │    └ <AsyncConnectionPool [Requests: 0 active, 0 queued | Connections: 0 active, 0 idle]>
                 └ <httpx.AsyncHTTPTransport object at 0x7df1425fe480>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
                     │          └ <function AsyncHTTPConnection.handle_async_request at 0x7df19cac6fc0>
                     └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 101, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
                   │    │        └ <Request [b'GET']>
                   │    └ <function AsyncHTTPConnection._connect at 0x7df19cac7060>
                   └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 156, in _connect
    stream = await stream.start_tls(**kwargs)
                   │      │           └ {'ssl_context': <ssl.SSLContext object at 0x7df141b14c50>, 'server_hostname': 'hia.ailoveworld.cn', 'timeout': 5.0}
                   │      └ <function AnyIOStream.start_tls at 0x7df19cad68e0>
                   └ <httpcore._backends.anyio.AnyIOStream object at 0x7df1407ff890>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_backends/anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
         │              └ {<class 'TimeoutError'>: <class 'httpcore.ConnectTimeout'>, <class 'anyio.BrokenResourceError'>: <class 'httpcore.ConnectErro...
         └ <function map_exceptions at 0x7df19cbf2a20>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)')
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_exceptions at 0x7df1407cbc40>
    └ <contextlib._GeneratorContextManager object at 0x7df1425fc0e0>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
          └ <class 'httpcore.ConnectError'>

httpcore.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 4
               │     └ 29
               └ <function _main at 0x7df19eb76980>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 4
           │    └ <function BaseProcess._bootstrap at 0x7df19ec94c20>
           └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7df19ec94180>
    └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7df19e900410>, 'target': <bound method Process.target of <uvicorn.supervisors.m...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
    │    └ <function subprocess_started at 0x7df19df319e0>
    └ <SpawnProcess name='SpawnProcess-6' parent=7 started>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Process.target of <uvicorn.supervisors.multiprocess.Process object at 0x7df19dfe3080>>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/supervisors/multiprocess.py", line 63, in target
    return self.real_target(sockets)
           │    │           └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │    └ <bound method Server.run of <uvicorn.server.Server object at 0x7df19dfe3110>>
           └ <uvicorn.supervisors.multiprocess.Process object at 0x7df19dfe3080>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7df19df30f40>
           │       │   └ <uvicorn.server.Server object at 0x7df19dfe3110>
           │       └ <function run at 0x7df19e87b420>
           └ <module 'asyncio' from '/usr/local/lib/python3.12/asyncio/__init__.py'>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7df19de38900>
           │      └ <function Runner.run at 0x7df19e856ac0>
           └ <asyncio.runners.Runner object at 0x7df19ece2270>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.12/site-packages/uvicorn/server.py:70> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7df19df08110>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7df19ece2270>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7df19e90cb30>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425fda...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │    └ <fastapi.applications.FastAPI object at 0x7df19d1369c0>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7df19e90cb30>
  File "/usr/local/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425fda...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 409, in _sentry_patched_asgi_app
    return await middleware(scope, receive, send)
                 │          │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425fda...
                 │          │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425...
                 │          └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7df14116ea20>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 158, in _run_asgi3
    return await self._run_app(scope, receive, send, asgi_version=3)
                 │    │        │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425fda...
                 │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425...
                 │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │    └ <function SentryAsgiMiddleware._run_app at 0x7df194443ec0>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7df14116ea20>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 255, in _run_app
    return await self.app(
                 │    └ <member 'app' of 'SentryAsgiMiddleware' objects>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7df14116ea20>
  File "/usr/local/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function SentryAsgiMiddleware._run_app.<locals>._sentry_wrapped_send at 0x7df1411b4cc0>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7df1425...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7df1426dbb60>
          └ <fastapi.applications.FastAPI object at 0x7df19d1369c0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7df1406056c0>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │        └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7df1426dbb60>
                 └ <function ServerErrorMiddleware.__call__ at 0x7df197fc3e20>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7df140606200>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7df1426dbb60>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 298, in _sentry_exceptionmiddleware_call
    await old_call(self, scope, receive, send)
          │        │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7df140606200>
          │        │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
          │        │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
          └ <function _enable_span_for_middleware.<locals>._create_span_call at 0x7df14262e700>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7df140605260>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
                 └ <function ExceptionMiddleware.__call__ at 0x7df197ffc4a0>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7df140605260>
          │                            │    │    │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x7df1425fff20>
          │                            │    └ <fastapi.routing.APIRouter object at 0x7df1947645f0>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7df1426dbc20>
          └ <function wrap_app_handling_exceptions at 0x7df197fa1d00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df140605f80>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x7df1947645f0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df140605f80>
          │    │                │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x7df1947645f0>>
          └ <fastapi.routing.APIRouter object at 0x7df1947645f0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df140605f80>
          │     │      │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │     └ <function Route.handle at 0x7df197fa31a0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df140605f80>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x7df14274b4c0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df140605f80>
          │                            │    │        │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x7df1425fe060>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x7df1406060c0>
          └ <function wrap_app_handling_exceptions at 0x7df197fa1d00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7df140606700>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7df140607f60>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x7df1406060c0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x7df1425fe060>
                     └ <function patch_get_request_handler.<locals>._sentry_get_request_handler.<locals>._sentry_app at 0x7df142b62f20>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/fastapi.py", line 143, in _sentry_app
    return await old_app(*args, **kwargs)
                 │        │       └ {}
                 │        └ (<starlette.requests.Request object at 0x7df1425fe060>,)
                 └ <function get_request_handler.<locals>.app at 0x7df142b60040>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x7df197fa16c0>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': User(username='test_by_wzz', password='$2b$10$LzhNsBGw3F6RCs3Q7F29X...ZaOlXV1CPMnAWeZQ9r3vk5FzCGlJu', create...
                 │         └ <function get_owned_agents at 0x7df145d28cc0>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[Dependant(path_p...

> File "/app/app/agents/api/routes.py", line 1037, in get_owned_agents
    keys = await billing_client.keys.list_available_keys(
                 │              │    └ <function Keys.list_available_keys at 0x7df18d017d80>
                 │              └ <billing.keys.Keys object at 0x7df1425ffb00>
                 └ <billing.HkJingXiuBilling object at 0x7df1406307d0>

  File "/usr/local/lib/python3.12/site-packages/billing/keys/__init__.py", line 61, in list_available_keys
    response = await self.client.get("/keys/available", params=params)
                     │    │      │                             └ {'user_id': '149', 'service_code': 'AGC'}
                     │    │      └ <function HttpClient.get at 0x7df18d016a20>
                     │    └ <billing.http.HttpClient object at 0x7df1425ffc20>
                     └ <billing.keys.Keys object at 0x7df1425ffb00>
  File "/usr/local/lib/python3.12/site-packages/billing/http.py", line 38, in get
    response = await client.get(
                     │      └ <function AsyncClient.get at 0x7df1944f8400>
                     └ <httpx.AsyncClient object at 0x7df1425fee10>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
                 │    └ <function AsyncClient.request at 0x7df1944f7f60>
                 └ <httpx.AsyncClient object at 0x7df1425fee10>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
                 │    │    │             │                      └ <httpx._client.UseClientDefault object at 0x7df194543290>
                 │    │    │             └ <httpx._client.UseClientDefault object at 0x7df194543290>
                 │    │    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                 │    └ <function _install_httpx_async_client.<locals>.send at 0x7df18dfd5620>
                 └ <httpx.AsyncClient object at 0x7df1425fee10>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/httpx.py", line 142, in send
    rv = await real_send(self, request, **kwargs)
               │         │     │          └ {'auth': <httpx._client.UseClientDefault object at 0x7df194543290>, 'follow_redirects': <httpx._client.UseClientDefault objec...
               │         │     └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
               │         └ <httpx.AsyncClient object at 0x7df1425fee10>
               └ <function AsyncClient.send at 0x7df1944f8180>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
                     │    └ <function AsyncClient._send_handling_auth at 0x7df1944f8220>
                     └ <httpx.AsyncClient object at 0x7df1425fee10>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
                     │    └ <function AsyncClient._send_handling_redirects at 0x7df1944f82c0>
                     └ <httpx.AsyncClient object at 0x7df1425fee10>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
                     │    │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │    └ <function AsyncClient._send_single_request at 0x7df1944f8360>
                     └ <httpx.AsyncClient object at 0x7df1425fee10>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
                     │         │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │         └ <function AsyncHTTPTransport.handle_async_request at 0x7df1944f4fe0>
                     └ <httpx.AsyncHTTPTransport object at 0x7df1425fe480>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         └ <function map_httpcore_exceptions at 0x7df1944f45e0>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ConnectError(SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'))
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_httpcore_exceptions at 0x7df1425c6d40>
    └ <contextlib._GeneratorContextManager object at 0x7df1425ff2f0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
          │          └ '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'
          └ <class 'httpx.ConnectError'>

httpx.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
2025-05-28 10:46:32 | ERROR    | app.agents.api.routes:get_owned_agents:1061 - 获取用户拥有的智能体失败: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
Traceback (most recent call last):

  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
                 │    │     │                    └ <Request [b'GET']>
                 │    │     └ <function AsyncConnectionPool.handle_async_request at 0x7cd72761bce0>
                 │    └ <AsyncConnectionPool [Requests: 0 active, 0 queued | Connections: 0 active, 0 idle]>
                 └ <httpx.AsyncHTTPTransport object at 0x7cd6cc8cca40>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
                     │          └ <function AsyncHTTPConnection.handle_async_request at 0x7cd72761afc0>
                     └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 101, in handle_async_request
    raise exc
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 78, in handle_async_request
    stream = await self._connect(request)
                   │    │        └ <Request [b'GET']>
                   │    └ <function AsyncHTTPConnection._connect at 0x7cd72761b060>
                   └ <AsyncHTTPConnection [CONNECTION FAILED]>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_async/connection.py", line 156, in _connect
    stream = await stream.start_tls(**kwargs)
                   │      │           └ {'ssl_context': <ssl.SSLContext object at 0x7cd6cd40f8d0>, 'server_hostname': 'hia.ailoveworld.cn', 'timeout': 5.0}
                   │      └ <function AnyIOStream.start_tls at 0x7cd72762a8e0>
                   └ <httpcore._backends.anyio.AnyIOStream object at 0x7cd6cc8cee10>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_backends/anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
         │              └ {<class 'TimeoutError'>: <class 'httpcore.ConnectTimeout'>, <class 'anyio.BrokenResourceError'>: <class 'httpcore.ConnectErro...
         └ <function map_exceptions at 0x7cd727752a20>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)')
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_exceptions at 0x7cd6cd323970>
    └ <contextlib._GeneratorContextManager object at 0x7cd6cc8cee70>
  File "/usr/local/lib/python3.12/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
          └ <class 'httpcore.ConnectError'>

httpcore.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)


The above exception was the direct cause of the following exception:


Traceback (most recent call last):

  File "<string>", line 1, in <module>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 122, in spawn_main
    exitcode = _main(fd, parent_sentinel)
               │     │   └ 6
               │     └ 9
               └ <function _main at 0x7cd7296c2980>
  File "/usr/local/lib/python3.12/multiprocessing/spawn.py", line 135, in _main
    return self._bootstrap(parent_sentinel)
           │    │          └ 6
           │    └ <function BaseProcess._bootstrap at 0x7cd7297e8c20>
           └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 314, in _bootstrap
    self.run()
    │    └ <function BaseProcess.run at 0x7cd7297e8180>
    └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
  File "/usr/local/lib/python3.12/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
    │    │        │    │        │    └ {'config': <uvicorn.config.Config object at 0x7cd72944fc80>, 'target': <bound method Process.target of <uvicorn.supervisors.m...
    │    │        │    │        └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
    │    │        │    └ ()
    │    │        └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
    │    └ <function subprocess_started at 0x7cd728a7d9e0>
    └ <SpawnProcess name='SpawnProcess-1' parent=7 started>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/_subprocess.py", line 80, in subprocess_started
    target(sockets=sockets)
    │              └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
    └ <bound method Process.target of <uvicorn.supervisors.multiprocess.Process object at 0x7cd728b26ed0>>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/supervisors/multiprocess.py", line 63, in target
    return self.real_target(sockets)
           │    │           └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │    └ <bound method Server.run of <uvicorn.server.Server object at 0x7cd728b27080>>
           └ <uvicorn.supervisors.multiprocess.Process object at 0x7cd728b26ed0>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/server.py", line 66, in run
    return asyncio.run(self.serve(sockets=sockets))
           │       │   │    │             └ [<socket.socket fd=3, family=2, type=1, proto=0, laddr=('0.0.0.0', 8000)>]
           │       │   │    └ <function Server.serve at 0x7cd728a7cf40>
           │       │   └ <uvicorn.server.Server object at 0x7cd728b27080>
           │       └ <function run at 0x7cd7293cb420>
           └ <module 'asyncio' from '/usr/local/lib/python3.12/asyncio/__init__.py'>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 195, in run
    return runner.run(main)
           │      │   └ <coroutine object Server.serve at 0x7cd72897c900>
           │      └ <function Runner.run at 0x7cd729426ac0>
           └ <asyncio.runners.Runner object at 0x7cd7296156a0>
  File "/usr/local/lib/python3.12/asyncio/runners.py", line 118, in run
    return self._loop.run_until_complete(task)
           │    │     │                  └ <Task pending name='Task-1' coro=<Server.serve() running at /usr/local/lib/python3.12/site-packages/uvicorn/server.py:70> wai...
           │    │     └ <cyfunction Loop.run_until_complete at 0x7cd728a4c110>
           │    └ <uvloop.Loop running=True closed=False debug=False>
           └ <asyncio.runners.Runner object at 0x7cd7296156a0>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/protocols/http/httptools_impl.py", line 409, in run_asgi
    result = await app(  # type: ignore[func-returns-value]
                   └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7cd728b08b90>
  File "/usr/local/lib/python3.12/site-packages/uvicorn/middleware/proxy_headers.py", line 60, in __call__
    return await self.app(scope, receive, send)
                 │    │   │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7cd6cc8cd3...
                 │    │   │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7cd6cc8...
                 │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │    └ <fastapi.applications.FastAPI object at 0x7cd727ce2a50>
                 └ <uvicorn.middleware.proxy_headers.ProxyHeadersMiddleware object at 0x7cd728b08b90>
  File "/usr/local/lib/python3.12/site-packages/fastapi/applications.py", line 1054, in __call__
    await super().__call__(scope, receive, send)
                           │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7cd6cc8cd3...
                           │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7cd6cc8...
                           └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 409, in _sentry_patched_asgi_app
    return await middleware(scope, receive, send)
                 │          │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7cd6cc8cd3...
                 │          │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7cd6cc8...
                 │          └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7cd6cd419cb0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 158, in _run_asgi3
    return await self._run_app(scope, receive, send, asgi_version=3)
                 │    │        │      │        └ <bound method RequestResponseCycle.send of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7cd6cc8cd3...
                 │    │        │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7cd6cc8...
                 │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │    └ <function SentryAsgiMiddleware._run_app at 0x7cd7240f3ec0>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7cd6cd419cb0>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/asgi.py", line 255, in _run_app
    return await self.app(
                 │    └ <member 'app' of 'SentryAsgiMiddleware' objects>
                 └ <sentry_sdk.integrations.asgi.SentryAsgiMiddleware object at 0x7cd6cd419cb0>
  File "/usr/local/lib/python3.12/site-packages/starlette/applications.py", line 112, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function SentryAsgiMiddleware._run_app.<locals>._sentry_wrapped_send at 0x7cd6cc860f40>
          │    │                │      └ <bound method RequestResponseCycle.receive of <uvicorn.protocols.http.httptools_impl.RequestResponseCycle object at 0x7cd6cc8...
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7cd6cd5e7920>
          └ <fastapi.applications.FastAPI object at 0x7cd727ce2a50>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7cd6cc861e40>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │        └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7cd6cd5e7920>
                 └ <function ServerErrorMiddleware.__call__ at 0x7cd726b6be20>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/errors.py", line 165, in __call__
    await self.app(scope, receive, _send)
          │    │   │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7cd6cc862840>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7cd6cd5e79b0>
          └ <starlette.middleware.errors.ServerErrorMiddleware object at 0x7cd6cd5e7920>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 298, in _sentry_exceptionmiddleware_call
    await old_call(self, scope, receive, send)
          │        │     │      │        └ <function ServerErrorMiddleware.__call__.<locals>._send at 0x7cd6cc862840>
          │        │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
          │        │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7cd6cd5e79b0>
          └ <function _enable_span_for_middleware.<locals>._create_span_call at 0x7cd6cd53e700>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/starlette.py", line 200, in _create_span_call
    return await old_call(app, scope, new_receive, new_send, **kwargs)
                 │        │    │      │            │           └ {}
                 │        │    │      │            └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7cd6cc862f20>
                 │        │    │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
                 │        │    └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
                 │        └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7cd6cd5e79b0>
                 └ <function ExceptionMiddleware.__call__ at 0x7cd726ba44a0>
  File "/usr/local/lib/python3.12/site-packages/starlette/middleware/exceptions.py", line 62, in __call__
    await wrap_app_handling_exceptions(self.app, conn)(scope, receive, send)
          │                            │    │    │     │      │        └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_send at 0x7cd6cc862f20>
          │                            │    │    │     │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
          │                            │    │    │     └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │                            │    │    └ <starlette.requests.Request object at 0x7cd6cc8cda60>
          │                            │    └ <fastapi.routing.APIRouter object at 0x7cd724452990>
          │                            └ <starlette.middleware.exceptions.ExceptionMiddleware object at 0x7cd6cd5e79b0>
          └ <function wrap_app_handling_exceptions at 0x7cd726b49d00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7cd6cc862de0>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          └ <fastapi.routing.APIRouter object at 0x7cd724452990>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 715, in __call__
    await self.middleware_stack(scope, receive, send)
          │    │                │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7cd6cc862de0>
          │    │                │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
          │    │                └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <bound method Router.app of <fastapi.routing.APIRouter object at 0x7cd724452990>>
          └ <fastapi.routing.APIRouter object at 0x7cd724452990>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 735, in app
    await route.handle(scope, receive, send)
          │     │      │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7cd6cc862de0>
          │     │      │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
          │     │      └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │     └ <function Route.handle at 0x7cd726b4b1a0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 288, in handle
    await self.app(scope, receive, send)
          │    │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7cd6cc862de0>
          │    │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
          │    │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │    └ <function request_response.<locals>.app at 0x7cd6cd6574c0>
          └ APIRoute(path='/agents/owned', name='get_owned_agents', methods=['GET'])
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 76, in app
    await wrap_app_handling_exceptions(app, request)(scope, receive, send)
          │                            │    │        │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7cd6cc862de0>
          │                            │    │        │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
          │                            │    │        └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          │                            │    └ <starlette.requests.Request object at 0x7cd6cc8ce420>
          │                            └ <function request_response.<locals>.app.<locals>.app at 0x7cd6cc8628e0>
          └ <function wrap_app_handling_exceptions at 0x7cd726b49d00>
  File "/usr/local/lib/python3.12/site-packages/starlette/_exception_handler.py", line 42, in wrapped_app
    await app(scope, receive, sender)
          │   │      │        └ <function wrap_app_handling_exceptions.<locals>.wrapped_app.<locals>.sender at 0x7cd6cc863560>
          │   │      └ <function _enable_span_for_middleware.<locals>._create_span_call.<locals>._sentry_receive at 0x7cd6cc861940>
          │   └ {'type': 'http', 'asgi': {'version': '3.0', 'spec_version': '2.3'}, 'http_version': '1.1', 'server': ('172.18.0.8', 8000), 'c...
          └ <function request_response.<locals>.app.<locals>.app at 0x7cd6cc8628e0>
  File "/usr/local/lib/python3.12/site-packages/starlette/routing.py", line 73, in app
    response = await f(request)
                     │ └ <starlette.requests.Request object at 0x7cd6cc8ce420>
                     └ <function patch_get_request_handler.<locals>._sentry_get_request_handler.<locals>._sentry_app at 0x7cd6cd772f20>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/fastapi.py", line 143, in _sentry_app
    return await old_app(*args, **kwargs)
                 │        │       └ {}
                 │        └ (<starlette.requests.Request object at 0x7cd6cc8ce420>,)
                 └ <function get_request_handler.<locals>.app at 0x7cd6cd770040>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 301, in app
    raw_response = await run_endpoint_function(
                         └ <function run_endpoint_function at 0x7cd726b496c0>
  File "/usr/local/lib/python3.12/site-packages/fastapi/routing.py", line 212, in run_endpoint_function
    return await dependant.call(**values)
                 │         │      └ {'current_user': User(username='test_by_wzz', password='$2b$10$LzhNsBGw3F6RCs3Q7F29X...ZaOlXV1CPMnAWeZQ9r3vk5FzCGlJu', create...
                 │         └ <function get_owned_agents at 0x7cd6cdb38f40>
                 └ Dependant(path_params=[], query_params=[], header_params=[], cookie_params=[], body_params=[], dependencies=[Dependant(path_p...

> File "/app/app/agents/api/routes.py", line 1037, in get_owned_agents
    keys = await billing_client.keys.list_available_keys(
                 │              │    └ <function Keys.list_available_keys at 0x7cd718027d80>
                 │              └ <billing.keys.Keys object at 0x7cd6cc977950>
                 └ <billing.HkJingXiuBilling object at 0x7cd6cd43f200>

  File "/usr/local/lib/python3.12/site-packages/billing/keys/__init__.py", line 61, in list_available_keys
    response = await self.client.get("/keys/available", params=params)
                     │    │      │                             └ {'user_id': '149', 'service_code': 'AGC'}
                     │    │      └ <function HttpClient.get at 0x7cd718026a20>
                     │    └ <billing.http.HttpClient object at 0x7cd6cd43f020>
                     └ <billing.keys.Keys object at 0x7cd6cc977950>
  File "/usr/local/lib/python3.12/site-packages/billing/http.py", line 38, in get
    response = await client.get(
                     │      └ <function AsyncClient.get at 0x7cd71bf08400>
                     └ <httpx.AsyncClient object at 0x7cd6cc8cd850>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1768, in get
    return await self.request(
                 │    └ <function AsyncClient.request at 0x7cd71bf07f60>
                 └ <httpx.AsyncClient object at 0x7cd6cc8cd850>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1540, in request
    return await self.send(request, auth=auth, follow_redirects=follow_redirects)
                 │    │    │             │                      └ <httpx._client.UseClientDefault object at 0x7cd7243abbc0>
                 │    │    │             └ <httpx._client.UseClientDefault object at 0x7cd7243abbc0>
                 │    │    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                 │    └ <function _install_httpx_async_client.<locals>.send at 0x7cd7189e5620>
                 └ <httpx.AsyncClient object at 0x7cd6cc8cd850>
  File "/usr/local/lib/python3.12/site-packages/sentry_sdk/integrations/httpx.py", line 142, in send
    rv = await real_send(self, request, **kwargs)
               │         │     │          └ {'auth': <httpx._client.UseClientDefault object at 0x7cd7243abbc0>, 'follow_redirects': <httpx._client.UseClientDefault objec...
               │         │     └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
               │         └ <httpx.AsyncClient object at 0x7cd6cc8cd850>
               └ <function AsyncClient.send at 0x7cd71bf08180>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1629, in send
    response = await self._send_handling_auth(
                     │    └ <function AsyncClient._send_handling_auth at 0x7cd71bf08220>
                     └ <httpx.AsyncClient object at 0x7cd6cc8cd850>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
                     │    └ <function AsyncClient._send_handling_redirects at 0x7cd71bf082c0>
                     └ <httpx.AsyncClient object at 0x7cd6cc8cd850>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
                     │    │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │    └ <function AsyncClient._send_single_request at 0x7cd71bf08360>
                     └ <httpx.AsyncClient object at 0x7cd6cc8cd850>
  File "/usr/local/lib/python3.12/site-packages/httpx/_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
                     │         │                    └ <Request('GET', 'https://hia.ailoveworld.cn/api/keys/available?user_id=149&service_code=AGC')>
                     │         └ <function AsyncHTTPTransport.handle_async_request at 0x7cd71bf04fe0>
                     └ <httpx.AsyncHTTPTransport object at 0x7cd6cc8cca40>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
         └ <function map_httpcore_exceptions at 0x7cd71bf045e0>
  File "/usr/local/lib/python3.12/contextlib.py", line 158, in __exit__
    self.gen.throw(value)
    │    │   │     └ ConnectError(SSLError(1, '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'))
    │    │   └ <method 'throw' of 'generator' objects>
    │    └ <generator object map_httpcore_exceptions at 0x7cd6cc8d5440>
    └ <contextlib._GeneratorContextManager object at 0x7cd6cc8cc0b0>
  File "/usr/local/lib/python3.12/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
          │          └ '[SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)'
          └ <class 'httpx.ConnectError'>

httpx.ConnectError: [SSL: TLSV1_ALERT_INTERNAL_ERROR] tlsv1 alert internal error (_ssl.c:1010)
