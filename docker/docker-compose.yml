services:
  db:
    image: postgres:17.0
    container_name: db
    environment:
      TZ: Asia/Shanghai
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: plwIvnCDBi
      POSTGRES_DB: aq
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - ./db/data:/var/lib/postgresql/data/pgdata
    ports:
      - 5432:5432
    restart: unless-stopped
  redis:
    image: redis
    container_name: redis
    restart: always
    command: redis-server --requirepass 123394 --bind 0.0.0.0 -::*
    ports:
      - 6379:6379
  gateway:
    image: caddy
    container_name: gateway
    restart: unless-stopped
    environment:
      TZ: Asia/Shanghai
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./gateway/Caddyfile:/etc/caddy/Caddyfile
      - ./gateway/.certificates:/data/caddy/certificates
  client-api:
    build:
      context: ../api
      dockerfile: Dockerfile
      args:
        WORKERS: 4
    container_name: client-api
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      TZ: Asia/Shanghai
      TYPE: client
    volumes:
      - ./api/logs:/app/.logs
      - ./api/.env:/app/.env
  client-ui:
    build:
      context: ../client
      dockerfile: Dockerfile
    container_name: client-ui
    restart: unless-stopped
    ports:
      - "8001:3000"
    environment:
      TZ: Asia/Shanghai
  admin-api:
    build:
      context: ../api
      dockerfile: Dockerfile
      args:
        WORKERS: 1
    container_name: admin-api
    restart: unless-stopped
    ports:
      - "8002:8000"
    environment:
      TZ: Asia/Shanghai
      TYPE: admin
    volumes:
      - ./api/logs:/app/.logs
      - ./api/.env:/app/.env
  admin-ui:
    build:
      context: ../admin
      dockerfile: Dockerfile
    container_name: admin-ui
    restart: unless-stopped
    ports:
      - "8003:3000"
    environment:
      TZ: Asia/Shanghai
