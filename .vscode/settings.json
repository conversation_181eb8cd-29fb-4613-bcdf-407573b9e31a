{
  "python.defaultInterpreterPath": "${workspaceFolder}/api/.venv/Scripts/python.exe",
  "editor.codeActionsOnSave": {
    "source.fixAll": "explicit"
  },
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.tabSize": 2,
  "eslint.validate": [
    "javascript",
    "javascriptreact",
    "vue",
    "react",
    "typescript",
    "typescriptreact"
  ],
  // 行末的空格、文件末尾的空行
  "files.trimTrailingWhitespace": true,
  "files.insertFinalNewline": true,
  "files.trimFinalNewlines": true,
  "files.eol": "\n",
  "[html]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[vue]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[react]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[css]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[scss]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[less]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[javascript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[typescript]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[json]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  "[markdown]": {
    "editor.defaultFormatter": "esbenp.prettier-vscode"
  },
  // 定义逗号分隔符后面的空格处理。
  "javascript.format.insertSpaceAfterCommaDelimiter": true,

  // 定义构造函数关键字后面的空格处理方式。
  "javascript.format.insertSpaceAfterConstructor": false,

  // 定义匿名函数的函数关键字后面的空格处理。
  "javascript.format.insertSpaceAfterFunctionKeywordForAnonymousFunctions": true,

  // 定义控制流语句中关键字后面的空格处理。
  "javascript.format.insertSpaceAfterKeywordsInControlFlowStatements": true,

  // 定义空大括号中左括号后和右括号前的空格处理方式。
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingEmptyBraces": true,

  // 定义 JSX 表达式括号中左括号后和右括号前的空格处理方式。
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingJsxExpressionBraces": false,

  // 定义非空大括号中左括号后和右括号前的空格处理方式。
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBraces": true,

  // 定义非空中括号的左括号后和右括号前的空格处理方式。
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyBrackets": false,

  // 定义非空小括号的左括号后和右括号前的空格处理方式。
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingNonemptyParenthesis": false,

  // 定义模板字符串括号中左括号后和右括号前的空格处理方式。
  "javascript.format.insertSpaceAfterOpeningAndBeforeClosingTemplateStringBraces": false,

  // 定义 for 语句中分号之后的空格处理方式。
  "javascript.format.insertSpaceAfterSemicolonInForStatements": true,

  // 定义二进制运算符后面的空格处理
  "javascript.format.insertSpaceBeforeAndAfterBinaryOperators": true,

  // 定义函数参数括号前的空格处理方式。
  "javascript.format.insertSpaceBeforeFunctionParenthesis": false,

  // 定义控制块的左括号是否放置在新的一行。
  "javascript.format.placeOpenBraceOnNewLineForControlBlocks": false,

  // 定义函数的左大括号是否放置在新的一行。
  "javascript.format.placeOpenBraceOnNewLineForFunctions": false
}
