{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "运行客户端api",
            "type": "shell",
            "command": ".venv/Scripts/Activate.ps1; uvicorn app.main:app --port 20000 --host 0.0.0.0 --reload",
            "problemMatcher": [],
            "options": {
                "cwd": "${workspaceFolder}/api",
                "env": {
                    "TYPE": "client",
                    "PYTHONPATH": "${workspaceFolder}/api"
                }
            }
        },
        {
            "label": "运行客户端界面",
            "type": "shell",
            "command": "pnpm run dev",
            "problemMatcher": [],
            "options": {
                "cwd": "${workspaceFolder}/client"
            }
        },
        {
            "label": "编译客户端界面",
            "type": "shell",
            "command": "pnpm run compile",
            "problemMatcher": [],
            "options": {
                "cwd": "${workspaceFolder}/client"
            }
        },
        {
            "label": "运行管理端api",
            "type": "shell",
            "command": ".venv/Scripts/Activate.ps1; uvicorn app.main:app --port 20001 --host 0.0.0.0 --reload",
            "problemMatcher": [],
            "options": {
                "cwd": "${workspaceFolder}/api",
                "env": {
                    "TYPE": "admin",
                    "PYTHONPATH": "${workspaceFolder}/api"
                }
            }
        },
        {
            "label": "运行管理端界面",
            "type": "shell",
            "command": "pnpm run dev",
            "problemMatcher": [],
            "options": {
                "cwd": "${workspaceFolder}/admin"
            }
        },
        {
            "label": "编译管理端界面",
            "type": "shell",
            "command": "pnpm run compile",
            "problemMatcher": [],
            "options": {
                "cwd": "${workspaceFolder}/admin"
            }
        }
    ]
}
