{
  "compilerOptions": {
    "target": "ES2020",
    "useDefineForClassFields": true,
    "lib": ["ES2020", "DOM", "DOM.Iterable"],
    "module": "ESNext",
    "skipLibCheck": true,

    /* Bundler mode */
    "moduleResolution": "bundler",
    "allowImportingTsExtensions": true,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",

    /* Linting */
    "strict": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "noFallthroughCasesInSwitch": true,


    /* Path Alias */
    "baseUrl": ".",
    "paths": {
      "~/*": ["src/*"],
      "@dify_schemas/*": ["./packages/dify_schema/src/*"]
    }
  },
  "include": ["src", "packages/dify_schema/src"],
  "references": [{ "path": "./tsconfig.node.json" }]
}
