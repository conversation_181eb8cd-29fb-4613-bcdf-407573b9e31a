import { defineConfig, presetUno, presetAttributify, presetIcons, presetTypography, presetWebFonts } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
    }),
    presetTypography(),
    presetWebFonts({
      provider: 'google',
      fonts: {
        sans: 'Roboto',
        serif: 'Merriweather',
        mono: 'Fira Code',
        custom: [
          {
            name: 'Open Sans',
            weights: ['400', '700'],
            italic: true,
          },
          {
            name: 'monospace',
            provider: 'none',
          },
        ],
      },
    }),
  ],
  shortcuts: [
    // 可以在这里添加自定义快捷方式
  ],
  rules: [
    // 可以在这里添加自定义规则
  ],
}) 