import { z } from 'zod';
export const DatasetTagSchema = z.object({
  id: z.string().describe('标签ID'),
  name: z.string().describe('标签名称'),
  type: z.string().describe('标签类型'),
  binding_count: z.number().describe('绑定数量'),
});

export type DatasetTag = z.infer<typeof DatasetTagSchema>;

/**
 * 数据集Schema
 *
 * @property id - 数据集ID
 * @property name - 数据集名称
 * @property description - 数据集描述
 * @property created_at - 创建时间
 * @property updated_at - 更新时间
 * @property created_by - 创建者
 * @property status - 数据集状态
 */
export const DatasetSchema = z.object({
  id: z.string().describe('数据集ID'),
  name: z.string().describe('数据集名称'),
  description: z.string().optional().describe('数据集描述'),
  created_at: z.number().optional().describe('创建时间'),
  updated_at: z.number().optional().describe('更新时间'),
  created_by: z.string().optional().describe('创建者'),
  status: z.string().optional().describe('数据集状态'),
  permission: z.string().describe('数据集权限'),
  data_source_type: z.string().describe('数据源类型'),
  indexing_technique: z.string().describe('索引技术'),
  app_count: z.number().describe('应用数量'),
  document_count: z.number().describe('文档数量'),
  word_count: z.number().describe('字数'),
  updated_by: z.string().nullable().describe('更新者'),
  tags: z.array(DatasetTagSchema).describe('标签列表'),
});

export type Dataset = z.infer<typeof DatasetSchema>;
/**
 * 数据集列表Schema
 */
export const DatasetListSchema = z.object({
  data: z.array(DatasetSchema).describe('数据集列表'),
  total: z.number().describe('总数'),
  has_more: z.boolean().describe('是否有更多'),
  limit: z.number().describe('每页数量'),
  page: z.number().describe('当前页码'),
});

export type DatasetList = z.infer<typeof DatasetListSchema>;
