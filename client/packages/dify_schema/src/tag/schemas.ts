import {z} from "zod";

export const TagType = z.enum(["app", "knowledge"]);
export type TagType = z.infer<typeof TagType>;

export const Tag1 = z.object({
  id: z.string().describe("标签唯一标识"),
  name: z.string().describe("标签名称"),
  type: TagType.describe("标签类型"),
  binding_count: z.number().optional().default(0).describe("绑定数量")
});

export type Tag1 = z.infer<typeof Tag1>;

export const BindingPayloads = z.object({
  tag_ids: z.array(z.string()).describe("要绑定的标签ID列表"),
  target_id: z.string().describe("目标对象ID"),
  type: TagType.describe("标签类型")
});

export type BindingPayloads = z.infer<typeof BindingPayloads>;
