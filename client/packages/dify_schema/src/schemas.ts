import {z} from "zod";

/**
 * 分页请求模型
 */
export const PaginationSchema = z.object({
  /** 当前页码 */
  page: z.number().default(1),
  /** 每页条数 */
  limit: z.number().default(10),
  /** 总条数 */
  total: z.number().optional(),
  /** 是否有更多数据 */
  has_more: z.boolean().optional(),
  /** 数据列表 */
  data: z.array(z.unknown()).optional()
});

/**
 * 错误模型
 */
export const ErrorSchema = z.object({
  /** 错误码 */
  code: z.string(),
  /** 错误信息 */
  message: z.string()
});

/**
 * 泛型对类型，包含一个值和一个错误
 */
export const PairSchema = z.object({
  /** 值 */
  value: z.unknown().optional(),
  /** 错误信息 */
  error: ErrorSchema.optional()
}).transform((data) => ({
  ...data,
  /** 判断是否成功 */
  isOk: () => data.error === undefined,
  /** 判断是否失败 */
  isErr: () => data.error !== undefined,
  /** 获取值，如果存在错误则抛出异常 */
  unwrap: () => {
    if (data.error) {
      throw new Error(`Unwrap failed: ${data.error}`);
    }
    return data.value;
  },
  /** 获取值，如果存在错误则返回默认值 */
  unwrapOr: (defaultValue: unknown) => data.error ? defaultValue : data.value
}));

export type Pagination = z.infer<typeof PaginationSchema>;
export type Error = z.infer<typeof ErrorSchema>;
export type Pair = z.infer<typeof PairSchema>;
