import {z} from "zod";

/**
 * 多语言标签Schema
 */
const MultiLanguageSchema = z.object({
  /**
   * 中文标签
   */
  zh_Hans: z.string().optional(),
  /**
   * 英文标签
   */
  en_US: z.string().optional()
});

export type MultiLanguage = z.infer<typeof MultiLanguageSchema>;
/**
* 模型属性Schema
*/
const ModelPropertiesSchema = z.object({
  /**
   * 上下文大小
   */
  context_size: z.number().optional(),
  /**
   * 模式
   */
  mode: z.string().optional()
});

export type ModelProperties = z.infer<typeof ModelPropertiesSchema>;
/**
* LLM模型Schema
*/
export const ModelSchema = z.object({
  /**
   * 模型名称
   */
  model: z.string(),
  /**
   * 模型标签，包含中英文
   */
  label: MultiLanguageSchema,
  /**
   * 模型类型
   */
  model_type: z.string(),
  /**
   * 模型支持的功能列表
   */
  features: z.array(z.string()),
  /**
   * 模型来源
   */
  fetch_from: z.string(),
  /**
   * 模型属性
   */
  model_properties: ModelPropertiesSchema,
  /**
   * 是否已弃用
   */
  deprecated: z.boolean().default(false),
  /**
   * 模型状态
   */
  status: z.string(),
  /**
   * 是否启用负载均衡
   */
  load_balancing_enabled: z.boolean().default(false)
});

export type Model = z.infer<typeof ModelSchema>;
/**
* LLM模型提供者Schema
*/
const LLMSchema = z.object({
  /**
   * 租户ID
   */
  tenant_id: z.string(),
  /**
   * 模型提供者
   */
  provider: z.string(),
  /**
   * 模型标签，包含中英文
   */
  label: MultiLanguageSchema,
  /**
   * 小图标，包含中英文
   */
  icon_small: MultiLanguageSchema,
  /**
   * 大图标，包含中英文
   */
  icon_large: MultiLanguageSchema,
  /**
   * 模型状态
   */
  status: z.string(),
  /**
   * 模型列表
   */
  models: z.array(ModelSchema)
});

export type LLM = z.infer<typeof LLMSchema>;
/**
* LLM模型列表Schema
*/
//eslint-disable-next-line @typescript-eslint/no-unused-vars
const LLMListSchema = z.object({
  /**
   * 模型列表
   */
  data: z.array(LLMSchema),
  /**
   * 模型总数
   */
  total: z.number()
});

export type LLMList = z.infer<typeof LLMListSchema>;

/**
 * 模型参数规则Schema
 */
const ModelParameterRuleSchema = z.object({
  /**
   * 参数名称
   */
  name: z.string(),
  /**
   * 使用模板
   */
  use_template: z.string().nullable().optional(),
  /**
   * 参数标签，包含中英文
   */
  label: MultiLanguageSchema,
  /**
   * 参数类型
   */
  type: z.string(),
  /**
   * 参数帮助信息，包含中英文
   */
  help: MultiLanguageSchema,
  /**
   * 是否必需
   */
  required: z.boolean(),
  /**
   * 默认值
   */
  default: z.union([z.string(), z.number(), z.boolean()]).nullable().optional(),
  /**
   * 最小值
   */
  min: z.number().nullable().optional(),
  /**
   * 最大值
   */
  max: z.number().nullable().optional(),
  /**
   * 精度
   */
  precision: z.number().nullable().optional(),
  /**
   * 选项列表
   */
  options: z.array(z.string()).default([])
});

export type ModelParameterRule = z.infer<typeof ModelParameterRuleSchema>;

/**
 * 模型参数规则列表Schema
 */
const ModelParameterRuleListSchema = z.object({
  /**
   * 参数规则列表
   */
  data: z.array(ModelParameterRuleSchema)
});

export type ModelParameterRuleList = z.infer<typeof ModelParameterRuleListSchema>;
