import {z} from "zod";

/**
 * 文件上传响应Schema
 *
 * @property id - 文件ID
 * @property name - 文件名
 * @property size - 文件大小
 * @property extension - 文件扩展名
 * @property mime_type - 文件MIME类型
 * @property created_by - 创建者
 * @property created_at - 创建时间
 */
export const FileUploadResponseSchema = z.object({
  id: z.string().describe("文件ID"),
  name: z.string().describe("文件名"),
  size: z.number().describe("文件大小"),
  extension: z.string().describe("文件扩展名"),
  mime_type: z.string().describe("文件MIME类型"),
  created_by: z.string().optional().describe("创建者"),
  created_at: z.number().optional().describe("创建时间")
});

export type FileUploadResponse = z.infer<typeof FileUploadResponseSchema>;

