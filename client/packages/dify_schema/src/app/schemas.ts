import { z } from "zod";

/**
 * 应用模式枚举
 */
export const AppModeSchema = z.enum([
  "chat",
  "agent-chat",
  "workflow",
  "completion"
]);
export type AppMode = z.infer<typeof AppModeSchema>;
/**
* 检索资源Schema
*/
const RetrieverResourceSchema = z.object({
  /**
   * 段落位置
   */
  position: z.number().optional(),
  /**
   * 文档ID
   */
  document_id: z.string().optional(),
  /**
   * 内容摘要
   */
  content: z.string().optional()
});
export type RetrieverResource = z.infer<typeof RetrieverResourceSchema>;

/**
 * 标签模型
 */
export const TagSchema = z.object({
  /** 标签ID */
  id: z.string(),
  /** 标签名称 */
  name: z.string(),
  /** 标签类型 */
  type: z.string()
});
export type Tag = z.infer<typeof TagSchema>;

/**
 * 相似推荐配置模型
 */
export const MoreLikeThisSchema = z.object({
  /** 是否启用相似推荐功能 */
  enabled: z.boolean().default(false)
});
export type MoreLikeThis = z.infer<typeof MoreLikeThisSchema>;

/**
 * 回答后的建议问题配置模型
 */
export const SuggestedQuestionsAfterAnswerSchema = z.object({
  /** 是否启用回答后的建议问题功能 */
  enabled: z.boolean().default(false)
});
export type SuggestedQuestionsAfterAnswer = z.infer<typeof SuggestedQuestionsAfterAnswerSchema>;

/**
 * 语音转文本配置
 */
export const SpeechToTextSchema = z.object({
  /** 是否开启语音转文本功能 */
  enabled: z.boolean().default(false)
});
export type SpeechToText = z.infer<typeof SpeechToTextSchema>;

/**
 * 文字转语音配置
 */
export const TextToSpeechSchema = z.object({
  /** 是否开启文字转语音功能 */
  enabled: z.boolean().default(false),
  /** 语音语言 */
  language: z.string().default(""),
  /** 语音类型 */
  voice: z.string().default("")
});
export type TextToSpeech = z.infer<typeof TextToSpeechSchema>;



/**
 * 敏感词规避配置
 */
export const SensitiveWordAvoidanceSchema = z.object({
  /** 是否开启敏感词规避功能 */
  enabled: z.boolean().default(false),
  /** 敏感词规避类型 */
  type: z.string().default(""),
  /** 敏感词规避配置 */
  configs: z.record(z.unknown()).optional()
});
export type SensitiveWordAvoidance = z.infer<typeof SensitiveWordAvoidanceSchema>;

/**
 * 工具配置
 */
export const ToolSchema = z.object({
  /** 提供者ID */
  provider_id: z.string().optional(),
  /** 提供者类型 */
  provider_type: z.string().optional(),
  /** 提供者名称 */
  provider_name: z.string().optional(),
  /** 工具名称 */
  tool_name: z.string().optional(),
  /** 工具标签 */
  tool_label: z.string().optional(),
  /** 工具参数配置 */
  tool_parameters: z.record(z.unknown()).optional(),
  /** 是否非作者 */
  notAuthor: z.boolean().default(false),
  /** 是否启用 */
  enabled: z.boolean().default(true)
});
export type Tool = z.infer<typeof ToolSchema>;

/**
 * 代理模式配置
 */
export const AgentModeSchema = z.object({
  /** 最大迭代次数 */
  max_iteration: z.number().default(5),
  /** 是否启用 */
  enabled: z.boolean().default(true),
  /** 工具列表 */
  tools: z.array(ToolSchema).default([]),
  /** 策略类型 */
  strategy: z.string().default("react")
});
export type AgentMode = z.infer<typeof AgentModeSchema>;

/**
 * 数据集配置
 */
export const DatasetConfigsSchema = z.object({
  /** 检索模型类型 */
  retrieval_model: z.string().default("multiple"),
  /** 数据集配置，包含数据集列表 */
  datasets: z.object({
    datasets: z.array(z.unknown())
  }).default({ datasets: [] }),
  /** 是否启用重排序 */
  reranking_enable: z.boolean().default(false),
  /** 返回结果数量 */
  top_k: z.number().default(4),
  /** 重排序模式 */
  reranking_mode: z.string().default("reranking_model"),
  /** 重排序模型 */
  reranking_model: z.object({
    reranking_provider_name: z.string().default("langgenius/tongyi/tongyi"),
    reranking_model_name: z.string().default("gte-rerank")
  })
});
export type DatasetConfigs = z.infer<typeof DatasetConfigsSchema>;

/**
 * 文件上传配置
 */
export const FileUploadConfigSchema = z.object({
  /** 文件大小限制（MB） */
  file_size_limit: z.number().default(15),
  /** 批量上传数量限制 */
  batch_count_limit: z.number().default(5),
  /** 图片文件大小限制（MB） */
  image_file_size_limit: z.number().default(10),
  /** 视频文件大小限制（MB） */
  video_file_size_limit: z.number().default(100),
  /** 音频文件大小限制（MB） */
  audio_file_size_limit: z.number().default(50),
  /** 工作流文件上传限制（MB） */
  workflow_file_upload_limit: z.number().default(10)
});
export type FileUploadConfig = z.infer<typeof FileUploadConfigSchema>;

/**
 * 图片设置
 */
export const ImageSchema = z.object({
  /** 是否开启 */
  enabled: z.boolean().default(false),
  /** 图片数量限制，默认3 */
  number_limits: z.number().default(3),
  /** 传递方式列表，remote_url, local_file，必选一个 */
  transfer_methods: z.array(z.string()),
  /** 图片详情 */
  detail: z.string().default("high").optional()
});
export type Image = z.infer<typeof ImageSchema>;

/**
 * 文件上传配置
 */
export const FileUploadSchema = z.object({
  /** 图片上传配置 */
  image: ImageSchema,
  /** 是否启用文件上传功能 */
  enabled: z.boolean().default(false),
  /** 允许上传的文件类型 */
  allowed_file_types: z.array(z.string()),
  /** 允许上传的文件扩展名 */
  allowed_file_extensions: z.array(z.string()),
  /** 允许的文件上传方式 */
  allowed_file_upload_methods: z.array(z.string()),
  /** 文件上传数量限制 */
  number_limits: z.number(),
  /** 文件上传配置 */
  fileUploadConfig: FileUploadConfigSchema
});
export type FileUpload = z.infer<typeof FileUploadSchema>;

/**
 * 标记回复配置
 */
export const AnnotationReplySchema = z.object({
  /** 是否开启标记回复功能 */
  enabled: z.boolean().default(false)
});
export type AnnotationReply = z.infer<typeof AnnotationReplySchema>;

/**
 * 基础输入控件配置
 */
export const BaseInputSchema = z.object({
  /** 控件展示标签名 */
  label: z.string(),
  /** 控件ID */
  variable: z.string(),
  /** 最大长度 */
  max_length: z.number().optional(),
  /** 是否必填 */
  required: z.boolean().optional()
});
export type BaseInput = z.infer<typeof BaseInputSchema>;

/**
 * 文本输入项
 */
export const TextInputSchema = BaseInputSchema.extend({
  /** 控件类型 */
  type: z.literal('text-input'),
});
export type TextInput = z.infer<typeof TextInputSchema>;

/**
 * 数字输入项
 */
export const NumberInputSchema = BaseInputSchema.extend({
  /** 控件类型 */
  type: z.literal('number'),
});
export type NumberInput = z.infer<typeof NumberInputSchema>;

/**
 * 段落输入项
 */
export const ParagraphInputSchema = BaseInputSchema.extend({
  /** 控件类型 */
  type: z.literal('paragraph'),
});
export type ParagraphInput = z.infer<typeof ParagraphInputSchema>;

/**
 * 下拉输入项
 */
export const SelectInputSchema = BaseInputSchema.extend({
  /** 选项值列表 */
  options: z.array(z.string()).default([]),
  /** 控件类型 */
  type: z.literal('select'),
});
export type SelectInput = z.infer<typeof SelectInputSchema>;

/**
 * 用户输入项
 */
export const UserInputItemSchema = z.object({
  /** 文本输入项 */
  "text-input": TextInputSchema.optional(),
  /** 数字输入项 */
  number: NumberInputSchema.optional(),
  /** 段落输入项 */
  paragraph: ParagraphInputSchema.optional(),
  /** 下拉输入项 */
  select_input: SelectInputSchema.optional()
});
export type UserInputItem = z.infer<typeof UserInputItemSchema>;
/**
 * 模型提供者配置
 */
export const ModelProviderConfigSchema = z.object({
  /** 模型提供者名称 */
  provider: z.string(),
  /** 模型名称 */
  name: z.string(),
  /** 模型模式 */
  mode: z.string(),
  /** 完成参数配置 */
  completion_params: z.record(z.unknown()).default({})
});
export type ModelProviderConfig = z.infer<typeof ModelProviderConfigSchema>;

/**
 * AI应用配置模型
 */
export const ModelConfigSchema = z.object({
  /** 提示词 */
  pre_prompt: z.string().optional(),
  /** 提示词类型 */
  prompt_type: z.string().optional(),
  /** 对话提示配置 */
  chat_prompt_config: z.record(z.unknown()).optional(),
  /** 补全提示配置 */
  completion_prompt_config: z.record(z.unknown()).optional(),
  /** 用户输入表单配置 */
  user_input_form: z.array(UserInputItemSchema).optional(),
  /** 数据集查询变量 */
  dataset_query_variable: z.string().optional(),
  /** 开场白文本 */
  opening_statement: z.string().optional(),
  /** 相似推荐配置 */
  more_like_this: MoreLikeThisSchema.optional(),
  /** 建议问题列表 */
  suggested_questions: z.array(z.string()).optional(),
  /** 回答后的建议问题配置 */
  suggested_questions_after_answer: SuggestedQuestionsAfterAnswerSchema.optional(),
  /** 文字转语音配置 */
  text_to_speech: TextToSpeechSchema.optional(),
  /** 语音转文字配置 */
  speech_to_text: SpeechToTextSchema.optional(),
  /** 检索资源配置 */
  retriever_resource: RetrieverResourceSchema.optional(),
  /** 敏感词规避配置 */
  sensitive_word_avoidance: SensitiveWordAvoidanceSchema.optional(),
  /** 代理模式配置 */
  agent_mode: AgentModeSchema.optional(),
  /** 数据集配置 */
  dataset_configs: DatasetConfigsSchema.optional(),
  /** 文件上传配置 */
  file_upload: FileUploadSchema.optional(),
  /** 注释回复配置 */
  annotation_reply: AnnotationReplySchema.optional(),
  /** 是否支持注释功能 */
  supportAnnotation: z.boolean().default(true),
  /** 应用ID */
  appId: z.string().optional(),
  /** 是否支持引用命中信息 */
  supportCitationHitInfo: z.boolean().default(true),
  /** 模型配置 */
  model: ModelProviderConfigSchema.optional()
});
export type ModelConfig = z.infer<typeof ModelConfigSchema>;

/**
 * Dify应用模型
 */
export const AppSchema = z.object({
  /** 应用ID */
  id: z.string().optional(),
  /** 应用名称 */
  name: z.string().optional(),
  /** 最大活跃请求数 */
  max_active_requests: z.number().optional(),
  /** 应用描述 */
  description: z.string().optional(),
  /** 应用模式 */
  mode: AppModeSchema.optional(),
  /** 图标类型 */
  icon_type: z.string().optional(),
  /** 图标 */
  icon: z.string().optional(),
  /** 图标背景 */
  icon_background: z.string().optional(),
  /** 图标URL */
  icon_url: z.string().optional(),
  /** 模型配置 */
  app_config: ModelConfigSchema.default({}),
  /** 工作流 */
  workflow: z.record(z.unknown()).optional(),
  /** 是否使用图标作为回答图标 */
  use_icon_as_answer_icon: z.boolean().optional(),
  /** 创建者 */
  created_by: z.string().optional(),
  /** 创建时间 */
  created_at: z.date().optional(),
  /** 更新者 */
  updated_by: z.string().optional(),
  /** 更新时间 */
  updated_at: z.date().optional(),
  /** 标签 */
  tags: z.array(TagSchema).default([])
});
export type App = z.infer<typeof AppSchema>;

/**
 * 聊天完成响应
 */
export const ChatCompletionResponseSchema = z.object({
  /** 消息ID */
  message_id: z.string(),
  /** 会话ID */
  conversation_id: z.string(),
  /** 回答 */
  answer: z.string()
});
export type ChatCompletionResponse = z.infer<typeof ChatCompletionResponseSchema>;

/**
 * 图片上传方式
 */
export enum TransferMethod {
  /**
   * 远程URL
   */
  REMOTE_URL = "remote_url",
  /**
   * 本地文件
   */
  LOCAL_FILE = "local_file"
}

/**
 * 文件类型枚举
 */
export enum FileType {
  /**
   * 文档类型,具体类型包含：'TXT', 'MD', 'MARKDOWN', 'PDF', 'HTML', 'XLSX', 'XLS', 'DOCX', 'CSV', 'EML', 'MSG', 'PPTX', 'PPT', 'XML', 'EPUB'
   */
  DOCUMENT = "document",
  /**
   * 图片类型,具体类型包含：'JPG', 'JPEG', 'PNG', 'GIF', 'WEBP', 'SVG'
   */
  IMAGE = "image",
  /**
   * 音频类型,具体类型包含：'MP3', 'M4A', 'WAV', 'WEBM', 'AMR'
   */
  AUDIO = "audio",
  /**
   * 视频类型,具体类型包含：'MP4', 'MOV', 'MPEG', 'MPGA'
   */
  VIDEO = "video",
  /**
   * 自定义类型
   */
  CUSTOM = "custom"
}

/**
 * 用户上传的文件对象
 */
export const FilePayloadSchema = z.object({
  /**
   * 文件类型，`chat`模式仅支持图片格式
   */
  type: z.nativeEnum(FileType).optional(),
  /**
   * 文件MIME类型
   */
  mime_type: z.string().optional(),
  /**
   * 文件大小
   */
  size: z.number().optional(),
  /**
   * 文件名称
   */
  name: z.string().optional(),
  /**
   * 文件传递方式
   */
  transfer_method: z.nativeEnum(TransferMethod),
  /**
   * 图片地址（仅当传递方式为remote_url时）
   */
  url: z.string().optional(),
  /**
   * 上传文件ID（仅当传递方式为local_file时）
   */
  upload_file_id: z.string().optional()
}).refine(data => {
  if (data.transfer_method === "remote_url" && !data.url) {
    return false;
  }
  if (data.transfer_method === "local_file" && !data.upload_file_id) {
    return false;
  }
  return true;
}, {
  message: "当传递方式为remote_url时必须提供url，当传递方式为local_file时必须提供upload_file_id"
});
export type FilePayload = z.infer<typeof FilePayloadSchema>;
/**
 * 聊天请求配置
 */
export const ChatPayloadsSchema = z.object({
  /** 用户输入/提问内容 */
  query: z.string(),
  /** 额外的输入参数配置 */
  inputs: z.record(z.unknown()).default({}),
  /** 响应模式 */
  response_mode: z.enum(["streaming", "blocking"]).default("streaming"),
  /** 用户标识 */
  user: z.string(),
  /** 对话ID */
  conversation_id: z.string().optional(),
  /** 上传的文件列表 */
  files: z.array(FilePayloadSchema).default([]),
  /** 自动生成标题 */
  auto_generate_name: z.boolean().default(true),
  /** 父消息ID */
  parent_message_id: z.string().optional(),
  /** 模型配置 */
  model_config: ModelConfigSchema.optional()
});
export type ChatPayloads = z.infer<typeof ChatPayloadsSchema>;

/**
 * 运行工作流请求配置
 */
export const RunWorkflowPayloadsSchema = z.object({
  /** 额外的输入参数配置 */
  inputs: z.record(z.unknown()).default({}),
  /** 响应模式 */
  response_mode: z.string().default("streaming"),
  /** 用户标识 */
  user: z.string().optional(),
  /** 上传的文件列表 */
  files: z.array(FilePayloadSchema).default([])
});
export type RunWorkflowPayloads = z.infer<typeof RunWorkflowPayloadsSchema>;

/**
 * 和`Dify`应用在对话期间可能返回的事件类型
 */
export enum ConversationEventType {
  /**
   * LLM 返回文本块事件，即：完整的文本以分块的方式输出。
   */
  MESSAGE = "message",
  /**
   * Agent模式下返回文本块事件，即：在Agent模式下，文章的文本以分块的方式输出（仅Agent模式下使用）
   */
  AGENT_MESSAGE = "agent_message",
  /**
   * Agent模式下有关Agent思考步骤的相关内容，涉及到工具调用（仅Agent模式下使用）
   */
  AGENT_THOUGHT = "agent_thought",
  /**
   * 表示有新文件需要展示
   */
  MESSAGE_FILE = "message_file",
  /**
   * 消息结束事件，收到此事件则代表流式返回结束
   */
  MESSAGE_END = "message_end",
  /**
   * TTS消息
   */
  TTS_MESSAGE = "tts_message",
  /**
   * TTS消息结束
   */
  TTS_MESSAGE_END = "tts_message_end",
  /**
   * 消息替换
   */
  MESSAGE_REPLACE = "message_replace",
  /**
   * 错误
   */
  ERROR = "error",
  /**
   * 心跳检测
   */
  PING = "ping",
  /**
   * 工作流开始
   */
  WORKFLOW_STARTED = "workflow_started",
  /**
   * 工作流结束
   */
  WORKFLOW_FINISHED = "workflow_finished",
  /**
   * 节点开始
   */
  NODE_STARTED = "node_started",
  /**
   * 节点结束
   */
  NODE_FINISHED = "node_finished",
  /**
   * 文本片段
   */
  TEXT_CHUNK = "text_chunk"
}








// 错误事件 Schema
export const ErrorEventSchema = z.object({
  event: z.literal(ConversationEventType.ERROR),
  task_id: z.string().optional().nullable(),
  message_id: z.string(),
  status: z.number(),
  code: z.string(),
  message: z.string()
})
export type DifyErrorEvent = z.infer<typeof ErrorEventSchema>;
// ChatMessageEvent Schema
const ChatMessageEventSchema = z.object({
  event: z.literal(ConversationEventType.MESSAGE),
  task_id: z.string().optional(),
  message_id: z.string(),
  conversation_id: z.string().optional(),
  answer: z.string(),
  created_at: z.number()
});
export type ChatMessageEvent = z.infer<typeof ChatMessageEventSchema>;
// AgentMessageEvent Schema
const AgentMessageEventSchema = z.object({
  event: z.literal(ConversationEventType.AGENT_MESSAGE),
  task_id: z.string(),
  message_id: z.string(),
  conversation_id: z.string(),
  answer: z.string(),
  created_at: z.number()
});
export type AgentMessageEvent = z.infer<typeof AgentMessageEventSchema>;

/**
* Agent思考步骤事件 Schema
*/
const AgentThoughtEventSchema = z.object({
  /**
   * 事件类型
   */
  event: z.literal(ConversationEventType.AGENT_THOUGHT),
  /**
   * 会话ID
   */
  conversation_id: z.string(),
  /**
   * 消息ID
   */
  message_id: z.string(),

  /**
   * 创建时间戳
   */
  created_at: z.number(),
  /**
   * 任务ID
   */
  task_id: z.string(),

  /**
   * 思考ID
   */
  id: z.string(),

  /**
   * 思考位置
   */
  position: z.number(),
  /**
   * 思考内容
   */
  thought: z.string().optional(),
  /**
   * 工具返回结果
   */
  observation: z.string().optional(),
  /**
   * 使用工具
   */
  tool: z.string().optional(),
  /**
   * 工具输入参数
   */
  tool_input: z.string().optional(),
  /**
   * 关联文件ID
   */
  message_files: z.array(z.string()).optional()
});
export type AgentThoughtEvent = z.infer<typeof AgentThoughtEventSchema>;


/**
* 消息文件事件Schema
*/
const MessageFileEventSchema = z.object({
  /**
   * 事件类型
   */
  event: z.literal(ConversationEventType.MESSAGE_FILE),
  /**
   * 会话ID
   */
  conversation_id: z.string(),
  /**
   * 消息ID
   */
  message_id: z.string(),
  /**
   * 创建时间戳
   */
  created_at: z.number(),
  /**
   * 任务ID
   */
  task_id: z.string(),
  /**
   * 文件唯一ID
   */
  id: z.string(),
  /**
   * 文件类型,目前仅为image
   */
  type: z.string(),
  /**
   * 文件归属,user或assistant，该接口返回仅为 assistant
   */
  belongs_to: z.string(),
  /**
   * 文件访问地址
   */
  url: z.string(),
});
export type MessageFileEvent = z.infer<typeof MessageFileEventSchema>;


/**
* 使用情况统计Schema
*/
const UsageSchema = z.object({
  /**
   * 提示词消耗的token数量
   */
  prompt_tokens: z.number(),
  /**
   * 提示词单价
   */
  prompt_unit_price: z.string(),
  /**
   * 提示词价格单位
   */
  prompt_price_unit: z.string(),
  /**
   * 提示词总价
   */
  prompt_price: z.string(),
  /**
   * 补全消耗的token数量
   */
  completion_tokens: z.number(),
  /**
   * 补全单价
   */
  completion_unit_price: z.string(),
  /**
   * 补全价格单位
   */
  completion_price_unit: z.string(),
  /**
   * 补全总价
   */
  completion_price: z.string(),
  /**
   * 总token数量
   */
  total_tokens: z.number(),
  /**
   * 总价格
   */
  total_price: z.string(),
  /**
   * 货币单位
   */
  currency: z.string(),
  /**
   * 请求延迟时间（秒）
   */
  latency: z.number()
});
export type Usage = z.infer<typeof UsageSchema>;

/**
* 消息结束事件Schema
*/
const MessageEndEventSchema = z.object({
  /**
   * 事件类型
   */
  event: z.literal(ConversationEventType.MESSAGE_END),
  /**
   * 任务ID，用于请求跟踪和停止响应接口
   */
  task_id: z.string(),
  /**
   * 消息唯一ID
   */
  message_id: z.string(),
  /**
   * 会话ID
   */
  conversation_id: z.string().optional(),
  /**
   * 元数据信息
   */
  metadata: z.object({
    usage: UsageSchema.optional(),
    retriever_resources: z.array(RetrieverResourceSchema).optional(),
  }).passthrough()
});
export type MessageEndEvent = z.infer<typeof MessageEndEventSchema>;

/**
* 消息替换事件Schema
*/
const MessageReplaceEventSchema = z.object({
  /**
   * 事件类型
   */
  event: z.literal(ConversationEventType.MESSAGE_REPLACE),
  /**
   * 任务ID，用于请求跟踪和停止响应接口
   */
  task_id: z.string(),
  /**
   * 消息唯一ID
   */
  message_id: z.string(),
  /**
   * 会话ID
   */
  conversation_id: z.string(),
  /**
   * 替换内容（直接替换LLM所有回复文本）
   */
  answer: z.string(),
  /**
   * 创建时间戳
   */
  created_at: z.number()
});
export type MessageReplaceEvent = z.infer<typeof MessageReplaceEventSchema>;
/**
* TTS 音频流结束事件Schema
*/
const TTSMessageEndEventSchema = z.object({
  /**
   * 事件类型
   */
  event: z.literal(ConversationEventType.TTS_MESSAGE_END),
  /**
   * 任务ID，用于请求跟踪和停止响应接口
   */
  task_id: z.string(),
  /**
   * 消息唯一ID
   */
  message_id: z.string(),
  /**
   * 音频内容（结束事件为空字符串）
   */
  audio: z.string(),
  /**
   * 创建时间戳
   */
  created_at: z.number()
});
export type TTSMessageEndEvent = z.infer<typeof TTSMessageEndEventSchema>;
/**
* TTS 音频流事件Schema
*/
const TTSMessageEventSchema = z.object({
  /**
   * 事件类型
   */
  event: z.literal(ConversationEventType.TTS_MESSAGE),
  /**
   * 任务ID，用于请求跟踪和停止响应接口
   */
  task_id: z.string(),
  /**
   * 消息唯一ID
   */
  message_id: z.string(),
  /**
   * 语音合成音频块（Base64编码的Mp3格式）
   */
  audio: z.string(),
  /**
   * 创建时间戳
   */
  created_at: z.number()
});
export type TTSMessageEvent = z.infer<typeof TTSMessageEventSchema>;


/**
* 工作流开始事件Schema
*/
const WorkflowStartedEventSchema = z.object({
  /**
   * 事件类型
   */
  event: z.literal(ConversationEventType.WORKFLOW_STARTED),
  /**
   * 任务ID，用于请求跟踪和停止响应接口
   */
  task_id: z.string().optional().nullable(),
  /**
   * workflow执行ID
   */
  workflow_run_id: z.string().optional().nullable(),
  /**
   * 详细内容，包含工作流执行信息
   */
  data: z.any().optional().nullable()
});
export type WorkflowStartedEvent = z.infer<typeof WorkflowStartedEventSchema>;
/**
* 节点开始事件数据Schema
*/
const NodeStartedDataSchema = z.object({
  /**
   * workflow 执行 ID
   */
  id: z.string().optional().nullable(),
  /**
   * 节点 ID
   */
  node_id: z.string().optional().nullable(),
  /**
   * 节点类型
   */
  node_type: z.string().optional().nullable(),
  /**
   * 节点名称
   */
  title: z.string().optional().nullable(),
  /**
   * 执行序号，用于展示 Tracing Node 顺序
   */
  index: z.number().optional().nullable(),
  /**
   * 前置节点 ID，用于画布展示执行路径
   */
  predecessor_node_id: z.string().optional().nullable(),
  /**
   * 节点中所有使用到的前置节点变量内容
   */
  inputs: z.record(z.any()).default({}).optional().nullable(),
  /**
   * 开始时间戳
   */
  created_at: z.number().optional().nullable()
});
export type NodeStartedData = z.infer<typeof NodeStartedDataSchema>;

/**
* 节点开始事件Schema
*/
const NodeStartedEventSchema = z.object({
  /**
   * 事件类型
   */
  event: z.literal(ConversationEventType.NODE_STARTED),
  /**
   * 任务ID，用于请求跟踪和停止响应接口
   */
  task_id: z.string().optional().nullable(),
  /**
   * workflow执行ID
   */
  workflow_run_id: z.string().optional().nullable(),
  /**
   * 详细内容，包含节点执行信息
   */
  data: NodeStartedDataSchema.optional().nullable()
});
export type NodeStartedEvent = z.infer<typeof NodeStartedEventSchema>;
/**
* 节点执行元数据Schema
*/
const NodeExecutionMetaSchema = z.object({
  /**
   * 总使用tokens数量
   */
  total_tokens: z.number().optional().nullable(),
  /**
   * 总费用
   */
  total_price: z.string().optional().nullable(),
  /**
   * 货币单位，如USD/RMB
   */
  currency: z.string().optional().nullable()
});
export type NodeExecutionMeta = z.infer<typeof NodeExecutionMetaSchema>;
const NodeStatusSchema = z.enum(["running", "succeeded", "failed", "stopped"]);
export type NodeStatus = z.infer<typeof NodeStatusSchema>;
/**
* 节点完成数据Schema
*/
export const NodeFinishedDataSchema = z.object({
  /**
   * 节点执行ID
   */
  id: z.string().optional().nullable(),
  /**
   * 节点ID
   */
  node_id: z.string().optional().nullable(),
  /**
   * 执行序号，用于展示Tracing Node顺序
   */
  index: z.number().optional().nullable(),
  /**
   * 前置节点ID，用于画布展示执行路径
   */
  predecessor_node_id: z.string().optional().nullable(),
  /**
   * 节点中所有使用到的前置节点变量内容
   */
  inputs: z.record(z.any()).optional().nullable(),
  /**
   * 节点过程数据
   */
  process_data: z.record(z.any()).optional().nullable(),
  /**
   * 输出内容
   */
  outputs: z.record(z.any()).optional().nullable(),
  /**
   * 执行状态，包括running/succeeded/failed/stopped
   */
  status: NodeStatusSchema.optional().nullable(),
  /**
   * 错误原因
   */
  error: z.string().optional().nullable(),
  /**
   * 耗时（秒）
   */
  elapsed_time: z.number().optional().nullable(),
  /**
   * 元数据
   */
  execution_metadata: NodeExecutionMetaSchema.optional().nullable(),
  /**
   * 开始时间戳
   */
  created_at: z.number().optional().nullable()
});
export type NodeFinishedData = z.infer<typeof NodeFinishedDataSchema>;
/**
* 节点完成事件Schema
*/
const NodeFinishedEventSchema = z.object({
  /**
   * 事件类型，固定为'node_finished'
   */
  event: z.literal(ConversationEventType.NODE_FINISHED),
  /**
   * 任务ID，用于请求跟踪和停止响应接口
   */
  task_id: z.string().optional().nullable(),
  /**
   * workflow执行ID
   */
  workflow_run_id: z.string().optional().nullable(),
  /**
   * 详细内容，包含节点执行信息
   */
  data: NodeFinishedDataSchema.optional().nullable()
});
export type NodeFinishedEvent = z.infer<typeof NodeFinishedEventSchema>;
/**
* 工作流状态枚举
*/
const WorkflowStatusSchema = z.enum(["running", "succeeded", "failed", "stopped"]);
export type WorkflowStatus = z.infer<typeof WorkflowStatusSchema>;

/**
* 工作流完成数据Schema
*/
const WorkflowFinishedDataSchema = z.object({
  /**
   * workflow执行ID
   */
  id: z.string().optional().nullable(),
  /**
   * 关联Workflow ID
   */
  workflow_id: z.string().optional().nullable(),
  /**
   * 执行状态，包括running/succeeded/failed/stopped
   */
  status: WorkflowStatusSchema.optional().nullable(),
  /**
   * 输出内容
   */
  outputs: z.record(z.any()).optional().nullable(),
  /**
   * 错误原因
   */
  error: z.string().optional().nullable(),
  /**
   * 耗时（秒）
   */
  elapsed_time: z.number().optional().nullable(),
  /**
   * 总使用tokens
   */
  total_tokens: z.number().optional().nullable(),
  /**
   * 总步数
   */
  total_steps: z.number().optional().nullable(),
  /**
   * 开始时间戳
   */
  created_at: z.number().optional().nullable(),
  /**
   * 结束时间戳
   */
  finished_at: z.number().optional().nullable()
});
export type WorkflowFinishedData = z.infer<typeof WorkflowFinishedDataSchema>;

/**
* 工作流完成事件Schema
*/
const WorkflowFinishedEventSchema = z.object({
  /**
   * 事件类型，固定为'workflow_finished'
   */
  event: z.literal(ConversationEventType.WORKFLOW_FINISHED),
  /**
   * 任务ID，用于请求跟踪和停止响应接口
   */
  task_id: z.string().optional().nullable(),
  /**
   * workflow执行ID
   */
  workflow_run_id: z.string().optional().nullable(),
  /**
   * 工作流完成数据
   */
  data: WorkflowFinishedDataSchema.optional().nullable()
});
export type WorkflowFinishedEvent = z.infer<typeof WorkflowFinishedEventSchema>;
/**
* 文本片段数据Schema
*/
const TextChunkDataSchema = z.object({
  /**
   * 文本内容
   */
  text: z.string().optional(),
  /**
   * 变量选择器来源
   */
  from_variable_selector: z.array(z.string()).optional()
});
export type TextChunkData = z.infer<typeof TextChunkDataSchema>;

/**
* 文本片段事件Schema
*/
const TextChunkEventSchema = z.object({
  /**
   * 事件类型，固定为'text_chunk'
   */
  event: z.literal(ConversationEventType.TEXT_CHUNK),
  /**
   * 任务ID，用于请求跟踪和停止响应接口
   */
  task_id: z.string().optional().nullable(),
  /**
   * workflow执行ID
   */
  workflow_run_id: z.string().optional().nullable(),
  /**
   * 文本片段数据
   */
  data: TextChunkDataSchema.optional().nullable()
});
export type TextChunkEvent = z.infer<typeof TextChunkEventSchema>;


// 使用 discriminatedUnion 创建联合类型
export const ConversationEventSchema = z.discriminatedUnion("event", [
  ChatMessageEventSchema,
  AgentMessageEventSchema,
  AgentThoughtEventSchema,
  MessageFileEventSchema,
  MessageEndEventSchema,
  TTSMessageEventSchema,
  TTSMessageEndEventSchema,
  MessageReplaceEventSchema,
  ErrorEventSchema,
  WorkflowStartedEventSchema,
  NodeStartedEventSchema,
  NodeFinishedEventSchema,
  WorkflowFinishedEventSchema,
  TextChunkEventSchema
]);

export type ConversationEvent = z.infer<typeof ConversationEventSchema>;
