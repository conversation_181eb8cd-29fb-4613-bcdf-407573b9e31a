import {z} from "zod";
import { ErrorSchema } from "../../schemas";

export const ConversationSchema = z.object({
  id: z.string().describe("会话ID"),
  name: z.string().describe("会话名称"),
  inputs: z.record(z.unknown()).describe("用户输入参数").optional(),
  status: z.string().describe("会话状态").optional(),
  introduction: z.string().describe("开场白").optional(),
  created_at: z.number().describe("创建时间戳"),
  updated_at: z.number().describe("更新时间戳"),
});
export type Conversation = z.infer<typeof ConversationSchema>;
//eslint-disable-next-line @typescript-eslint/no-unused-vars
const ConversationListSchema = z.object({
  data: z.array(ConversationSchema).describe("会话列表"),
  has_more: z.boolean().describe("是否有更多数据"),
  limit: z.number().describe("实际返回数量"),
});
export type ConversationList = z.infer<typeof ConversationListSchema>;

export const SortBy = z.enum([
  "created_at",
  "-created_at",
  "updated_at",
  "-updated_at",
]);

export const ConversationListQueryPayloads = z.object({
  user: z.string().describe("用户标识，需保证在应用内唯一"),
  last_id: z.string().optional().describe("当前页最后一条记录的ID"),
  limit: z.number().min(1).max(100).optional().default(20).describe("返回记录数量"),
  sort_by: z.string().optional().default("-updated_at").describe(
    "排序字段，可选值：created_at, -created_at, updated_at, -updated_at"
  ),
});

export const MessageListQueryPayloads = z.object({
  conversation_id: z.string().describe("会话ID"),
  user: z.string().describe("用户标识，需保证在应用内唯一"),
  first_id: z.string().optional().describe("当前页第一条聊天记录的ID"),
  limit: z.number().optional().default(20).describe("返回聊天记录数量"),
});

const MessageFileSchema = z.object({
  id: z.string().optional().describe("文件ID"),
  type: z.string().optional().describe("文件类型"),
  url: z.string().optional().describe("预览地址"),
  belongs_to: z.enum(["user", "assistant"]).optional().describe(
    "文件归属方，可选值：user, assistant"
  ),
  filename: z.string().optional().describe("文件名"),
  mime_type: z.string().optional().describe("MIME类型"),
  size: z.number().optional().describe("文件大小"),
  transfer_method: z.string().optional().describe("传输方式"),
});
export type MessageFile = z.infer<typeof MessageFileSchema>;
const AgentThoughtSchema = z.object({
  id: z.string().optional().describe("思考ID"),
  message_id: z.string().optional().describe("消息ID"),
  position: z.number().optional().describe("思考位置"),
  thought: z.string().optional().describe("思考内容"),
  observation: z.string().optional().describe("工具返回结果"),
  tool: z.string().optional().describe("使用工具"),
  tool_input: z.string().optional().describe("工具输入参数"),
  message_files: z.array(MessageFileSchema).optional().describe("关联文件ID"),
});
export type AgentThought = z.infer<typeof AgentThoughtSchema>;
const FeedbackSchema = z.object({
  rating: z.string().optional().describe("用户反馈"),
});
export type Feedback = z.infer<typeof FeedbackSchema>;


const MessageSchema = z.object({
  id: z.string().optional().describe("消息ID"),
  conversation_id: z.string().optional().describe("会话ID"),
  inputs: z.record(z.unknown()).optional().describe("输入参数"),
  query: z.string().optional().describe("用户提问"),
  message_files: z.array(MessageFileSchema).optional().describe("消息文件"),
  agent_thoughts: z.array(AgentThoughtSchema).optional().describe(
    "Agent思考过程"
  ),
  answer: z.string().optional().describe("回答内容"),
  created_at: z.number().optional().describe("创建时间"),
  created_time: z.string().optional().describe("创建时间"),
  feedback: FeedbackSchema.optional() .describe("用户反馈"),
  retriever_resources: z.array(z.unknown()).optional().describe(
    "检索资源"
  ),
  error: ErrorSchema.optional().describe("错误信息"),
});
export type Message = z.infer<typeof MessageSchema>;
//eslint-disable-next-line @typescript-eslint/no-unused-vars
const MessageListSchema = z.object({
  data: z.array(MessageSchema).optional().default([]).describe("消息列表"),
  has_more: z.boolean().optional().default(false).describe("是否有更多数据"),
  limit: z.number().optional().default(20).describe("实际返回数量"),
});
export type MessageList = z.infer<typeof MessageListSchema>;
//eslint-disable-next-line @typescript-eslint/no-unused-vars
const ConversationRenamePayloadsSchema = z.object({
  name: z.string().optional().describe("新会话名称"),
  auto_generate: z.boolean().optional().default(false).describe("是否自动生成标题"),
  user: z.string().describe("用户标识"),
});
export type ConversationRenamePayloads = z.infer<typeof ConversationRenamePayloadsSchema>;
//eslint-disable-next-line @typescript-eslint/no-unused-vars
const MessageFeedbackPayloadsSchema = z.object({
  rating: z.string().optional().describe("点赞 like, 点踩 dislike, 撤销点赞 null"),
  user: z.string().describe("用户标识"),
  content: z.string().optional().describe("反馈的具体信息"),
});
export type MessageFeedbackPayloads = z.infer<typeof MessageFeedbackPayloadsSchema>;
