import {z} from "zod";


/**
 * 位置Schema
 */
const PositionSchema = z.object({
  /**
   * x坐标
   */
  x: z.number().optional(),
  /**
   * y坐标
   */
  y: z.number().optional()
});

export type Position = z.infer<typeof PositionSchema>;

/**
* 工作流节点数据Schema
*/
const WorkflowNodeDataSchema = z.object({
  /**
   * 节点类型
   */
  type: z.string().optional(),
  /**
   * 节点标题
   */
  title: z.string().optional(),
  /**
   * 是否被选中
   */
  selected: z.boolean().default(false),
  /**
   * 节点描述
   */
  desc: z.string().optional()
});

export type WorkflowNodeData = z.infer<typeof WorkflowNodeDataSchema>;

/**
* 工作流节点Schema
*/
const WorkflowNodeSchema = z.object({
  /**
   * 节点数据
   */
  data: WorkflowNodeDataSchema.optional(),
  /**
   * 节点高度
   */
  height: z.number().optional(),
  /**
   * 节点ID
   */
  id: z.string().optional(),
  /**
   * 节点位置
   */
  position: PositionSchema.optional(),
  /**
   * 节点绝对位置
   */
  position_absolute: PositionSchema.optional(),
  /**
   * 是否被选中
   */
  selected: z.boolean().default(false),
  /**
   * 源位置
   */
  source_position: z.string().optional(),
  /**
   * 目标位置
   */
  target_position: z.string().optional(),
  /**
   * 节点类型
   */
  type: z.string().optional(),
  /**
   * 节点宽度
   */
  width: z.number().optional()
});

export type WorkflowNode = z.infer<typeof WorkflowNodeSchema>;

/**
* 工作流边数据Schema
*/
const WorkflowEdgeDataSchema = z.object({
  /**
   * 是否在迭代中
   */
  isInIteration: z.boolean().default(false),
  /**
   * 起始节点类型
   */
  sourceType: z.string().optional(),
  /**
   * 目标节点类型
   */
  targetType: z.string().optional()
});

export type WorkflowEdgeData = z.infer<typeof WorkflowEdgeDataSchema>;

/**
* 工作流边Schema
*/
const WorkflowEdgeSchema = z.object({
  /**
   * 边数据
   */
  data: WorkflowEdgeDataSchema.optional(),
  /**
   * 边ID
   */
  id: z.string().optional(),
  /**
   * 起始节点ID
   */
  source: z.string().optional(),
  /**
   * 起始节点句柄
   */
  sourceHandle: z.string().optional(),
  /**
   * 目标节点ID
   */
  target: z.string().optional(),
  /**
   * 目标节点句柄
   */
  targetHandle: z.string().optional(),
  /**
   * 边类型
   */
  type: z.string().optional(),
  /**
   * z轴索引
   */
  zIndex: z.number().default(0)
});

export type WorkflowEdge = z.infer<typeof WorkflowEdgeSchema>;

/**
* 工作流图Schema
*/
const WorkflowGraphSchema = z.object({
  /**
   * 节点列表
   */
  nodes: z.array(WorkflowNodeSchema).default([]),
  /**
   * 边列表
   */
  edges: z.array(WorkflowEdgeSchema).default([])
});

export type WorkflowGraph = z.infer<typeof WorkflowGraphSchema>;

/**
* 工作流发布详情Schema
*/
//eslint-disable-next-line @typescript-eslint/no-unused-vars
export const WorkflowPublishSchema = z.object({
  /**
   * 工作流ID
   */
  id: z.string(),
  /**
   * 工作流图
   */
  graph: WorkflowGraphSchema
});

export type WorkflowPublish = z.infer<typeof WorkflowPublishSchema>;
