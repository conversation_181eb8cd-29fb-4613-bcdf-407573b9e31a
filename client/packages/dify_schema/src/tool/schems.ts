import { z } from 'zod';
/** * 工具类型枚举
 */
export const ToolTypeSchema = z.enum(['builtin', 'api', 'workflow']);
export type ToolType = z.infer<typeof ToolTypeSchema>; /**
 * 图标内容模型 */
export const IconContentSchema = z.object({
  /** 图标内容 */ content: z.string() /** 图标背景色 */,
  background: z.string(),
});
export type IconContent = z.infer<typeof IconContentSchema>; /**
 * 多语言文本模型 */
export const MultiLanguageTextSchema = z.object({
  /** 简体中文 */ zh_Hans: z.string().optional() /** 英文(美国) */,
  en_US: z.string().optional() /** 葡萄牙语(巴西) */,
  pt_BR: z.string().optional(),
  /** 日语(日本) */ ja_JP: z.string().optional(),
});
export type MultiLanguageText = z.infer<typeof MultiLanguageTextSchema>;
/** * 工具参数模型
 */ export const ToolParameterSchema = z.object({
  /** 参数名称 */ name: z.string(),
  /** 参数标签 */ label: MultiLanguageTextSchema,
  /** 参数人类可读描述 */ human_description: MultiLanguageTextSchema,
  /** 参数类型 */ type: z.string(),
  /** 是否必填 */ required: z.boolean(),
  /** 默认值 */ default: z.any().optional(),
  /** 选项列表 */ options: z.array(z.record(z.any())).optional(),
});
export type ToolParameter = z.infer<typeof ToolParameterSchema>;
/** * 工具模型 */
export const Tool1Schema = z.object({
  /** 工具名称 */ name: z.string() /** 工具标签 */,
  label: MultiLanguageTextSchema /** 工具描述 */,
  description: MultiLanguageTextSchema /** 工具参数列表 */,
  parameters: z.array(ToolParameterSchema).default([]),
});
export type Tool1 = z.infer<typeof Tool1Schema>; /**
 * 工具提供者模型 */
export const ToolProviderSchema = z.object({
  /** 工具提供者ID */ id: z.string() /** 作者 */,
  author: z.string() /** 名称 */,
  name: z.string() /** 插件ID */,
  plugin_id: z.string().optional() /** 插件唯一标识符 */,
  plugin_unique_identifier: z.string().optional() /** 描述 */,
  description: MultiLanguageTextSchema /** 图标 */,
  icon: z.union([z.string(), IconContentSchema]) /** 标签 */,
  label: MultiLanguageTextSchema /** 类型 */,
  type: ToolTypeSchema.default('api') /** 团队凭证 */,
  team_credentials: z.record(z.any()).default({}) /** 是否团队授权 */,
  is_team_authorization: z.boolean() /** 是否允许删除 */,
  allow_delete: z.boolean() /** 工具列表 */,
  tools: z.array(Tool1Schema).default([]) /** 标签列表 */,
  labels: z.array(z.string()).default([]),
});
export type ToolProvider = z.infer<typeof ToolProviderSchema>;
/** * 工具响应模型
 */ export const ToolResponseSchema = z.object({
  /** 响应结果 */ result: z.record(z.any()),
});
export type ToolResponse = z.infer<typeof ToolResponseSchema>;
