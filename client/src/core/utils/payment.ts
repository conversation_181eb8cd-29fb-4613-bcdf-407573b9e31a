/**
 * 支付相关工具函数
 */

import React from 'react';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { API_BASE_URL } from '~/core/constants';
import { createOrder } from '~/user/api';
import { OrderCreateRequest, OrderResult } from '~/user/schemas';
import { ResponsePayloads } from '~/core/schemas';
import { useMutation } from '@tanstack/react-query';

// 定义订单接口，与后端响应一致
interface Order {
  id: string;
  amount: number;
  status: 'PENDING' | 'PAID' | 'CANCELLED' | 'REFUNDED';
  pay_time: string | null;
  product_id: string;
  created_at: string;
  user_id: number;
  payment_method: 'alipay' | 'wechatpay';
  product_type: string;
  payment_plan_id: number;
  quantity: number;
  updated_at: string;
}

/**
 * 打开微信支付二维码页面
 * @param paymentUrl 微信支付链接
 * @param orderId 订单ID（可选）
 * @param navigate React Router的navigate函数
 */
export const openWechatPaymentPage = (
  paymentUrl: string,
  navigate: (to: string) => void,
  orderId?: string
) => {
  // 构建查询参数
  const params = new URLSearchParams({
    url: encodeURIComponent(paymentUrl),
  });

  if (orderId) {
    params.append('orderId', orderId);
  }

  // 使用navigate跳转到微信支付页面
  const paymentPageUrl = `/wechat-payment?${params.toString()}`;
  navigate(paymentPageUrl);
};

/**
 * 打开支付宝支付页面
 * @param paymentUrl 支付宝支付链接
 */
export const openAlipayPaymentWindow = (paymentUrl: string) => {
  window.open(paymentUrl, '_blank');
};

/**
 * 通用的支付处理Hook
 * @param onOrderIdChange 订单ID变化回调
 * @param navigate React Router的navigate函数
 * @param onPaymentSuccess 支付成功回调
 */
export const usePaymentMutation = (
  onOrderIdChange?: (orderId: string) => void,
  navigate?: (to: string) => void,
  onPaymentSuccess?: () => void
) => {
  return useMutation<ResponsePayloads<OrderResult>, Error, Omit<OrderCreateRequest, 'payment_method'> & { selectedPaymentMethod: 'alipay' | 'wechatpay' }>({
    mutationFn: async (data) => {
      const { selectedPaymentMethod, ...orderData } = data;
      return createOrder({
        ...orderData,
        payment_method: selectedPaymentMethod,
      });
    },
    onSuccess: (response, variables) => {
      if (response.data) {
        notifications.show({
          title: '提示',
          message: '订单已创建，正在跳转...',
          color: 'green',
        });

        // 通知订单ID变化
        if (onOrderIdChange) {
          onOrderIdChange(response.data.order_id);
        }

        if (response.data?.payment_url && variables.selectedPaymentMethod) {
          if (variables.selectedPaymentMethod === 'wechatpay') {
            // 跳转到微信支付二维码页面
            if (navigate) {
              openWechatPaymentPage(response.data.payment_url, navigate, response.data.order_id);
            } else {
              console.error('navigate函数未提供，无法跳转到微信支付页面');
            }
          } else {
            // 在新窗口打开支付宝支付页面（外部链接）
            openAlipayPaymentWindow(response.data.payment_url);

            // 支付宝支付后，显示提示并启动轮询
            notifications.show({
              title: '支付提示',
              message: '请在新窗口完成支付，支付完成后将自动检测支付状态',
              color: 'blue',
              autoClose: 5000,
            });
          }
        }
      } else {
        notifications.show({
          title: '支付失败',
          message: response.error?.message || '未知错误',
          color: 'red',
        });
      }
    },
    onError: (error) => {
      notifications.show({
        title: '请求失败',
        message: error.message,
        color: 'red',
      });
    },
  });
};

/**
 * 支付窗口消息监听Hook
 * @param orderId 订单ID
 * @param enabled 是否启用监听
 * @param onSuccess 支付成功回调
 * @param onError 支付失败回调
 */
export const usePaymentMessageListener = (
  orderId: string | null,
  enabled: boolean = false,
  onSuccess?: () => void,
  onError?: () => void
) => {
  // 使用 ref 来跟踪是否已经处理过该订单的支付成功消息
  const processedOrdersRef = React.useRef<Set<string>>(new Set());

  React.useEffect(() => {
    if (!enabled || !orderId) return;

    const handleMessage = (event: MessageEvent) => {
      // 验证消息来源（可以根据需要添加更严格的验证）
      if (event.data?.type === 'PAYMENT_SUCCESS' && event.data?.orderId === orderId) {
        console.log('收到支付成功消息:', event.data);

        // 检查是否已经处理过该订单
        if (processedOrdersRef.current.has(orderId)) {
          console.log('订单已处理过，跳过重复通知:', orderId);
          return;
        }

        // 标记该订单已处理
        processedOrdersRef.current.add(orderId);

        onSuccess?.();
      } else if (event.data?.type === 'PAYMENT_FAILED' && event.data?.orderId === orderId) {
        console.log('收到支付失败消息:', event.data);

        onError?.();
      }
    };

    // 添加消息监听器
    window.addEventListener('message', handleMessage);

    // 清理函数
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [enabled, orderId, onSuccess, onError]);

  // 当组件卸载时清理已处理的订单记录
  React.useEffect(() => {
    return () => {
      processedOrdersRef.current.clear();
    };
  }, []);
};

/**
 * 查询订单支付状态（用于微信支付轮询）
 * @param orderId 订单ID
 */
export async function checkOrderStatus(orderId: string): Promise<ResponsePayloads<Order>> {
  const response = await fetch(`${API_BASE_URL}/orders/detail?order_id=${orderId}`, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
    },
  });

  if (!response.ok) {
    console.error('响应状态:', response.status, response.statusText);
    throw new Error(`查询订单状态失败: ${response.statusText}`);
  }

  const data = await response.json();
  console.log('原始响应数据:', data);
  return data;
}

/**
 * 微信支付状态轮询Hook
 * @param orderId 订单ID
 * @param enabled 是否启用轮询
 * @param onSuccess 支付成功回调
 * @param onError 支付失败回调
 */
export const useWechatPaymentPolling = (
  orderId: string | null,
  enabled: boolean = false,
  onSuccess?: () => void,
  onError?: () => void
) => {
  const queryClient = useQueryClient();

  const query = useQuery({
    queryKey: ['wechatPaymentStatus', orderId],
    queryFn: () => checkOrderStatus(orderId!),
    enabled: enabled && !!orderId,
    refetchInterval: (data) => {
      console.log('status:', data?.state?.data?.data?.status);
      // 进行判断
      if (
        data?.state?.data?.data?.status === 'PAID' ||
        data?.state?.data?.data?.status === 'CANCELLED' ||
        data?.state?.data?.data?.status === 'REFUNDED'
      ) {
        return false;
      }
      return 3000; // 每3秒轮询一次
    },
    refetchIntervalInBackground: false,
    retry: false,
  });

  // 使用 useEffect 监听查询结果变化
  React.useEffect(() => {
    if (!query.data) return;

    const data = query.data;
    console.log('微信支付轮询返回数据:', data);
    console.log('订单状态:', data.data?.status);
    console.log('状态类型:', typeof data.data?.status);

    if (data.data?.status === 'PAID') {
      console.log('检测到支付成功，停止轮询并处理...');

      // 立即停止轮询
      queryClient.cancelQueries({ queryKey: ['wechatPaymentStatus', orderId] });

      // 支付成功，通知父窗口（如果是在弹窗中打开）
      try {
        if (window.opener) {
          window.opener.postMessage(
            {
              type: 'PAYMENT_SUCCESS',
              orderId: orderId,
              status: 'success',
              paymentMethod: 'wechatpay',
            },
            '*'
          );
        }
      } catch (e) {
        console.error('无法通知父窗口:', e);
      }

      onSuccess?.();
    } else if (data.data?.status === 'CANCELLED' || data.data?.status === 'REFUNDED') {
      console.log('检测到支付失败（订单已取消或退款），停止轮询并处理...');
      queryClient.cancelQueries({ queryKey: ['wechatPaymentStatus', orderId] });

      notifications.show({
        title: '支付失败',
        message: '订单已取消或已退款',
        color: 'red',
      });

      onError?.();
    } else {
      console.log('订单状态未变化，继续轮询...');
    }
  }, [query.data, queryClient, orderId, onSuccess, onError]);

  // 监听查询错误
  React.useEffect(() => {
    if (!query.error) return;

    console.error('查询支付状态失败:', query.error);
    queryClient.cancelQueries({ queryKey: ['wechatPaymentStatus', orderId] });
    notifications.show({
      title: '查询失败',
      message: '无法获取订单状态，请稍后重试',
      color: 'red',
    });
    onError?.();
  }, [query.error, queryClient, orderId, onError]);

  return query;
};