/**
 * 二维码工具函数
 * 使用qrcode.react组件生成二维码
 */

// 二维码状态接口
export interface QRCodeState {
  isGenerating: boolean;
  qrCodeUrl: string | null;
  error: string | null;
}

// 二维码组件属性接口
export interface QRCodeProps {
  value: string;
  size?: number;
  level?: 'L' | 'M' | 'Q' | 'H';
  fgColor?: string;
  bgColor?: string;
}

/**
 * 创建初始的二维码状态
 */
export const createInitialQRCodeState = (): QRCodeState => ({
  isGenerating: false,
  qrCodeUrl: null,
  error: null,
});

/**
 * 创建加载中的二维码状态
 */
export const createLoadingQRCodeState = (): QRCodeState => ({
  isGenerating: true,
  qrCodeUrl: null,
  error: null,
});

/**
 * 创建成功的二维码状态
 */
export const createSuccessQRCodeState = (qrCodeUrl: string): QRCodeState => ({
  isGenerating: false,
  qrCodeUrl,
  error: null,
});

/**
 * 创建错误的二维码状态
 */
export const createErrorQRCodeState = (error: string): QRCodeState => ({
  isGenerating: false,
  qrCodeUrl: null,
  error,
});
