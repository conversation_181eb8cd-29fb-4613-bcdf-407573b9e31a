import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { API_BASE_URL } from '../constants';

// 设置默认语言为中文
dayjs.locale('zh-cn');

/**
 * 补全 OSS 地址前缀
 * @param url 静态资源相对地址
 * @returns ossDomain + url
 */
export function ossUrl(url: string) {
  const ossDomain = 'https://ai-quantum.oss-cn-hangzhou.aliyuncs.com/v2';
  let assetUrl = url || '';

  if (assetUrl && !assetUrl?.includes('http')) {
    if (assetUrl[0] !== '/') {
      assetUrl = ossDomain + '/' + assetUrl;
    } else {
      assetUrl = ossDomain + assetUrl;
    }
  }

  return assetUrl;
}

/**
 * 数字转换为千分位格式
 * @param num 数字
 * @returns 千分位格式的字符串
 */
export const thousandChar = (num: number) => {
  return num.toLocaleString();
};

/**
 * 格式化日期
 * @param date 日期对象或时间戳
 * @param format 格式化字符串，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns 格式化后的日期字符串
 */
export const formatDate = (date: Date | number | string, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
  if (!date) return '-';

  // 处理字符串类型的时间戳
  if (typeof date === 'string') {
    // 尝试将字符串转换为数字
    const numericDate = Number(date);
    if (!isNaN(numericDate)) {
      date = numericDate;
    }
  }

  // 处理数字类型的时间戳
  if (typeof date === 'number') {
    // 检查时间戳长度，判断是秒还是毫秒
    // Unix时间戳通常是10位数（秒），而JavaScript时间戳是13位数（毫秒）
    if (date.toString().length <= 10) {
      // 如果是秒级时间戳，转换为毫秒
      date = date * 1000;
    }
  }

  return dayjs(date).format(format);
};

/**
 * 格式化相对时间（例如：3小时前，2天前）
 * @param date 日期对象或时间戳
 * @returns 相对时间字符串
 */
export const formatRelativeTime = (date: Date | number): string => {
  const now = dayjs();
  const target = dayjs(date);
  const diffMinutes = now.diff(target, 'minute');

  if (diffMinutes < 1) {
    return '刚刚';
  } else if (diffMinutes < 60) {
    return `${diffMinutes}分钟前`;
  } else if (diffMinutes < 24 * 60) {
    return `${Math.floor(diffMinutes / 60)}小时前`;
  } else if (diffMinutes < 30 * 24 * 60) {
    return `${Math.floor(diffMinutes / (24 * 60))}天前`;
  } else if (diffMinutes < 12 * 30 * 24 * 60) {
    return `${Math.floor(diffMinutes / (30 * 24 * 60))}个月前`;
  } else {
    return `${Math.floor(diffMinutes / (365 * 24 * 60))}年前`;
  }
};

/**
 * Uploads a file to MinIO and returns its permanent URL.
 * @param file The file to upload.
 * @param path Optional path within the bucket to store the file (e.g., 'images/', 'documents/2023/').
 * @returns Promise<string> The permanent URL of the uploaded file.
 */
export const uploadFileToMinio = async (file: File, path?: string): Promise<string> => {
  try {
    // 创建FormData对象
    const formData = new FormData();
    formData.append('file', file);

    // 如果提供了路径，添加到表单数据中
    if (path) {
      formData.append('path', path);
    }

    // 调用后端API上传文件
    const response = await fetch(`${API_BASE_URL}/files/upload_to_minio`, {
      method: 'POST',
      body: formData,
    });

    // 检查响应状态
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || '上传文件失败');
    }

    // 解析响应数据
    const data = await response.json();

    // 返回文件URL
    return data.data.url;
  } catch (e) {
    console.error('Error uploading file to MinIO:', e);
    throw e;
  }
};
