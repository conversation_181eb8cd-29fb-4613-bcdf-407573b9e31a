import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Box, Button, Group, Paper, Stack, Text, Title } from '@mantine/core';
import { IoAlertCircleOutline, IoRefreshOutline } from 'react-icons/io5';

interface ErrorBoundaryProps {
  children: ReactNode;
  fallback?: ReactNode;
  onReset?: () => void;
}

interface ErrorBoundaryState {
  hasError: boolean;
  error: Error | null;
  errorInfo: ErrorInfo | null;
}

/**
 * 错误边界组件，用于捕获子组件树中的 JavaScript 错误
 * 记录错误并显示备用 UI，防止整个应用崩溃
 */
class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = {
      hasError: false,
      error: null,
      errorInfo: null,
    };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    // 更新状态，下一次渲染将显示错误UI
    return {
      hasError: true,
      error,
      errorInfo: null,
    };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo): void {
    // 可以在这里记录错误信息
    console.error('ErrorBoundary捕获到错误:', error, errorInfo);
    this.setState({
      errorInfo,
    });
  }

  handleReset = (): void => {
    // 重置错误状态
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null,
    });

    // 如果提供了onReset回调，则调用它
    if (this.props.onReset) {
      this.props.onReset();
    }
  };

  render(): ReactNode {
    if (this.state.hasError) {
      // 如果提供了自定义的fallback，则使用它
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // 默认的错误UI
      return (
        <Box
          style={{
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            minHeight: '100vh',
            padding: '16px',
          }}
        >
          <Paper p="md" withBorder shadow="sm" radius="md" w="100%" maw={500}>
            <Stack gap="md">
              <Group justify="center">
                <IoAlertCircleOutline size={32} color="red" />
                <Title order={3}>出错了</Title>
              </Group>

              <Text c="dimmed">很抱歉，发生了一些错误。您可以尝试重新加载或联系支持团队。</Text>

              {this.state.error && (
                <Box bg="gray.1" p="sm" style={{ borderRadius: '4px' }}>
                  <Text size="sm" c="red">
                    {this.state.error.toString()}
                  </Text>
                </Box>
              )}

              <Group justify="center">
                <Button leftSection={<IoRefreshOutline size={16} />} onClick={this.handleReset} color="blue">
                  重试
                </Button>
              </Group>
            </Stack>
          </Paper>
        </Box>
      );
    }

    // 正常情况下渲染子组件
    return this.props.children;
  }
}

export default ErrorBoundary;
