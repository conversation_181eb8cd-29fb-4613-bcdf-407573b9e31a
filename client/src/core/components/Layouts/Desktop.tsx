import { AppShell, Group, Divider, ScrollArea, Image, Stack, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { ossUrl } from '~/core/utils';
import { useDisclosure } from '@mantine/hooks';
import { Menu, SecondaryMenu, History, Settings, Dialog, ErrorBoundary } from '~/core/components';
import { RiMessage2Line, RiRobot2Line, RiUserLine, RiBookLine, RiBookOpenLine, RiKeyLine, RiDashboardLine, RiFlowChart } from 'react-icons/ri';
import { useChatContext } from '~/agent/chat/contexts/ChatContext';
import { useEffect, useState } from 'react';

import { MenuItem } from '~/core/components/Menu/types';
import { Conversation } from '~/agent/chat/schemas';
import { useUserStore } from '~/user/core/store';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const chatContext = useChatContext();
  const location = useLocation();
  const navigate = useNavigate(); // 新增：获取导航函数
  const userInfo = useUserStore((state) => state.userInfo);
  if (!userInfo) {
    navigate('/login');
  }
  const mainLayoutBackgroundColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const subNavBgColor = isDark ? theme.colors.dark[9] : theme.colors.gray[1];
  const subNavBorderColor = isDark ? theme.colors.dark[6] : 'rgba(240, 242, 245, 1)';

  // 存储会话列表数据
  const [historyData, setHistoryData] = useState<
    Array<{
      id: string;
      title: string;
      list: Array<Conversation>;
    }>
  >([]);

  // 加载会话列表
  useEffect(() => {
    const loadConversations = async () => {
      try {
        const conversations = await chatContext.getConversationsByDate();
        setHistoryData(conversations);
      } catch (error) {
        console.error('加载会话列表失败:', error);
      }
    };

    loadConversations();
  }, [chatContext]);

  const [showSecondaryNav, { close: closeSecondaryNav, open: openSecondaryNav }] = useDisclosure(false);

  // 根据当前路径自动显示/隐藏二级菜单
  useEffect(() => {
    if (location.pathname.startsWith('/ucenter')) {
      openSecondaryNav();
    } else {
      closeSecondaryNav();
    }
  }, [location.pathname, openSecondaryNav, closeSecondaryNav]);

  const handleMenuItemClick = (item: MenuItem) => {
    if (item.label === '个人中心') {
      openSecondaryNav();
    } else {
      closeSecondaryNav();
    }
  };

  const [opened, { close }] = useDisclosure(false);

  const handleConfirmLogout = () => {
    // 这里添加实际登出逻辑
    close();
  };

  const handleCancelLogout = () => {
    close();
  };

  const menuData = {
    navbar: [
      { label: '对话', icon: RiMessage2Line, path: '/chat' },
      { label: '智能体', icon: RiRobot2Line, path: '/agent/marketplace' },
      { label: '工作流', icon: RiFlowChart, path: '/workflow/marketplace' },
      { label: '课程', icon: RiBookLine, path: '/course' },
      { label: '知识库', icon: RiBookOpenLine, path: '/knowledge' },
      { label: '数字人', icon: RiRobot2Line, path: '/szr' },
    ],

    settings: [{ label: '个人中心', icon: RiUserLine, path: '/ucenter' }],

    ucenter: [
      {
        label: '资产',
        icon: RiDashboardLine,
        path: '/ucenter/assets',
        children: [
          { label: '智能体', icon: RiRobot2Line, path: '/ucenter/assets/agent' },
          { label: '课程', icon: RiBookLine, path: '/ucenter/assets/course' },
        ],
      },
      { label: '用量', icon: RiKeyLine, path: '/ucenter/usage' },
    ],
  };

  return (
    <>
      <AppShell
        navbar={{
          width: 304,
          breakpoint: 'sm',
        }}
        withBorder={false}
      >
        <AppShell.Navbar px={24} py={32}>
          <AppShell.Section>
            <Image w={157} h={41} alt="Logo" src={ossUrl('https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1748940241-17ef2c01.png')} />
          </AppShell.Section>

          <AppShell.Section mt={40}>
            <Menu items={menuData.navbar} onClick={handleMenuItemClick} />
          </AppShell.Section>

          <Divider my={12} />

          <AppShell.Section grow my={12} component={ScrollArea} type="never">
            {historyData.map((item) => (
              <History key={item.id} list={item.list} date={item.title} />
            ))}
          </AppShell.Section>

          <Divider my={12} />

          <AppShell.Section>
            <Stack gap={16}>
              {/* <Ucenter /> */}
              <Menu items={menuData.settings} onClick={handleMenuItemClick} />
              <Settings />
            </Stack>
          </AppShell.Section>
        </AppShell.Navbar>

        <AppShell.Main bg={mainLayoutBackgroundColor}>
          <Group className="w-full h-full" align="flex-start" justify="flex-start" gap={0}>
            {/* 二级导航 */}
            {showSecondaryNav && (
              <AppShell.Section
                className="h-screen flex-none"
                w={240}
                px={16}
                py={32}
                bg={subNavBgColor}
                style={{
                  borderRight: `1px solid ${subNavBorderColor}`,
                  position: 'fixed',
                  left: 304, // 一级菜单宽度
                  top: 0,
                  zIndex: 100,
                }}
              >
                <SecondaryMenu items={menuData.ucenter} />
              </AppShell.Section>
            )}

            {/* 内容区域 */}
            <AppShell.Section
              className="flex-auto"
              style={{
                flexGrow: 1,
                marginLeft: showSecondaryNav ? 240 : 0,
                transition: 'margin-left 0.2s ease',
              }}
            >
              <ErrorBoundary>
                <Outlet />
              </ErrorBoundary>
            </AppShell.Section>
          </Group>
        </AppShell.Main>
      </AppShell>

      <Dialog
        opened={opened}
        type="error"
        title="退出登录"
        message="推出后将失去账号功能，您确定要退出么？"
        onCancel={handleCancelLogout}
        onConfirm={handleConfirmLogout}
      />
    </>
  );
};

export default Desktop;
