import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

const MainLayout = () => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile />;
  }

  return <Desktop />;
};

export default MainLayout;
MainLayout.displayName = 'MainLayout';
