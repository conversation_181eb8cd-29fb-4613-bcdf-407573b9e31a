import { useTheme } from '~/core/features/mantine';
import { Stack, Group, Avatar, Text } from '@mantine/core';
import { FiMail } from 'react-icons/fi';

const Desktop = () => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const nicknameTextColor = isDark ? '#FFFFFF' : 'rgba(56, 56, 56, 1)';
  const emailTextColor = isDark ? '#FFFFFF' : 'rgba(128, 128, 128, 1)';

  return (
    <Stack align="center" gap={0}>
      <Avatar src="avatar.png" alt="it's me" size={90} />
      <Text className="leading-[29px] fw-500" fz={20} mt={16} mb={4} c={nicknameTextColor}>
        Sooyaa9527
      </Text>
      <Group align="center" gap={8}>
        <FiMail size={12} color={emailTextColor} />
        <Text className="leading-[14px] fw-500" fz={12} c={emailTextColor}>
          <EMAIL>
        </Text>
      </Group>
    </Stack>
  );
};

export default Desktop;
