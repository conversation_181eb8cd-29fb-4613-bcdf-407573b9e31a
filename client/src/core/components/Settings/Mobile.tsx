/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useState } from 'react';
import { Stack, Box, UnstyledButton, Text, Menu, Group, Select, useMantineTheme } from '@mantine/core';
import { RiSettings3Line, RiSunLine, RiTranslate } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const textColor = isDark ? '#E6E6E6' : 'rgba(56, 56, 56, 1)';
  const hoverBackgroundColor = isDark
    ? theme.colors.gray[8]
    : 'linear-gradient(0deg, rgba(247, 248, 255, 1), rgba(247, 248, 255, 1)), rgba(243, 245, 255, 1)';
  const activeBackgroundColor = 'rgba(73, 81, 235, 1)'; // Indigo color for active item
  const activeTextColor = '#FFFFFF';

  const [colorScheme, setColorScheme] = useState<string | null>('auto');
  const [language, setLanguage] = useState<string | null>('zh-CN');

  const colorSchemeInfo = {
    label: '界面主题',
    key: 'colorScheme',
    icon: RiSunLine,
    options: [
      { label: '跟随系统', value: 'auto' },
      { label: '明', value: 'light' },
      { label: '暗', value: 'dark' },
    ],
  };
  const languageInfo = {
    label: '语言',
    key: 'language',
    icon: RiTranslate,
    options: [
      { label: '简体中文', value: 'zh-CN' },
      { label: 'English', value: 'en-US' },
    ],
  };

  return (
    <Menu
      position="right-end"
      styles={{
        dropdown: {
          padding: '8px',
          border: 0,
          borderRadius: '12px',
          boxShadow: '-5px 3px 15px  rgba(232, 239, 252, 0.5)',
        },
        item: {
          borderRadius: '8px',
        },
      }}
    >
      <Menu.Target>
        <Box position="relative" w="100%" h="45px" style={{ color: textColor }}>
          <UnstyledButton
            className={`flex items-center w-full h-full rounded-[10px]`}
            px={16}
            css={css`
              &:hover {
                background: ${hoverBackgroundColor};
              }
              &.active {
                background: ${activeBackgroundColor};
                color: ${activeTextColor};
              }
            `}
          >
            <RiSettings3Line size={20} />
            <Text className="flex-auto leading-[18px] fw-700" ml={16} fz={16}>
              设置
            </Text>
          </UnstyledButton>
        </Box>
      </Menu.Target>

      <Menu.Dropdown p={16}>
        <Stack gap={16}>
          <Group align="center" justify="space-between">
            <Group align="center" gap={8}>
              <colorSchemeInfo.icon size={18} color="rgba(56, 56, 56, 1)" />
              <Text fz={16} fw={500} lh="24px" c="rgba(56, 56, 56, 1)">
                {colorSchemeInfo.label}
              </Text>
            </Group>
            <Select
              w={100}
              variant="filled"
              size="xs"
              value={colorScheme}
              onChange={setColorScheme}
              data={colorSchemeInfo.options}
            />
          </Group>
          <Group align="center" justify="space-between">
            <Group align="center" gap={8}>
              <languageInfo.icon size={18} color="rgba(56, 56, 56, 1)" />
              <Text fz={16} fw={500} lh="24px" c="rgba(56, 56, 56, 1)">
                {languageInfo.label}
              </Text>
            </Group>
            <Select w={100} variant="filled" size="xs" value={language} onChange={setLanguage} data={languageInfo.options} />
          </Group>
        </Stack>
      </Menu.Dropdown>
    </Menu>
  );
};

export default Desktop;
