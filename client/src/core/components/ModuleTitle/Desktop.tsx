import React from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';
import { Stack, Text, useMantineTheme } from '@mantine/core';

import { ModuleTitleProps } from './types';

const Desktop: React.FC<ModuleTitleProps> = ({ title, subtitle }) => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();
  const isDark = actualColorScheme === 'dark';

  // Determine text colors based on theme
  const titleColor = isDark ? theme.colors.indigo[3] : 'rgba(73, 81, 235, 1)';
  const subtitleColor = isDark ? theme.colors.gray[4] : 'rgba(56, 56, 56, 1)';

  return (
    <Stack align="center" gap={18}>
      <Text className="leading-[48px] fw-700" fz={38} c={titleColor}>
        {title}
      </Text>
      <Text className="leading-[36px] fw-400" fz={22} c={subtitleColor}>
        {subtitle}
      </Text>
    </Stack>
  );
};

export default Desktop;
