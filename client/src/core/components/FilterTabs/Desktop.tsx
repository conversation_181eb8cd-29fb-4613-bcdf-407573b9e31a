/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { ScrollArea, Box, UnstyledButton } from '@mantine/core';
import { useTheme } from '~/core/features/mantine/theme-context';

import { FilterTabsProps } from './types';

/**
 * Desktop version of the FilterTabs component
 */
const Desktop: React.FC<FilterTabsProps> = ({ categories, selectedCategory, onCategoryChange }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const defaultTextColor = isDark ? 'white' : 'rgba(92, 92, 92, 1)';
  const activeTextColor = isDark ? 'white' : 'rgba(56, 56, 56, 1)';
  const activeLineColor = isDark ? 'white' : 'rgba(255, 235, 59, 1)';
  const shadowColor = isDark ? 'white' : 'rgba(0, 0, 0, 0.05)';

  const controls = categories.map((category) => (
    <UnstyledButton
      className="relative leading-[26px] fw-400"
      key={category.title}
      onClick={() => onCategoryChange(category)}
      mod={{ active: category.title === selectedCategory.title }}
      mx={16}
      fz={18}
      style={{
        color: defaultTextColor,
        transition: 'color 100ms ease',
      }}
      css={css`
        &[data-active] {
          color: ${activeTextColor} !important;
        }
        &[data-active]:after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          width: calc(100% - 8px);
          height: 3px;
          border-radius: 8px;
          background: ${activeLineColor};
          box-shadow: 0px 6px 6px ${shadowColor};
          transform: translateX(-50%);
        }
      `}
    >
      <span className="relative z-1">{category.title}</span>
    </UnstyledButton>
  ));

  return (
    <Box className="sticky w-full">
      <ScrollArea type="never" style={{ height: '34px', whiteSpace: 'nowrap' }}>
        {controls}
      </ScrollArea>
    </Box>
  );
};

export default Desktop;
