import React from 'react';
import { Stack, Group, Avatar, Text, Divider } from '@mantine/core';

import { SearchItemProps } from './types';

const Mobile: React.FC<SearchItemProps> = ({ border = true, asset, onClick }) => {
  // 处理点击事件
  const handleClick = () => {
    if (asset && onClick) {
      onClick(asset);
    }
  };

  return (
    <>
      <Group className="cursor-pointer" py={8} gap={16} onClick={handleClick} wrap="nowrap">
        <Avatar src={asset?.icon || null} alt={asset?.name || '资产'} color="blue" size="md">
          {asset?.name ? asset.name.charAt(0) : '?'}
        </Avatar>
        <Stack gap={0} style={{ flex: 1, minWidth: 0 }}>
          <Text fz={18} fw={700} lh="24px" c="black" lineClamp={1}>
            {asset?.name || '未命名资产'}
          </Text>
          <Text fz={14} fw={500} lh="24px" c="rgba(56, 56, 56, 1)" lineClamp={1}>
            {asset?.description || '暂无描述'}
          </Text>
        </Stack>
      </Group>

      {border && <Divider />}
    </>
  );
};

export default Mobile;
