import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { IframeEmbed } from '~/core/components';
import { useUserStore } from '~/user/core/store';
import { getUserKeys } from '~/user/core/api/usage';
import { Center, Loader, Text, Stack, Button } from '@mantine/core';
import { RiLockLine } from 'react-icons/ri';

/**
 * 数字人页面组件
 * 嵌入蝉镜网页
 */
const SZR = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [mounted, setMounted] = useState(false);
  const [loading, setLoading] = useState(true);
  const [dgvKey, setDgvKey] = useState<string>('');
  const [hasPermission, setHasPermission] = useState(true); // 是否有数字人权限
  const { userInfo, token } = useUserStore();

  // 从URL中获取查询参数
  const searchParams = new URLSearchParams(location.search);
  const userId = searchParams.get('user_id') || '';
  const projectId = searchParams.get('project_id') || '';
  const availableKey = searchParams.get('available_key') || '';

  // 构建参数对象
  const params: Record<string, string> = {};
  if (userId) params.user_id = userId;
  if (projectId) params.project_id = projectId;
  if (availableKey || dgvKey) params.available_key = availableKey || dgvKey;

  // 查询用户DGV密钥
  useEffect(() => {
    const fetchDgvKey = async () => {
      // 如果URL中已经有available_key参数，则不需要查询
      if (availableKey) {
        setLoading(false);
        setMounted(true);
        return;
      }

      // 检查用户是否已登录
      if (!token || !userInfo) {
        // 如果用户未登录，跳转到登录页面
        console.log('用户未登录，跳转到登录页面');
        navigate('/login');
        return;
      }

      try {
        // 获取用户密钥列表
        const response = await getUserKeys();
        const keys = response.data?.keys || [];

        console.log('获取到的所有密钥:', keys);

        // 查找DGV服务的密钥 - 修复筛选条件
        const dgvKeys = keys.filter(key => {
          console.log(`检查密钥: service_code=${key.service_code}, status=${key.status}`);
          return key.service_code === 'DGV';
        });

        console.log('筛选出的DGV密钥:', dgvKeys);

        if (dgvKeys.length === 0) {
          // 如果没有DGV密钥，显示权限提示
          console.log('没有找到DGV密钥，显示权限提示');
          setHasPermission(false);
        } else {
          // 使用第一个可用的DGV密钥
          const firstDgvKey = dgvKeys[0];
          console.log('使用DGV密钥:', firstDgvKey.id);
          setDgvKey(firstDgvKey.id);
        }

        setLoading(false);
        setMounted(true);
      } catch (error) {
        console.error('获取DGV密钥失败:', error);
        // 出错时显示权限提示
        console.log('获取密钥失败，显示权限提示');
        setHasPermission(false);
        setLoading(false);
        setMounted(true);
      }
    };

    // 只在初始化时执行一次
    if (loading) {
      fetchDgvKey();
    }
  }, [loading]); // 简化依赖项，避免重复执行

  // 组件挂载时设置状态
  useEffect(() => {
    // 处理页面刷新
    const handleBeforeUnload = () => {
      // 在这里可以添加刷新前的逻辑
      sessionStorage.setItem('szr_last_path', location.pathname + location.search);
    };

    window.addEventListener('beforeunload', handleBeforeUnload);

    // 检查是否是从刷新恢复
    const lastPath = sessionStorage.getItem('szr_last_path');
    if (lastPath && lastPath.startsWith('/szr')) {
      // 可以在这里添加恢复逻辑
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [location]);

  // 显示加载状态
  if (loading) {
    return (
      <Center h="100vh">
        <div style={{ textAlign: 'center' }}>
          <Loader size="lg" mb="md" />
          <Text size="sm" c="dimmed">
            正在获取数字人服务密钥...
          </Text>
        </div>
      </Center>
    );
  }

  // 显示权限提示
  if (!hasPermission) {
    return (
      <Center h="100vh">
        <Stack align="center" gap="lg">
          <RiLockLine size={64} color="#868e96" />
          <Text size="lg" fw={500} c="dimmed">
            暂未数字人权限，请先开通
          </Text>
          <Button
            variant="filled"
            size="md"
            onClick={() => navigate('/')}
          >
            返回首页
          </Button>
        </Stack>
      </Center>
    );
  }

  // 确保组件已挂载再渲染iframe，避免刷新问题
  if (!mounted) {
    return null; // 避免显示加载指示器，提供无感知加载体验
  }

  return <IframeEmbed url="https://szr.ailqgx.cn" params={params} />;
};

export default SZR;
