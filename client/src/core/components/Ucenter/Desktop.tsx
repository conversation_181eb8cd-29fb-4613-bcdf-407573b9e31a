/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { Stack, Box, UnstyledButton, Text, Menu, Group, Avatar, Divider, useMantineTheme } from '@mantine/core';
import { Link } from 'react-router-dom';
import { useTheme } from '~/core/features/mantine';
import { RiRobot2Line, RiFlowChart, RiUserLine, RiBookOpenLine, RiLogoutBoxRLine } from 'react-icons/ri';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const textColor = isDark ? '#E6E6E6' : 'rgba(56, 56, 56, 1)';
  const hoverBackgroundColor = isDark
    ? theme.colors.gray[8]
    : 'linear-gradient(0deg, rgba(247, 248, 255, 1), rgba(247, 248, 255, 1)), rgba(243, 245, 255, 1)';
  const activeBackgroundColor = 'rgba(73, 81, 235, 1)'; // Indigo color for active item
  const activeTextColor = '#FFFFFF';
  const nikenameColor = isDark ? theme.colors.gray[3] : 'rgba(43, 50, 65, 1)';
  const validityColor = isDark ? 'rgba(160, 160, 160, 1)' : 'rgba(166, 166, 166, 1)';
  const labelColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const boxShadow = isDark ? '0px 3px 15px rgba(23, 16, 3, 0.5)' : '0px 3px 15px rgba(232, 239, 252, 0.5)';

  const ucenterData = [
    { label: '账号设置', icon: RiUserLine, path: '/ucenter/account' },
    { label: '我的课程', icon: RiBookOpenLine, path: '/ucenter/course' },
    { label: '我的工作流', icon: RiFlowChart, path: '/ucenter/workflow' },
    { label: '我的智能体', icon: RiRobot2Line, path: '/ucenter/agent' },
    { label: '退出登录', icon: RiLogoutBoxRLine, path: '/ucenter/logout' },
  ];

  return (
    <Menu
      position="right-end"
      styles={{
        dropdown: {
          width: '240px',
          padding: '8px',
          border: 0,
          borderRadius: '12px',
          boxShadow: boxShadow,
        },
        item: {
          borderRadius: '8px',
        },
      }}
    >
      <Menu.Target>
        <Box position="relative" w="100%" h="45px" style={{ color: textColor }}>
          <UnstyledButton
            className={`flex items-center w-full h-full rounded-[10px]`}
            px={16}
            css={css`
              &:hover {
                background: ${hoverBackgroundColor};
              }
              &.active {
                background: ${activeBackgroundColor};
                color: ${activeTextColor};
              }
            `}
          >
            <RiUserLine size={20} />
            <Text className="flex-auto leading-[18px] fw-700" ml={16} fz={16}>
              个人中心
            </Text>
          </UnstyledButton>
        </Box>
      </Menu.Target>

      <Menu.Dropdown p={16}>
        <Group align="center" gap={14}>
          <Avatar
            src=""
            style={{
              '--avatar-size': '32px', // 确保 Avatar 的大小是 '32px
            }}
          />
          <Stack gap={0}>
            <Text fz={14} fw={500} lh="20px" c={nikenameColor}>
              Sooyaa9527
            </Text>
            <Text fz={10} fw={500} lh="16px" c={validityColor}>
              2026-03-09 到期
            </Text>
          </Stack>
        </Group>

        <Divider my={14} />

        <Stack gap={8}>
          {ucenterData.map((item) => {
            if (!item.path) return null; // 跳过无效的菜单项

            return (
              <Menu.Item>
                <UnstyledButton className={`flex items-center h-full rounded-[10px]`} component={Link} to={item.path}>
                  <item.icon size={20} color={labelColor} />
                  <Text className="flex-auto" ml={16} fz={16} fw={500} lh="24px" c={labelColor}>
                    {item.label}
                  </Text>
                </UnstyledButton>
              </Menu.Item>
            );
          })}
        </Stack>
      </Menu.Dropdown>
    </Menu>
  );
};

export default Desktop;
