import React from 'react';

/**
 * 移动端不支持提示组件
 * 当检测到移动设备时显示此组件，提示用户使用桌面端访问
 */
const MobileNotSupported: React.FC = () => {
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100vh',
        backgroundColor: '#f5f5f5',
        padding: '20px',
        textAlign: 'center',
      }}
    >
      <div
        style={{
          backgroundColor: 'white',
          padding: '40px',
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
          maxWidth: '400px',
          width: '100%',
        }}
      >
        <div
          style={{
            fontSize: '48px',
            marginBottom: '20px',
          }}
        >
          📱
        </div>
        <h2
          style={{
            color: '#333',
            marginBottom: '16px',
            fontSize: '24px',
          }}
        >
          暂不支持移动端
        </h2>
        <p
          style={{
            color: '#666',
            lineHeight: '1.6',
            fontSize: '16px',
          }}
        >
          为了给您提供更好的使用体验，请在电脑端访问本应用。
        </p>
      </div>
    </div>
  );
};

export default MobileNotSupported;
