import React from 'react';
import { Mo<PERSON>, <PERSON><PERSON>, <PERSON>, Group, Button, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiCheckboxCircleLine, RiAlertLine, RiInformationLine } from 'react-icons/ri';

import { DialogProps } from './types';

const Desktop: React.FC<DialogProps> = ({
  opened = false,
  type,
  title,
  message,
  cancelText = '取消',
  confirmText = '确定',
  onCancel,
  onConfirm,
}) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const alertBgColor = isDark ? theme.colors.dark[6] : 'rgba(250, 250, 250, 1)';
  const alertTitleColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const alertTextColor = isDark ? 'rgba(127, 127, 127, 1)' : 'rgba(128, 128, 128, 1)';

  const alertIcon = (() => {
    switch (type) {
      case 'success':
        return <RiCheckboxCircleLine color="rgba(46, 204, 113, 1)" size={24} />;
      case 'error':
        return <RiAlertLine color="rgba(231, 76, 60, 1)" size={24} />;
      case 'warning':
        return <RiAlertLine color="rgba(241, 196, 15, 1)" size={24} />;
      case 'info':
        return <RiInformationLine color="rgba(52, 152, 219, 1)" size={24} />;
      default:
        return <RiInformationLine color="rgba(52, 152, 219, 1)" size={24} />;
    }
  })();

  const alertIconBgColor = (() => {
    switch (type) {
      case 'success':
        return 'rgba(183, 235, 201, 1)'; // 浅绿色背景
      case 'error':
        return 'rgba(255, 197, 193, 1)'; // 浅红色背景
      case 'warning':
        return 'rgba(255, 235, 156, 1)'; // 浅黄色背景
      case 'info':
        return 'rgba(191, 226, 255, 1)'; // 浅蓝色背景
      default:
        return 'rgba(191, 226, 255, 1)'; // 默认浅蓝色背景
    }
  })();

  // const [opened, { close }] = useDisclosure(false);

  const handleConfirmEvent = () => {
    // close();
    if (onConfirm) onConfirm();
  };

  const handleCancelEvent = () => {
    // close();
    if (onCancel) onCancel();
  };

  return (
    <Modal
      opened={opened}
      withCloseButton={false}
      onClose={close}
      centered
      size="100%"
      styles={{
        content: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'transparent',
          boxShadow: 'none',
        },
        body: {
          width: '510px',
          padding: 0,
        },
      }}
    >
      <Alert
        variant="white"
        title={title}
        icon={alertIcon}
        styles={{
          root: {
            width: '510px',
            height: '190px',
            padding: '24px',
            borderRadius: '10px',
            background: alertBgColor,
            boxShadow: '0px 4px 24px  rgba(0, 0, 0, 0.1)',
          },
          icon: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '44px',
            height: '44px',
            backgroundColor: alertIconBgColor,
            borderRadius: '100%',
          },
          title: {
            fontSize: 20,
            fontWeight: 700,
            lineHeight: '44px',
            color: alertTitleColor,
          },
        }}
      >
        <Text mt={8} fz={16} fw={400} lh="24px" c={alertTextColor}>
          {message}
        </Text>
        <Group align="center" justify="flex-end" mt={28} gap={24}>
          <Button variant="default" onClick={handleCancelEvent}>
            {cancelText}
          </Button>
          <Button color="red" onClick={handleConfirmEvent}>
            {confirmText}
          </Button>
        </Group>
      </Alert>
    </Modal>
  );
};

export default Desktop;
