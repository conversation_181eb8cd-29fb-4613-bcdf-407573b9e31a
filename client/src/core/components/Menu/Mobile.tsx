/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { useTheme } from '~/core/features/mantine';
import { Stack, UnstyledButton, Text } from '@mantine/core';
import { Link, useLocation } from 'react-router-dom';
import { FiChevronRight } from 'react-icons/fi';

import { MenuProps } from './types';

const Mobile: React.FC<MenuProps> = ({ items, showArrow = false }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const location = useLocation(); // 新增：获取当前路由

  const textColor = isDark ? '#E6E6E6' : 'rgba(56, 56, 56, 1)';
  const hoverBackgroundColor = 'linear-gradient(0deg, rgba(247, 248, 255, 1), rgba(247, 248, 255, 1)), rgba(243, 245, 255, 1)';
  const activeBackgroundColor = 'rgba(73, 81, 235, 1)'; // Indigo color for active item
  const activeTextColor = '#FFFFFF';

  return (
    <Stack component="ul" gap={16}>
      {items.map((item) => {
        if (!item.path) return null; // 跳过无效的菜单项

        return (
          <li key={item.path} position="relative" w="100%" h="45px" style={{ color: textColor }}>
            <UnstyledButton
              className={`flex items-center h-full rounded-[10px] ${location.pathname === item.path ? 'active' : ''}`}
              component={Link}
              to={item.path}
              px={16}
              css={css`
                &:hover {
                  background: ${hoverBackgroundColor};
                }
                &.active {
                  background: ${activeBackgroundColor};
                  color: ${activeTextColor};
                }
              `}
            >
              <item.icon size={20} />
              <Text className="flex-auto leading-[18px] fw-700" ml={16} fz={16}>
                {item.label}
              </Text>

              {showArrow && <FiChevronRight size={20} />}
            </UnstyledButton>
          </li>
        );
      })}
    </Stack>
  );
};

export default Mobile;
