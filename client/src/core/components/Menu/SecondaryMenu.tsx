/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useState } from 'react';
import { useTheme } from '~/core/features/mantine';
import { Stack, Box, UnstyledButton, Text, Collapse } from '@mantine/core';
import { useLocation, useNavigate } from 'react-router-dom';
import { FiChevronRight, FiChevronDown } from 'react-icons/fi';

import { MenuProps, MenuItem } from './types';

const SecondaryMenu: React.FC<MenuProps> = ({ items, onClick }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const location = useLocation();
  const navigate = useNavigate();
  const [expandedItems, setExpandedItems] = useState<Set<string>>(new Set(['资产'])); // 默认展开资产

  const textColor = isDark ? '#E6E6E6' : 'rgba(56, 56, 56, 1)';
  const hoverBackgroundColor = isDark ? 'rgba(255, 255, 255, 0.05)' : 'rgba(73, 81, 235, 0.06)';
  const activeBackgroundColor = isDark
    ? 'linear-gradient(135deg, rgba(73, 81, 235, 0.9), rgba(99, 102, 241, 0.9))'
    : 'linear-gradient(135deg, rgba(73, 81, 235, 1), rgba(99, 102, 241, 1))';
  const activeTextColor = '#FFFFFF';
  const subItemTextColor = isDark ? '#C0C0C0' : 'rgba(96, 96, 96, 1)';
  const parentActiveBackgroundColor = isDark ? 'rgba(73, 81, 235, 0.15)' : 'rgba(73, 81, 235, 0.08)';

  const toggleExpanded = (label: string) => {
    const newExpanded = new Set(expandedItems);
    if (newExpanded.has(label)) {
      newExpanded.delete(label);
    } else {
      newExpanded.add(label);
    }
    setExpandedItems(newExpanded);
  };

  const isActive = (item: MenuItem): boolean => {
    if (!item.path) return false;

    return location.pathname === item.path || location.pathname.startsWith(item.path + '/');
  };

  const isParentActive = (item: MenuItem): boolean => {
    if (!item.children) return false;
    return item.children.some((child) => isActive(child));
  };

  const handleItemClick = (item: MenuItem, e?: React.MouseEvent) => {
    if (e) e.preventDefault();

    if (item.children) {
      // 如果有子菜单，切换展开状态
      toggleExpanded(item.label);
    } else {
      // 如果没有子菜单，执行导航
      if (onClick) onClick(item);
      if (item.onClick) item.onClick();
      if (item.path) navigate(item.path);
    }
  };

  const renderMenuItem = (item: MenuItem, isSubItem = false) => {
    const hasChildren = item.children && item.children.length > 0;
    const isExpanded = expandedItems.has(item.label);
    const itemIsActive = isActive(item);
    const parentIsActive = isParentActive(item);

    return (
      <Box key={item.label}>
        <Box position="relative" w="100%" h={isSubItem ? '40px' : '48px'} style={{ color: textColor }}>
          {/* 子菜单项的连接线 */}
          {isSubItem && (
            <Box
              position="absolute"
              left={12}
              top={0}
              bottom={0}
              w={2}
              bg={isDark ? 'rgba(73, 81, 235, 0.4)' : 'rgba(73, 81, 235, 0.2)'}
              style={{ borderRadius: '1px' }}
            />
          )}

          <UnstyledButton
            className={`flex items-center h-full rounded-[12px] ${
              itemIsActive ? 'active' : ''
            } ${hasChildren && !isSubItem && parentIsActive ? 'parent-active' : ''}`}
            onClick={(e) => handleItemClick(item, e)}
            px={isSubItem ? 12 : 16}
            ml={isSubItem ? 28 : 0} // 子菜单项向右缩进
            css={css`
              transition: all 0.2s ease;
              border: 1px solid transparent;

              &:hover {
                background: ${hoverBackgroundColor};
                transform: translateY(-1px);
                box-shadow: ${isDark ? '0 4px 12px rgba(0, 0, 0, 0.3)' : '0 4px 12px rgba(73, 81, 235, 0.15)'};
              }

              &.active {
                background: ${activeBackgroundColor};
                color: ${activeTextColor};
                box-shadow: ${isDark ? '0 6px 20px rgba(73, 81, 235, 0.4)' : '0 6px 20px rgba(73, 81, 235, 0.3)'};
                border: 1px solid rgba(255, 255, 255, 0.1);

                svg {
                  color: ${activeTextColor};
                }
              }

              &.parent-active:not(.active) {
                background: ${parentActiveBackgroundColor};
                color: ${textColor} !important;
                border: 1px solid rgba(73, 81, 235, 0.2);

                svg {
                  color: ${textColor} !important;
                }
              }
            `}
          >
            <Box
              style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                width: isSubItem ? 18 : 20,
                height: isSubItem ? 18 : 20,
                transition: 'transform 0.2s ease',
              }}
              css={css`
                svg {
                  transition: all 0.2s ease;
                }
                &:hover svg {
                  transform: scale(1.1);
                }
              `}
            >
              <item.icon size={isSubItem ? 16 : 18} />
            </Box>

            <Text
              className="flex-auto"
              ml={isSubItem ? 12 : 16}
              fz={isSubItem ? 13 : 15}
              fw={isSubItem ? 500 : 600}
              lh={isSubItem ? '18px' : '20px'}
              style={{
                letterSpacing: isSubItem ? '0.2px' : '0.3px',
                transition: 'all 0.2s ease',
              }}
              c={
                itemIsActive
                  ? activeTextColor
                  : isSubItem
                    ? subItemTextColor
                    : parentIsActive && hasChildren && !itemIsActive
                      ? textColor
                      : textColor
              }
            >
              {item.label}
            </Text>

            {hasChildren && (
              <Box
                style={{
                  transition: 'transform 0.2s ease',
                  transform: isExpanded ? 'rotate(0deg)' : 'rotate(0deg)',
                }}
              >
                {isExpanded ? (
                  <FiChevronDown
                    size={14}
                    color={itemIsActive ? activeTextColor : textColor}
                    style={{ transition: 'all 0.2s ease' }}
                  />
                ) : (
                  <FiChevronRight
                    size={14}
                    color={itemIsActive ? activeTextColor : textColor}
                    style={{ transition: 'all 0.2s ease' }}
                  />
                )}
              </Box>
            )}
          </UnstyledButton>
        </Box>

        {hasChildren && (
          <Collapse in={isExpanded} transitionDuration={200} transitionTimingFunction="ease-out">
            <Stack gap={4} mt={6} mb={2}>
              {item.children!.map((child) => renderMenuItem(child, true))}
            </Stack>
          </Collapse>
        )}
      </Box>
    );
  };

  return (
    <Stack gap={6} p={4}>
      {items.map((item) => renderMenuItem(item))}
    </Stack>
  );
};

export default SecondaryMenu;
