/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { useTheme } from '~/core/features/mantine';
import { Stack, Box, UnstyledButton, Text, useMantineTheme } from '@mantine/core';
import { useLocation, useNavigate } from 'react-router-dom';
import { FiChevronRight } from 'react-icons/fi';

import { MenuProps } from './types';

const Desktop: React.FC<MenuProps> = ({ items, showArrow = false, onClick }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const location = useLocation(); // 新增：获取当前路由
  const navigate = useNavigate();

  const textColor = isDark ? '#E6E6E6' : 'rgba(56, 56, 56, 1)';
  const hoverBackgroundColor = isDark
    ? theme.colors.gray[8]
    : 'linear-gradient(0deg, rgba(247, 248, 255, 1), rgba(247, 248, 255, 1)), rgba(243, 245, 255, 1)';
  const activeBackgroundColor = 'rgba(73, 81, 235, 1)'; // Indigo color for active item
  const activeTextColor = '#FFFFFF';

  return (
    <Stack gap={16}>
      {items.map((item) => {
        const isActive =
          item.path &&
          (item.path === '/chat'
            ? location.pathname === '/chat' // Only active if it's exactly /chat
            : location.pathname === item.path || location.pathname.startsWith(item.path + '/'));
        return (
          <Box key={item.label} position="relative" w="100%" h="45px" style={{ color: textColor }}>
            <UnstyledButton
              className={`flex items-center w-full h-full rounded-[10px] ${isActive ? 'active' : ''}`}
              onClick={(e) => {
                e.preventDefault();
                if (onClick) onClick(item);
                if (item.onClick) item.onClick();
                if (item.path) navigate(item.path);
              }}
              px={16}
              css={css`
                &:hover {
                  background: ${hoverBackgroundColor};
                }
                &.active {
                  background: ${activeBackgroundColor};
                  color: ${activeTextColor};
                }
              `}
            >
              <item.icon size={20} />
              <Text className="flex-auto leading-[18px] fw-700" ml={16} fz={16}>
                {item.label}
              </Text>

              {showArrow && <FiChevronRight size={20} />}
            </UnstyledButton>
          </Box>
        );
      })}
    </Stack>
  );
};

export default Desktop;
