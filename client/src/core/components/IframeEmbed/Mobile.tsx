import React, { useMemo } from 'react';
import { Box } from '@mantine/core';

interface MobileProps {
  /**
   * 要嵌入的URL
   */
  url: string;
  /**
   * 可选的URL参数
   */
  params?: Record<string, string>;
}

/**
 * 移动版iframe嵌入组件
 */
const Mobile: React.FC<MobileProps> = ({ url, params }) => {
  // 构建完整的URL，包括查询参数
  const fullUrl = useMemo(() => {
    if (!params || Object.keys(params).length === 0) {
      return url;
    }

    const urlObj = new URL(url);
    Object.entries(params).forEach(([key, value]) => {
      if (value) {
        urlObj.searchParams.append(key, value);
      }
    });

    return urlObj.toString();
  }, [url, params]);

  return (
    <Box
      style={{
        width: '100%',
        height: '100vh',
        overflow: 'hidden',
        position: 'relative',
      }}
    >
      <iframe
        src={fullUrl}
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          position: 'absolute',
          top: 0,
          left: 0,
        }}
        title="外部内容"
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        key={fullUrl} // 添加key属性，确保URL变化时iframe重新渲染
        referrerPolicy="no-referrer-when-downgrade" // 添加referrerPolicy
      />
    </Box>
  );
};

export default Mobile;
