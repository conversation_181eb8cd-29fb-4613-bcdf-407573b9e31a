import React, { useEffect, useState } from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

interface IframeEmbedProps {
  /**
   * 要嵌入的URL
   */
  url: string;
  /**
   * 可选的URL参数
   */
  params?: Record<string, string>;
}

/**
 * 响应式iframe嵌入组件，根据设备类型加载桌面版或移动版
 */
const IframeEmbed: React.FC<IframeEmbedProps> = (props) => {
  const { isMobile } = useDeviceDetect();
  const [isReady, setIsReady] = useState(false);

  // 确保组件在挂载后再渲染，避免刷新时的布局问题
  useEffect(() => {
    // 使用requestAnimationFrame确保DOM已完全加载
    const frameId = requestAnimationFrame(() => {
      setIsReady(true);
    });

    return () => cancelAnimationFrame(frameId);
  }, []);

  // 如果组件还没准备好，返回一个占位元素，保持相同的尺寸但不显示内容
  if (!isReady) {
    return <div style={{ width: '100%', height: '100vh', position: 'relative' }} />;
  }

  // 根据设备类型动态选择适当的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default IframeEmbed;
