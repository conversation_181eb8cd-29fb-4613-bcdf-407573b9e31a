import React, { useState, useRef, useEffect } from 'react';
import { Group, Input, Paper, Tabs, Box, Loader, Alert, Text, Center } from '@mantine/core';
import { useDebouncedValue } from '@mantine/hooks';
import { useTheme } from '~/core/features/mantine/theme-context';
import { RiSearchLine } from 'react-icons/ri';
import { IoWarningOutline } from 'react-icons/io5';
import { SearchItem } from '~/core/components';

import { SearchBoxProps } from './types';

/**
 * Desktop version of the SearchBox component
 */
const Desktop: React.FC<SearchBoxProps> = (props: SearchBoxProps) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const {
    placeholder = '搜索 GPT ...',
    onSearch,
    setSearchKeyword,
    searchKeyword,
    isSearchLoading,
    searchError,
    searchResult,
    onItemClick,
  } = props;
  const [isFocused, setIsFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  // 使用Mantine的useDebouncedValue hook为搜索添加防抖功能
  // 第二个参数300表示防抖延迟时间为300毫秒
  const [debouncedSearchKeyword] = useDebouncedValue(searchKeyword || '', 300);

  // 当防抖后的关键词变化时触发搜索
  useEffect(() => {
    if (debouncedSearchKeyword !== undefined) {
      onSearch?.();
    }
  }, [debouncedSearchKeyword, onSearch]);

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchKeyword?.(value);
    // 不再直接调用onSearch，而是等待防抖后的值变化触发useEffect中的搜索
  };

  // Determine background and shadow colors based on theme
  const bgColor = isDark ? '#1A1B1E' : 'white';
  const shadowColor = isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(232, 239, 252, 0.5)';
  const iconColor = isDark ? '#5C5F66' : 'rgba(199, 199, 199, 1)';

  return (
    <Box className="relative">
      <Group
        style={{
          position: 'relative',
          height: '64px',
          borderRadius: '12px',
          borderBottomRightRadius: isFocused ? 0 : '12px',
          borderBottomLeftRadius: isFocused ? 0 : '12px',
          backgroundColor: bgColor,
          boxShadow: `0px 6px 6px 0px ${shadowColor}`,
          overflow: 'hidden',
        }}
        px={24}
        onClick={() => inputRef.current?.focus()}
      >
        <RiSearchLine size={20} color={iconColor} />
        <Input
          ref={inputRef}
          className="flex-auto"
          variant="unstyled"
          value={searchKeyword}
          placeholder={placeholder}
          onChange={handleSearchChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setTimeout(() => setIsFocused(false), 200)}
          rightSection={
            isSearchLoading ? (
              <Loader size="xs" color="rgba(73, 81, 235, 1)" />
            ) : searchKeyword !== '' ? (
              <Input.ClearButton size="32px" onClick={() => setSearchKeyword?.('')} />
            ) : undefined
          }
          rightSectionPointerEvents="auto"
          styles={{
            input: {
              fontSize: '16px',
            },
          }}
          style={{
            border: 'none',
            outline: 'none',
            background: 'transparent',
            fontSize: '16px',
            lineHeight: '1.2',
            color: actualColorScheme === 'dark' ? '#C1C2C5' : '#000000',
          }}
        />
      </Group>

      {isFocused && (
        <Paper
          className="absolute top-[70%] left-0 right-0 z-1000"
          px={16}
          pb={8}
          style={{
            borderRadius: 0,
            borderBottomRightRadius: '12px',
            borderBottomLeftRadius: '12px',
            backgroundColor: bgColor,
            boxShadow: `0px 6px 6px 0px ${shadowColor}`,
          }}
        >
          <Tabs
            color="rgba(73, 81, 235, 1)"
            defaultValue="history"
            styles={{
              tab: {
                backgroundColor: 'transparent',
              },
            }}
          >
            <Tabs.List>
              <Tabs.Tab value="history">最近使用</Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="history" pt={8}>
              {isSearchLoading ? (
                <Center py={20}>
                  <Loader size="sm" color="rgba(73, 81, 235, 1)" />
                </Center>
              ) : searchError ? (
                <Alert icon={<IoWarningOutline size={16} />} title="搜索出错" color="red" variant="light" my={10}>
                  {searchError.message || '搜索时发生错误，请稍后重试'}
                </Alert>
              ) : searchResult && searchResult.length > 0 ? (
                searchResult.map((asset, index) => (
                  <SearchItem key={asset.id} asset={asset} border={index !== searchResult.length - 1} onClick={onItemClick} />
                ))
              ) : searchKeyword && searchKeyword.trim() !== '' ? (
                <Center py={20}>
                  <Text c="dimmed">未找到相关结果</Text>
                </Center>
              ) : null}
            </Tabs.Panel>
          </Tabs>
        </Paper>
      )}
    </Box>
  );
};

export default Desktop;
