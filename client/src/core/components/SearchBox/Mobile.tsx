import React from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';
import { IoSearch } from 'react-icons/io5';
import './styles.css';
import { SearchItem } from '~/core/components';

import { SearchBoxProps } from './types';

/**
 * Mobile version of the SearchBox component
 */
const Mobile: React.FC<SearchBoxProps> = (props: SearchBoxProps) => {
  const { actualColorScheme } = useTheme();
  const { placeholder = '搜索 GPT ...', onSearch, setSearchKeyword, searchKeyword, searchResult, onItemClick } = props;

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchKeyword?.(value);
    onSearch?.();
  };

  // Determine background and shadow colors based on theme
  const bgColor = actualColorScheme === 'dark' ? '#1A1B1E' : 'white';
  const shadowColor = actualColorScheme === 'dark' ? 'rgba(0, 0, 0, 0.5)' : 'rgba(232, 239, 252, 0.5)';
  const iconColor = actualColorScheme === 'dark' ? '#5C5F66' : '#A8A29E';

  return (
    <div className="relative">
      <div
        style={{
          width: '100%',
          height: '48px',
          position: 'relative',
          overflow: 'hidden',
          borderRadius: '8px',
          backgroundColor: bgColor,
          boxShadow: `0px 4px 4px 0px ${shadowColor}`,
        }}
      >
        <div style={{ position: 'absolute', left: '16px', top: '14px' }}>
          <IoSearch size={16} color={iconColor} />
        </div>

        <input
          type="text"
          placeholder={placeholder}
          value={searchKeyword}
          onChange={handleSearchChange}
          style={{
            width: '100%',
            height: '100%',
            border: 'none',
            outline: 'none',
            background: 'transparent',
            paddingLeft: '40px',
            paddingRight: '12px',
            fontSize: '14px',
            fontFamily: 'Source Han Sans SC',
            lineHeight: '1.2',
            color: actualColorScheme === 'dark' ? '#C1C2C5' : '#000000',
          }}
          className="agent-search-input"
        />
      </div>

      {/* 搜索结果列表 */}
      {searchKeyword && searchResult && searchResult.length > 0 && (
        <div className="absolute w-full mt-1 rounded-md shadow-lg z-50" style={{ backgroundColor: bgColor }}>
          {searchResult.map((asset, index) => (
            <SearchItem key={asset.id} asset={asset} border={index !== searchResult.length - 1} onClick={onItemClick} />
          ))}
        </div>
      )}
    </div>
  );
};

export default Mobile;
