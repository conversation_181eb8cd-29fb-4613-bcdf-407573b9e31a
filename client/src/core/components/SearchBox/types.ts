import { QueryObserverResult, RefetchOptions } from '@tanstack/react-query';
import { AssetBase } from '~/core/schemas';

export interface SearchBoxProps {
  /**
   * 搜索输入框的占位文本
   */
  placeholder?: string;
  /**
   * 搜索回调函数，接收 AssetQueryBase 参数并返回 Promise<AssetBase[]>。
   */
  onSearch?: (options?: RefetchOptions) => Promise<QueryObserverResult<AssetBase[], Error>>;

  /**
   * 当前是否正在搜索中
   */
  isSearchLoading?: boolean;

  /**
   * 搜索错误
   */
  searchError?: Error | null;

  /**
   * 搜索结果
   */
  searchResult?: AssetBase[];
  /**
   * 搜索关键词
   */
  searchKeyword?: string;

  /**
   * 设置搜索关键词
   */
  setSearchKeyword?: (keyword: string) => void;

  /**
   * 搜索结果项点击事件处理函数
   */
  onItemClick?: (asset: AssetBase) => void;
}
