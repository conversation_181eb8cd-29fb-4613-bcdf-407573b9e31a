import React from 'react';
import { Stack, Text, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine/theme-context';

import { CategoryTitleProps } from './types';

/**
 * Desktop version of the CategoryTitle component
 */
const Desktop: React.FC<CategoryTitleProps> = ({ title, subtitle }) => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();
  const isDark = actualColorScheme === 'dark';

  // Determine text colors based on theme
  const titleColor = isDark ? theme.colors.indigo[3] : 'rgba(73, 81, 235, 1)';
  const subtitleColor = isDark ? theme.colors.gray[4] : 'rgba(84, 84, 84, 1)';

  return (
    <Stack gap={0}>
      <Text className="leading-[48px] fw-700" fz={26} c={titleColor}>
        {title}
      </Text>
      <Text className="leading-[36px] fw-400" fz={16} c={subtitleColor}>
        {subtitle}
      </Text>
    </Stack>
  );
};

export default Desktop;
