import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

import { CategoryTitleProps } from './types';

/**
 * 响应式CategoryTitle组件，根据设备类型加载桌面版或移动版
 */
const CategoryTitle: React.FC<CategoryTitleProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // 根据设备类型动态选择适当的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default CategoryTitle;
CategoryTitle.displayName = 'CategoryTitle';
