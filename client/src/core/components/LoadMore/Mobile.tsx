import React from 'react';
import { useTheme } from '~/core/features/mantine';

import { LoadMoreProps } from './types';

/**
 * LoadMore 按钮组件的移动端版本
 */
const Mobile: React.FC<LoadMoreProps> = ({ text = '查看更多', onClick, disabled = false, loading = false }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  return (
    <button
      className={`relative w-full mt-2 py-2 px-3 rounded-md transition-colors duration-200 ${
        isDark ? 'bg-zinc-800 hover:bg-zinc-700 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
      onClick={onClick}
      disabled={disabled || loading}
    >
      <div className="flex items-center justify-center">
        {loading ? (
          <div
            className="w-4 h-4 border-2 border-t-transparent rounded-full animate-spin mr-1.5"
            style={{
              borderColor: isDark ? 'rgba(255,255,255,0.2)' : 'rgba(0,0,0,0.2)',
              borderTopColor: 'transparent',
            }}
          />
        ) : null}
        <span className="text-sm font-medium font-['Source_Han_Sans_SC']">{text}</span>
      </div>
    </button>
  );
};

export default Mobile;
