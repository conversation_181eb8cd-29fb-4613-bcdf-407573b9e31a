/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { Button, Loader, Group } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';

import { LoadMoreProps } from './types';

/**
 * LoadMore 按钮组件的桌面版本
 */
const Desktop: React.FC<LoadMoreProps> = ({ text = '查看更多', onClick, disabled = false, loading = false }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const hoverBackgroundColor = isDark ? 'rgba(255, 255, 255, 0.1)' : 'rgba(252, 252, 252, 1)';

  return (
    <Button
      variant="white"
      color="rgba(92, 92, 92, 1)"
      radius="xl"
      w="80%"
      h={40}
      disabled={disabled || loading}
      onClick={onClick}
      style={{
        backgroundColor: isDark ? 'black' : 'white',
      }}
      css={css`
        &:hover {
          background-color: ${hoverBackgroundColor} !important;
        }
      `}
    >
      <Group align="center" justify="center" gap={8}>
        {loading ? <Loader color="rgba(209, 225, 255, 1)" size="sm" /> : null}
        {text}
      </Group>
    </Button>
  );
};

export default Desktop;
