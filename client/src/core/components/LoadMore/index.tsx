import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

import { LoadMoreProps } from './types';

/**
 * 响应式 LoadMore 按钮组件，根据设备类型自动选择桌面版或移动版
 */
const LoadMore: React.FC<LoadMoreProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default LoadMore;
LoadMore.displayName = 'LoadMore';
