import React from 'react';
import './styles.css';

import { ModuleListProps } from './types';

/**
 * Mobile version of the ModuleList component
 */
const Mobile: React.FC<ModuleListProps> = ({ children, gap = 'md' }) => {
  // Map gap size to pixel values
  const gapSizeMap = {
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
  };

  return (
    <div
      className="agent-list-grid"
      style={{
        display: 'grid',
        gridTemplateColumns: '1fr',
        gap: gapSizeMap[gap],
      }}
    >
      {children}
    </div>
  );
};

export default Mobile;
