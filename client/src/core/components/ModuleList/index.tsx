import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

import { ModuleListProps } from './types';

/**
 * Responsive ModuleList component that loads Desktop or Mobile version
 * based on the device type
 */
const ModuleList: React.FC<ModuleListProps> = ({ children, columns = 3, gap = 'md' }) => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile gap={gap}>{children}</Mobile>;
  }

  return (
    <Desktop columns={columns} gap={gap}>
      {children}
    </Desktop>
  );
};

export default ModuleList;
