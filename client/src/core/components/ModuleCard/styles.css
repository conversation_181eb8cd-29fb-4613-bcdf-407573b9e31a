/* Custom styles for AgentCard component */

/* Shadow effect for agent icons */
.gizmo-shadow-stroke {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.dark .gizmo-shadow-stroke {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* Hover transitions */
.gizmo-link {
  transition: background-color 0.2s ease, transform 0.1s ease, box-shadow 0.2s ease;
  border: 1px solid transparent;
}

.gizmo-link:hover {
  transform: translateY(-2px);
  border-color: rgba(0, 0, 0, 0.1);
}

.dark .gizmo-link:hover {
  border-color: rgba(255, 255, 255, 0.1);
}

/* Line clamp utilities */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
