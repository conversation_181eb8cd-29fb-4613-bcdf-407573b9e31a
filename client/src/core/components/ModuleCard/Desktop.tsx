import React from 'react';
import { UnstyledButton, Stack, Image, Text } from '@mantine/core';
import { Link } from 'react-router-dom';
import { useTheme } from '~/core/features/mantine/theme-context';

import { ModuleCardProps } from './types';

/**
 * Desktop version of the ModuleCard component
 */
const Desktop: React.FC<ModuleCardProps> = ({ name, description, iconUrl, emphasized = false, linkUrl = '#', onClick }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const textColor = isDark ? 'white' : 'rgba(56, 56, 56, 1)';
  const descColor = isDark ? 'white' : 'rgba(92, 92, 92, 1)';

  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick();
    }
  };

  // Default style
  if (!emphasized) {
    return (
      <>
        <UnstyledButton
          className="flex items-center w-full rounded-[10px] hover:bg-white/70% transition-all"
          component={Link}
          to={linkUrl}
          px={16}
          py={20}
          onClick={handleClick}
        >
          {/* {index !== undefined && (
            <Text className="flex-none text-center leading-[26px] fw-500" w={16} fz={18} c={textColor}>
              {index}
            </Text>
          )} */}
          <Image className="flex-none" ml={24} radius={64} w={64} h={64} src={iconUrl} />
          <Stack className="flex-auto" gap={0} ml={24}>
            <Text className="leading-[24px] fw-500 line-clamp-1" fz={16} c={textColor}>
              {name}
            </Text>
            <Text className="w-full leading-[16px] fw-400 line-clamp-2" fz={12} c={descColor}>
              {description}
            </Text>
          </Stack>
        </UnstyledButton>
      </>
    );
  }

  // Emphasized style
  return (
    <UnstyledButton
      className="flex items-center w-full bg-white rounded-[10px] hover:bg-white/70% transition-all"
      component={Link}
      to={linkUrl}
      px={16}
      py={20}
      onClick={handleClick}
    >
      <Image className="flex-none" radius={80} w={80} h={80} src={iconUrl} />
      <Stack className="flex-auto" gap={8} ml={24}>
        <Text className="leading-[24px] fw-500 line-clamp-1" fz={16} c={textColor}>
          {name}
        </Text>
        <Text className="w-full leading-[16px] fw-400 line-clamp-2" fz={12} c={descColor}>
          {description}
        </Text>
      </Stack>
    </UnstyledButton>
  );
};

export default Desktop;
