import React from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';
import { Link } from 'react-router-dom';

import { ModuleCardProps } from './types';

/**
 * Mobile version of the ModuleCard component
 */
const Mobile: React.FC<ModuleCardProps> = ({ index, name, description, iconUrl, emphasized = false, linkUrl = '#', onClick }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick();
    }
  };

  // Default style
  if (!emphasized) {
    return (
      <div tabIndex={0} style={{ opacity: 1, transform: 'none' }}>
        <Link
          className={`
            gizmo-link cursor-pointer group
            flex h-[104px] items-center gap-2.5 overflow-hidden rounded-xl
            px-1 py-4
            ${isDark ? 'hover:bg-gray-800 hover:shadow-md' : 'hover:bg-gray-50 hover:shadow-md'}
          `}
          to={linkUrl}
          onClick={handleClick}
        >
          {index !== undefined && (
            <div className="text-md flex w-8 shrink-0 items-center justify-center font-semibold">{index}</div>
          )}
          <div className="flex w-full grow items-center gap-4 overflow-hidden">
            <div className="h-12 w-12 shrink-0">
              <div className="gizmo-shadow-stroke overflow-hidden rounded-full">
                <img
                  className="bg-token-main-surface-secondary h-full w-full"
                  alt="Agent Icon"
                  width="80"
                  height="80"
                  src={iconUrl}
                />
              </div>
            </div>
            <div className="overflow-hidden break-words text-ellipsis">
              <span className="line-clamp-2 text-sm leading-tight font-semibold">{name}</span>
              <span className="line-clamp-2 text-xs">{description}</span>
            </div>
          </div>
        </Link>
      </div>
    );
  }

  // Emphasized style
  return (
    <div tabIndex={0} style={{ opacity: 1, transform: 'none' }}>
      <Link
        className={`
          gizmo-link cursor-pointer group flex h-24 items-center gap-4
          overflow-hidden rounded-xl px-4 py-6
          ${isDark ? 'bg-gray-700 hover:bg-gray-800 hover:shadow-md' : 'bg-gray-50 hover:bg-gray-100 hover:shadow-md'}
        `}
        to={linkUrl}
        onClick={handleClick}
      >
        <div className="h-14 w-14 shrink-0">
          <div className="gizmo-shadow-stroke overflow-hidden rounded-full">
            <img
              className="bg-token-main-surface-secondary h-full w-full"
              alt="Agent Icon"
              width="80"
              height="80"
              src={iconUrl}
            />
          </div>
        </div>
        <div className="flex flex-col">
          <div className="line-clamp-1 font-semibold">{name}</div>
          <span className="line-clamp-2 text-xs">{description}</span>
        </div>
      </Link>
    </div>
  );
};

export default Mobile;
