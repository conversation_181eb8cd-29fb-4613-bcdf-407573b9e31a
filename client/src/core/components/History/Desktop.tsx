/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useState } from 'react';
import {
  Box,
  Stack,
  Text,
  UnstyledButton,
  useMantineTheme,
  ActionIcon,
  Menu,
  TextInput,
  Modal,
  Button,
  Group,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { useTheme } from '~/core/features/mantine';
import { useNavigate, useLocation } from 'react-router-dom';
import { RiMoreFill, RiEditLine, RiDeleteBinLine } from 'react-icons/ri';
import { Dialog } from '~/core/components';
import { useChatContext } from '~/agent/chat/contexts/ChatContext';

import { HistoryProps } from './types';

const Desktop: React.FC<HistoryProps> = ({ date, list }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const chatContext = useChatContext();
  const navigate = useNavigate();
  const location = useLocation(); // 新增：获取当前路由

  const textColor = isDark ? '#E6E6E6' : 'rgba(69, 69, 69, 1)';
  const hoverBackgroundColor = isDark
    ? theme.colors.gray[8]
    : 'linear-gradient(0deg, rgba(247, 248, 255, 1), rgba(247, 248, 255, 1)), rgba(243, 245, 255, 1)';
  const activeBackgroundColor = 'rgba(73, 81, 235, 1)'; // Indigo color for active item
  const activeTextColor = '#FFFFFF';
  const iconColor = isDark ? 'white' : 'black';
  const boxShadow = isDark ? '0px 3px 15px rgba(23, 16, 3, 0.5)' : '0px 3px 15px rgba(232, 239, 252, 0.5)';

  // 删除对话相关状态
  const [deletingItem, setDeletingItem] = useState<string | null>(null);
  const [opened, { toggle, close }] = useDisclosure(false);

  // 重命名对话相关状态
  const [renamingItem, setRenamingItem] = useState<string | null>(null);
  const [newName, setNewName] = useState<string>('');
  const [isRenaming, setIsRenaming] = useState<boolean>(false);

  const handleDeleteClick = (id: string) => {
    toggle();
    setDeletingItem(id);
  };

  const handleConfirmDelete = async () => {
    if (deletingItem) {
      try {
        const success = await chatContext.deleteConversation(deletingItem);
        if (success) {
          console.log('会话删除成功:', deletingItem);
        } else {
          console.error('会话删除失败:', deletingItem);
        }
      } catch (error) {
        console.error('删除会话时发生错误:', error);
      }
    }
    close();
  };

  const handleCancelDelete = () => {
    close();
  };

  // 处理重命名点击
  const handleRenameClick = (id: string, title: string) => {
    setRenamingItem(id);
    setNewName(title);
    setIsRenaming(true);
  };

  // 处理重命名确认
  const handleRenameConfirm = async () => {
    if (renamingItem && newName.trim()) {
      try {
        setIsRenaming(false);
        const success = await chatContext.renameConversation(renamingItem, newName.trim());
        if (success) {
          console.log('会话重命名成功:', renamingItem);
        } else {
          console.error('会话重命名失败:', renamingItem);
        }
      } catch (error) {
        console.error('重命名会话时发生错误:', error);
      }
      setRenamingItem(null);
    } else {
      setIsRenaming(false);
      setRenamingItem(null);
    }
  };

  // 处理重命名取消
  const handleRenameCancel = () => {
    setIsRenaming(false);
    setRenamingItem(null);
  };

  return (
    <Stack gap={0}>
      <Text my={12} fz={16} fw={500} lh="18px" c={textColor}>
        {date}
      </Text>
      {list.map((item) => {
        const isActive = location.pathname === `/chat/${item.id}`; // 新增：判断是否为活动项
        return (
          <Box
            className={`flex items-center justify-between rounded-[10px] cursor-pointer ${isActive ? 'active' : ''}`}
            key={item.id}
            position="relative"
            w="100%"
            h="45px"
            style={{ color: isActive ? activeTextColor : textColor }} // 修改：活动项文字颜色
            onClick={() => {
              // 设置当前会话
              if (chatContext.conversation?.id !== item.id) {
                chatContext.setMessages([]);
                chatContext.setConversation(item);
                chatContext.setAgent(item.agent);
              }
              // 路由跳转
              navigate(`/chat/${item.id}`);
            }}
            px={16}
            css={css`
              &:hover {
                background: ${hoverBackgroundColor};
              }
              &.active {
                background: ${activeBackgroundColor};
                color: ${activeTextColor};
              }
            `}
          >
            <UnstyledButton className="flex items-center h-full">
              <Text className="flex-auto leading-[18px] fw-700" fz={14} fw={500} lh="18px">
                {item.name}
              </Text>
            </UnstyledButton>

            <Menu
              position="right-end"
              styles={{
                dropdown: {
                  padding: '8px',
                  border: 0,
                  borderRadius: '12px',
                  boxShadow: boxShadow,
                },
                item: {
                  borderRadius: '8px',
                },
              }}
            >
              <Menu.Target>
                <ActionIcon variant="subtle" aria-label="Copy" color="gray" size="sm">
                  <RiMoreFill size={16} color={iconColor} />
                </ActionIcon>
              </Menu.Target>

              <Menu.Dropdown>
                <Menu.Item
                  leftSection={<RiEditLine size={16} />}
                  onClick={(e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    handleRenameClick(item.id, item.name ?? '');
                  }}
                >
                  重命名
                </Menu.Item>
                <Menu.Item
                  color="red"
                  leftSection={<RiDeleteBinLine size={16} />}
                  onClick={(e) => {
                    e.stopPropagation(); // 阻止事件冒泡
                    handleDeleteClick(item.id);
                  }}
                >
                  删除
                </Menu.Item>
              </Menu.Dropdown>
            </Menu>
          </Box>
        );
      })}

      <Dialog
        opened={opened}
        type="warning"
        title="删除对话"
        message="删除后不能恢复，确定要删除吗？"
        confirmText="确认删除"
        onCancel={handleCancelDelete}
        onConfirm={handleConfirmDelete}
      />

      <Modal opened={isRenaming} onClose={handleRenameCancel} title="重命名对话" centered size="sm">
        <Stack>
          <TextInput
            value={newName}
            onChange={(e) => setNewName(e.currentTarget.value)}
            placeholder="请输入新的对话名称"
            autoFocus
          />
          <Group justify="flex-end" mt="md">
            <Button variant="default" onClick={handleRenameCancel}>
              取消
            </Button>
            <Button onClick={handleRenameConfirm}>确认</Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
};

export default Desktop;
