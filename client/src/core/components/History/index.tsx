import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

import { HistoryProps } from './types';

/**
 * 响应式 History 按钮组件，根据设备类型自动选择桌面版或移动版
 */
const History: React.FC<HistoryProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default History;
