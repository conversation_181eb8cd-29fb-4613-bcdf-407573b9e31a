import { useState, useEffect, useRef, RefObject } from 'react';

/**
 * 分类项接口
 */
export interface CategoryItem {
  title: string;
  subtitle: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

/**
 * 滚动监听钩子的参数接口
 */
export interface UseScrollSpyOptions<T extends CategoryItem = CategoryItem> {
  /**
   * 容器引用，用于查找分类区块
   */
  containerRef: RefObject<HTMLElement | null>;

  /**
   * 分类项列表
   */
  items: T[];

  /**
   * 分类区块的数据属性名称，用于查找区块
   * 默认为 'data-section'
   */
  sectionAttribute?: string;

  /**
   * 分类区块的ID前缀
   * 默认为 'section-'
   */
  sectionIdPrefix?: string;

  /**
   * 粘性头部的CSS选择器
   * 默认为 '.sticky'
   */
  stickyHeaderSelector?: string;

  /**
   * 触发点的额外偏移量（像素）
   * 默认为 42
   */
  triggerOffset?: number;

  /**
   * 手动滚动后禁用自动检测的时间（毫秒）
   * 默认为 1000
   */
  manualScrollTimeout?: number;
}

/**
 * 滚动监听钩子的返回值接口
 */
export interface UseScrollSpyReturn<T extends CategoryItem = CategoryItem> {
  /**
   * 当前活动的分类标题
   */
  activeSection: string;

  /**
   * 用户选择的分类
   */
  selectedCategory: T;

  /**
   * 处理分类变更的函数
   */
  handleCategoryChange: (category: T) => void;
}

/**
 * 滚动监听钩子
 * 用于在滚动页面时自动更新当前活动的分类
 *
 * @param options 配置选项
 * @returns 包含活动分类和处理函数的对象
 */
export const useScrollSpy = <T extends CategoryItem = CategoryItem>(options: UseScrollSpyOptions<T>): UseScrollSpyReturn<T> => {
  const {
    containerRef,
    items,
    sectionAttribute = 'data-section',
    sectionIdPrefix = 'section-',
    stickyHeaderSelector = '.sticky',
    triggerOffset = 42,
    manualScrollTimeout = 1000,
  } = options;

  // 状态
  const [selectedCategory, setSelectedCategory] = useState<T>(items[0]);
  const [activeSection, setActiveSection] = useState(items[0].title);

  // Refs
  const isManualScrollRef = useRef(false);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  /**
   * 监听滚动事件，更新当前可见的分类
   */
  useEffect(() => {
    const handleScroll = () => {
      // 如果是手动点击导致的滚动，则跳过自动检测
      if (!containerRef.current || isManualScrollRef.current) return;

      const sections = containerRef.current.querySelectorAll(`[${sectionAttribute}]`);
      const stickyHeader = document.querySelector(stickyHeaderSelector);
      const stickyHeight = stickyHeader?.getBoundingClientRect().height || 0;
      const offset = stickyHeight + triggerOffset;

      let currentSection = activeSection;

      // 遍历所有分类区块，检查哪个在视口中
      sections.forEach((section) => {
        const rect = section.getBoundingClientRect();
        // 当区块顶部进入触发点时，将其设为当前活动分类
        if (rect.top <= offset && rect.bottom >= offset) {
          currentSection = section.getAttribute(sectionAttribute) || activeSection;
        }
      });

      setActiveSection(currentSection);
    };

    // 添加滚动事件监听
    window.addEventListener('scroll', handleScroll);

    // 组件卸载时移除事件监听
    return () => window.removeEventListener('scroll', handleScroll);
  }, [activeSection, containerRef, sectionAttribute, stickyHeaderSelector, triggerOffset]);

  /**
   * 处理分类标签点击事件
   * 点击分类标签时，滚动到对应的分类区块
   * @param category 选中的分类对象
   */
  const handleCategoryChange = (category: T) => {
    // 清除之前的定时器
    if (timerRef.current) {
      clearTimeout(timerRef.current);
    }

    // 设置为手动滚动，暂时禁用自动检测
    isManualScrollRef.current = true;
    setSelectedCategory(category);

    // 查找目标分类区块并滚动到该位置
    const sectionId = `${sectionIdPrefix}${category.title}`;
    const section = document.getElementById(sectionId);
    const stickyHeader = document.querySelector(stickyHeaderSelector);

    if (section && stickyHeader) {
      // 计算滚动位置，考虑粘性头部高度
      const stickyHeight = stickyHeader.getBoundingClientRect().height;
      const sectionTop = section.offsetTop - stickyHeight;

      // 平滑滚动到目标位置
      window.scrollTo({
        top: sectionTop,
        behavior: 'smooth',
      });

      // 更新当前活动分类
      setActiveSection(category.title);
    }

    // 设置定时器，指定时间后重新启用滚动检测
    timerRef.current = setTimeout(() => {
      isManualScrollRef.current = false;
    }, manualScrollTimeout);
  };

  return {
    activeSection,
    selectedCategory,
    handleCategoryChange,
  };
};

export default useScrollSpy;
