import { useState, useCallback, useRef } from 'react';

/**
 * 一个自定义 Hook，为 useState 的 setter 添加日志记录功能。
 * @param initialState 初始状态
 * @param stateName 状态的名称，用于日志输出
 * @returns [state, debugSetState]
 */
function useDebugState<S>(
  initialState: S,
  stateName: string = 'UnnamedState',
): [S, (value: S | ((prevState: S) => S), actionIdentifier?: string) => void] {
  const [state, setState] = useState<S>(initialState);
  const previousStateRef = useRef<S>(initialState);

  const debugSetState = useCallback(
    (value: S | ((prevState: S) => S), actionIdentifier?: string) => {
      const oldState = previousStateRef.current;
      let newState: S;

      if (typeof value === 'function') {
        newState = (value as (prevState: S) => S)(oldState);
      } else {
        newState = value;
      }

      console.groupCollapsed(`[State Update] ${stateName} ${actionIdentifier ? `- ${actionIdentifier}` : ''}`);
      console.log('Previous State:', oldState);
      console.log('Next State:', newState);
      console.trace('Setter called from:'); // 打印堆栈跟踪
      console.groupEnd();

      previousStateRef.current = newState;
      setState(newState);
    },
    [stateName], // 依赖项中包含 stateName，如果它可能改变
  );

  // 更新 ref 以确保在下一次 debugSetState 调用时 previousStateRef.current 是最新的 "oldState"
  // 注意：直接使用 state 变量在 debugSetState 的闭包中可能不是最新的，
  // 因此我们依赖于在渲染后更新 ref，或者在 setState 之后立即更新。
  // 为了简化，我们在这里依赖于 setState 触发的重新渲染来更新 ref 的时机，
  // 或者更准确地说，我们记录的是调用 debugSetState 时的 state。
  // 一个更精确的“前一个状态”记录方式是在 setState 完成后更新 ref，但这会更复杂。
  // 对于日志记录目的，当前实现通常足够。
  // 实际上，在上面的 useCallback 中，oldState 将是上一次渲染时的 state。
  // 如果你希望 oldState 是 setState 被调用那一刻的 state，可以这样做：
  // const oldState = state; // 在 useCallback 内部，这将捕获该次渲染的 state

  return [state, debugSetState];
}

export default useDebugState;

/*
// 如何使用:
// filepath: src/components/MyComponent.tsx
import React from 'react';
import useDebugState from './useDebugState'; // 假设你将上面的代码保存在 useDebugState.ts

function MyComponent() {
  const [count, setCount] = useDebugState(0, 'CounterState');
  const [text, setText] = useDebugState('', 'InputTextState');

  const handleIncrement = () => {
    setCount(prevCount => prevCount + 1, 'Increment');
  };

  const handleDecrement = () => {
    setCount(count - 1, 'Decrement'); // 也可以直接传递值
  };

  const handleReset = () => {
    setCount(0, 'Reset');
  };

  const handleChangeText = (event: React.ChangeEvent<HTMLInputElement>) => {
    setText(event.target.value, 'TextInputChange');
  };

  return (
    <div>
      <h2>Counter</h2>
      <p>Count: {count}</p>
      <button onClick={handleIncrement}>Increment</button>
      <button onClick={handleDecrement}>Decrement</button>
      <button onClick={handleReset}>Reset</button>

      <h2>Text Input</h2>
      <input type="text" value={text} onChange={handleChangeText} />
      <p>Text: {text}</p>
    </div>
  );
}

export default MyComponent;
*/
