import { useState, useEffect } from 'react';

/**
 * 检测设备类型的函数（在钩子外部定义，以便在初始化状态时使用）
 */
const detectMobileDevice = (): boolean => {
  if (typeof window === 'undefined' || typeof navigator === 'undefined') {
    return false; // 服务器端渲染时默认为桌面版
  }

  // 检查用户代理是否包含移动设备关键词
  const mobileCheck = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  // 同时检查屏幕宽度作为备选方案
  const screenCheck = window.innerWidth <= 768;

  return mobileCheck || screenCheck;
};

/**
 * Hook to detect device type (mobile or desktop)
 * @returns Object with isMobile flag
 */
export const useDeviceDetect = () => {
  // 初始化状态时就执行检测，而不是默认为false
  const [isMobile, setIsMobile] = useState(detectMobileDevice());

  useEffect(() => {
    // Function to check if device is mobile
    const checkMobile = () => {
      setIsMobile(detectMobileDevice());
    };

    // 组件挂载时再次检查（以防初始检测有误）
    checkMobile();

    // 添加resize监听器以在窗口大小变化时更新
    window.addEventListener('resize', checkMobile);

    // 清理函数
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  return { isMobile };
};

export default useDeviceDetect;
