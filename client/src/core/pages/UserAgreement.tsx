import React from 'react';
import { Container, Stack, Title, Text, Divider, Group, ActionIcon, useMantineTheme } from '@mantine/core';
import { useNavigate } from 'react-router-dom';
import { RiArrowLeftLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';

/**
 * 用户协议页面
 * 展示平台的用户服务协议条款
 */
const UserAgreement: React.FC = () => {
  const navigate = useNavigate();
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const handleBackClick = () => {
    navigate(-1);
  };

  const textColor = isDark ? theme.colors.gray[3] : theme.colors.gray[7];
  const titleColor = isDark ? theme.colors.gray[1] : theme.colors.gray[9];

  return (
    <Container size="md" py={40}>
      {/* 顶部导航 */}
      <Group mb={30}>
        <ActionIcon onClick={handleBackClick} variant="subtle" size="lg">
          <RiArrowLeftLine size={20} />
        </ActionIcon>
        <Title order={1} c={titleColor}>用户服务协议</Title>
      </Group>

      <Stack gap={24}>
        {/* 更新时间 */}
        <Text c="dimmed" size="sm">
          最后更新时间：2024年12月
        </Text>

        <Divider />

        {/* 协议内容 */}
        <Stack gap={20}>
          <div>
            <Title order={3} mb={12} c={titleColor}>欢迎使用我们的AI应用平台</Title>
            <Text c={textColor} lh={1.6}>
              感谢您选择使用我们的AI应用平台（以下简称"本平台"）。本协议是您与北京华坤锦绣科技有限公司（以下简称"我们"或"公司"）之间关于您使用本平台服务的法律协议。
            </Text>
          </div>

          <div>
            <Title order={3} mb={12} c={titleColor}>1. 服务内容</Title>
            <Text c={textColor} lh={1.6}>
              本平台为用户提供AI智能体对话、工作流自动化、知识库管理、课程学习等服务。我们致力于为用户提供高质量的AI应用体验，帮助用户提高工作效率和学习效果。
            </Text>
          </div>

          <div>
            <Title order={3} mb={12} c={titleColor}>2. 用户注册与账户</Title>
            <Stack gap={8}>
              <Text c={textColor} lh={1.6}>
                2.1 用户需要注册账户才能使用本平台的完整服务。注册时，您需要提供真实、准确、完整的信息。
              </Text>
              <Text c={textColor} lh={1.6}>
                2.2 您有责任维护账户信息的安全性，包括但不限于密码保护。因您的疏忽导致的账户安全问题，您将承担相应责任。
              </Text>
              <Text c={textColor} lh={1.6}>
                2.3 每个用户只能注册一个账户，不得恶意注册多个账户。
              </Text>
            </Stack>
          </div>

          <div>
            <Title order={3} mb={12} c={titleColor}>3. 使用规范</Title>
            <Stack gap={8}>
              <Text c={textColor} lh={1.6}>
                3.1 您承诺遵守国家法律法规，不得利用本平台从事违法违规活动。
              </Text>
              <Text c={textColor} lh={1.6}>
                3.2 不得发布、传播违法、有害、虚假、侵权的内容。
              </Text>
              <Text c={textColor} lh={1.6}>
                3.3 不得恶意攻击、破坏平台系统，或进行其他可能影响平台正常运行的行为。
              </Text>
              <Text c={textColor} lh={1.6}>
                3.4 尊重其他用户的合法权益，不得侵犯他人隐私或进行骚扰行为。
              </Text>
            </Stack>
          </div>

          <div>
            <Title order={3} mb={12} c={titleColor}>4. 知识产权</Title>
            <Stack gap={8}>
              <Text c={textColor} lh={1.6}>
                4.1 本平台的软件、技术、商标、版权等知识产权归公司所有。
              </Text>
              <Text c={textColor} lh={1.6}>
                4.2 用户上传的内容，其知识产权归用户所有，但用户授权本平台在提供服务范围内使用。
              </Text>
              <Text c={textColor} lh={1.6}>
                4.3 未经授权，任何人不得复制、修改、传播本平台的内容和技术。
              </Text>
            </Stack>
          </div>

          <div>
            <Title order={3} mb={12} c={titleColor}>5. 隐私保护</Title>
            <Text c={textColor} lh={1.6}>
              我们重视用户隐私保护，具体的隐私政策请参阅《隐私政策》。我们承诺按照相关法律法规和隐私政策处理您的个人信息。
            </Text>
          </div>

          <div>
            <Title order={3} mb={12} c={titleColor}>6. 服务变更与终止</Title>
            <Stack gap={8}>
              <Text c={textColor} lh={1.6}>
                6.1 我们有权根据业务需要调整、升级或终止部分服务功能，会提前通知用户。
              </Text>
              <Text c={textColor} lh={1.6}>
                6.2 如用户违反本协议，我们有权限制、暂停或终止用户的服务使用权。
              </Text>
              <Text c={textColor} lh={1.6}>
                6.3 用户可以随时停止使用本平台服务，并可申请注销账户。
              </Text>
            </Stack>
          </div>

          <div>
            <Title order={3} mb={12} c={titleColor}>7. 免责声明</Title>
            <Stack gap={8}>
              <Text c={textColor} lh={1.6}>
                7.1 本平台提供的AI服务基于当前技术水平，我们不保证服务结果的绝对准确性。
              </Text>
              <Text c={textColor} lh={1.6}>
                7.2 因不可抗力、网络故障、系统维护等原因导致的服务中断，我们不承担责任。
              </Text>
              <Text c={textColor} lh={1.6}>
                7.3 用户因使用本平台服务产生的任何损失，我们的责任以用户支付的服务费用为限。
              </Text>
            </Stack>
          </div>

          <div>
            <Title order={3} mb={12} c={titleColor}>8. 争议解决</Title>
            <Text c={textColor} lh={1.6}>
              本协议的解释、效力及争议解决均适用中华人民共和国法律。如发生争议，双方应友好协商解决；协商不成的，可向公司所在地人民法院提起诉讼。
            </Text>
          </div>

          <div>
            <Title order={3} mb={12} c={titleColor}>9. 联系我们</Title>
            <Stack gap={8}>
              <Text c={textColor} lh={1.6}>
                <strong>公司名称：</strong>北京华坤锦绣科技有限公司
              </Text>
              <Text c={textColor} lh={1.6}>
                <strong>办公地址：</strong>北京市昌平区科星西路106号院5号楼2层901
              </Text>
              <Text c={textColor} lh={1.6}>
                <strong>联系人：</strong>柴华
              </Text>
              <Text c={textColor} lh={1.6}>
                <strong>联系电话：</strong>15810632627
              </Text>
              <Text c={textColor} lh={1.6}>
                <strong>电子邮箱：</strong><EMAIL>
              </Text>
            </Stack>
          </div>

          <Divider />

          <Text c="dimmed" size="sm" ta="center">
            本协议自发布之日起生效。我们保留随时修改本协议的权利，修改后的协议将在平台上公布。
          </Text>
        </Stack>
      </Stack>
    </Container>
  );
};

export default UserAgreement;
UserAgreement.displayName = 'UserAgreement';
