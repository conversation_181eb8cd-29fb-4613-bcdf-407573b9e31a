import React, { ReactNode } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';

// Create default QueryClient
const defaultQueryClient = new QueryClient();

interface QueryProviderProps {
  children: ReactNode;
  client?: QueryClient;
}

/**
 * QueryProvider component that provides React Query context
 */
export const QueryProvider: React.FC<QueryProviderProps> = ({
  children,
  client = defaultQueryClient
}) => {
  return <QueryClientProvider client={client}>{children}</QueryClientProvider>;
};

/**
 * Create and export a function to create a custom QueryClient
 */
export const createQueryClient = (
  options?: ConstructorParameters<typeof QueryClient>[0]
) => {
  return new QueryClient(options);
};

/**
 * Export the default QueryClient
 */
export { defaultQueryClient };
