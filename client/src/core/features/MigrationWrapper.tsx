import React, { ReactNode, useEffect, useState } from 'react';
import { useUserStore } from '~/user/core/store';
import { DataMigration } from './DataMigration';
import {   useMigrationStore } from './useMigrationStore';
import { LoadingOverlay } from '@mantine/core';

interface MigrationWrapperProps {
  children: ReactNode;
}

/**
 * 数据迁移包装组件
 * 在应用启动时检查是否需要进行数据迁移
 */
export const MigrationWrapper: React.FC<MigrationWrapperProps> = ({ children }) => {
  const { isAuthenticated } = useUserStore();
  const {isCompleted, setCompleted } = useMigrationStore();
  const [showMigration, setShowMigration] = useState(false);
  const [isChecking, setIsChecking] = useState(true);

  useEffect(() => {
    // 只有在用户已登录时才检查是否需要迁移
    if (isAuthenticated) {
      setShowMigration(isCompleted === false);
    } else {
      setShowMigration(false);
    }
    setIsChecking(false);
  }, [isAuthenticated]);

  /**
   * 迁移完成回调
   */
  const handleMigrationComplete = () => {
    setCompleted(true);
    setShowMigration(false);
  };

  // 如果正在检查，显示加载状态
  if (isChecking) {
    return <LoadingOverlay visible />;
  }

  // 如果需要迁移且用户已登录，显示迁移界面
  if (showMigration && isAuthenticated) {
    return <DataMigration onComplete={handleMigrationComplete} />;
  }

  // 否则显示正常的应用内容
  return <>{children}</>;
};
