import * as Sentry from '@sentry/react';

/**
 * Initialize Sentry in production environment
 */
export const initSentry = (options?: Partial<Sentry.BrowserOptions>) => {
  if (import.meta.env.PROD) {
    Sentry.init({
      dsn: 'https://<EMAIL>/2',
      integrations: [],
      tracesSampleRate: 0,
      ...options
    });
    return true;
  }
  return false;
};

/**
 * Export Sentry for direct usage
 */
export { Sentry };
