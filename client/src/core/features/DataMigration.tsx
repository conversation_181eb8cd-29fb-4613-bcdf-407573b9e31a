import React, { useState, useEffect } from 'react';
import { Container, Paper, Text, Progress, Button, Stack, Alert, Group } from '@mantine/core';
import { FaCheck, FaTimes, FaRedo } from 'react-icons/fa';
import { useUserStore } from '~/user/core/store';
import { ChatDatabase } from '~/agent/chat/db';
import { getUserKeys } from '~/user/core/api/usage';
import { Conversation as DifyConversation, Message as DifyMessage } from '@dify_schemas/app/conversation/schemas';
import { Conversation, Message } from '~/agent/chat/schemas';
import { API_BASE_URL } from '~/core/constants';

interface MigrationStep {
  id: string;
  title: string;
  status: 'pending' | 'running' | 'completed' | 'error';
  progress: number;
  error?: string;
}

interface DataMigrationProps {
  onComplete: () => void;
}

/**
 * 数据迁移组件
 * 负责将老版本的聊天记录迁移到新版本
 */
export const DataMigration: React.FC<DataMigrationProps> = ({ onComplete }) => {
  const { token, userInfo } = useUserStore();
  const [steps, setSteps] = useState<MigrationStep[]>([
    { id: 'check-keys', title: '检查用户密钥', status: 'pending', progress: 0 },
    { id: 'get-agents', title: '获取用户智能体', status: 'pending', progress: 0 },
    { id: 'migrate-conversations', title: '迁移对话记录', status: 'pending', progress: 0 },
    { id: 'migrate-messages', title: '迁移消息记录', status: 'pending', progress: 0 },
    { id: 'cleanup', title: '清理和完成', status: 'pending', progress: 0 },
  ]);
  const [isRunning, setIsRunning] = useState(false);
  const [totalProgress, setTotalProgress] = useState(0);

  /**
   * 更新迁移步骤的状态和进度
   * @param stepId - 要更新的步骤ID
   * @param updates - 要更新的属性对象，可以包含status、progress和error等字段
   *
   * 实现说明:
   * 1. 接收步骤ID和部分更新对象作为参数
   * 2. 使用setSteps更新状态，传入一个函数来处理之前的状态
   * 3. 使用map遍历所有步骤，找到匹配的stepId
   * 4. 对匹配的步骤，使用展开运算符合并原有属性和新的更新
   * 5. 不匹配的步骤保持不变
   *
   * 使用示例:
   * updateStep('check-keys', { status: 'running', progress: 50 })
   * updateStep('migrate-messages', { status: 'error', error: '迁移失败' })
   */
  const updateStep = (stepId: string, updates: Partial<MigrationStep>) => {
    setSteps((prev) => prev.map((step) => (step.id === stepId ? { ...step, ...updates } : step)));
  };

  const calculateTotalProgress = () => {
    const totalSteps = steps.length;
    const completedSteps = steps.filter((step) => step.status === 'completed').length;
    const runningStep = steps.find((step) => step.status === 'running');

    let progress = (completedSteps / totalSteps) * 100;
    if (runningStep) {
      progress += runningStep.progress / totalSteps;
    }

    setTotalProgress(Math.min(progress, 100));
  };

  useEffect(() => {
    calculateTotalProgress();
  }, [steps]);

  /**
   * 获取老版本的对话列表
   */
  const getOldConversations = async (agentId: string): Promise<DifyConversation[]> => {
    const response = await fetch(`${API_BASE_URL}/agents/conversations?app_id=${agentId}&limit=100`, {
      headers: {
        Authorization: `Bearer ${token}`,
        Accept: 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`获取对话列表失败: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data?.data || [];
  };

  /**
   * 获取老版本的消息列表
   */
  const getOldMessages = async (agentId: string, conversationId: string): Promise<DifyMessage[]> => {
    const response = await fetch(
      `${API_BASE_URL}/agents/messages?app_id=${agentId}&conversation_id=${conversationId}&limit=100`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: 'application/json',
        },
      },
    );

    if (!response.ok) {
      throw new Error(`获取消息列表失败: ${response.statusText}`);
    }

    const result = await response.json();
    return result.data?.data || [];
  };

  /**
   * 将老版本的Message转换为新版本的Message
   */
  const convertOldMessageToNew = (oldMessage: DifyMessage, conversationId: string): Message[] => {
    const messages: Message[] = [];
    const baseTime = oldMessage.created_at || Date.now() / 1000;
    const timeStr = new Date(baseTime * 1000).toISOString();

    // 如果有用户问题，创建用户消息
    if (oldMessage.query) {
      messages.push({
        id: `${oldMessage.id}_user`,
        conversation_id: conversationId,
        created_at: baseTime,
        created_at_str: timeStr,
        role: 'user',
        content: [
          {
            id: `${oldMessage.id}_user_text`,
            type: 'text',
            content: oldMessage.query,
          },
        ],
      });
    }

    // 如果有助手回答，创建助手消息
    if (oldMessage.answer) {
      messages.push({
        id: `${oldMessage.id}_assistant`,
        conversation_id: conversationId,
        created_at: baseTime + 1, // 稍微延后一秒
        created_at_str: new Date((baseTime + 1) * 1000).toISOString(),
        role: 'assistant',
        content: [
          {
            id: `${oldMessage.id}_assistant_text`,
            type: 'text',
            content: oldMessage.answer,
          },
        ],
      });
    }

    return messages;
  };

  /**
   * 开始数据迁移
   */
  const startMigration = async () => {
    if (!token || !userInfo) {
      return;
    }

    setIsRunning(true);
    const db = new ChatDatabase();

    try {
      // 步骤1: 检查用户密钥
      updateStep('check-keys', { status: 'running', progress: 0 });
      const keysResponse = await getUserKeys();
      const agentKeys = keysResponse.data.keys.filter((key) => key.service_code === 'AGC');

      if (agentKeys.length === 0) {
        // 用户可用的智能体密钥为空，视为已经迁移过，直接跳过迁移
        updateStep('check-keys', { status: 'completed', progress: 100 });
        updateStep('get-agents', { status: 'completed', progress: 100 });
        updateStep('migrate-conversations', { status: 'completed', progress: 100 });
        updateStep('migrate-messages', { status: 'completed', progress: 100 });
        updateStep('cleanup', { status: 'completed', progress: 100 });

        // 延迟一下让用户看到完成状态，然后直接完成迁移
        setTimeout(() => {
          onComplete();
        }, 1000);
        return;
      }

      updateStep('check-keys', { status: 'completed', progress: 100 });

      // 步骤2: 获取用户智能体
      updateStep('get-agents', { status: 'running', progress: 0 });
      const agentIds = new Set<string>();
      agentKeys.forEach((key) => {
        if (key.scope) {
          key.scope.forEach((id) => agentIds.add(id));
        }
      });

      const agentIdArray = Array.from(agentIds);
      updateStep('get-agents', { status: 'completed', progress: 100 });

      // 步骤3: 迁移对话记录
      updateStep('migrate-conversations', { status: 'running', progress: 0 });
      let processedAgents = 0;

      for (const agentId of agentIdArray) {
        try {
          const oldConversations = await getOldConversations(agentId);

          for (const oldConv of oldConversations) {
            // 检查对话是否已存在
            const existingConv = await db.conversations.get(oldConv.id);
            if (!existingConv) {
              const newConversation: Conversation = {
                id: oldConv.id,
                name: oldConv.name,
                created_at: oldConv.created_at,
                updated_at: oldConv.updated_at,
                agent: {
                  id: agentId,
                  name: '', // 这里可以从智能体API获取名称
                  description: '',
                  icon: '',
                },
              };

              await db.conversations.add(newConversation);
            }
          }

          processedAgents++;
          updateStep('migrate-conversations', {
            status: 'running',
            progress: (processedAgents / agentIdArray.length) * 100,
          });
        } catch (error) {
          console.warn(`迁移智能体 ${agentId} 的对话失败:`, error);
        }
      }

      updateStep('migrate-conversations', { status: 'completed', progress: 100 });

      // 步骤4: 迁移消息记录
      updateStep('migrate-messages', { status: 'running', progress: 0 });
      processedAgents = 0;

      for (const agentId of agentIdArray) {
        try {
          const oldConversations = await getOldConversations(agentId);

          for (const oldConv of oldConversations) {
            try {
              const oldMessages = await getOldMessages(agentId, oldConv.id);

              for (const oldMessage of oldMessages) {
                // 检查消息是否已存在
                const existingUserMsg = await db.messages.get(`${oldMessage.id}_user`);
                const existingAssistantMsg = await db.messages.get(`${oldMessage.id}_assistant`);

                if (!existingUserMsg && !existingAssistantMsg) {
                  const newMessages = convertOldMessageToNew(oldMessage, oldConv.id);

                  for (const newMessage of newMessages) {
                    await db.messages.add(newMessage);
                  }
                }
              }
            } catch (error) {
              console.warn(`迁移对话 ${oldConv.id} 的消息失败:`, error);
            }
          }

          processedAgents++;
          updateStep('migrate-messages', {
            status: 'running',
            progress: (processedAgents / agentIdArray.length) * 100,
          });
        } catch (error) {
          console.warn(`迁移智能体 ${agentId} 的消息失败:`, error);
        }
      }

      updateStep('migrate-messages', { status: 'completed', progress: 100 });

      // 步骤5: 清理和完成
      updateStep('cleanup', { status: 'running', progress: 50 });

      updateStep('cleanup', { status: 'completed', progress: 100 });

      // 延迟一下让用户看到完成状态
      setTimeout(() => {
        onComplete();
      }, 1000);
    } catch (error) {
      console.error('数据迁移失败:', error);
      const runningStep = steps.find((step) => step.status === 'running');
      if (runningStep) {
        updateStep(runningStep.id, {
          status: 'error',
          error: error instanceof Error ? error.message : '未知错误',
        });
      }
    } finally {
      setIsRunning(false);
    }
  };

  /**
   * 跳过迁移
   */
  const skipMigration = () => {
    onComplete();
  };

  return (
    <Container size="sm" py="xl">
      <Paper p="xl" shadow="sm">
        <Stack gap="lg">
          <div>
            <Text size="xl" fw={600} mb="sm">
              数据迁移
            </Text>
            <Text c="dimmed">我们需要将您的历史聊天记录迁移到新版本。这个过程可能需要几分钟时间。</Text>
          </div>

          <Progress value={totalProgress} size="lg" />

          <Stack gap="sm">
            {steps.map((step) => (
              <Group key={step.id} justify="space-between">
                <Text size="sm">{step.title}</Text>
                <Group gap="xs">
                  {step.status === 'completed' && <FaCheck size={16} color="green" />}
                  {step.status === 'error' && <FaTimes size={16} color="red" />}
                  {step.status === 'running' && <Progress value={step.progress} size="sm" w={60} />}
                  {step.status === 'pending' && <div style={{ width: 16, height: 16 }} />}
                </Group>
              </Group>
            ))}
          </Stack>

          {steps.some((step) => step.status === 'error') && (
            <Alert color="red" title="迁移过程中出现错误">
              {steps.find((step) => step.status === 'error')?.error}
            </Alert>
          )}

          <Group justify="center" gap="md">
            <Button
              onClick={startMigration}
              disabled={isRunning}
              leftSection={isRunning ? undefined : <FaRedo size={16} />}
              loading={isRunning}
            >
              {isRunning ? '迁移中...' : '开始迁移'}
            </Button>

            <Button variant="outline" onClick={skipMigration} disabled={isRunning}>
              跳过迁移
            </Button>
          </Group>
        </Stack>
      </Paper>
    </Container>
  );
};
