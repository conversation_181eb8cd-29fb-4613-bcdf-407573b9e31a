import React, { ReactNode } from 'react';
import { MantineSetup, defaultTheme } from './mantine';
import { QueryProvider, defaultQueryClient } from './react-query';
import { initSentry } from './sentry';
import { ErrorBoundary } from '../components';
import { MigrationWrapper } from './MigrationWrapper';

// Initialize Sentry
initSentry();

interface AppProvidersProps {
  children: ReactNode;
}

/**
 * AppProviders component that combines all providers
 */
export const AppProviders: React.FC<AppProvidersProps> = ({ children }) => {
  return (
    <React.StrictMode>
      <ErrorBoundary>
        <QueryProvider client={defaultQueryClient}>
          <MantineSetup theme={defaultTheme}>
            <MigrationWrapper>{children}</MigrationWrapper>
          </MantineSetup>
        </QueryProvider>
      </ErrorBoundary>
    </React.StrictMode>
  );
};
