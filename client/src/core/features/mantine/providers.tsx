import React, { ReactNode } from 'react';
import { createTheme, MantineProvider } from '@mantine/core';
import { Notifications } from '@mantine/notifications';
import { ThemeProvider } from './theme-context';
import { DEFAULT_COLOR_SCHEME } from '~/core/constants';

// Create default theme
const defaultTheme = createTheme({});

interface MantineSetupProps {
  children: ReactNode;
  theme?: ReturnType<typeof createTheme>;
}

/**
 * MantineSetup component that provides Mantine context and notifications
 */
export const MantineSetup: React.FC<MantineSetupProps> = ({ children, theme = defaultTheme }) => {
  return (
    <MantineProvider defaultColorScheme={DEFAULT_COLOR_SCHEME} theme={theme}>
      <Notifications position="top-center" />
      <ThemeProvider>{children}</ThemeProvider>
    </MantineProvider>
  );
};

/**
 * Create and export a custom theme
 */
export const createCustomTheme = createTheme;

/**
 * Export the default theme
 */
export { defaultTheme };
