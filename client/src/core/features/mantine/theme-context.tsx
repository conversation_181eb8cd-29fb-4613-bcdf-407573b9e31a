import React, { createContext, useContext } from 'react';
import { useMantineColorScheme } from '@mantine/core';

type ThemeContextType = {
  actualColorScheme: 'light' | 'dark';
};

const ThemeContext = createContext<ThemeContextType | null>(null);

/**
 * ThemeProvider component that provides theme context
 */
export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({
  children
}) => {
  const { colorScheme } = useMantineColorScheme();

  // Handle 'auto' case, get actual theme
  const actualColorScheme =
    colorScheme === 'auto'
      ? window.matchMedia('(prefers-color-scheme: dark)').matches
        ? 'dark'
        : 'light'
      : (colorScheme as 'light' | 'dark');

  return (
    <ThemeContext.Provider value={{ actualColorScheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

/**
 * useTheme hook to access theme context
 */
export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
