import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';

interface MigrationState {
  /**
   * 是否已完成数据迁移
   */
  isCompleted: boolean;

  /**
   * 是否正在进行数据迁移
   */
  isRunning: boolean;

  /**
   * 迁移完成时间
   */
  completedAt: number | null;

  /**
   * 设置迁移完成状态
   */
  setCompleted: (completed: boolean) => void;

  /**
   * 设置迁移运行状态
   */
  setRunning: (running: boolean) => void;

  /**
   * 重置迁移状态
   */
  reset: () => void;
}

/**
 * 数据迁移状态管理
 */
export const useMigrationStore = create<MigrationState>()(
  devtools(
    persist(
      (set) => ({
        isCompleted: false,
        isRunning: false,
        completedAt: null,

        setCompleted: (completed) => set({
          isCompleted: completed,
          completedAt: completed ? Date.now() : null,
          isRunning: false,
        }, false, 'setCompleted'),

        setRunning: (running) => set({
          isRunning: running
        }, false, 'setRunning'),

        reset: () => set({
          isCompleted: false,
          isRunning: false,
          completedAt: null,
        }, false, 'reset'),
      }),
      {
        name: 'migration-storage',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          isCompleted: state.isCompleted,
          completedAt: state.completedAt,
        }),
      },
    ),
    {
      name: 'migration-store',
    },
  ),
);
