import { z } from 'zod';

/**
 * 错误信息模式
 */
export const ErrorSchema = z.object({
  code: z.string().describe('错误代码'),
  message: z.string().describe('错误消息'),
});

/**
 * API响应模式
 */
export const ResponsePayloadsSchema = <T extends z.ZodTypeAny>(dataSchema: T) =>
  z.object({
    data: dataSchema.describe('响应数据'),
    error: ErrorSchema.optional().describe('错误信息'),
  });

/**
 * 分页响应模式
 */
export const PaginationSchema = <T extends z.ZodTypeAny>(itemSchema: T) =>
  z.object({
    data: z.array(itemSchema).describe('数据列表'),
    total: z.number().describe('总数'),
    has_more: z.boolean().describe('是否有更多'),
  });

// 类型导出
export type Error = z.infer<typeof ErrorSchema>;
export type ResponsePayloads<T> = z.infer<ReturnType<typeof ResponsePayloadsSchema<z.ZodType<T>>>>;
export type Pagination<T> = z.infer<ReturnType<typeof PaginationSchema<z.ZodType<T>>>>;
/**
 * 资产基础模式
 */
export const AssetBaseSchema = z.object({
  id: z.string().describe('资产ID'),
  name: z.string().describe('资产名称'),
  description: z.string().optional().describe('资产描述'),
  icon: z.string().optional().nullable().describe('资产图标'),
});

// 类型导出
export type AssetBase = z.infer<typeof AssetBaseSchema>;

/**
 * 资产查询基础模式
 * 用于所有资产类型（智能体、课程、工作流等）的通用查询参数
 */
export const AssetQueryBaseSchema = z.object({
  keyword: z.string().optional().describe('关键词'),
  tags: z.array(z.string()).optional().describe('标签列表'),
  page: z.number().default(1).describe('页码，从1开始'),
  page_size: z.number().default(10).describe('每页数量'),
});

// 类型导出
export type AssetQueryBase = z.infer<typeof AssetQueryBaseSchema>;
