import { NodeFinishedDataSchema, UserInputItemSchema, WorkflowPublishSchema } from '@dify_schemas/index';
import { z } from 'zod';
import { AgentDetailSchema } from '~/agent/marketplace/schemas';

export const RunWorkflowContextSchema = z.object({
  /**
   * 工作流节点数据
   */
  nodes: z.array(NodeFinishedDataSchema).default([]),
  /**
   * 当前活动节点ID
   */
  activeNodeId: z.string().optional(),
  /**
   * 工作流输入参数
   */
  workflowInput: z.array(UserInputItemSchema).default([]),
  workflow_publish: WorkflowPublishSchema.optional(),
  /**
   * 工作流详情
   */
  agentDetail: AgentDetailSchema.optional(),
  /**
   * 运行工作流
   */
  runWorkflowMutation: z.function().args(z.record(z.any())).returns(z.void()),
  /**
   * 是否正在运行工作流
   */
  isPending: z.boolean(),
  /**
   * 是否运行工作流出错
   */
  isRunWorkflowError: z.boolean(),
  /**
   * 消息
   */
  msg: z.string().default(''),
});

export type RunWorkflowContextType = z.infer<typeof RunWorkflowContextSchema>;
