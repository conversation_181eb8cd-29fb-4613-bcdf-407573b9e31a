import { createContext, useContext } from 'react';
import { RunWorkflowContextType } from '../schemas';
import { ConversationEventType, NodeFinishedData, UserInputItem, WorkflowPublish } from '@dify_schemas/index';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRef, useState } from 'react';
import { useParams } from 'react-router-dom';
import { runWorkflow } from '~/agent/chat/api';
import { getDetail } from '~/agent/marketplace/api';
import { getWorkflowParameters } from '../api';
import { useUserStore } from '~/user/core/store';
const RunWorkflowContext = createContext<RunWorkflowContextType | undefined>(undefined);

export const RunWorkflowContextProvider = ({ children }: { children: React.ReactNode }) => {
  const workflowId = useParams<{ id: string }>().id;
  const taskId = useRef<string>('');
  const user = useUserStore((state) => state.userInfo);
  const [nodes, setNodes] = useState<Array<NodeFinishedData>>([]);
  const [activeNodeId, setActiveNodeId] = useState<string | undefined>();

  const [workflowInput, setWorkflowInput] = useState<UserInputItem[]>([]);
  const [workflow_publish, setWorkflowPublish] = useState<WorkflowPublish>();
  const [msg, setMsg] = useState<string>('');
  const { data: agentDetail } = useQuery({
    queryKey: ['agentDetail', workflowId],
    queryFn: async () => {
      const data = (await getDetail(workflowId!)).data;
      return data;
    },
    enabled: !!workflowId,
    refetchOnWindowFocus: false,
  });

  // 获取工作流参数
  useQuery({
    queryKey: ['workflowParameters', workflowId],
    queryFn: async () => {
      const data = (await getWorkflowParameters(workflowId!)).data;
      setWorkflowInput(data.parameters.user_input_form || []);
      setWorkflowPublish(data.workflow_publish);
      return data;
    },
    enabled: !!workflowId,
    refetchOnWindowFocus: false,
  });
  const {
    mutate: runWorkflowMutation,
    isPending,
    isError: isRunWorkflowError,
  } = useMutation({
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    mutationFn: (inputs: Record<string, any>) => {
      return runWorkflow(
        workflowId!,
        {
          inputs,
          response_mode: 'streaming',
          user: user!.id.toString(),
          files: [],
        },
        (event) => {
          if (event.task_id) {
            taskId.current = event.task_id;
          }
          switch (event.event) {
            case ConversationEventType.NODE_STARTED:
            case ConversationEventType.NODE_FINISHED: {
              const nodeData = event.data as NodeFinishedData;
              if (event.event === 'node_started') {
                setActiveNodeId(nodeData.node_id || undefined);
              }

              // 更新节点状态
              setNodes((prev) => [
                ...prev.filter((n) => n.id !== nodeData.id),
                {
                  ...nodeData,
                  status: event.event === 'node_started' ? 'running' : nodeData.status || 'succeeded',
                },
              ]);
              break;
            }
            case ConversationEventType.WORKFLOW_FINISHED: {
              break;
            }
            case ConversationEventType.TEXT_CHUNK: {
              setMsg((prev) => prev + event?.data?.text);
              console.log('msg', msg);
              break;
            }
          }
        },
        (error) => {
          throw error;
        },
      );
    },
  });
  return (
    <RunWorkflowContext.Provider
      value={{
        nodes,
        activeNodeId,
        workflowInput,
        workflow_publish,
        agentDetail,
        msg,
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        runWorkflowMutation: runWorkflowMutation as (inputs: Record<string, any>) => void,
        isPending,
        isRunWorkflowError,
      }}
    >
      {children}
    </RunWorkflowContext.Provider>
  );
};

export const useRunWorkflowContext = () => {
  const context = useContext(RunWorkflowContext);
  if (!context) {
    throw new Error('useRunWorkflowContext must be used within a RunWorkflowContextProvider');
  }
  return context;
};
