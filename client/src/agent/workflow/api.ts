import { UserInputItem, WorkflowPublish } from '@dify_schemas/index';
import { API_BASE_URL } from '~/core/constants';
import { ResponsePayloads } from '~/core/schemas';
import { useUserStore } from '~/user/core/store';

/**
 * 获取工作流参数配置
 * @param workflowId 工作流ID
 * @returns 工作流参数配置
 */
export async function getWorkflowParameters(workflowId: string): Promise<
  ResponsePayloads<{
    parameters: {
      user_input_form: UserInputItem[];
    };
    workflow_publish: WorkflowPublish;
  }>
> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/agents/workflow/parameters/${workflowId}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取工作流参数失败: ${response.statusText}`);
  }

  return response.json();
}
