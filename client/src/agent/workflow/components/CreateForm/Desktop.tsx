import React, { useRef, useState } from 'react';
import { Stack, Group, Image, Text, Button, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { FilterTabs } from '../index';

import { FiPlayCircle } from 'react-icons/fi';
import WorkflowForm, { WorkflowFormHandles } from '../WorkflowForm';
import { useRunWorkflowContext } from '../../contexts';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const { workflowInput, agentDetail, runWorkflowMutation, isPending } = useRunWorkflowContext();
  const formRef = useRef<WorkflowFormHandles>(null);
  const containerBgColor = isDark ? theme.colors.dark[9] : 'white';

  const tabs = [
    {
      label: 'Run Once',
      type: 'once',
    },
    // {
    //   label: 'Run Batch',
    //   type: 'batch',
    // },
  ];
  const [selectedTab, setSelectedTab] = useState(tabs[0]);

  const playIcon = <FiPlayCircle size={16} />;

  // 处理表单提交
  const handleSubmit = () => {
    // 调用 submit 方法获取验证结果和表单值
    const submitResult = formRef.current?.submit();
    if (!submitResult?.isValid || !submitResult.values) {
      return;
    }

    // 执行工作流
    runWorkflowMutation(submitResult.values);
  };

  // 处理表单重置
  const handleReset = () => {
    // 重置表单
    if (formRef.current) {
      formRef.current.submit();
    }
  };

  return (
    <Stack
      className="flex-none"
      w={600}
      p={64}
      style={{
        background: containerBgColor,
        boxShadow: '0px 2px 4px  rgba(0, 0, 0, 0.1)',
      }}
      gap={0}
    >
      <Group mb={24} gap={18}>
        <Image
          w={48}
          h={48}
          src={
            agentDetail?.icon ||
            'https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4'
          }
        />
        <Text fz={12} fw={400} lh="18px">
          {agentDetail?.name || '工作流'}
        </Text>
      </Group>

      <FilterTabs tabs={tabs} selected={selectedTab} onChange={setSelectedTab} />

      <Stack mt={20} mb={24} gap={16}>
        {/* 动态表单组件 */}
        <WorkflowForm formFields={workflowInput} ref={formRef} />
      </Stack>

      <Group align="center" justify="space-between">
        <Button variant="default" onClick={handleReset}>
          清除
        </Button>
        <Button
          variant="filled"
          w={120}
          leftSection={playIcon}
          color="rgba(73, 81, 235, 1)"
          onClick={handleSubmit}
          loading={isPending}
        >
          {isPending ? '执行中...' : '执行'}
        </Button>
      </Group>
    </Stack>
  );
};

export default Desktop;
