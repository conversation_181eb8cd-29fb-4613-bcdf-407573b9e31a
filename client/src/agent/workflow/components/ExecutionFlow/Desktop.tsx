import { Stack, Group, Text, Accordion, Avatar, useMantineTheme } from '@mantine/core';
import {
  Ri<PERSON>lowChart,
  RiCheckboxCircleFill,
  RiErrorWarningFill,
  RiCloseCircleFill,
  RiFilmLine,
  RiLinkM,
  RiPlayCircleFill,
} from 'react-icons/ri';
import { RiRobotFill, RiDatabaseFill, RiFlagFill } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';
import { useRunWorkflowContext } from '../../contexts/RunWorkflowContext';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
// 工作流节点数据接口
interface WorkflowNodeData {
  type?: string;
  title?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  [key: string]: any;
}

// 根据节点类型获取图标
const getNodeIcon = (type?: string) => {
  switch (type?.toLowerCase()) {
    case 'start':
      return <RiPlayCircleFill size={8} />;
    case 'database':
      return <RiDatabaseFill size={8} />;
    case 'crawl':
      return <RiFilmLine size={8} />;
    case 'llm':
      return <RiRobotFill size={8} />;
    case 'end':
      return <RiFlagFill size={8} />;
    case 'link':
      return <RiLinkM size={8} />;
    default:
      return <RiFlowChart size={8} />;
  }
};

// 根据节点状态获取状态图标
const getStatusIcon = (status?: string) => {
  switch (status) {
    case 'running':
      return <RiErrorWarningFill className="flex-none" size={16} color="rgba(255, 235, 59, 1)" />;
    case 'succeeded':
      return <RiCheckboxCircleFill className="flex-none" size={16} color="rgba(165, 214, 63, 1)" />;
    case 'failed':
      return <RiCloseCircleFill className="flex-none" size={16} color="rgba(212, 48, 48, 1)" />;
    default:
      return null;
  }
};

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const { nodes, msg, workflow_publish } = useRunWorkflowContext();

  const containerBgColor = isDark ? theme.colors.dark[9] : 'white';
  const itemBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const panelItemBgColor = isDark ? theme.colors.dark[9] : 'white';
  return (
    <Stack
      className="flex-auto"
      w={800}
      p={64}
      style={{
        background: containerBgColor,
        boxShadow: '0px 2px 4px  rgba(0, 0, 0, 0.1)',
      }}
      gap={0}
    >
      <Accordion defaultValue="flow">
        <Accordion.Item
          key="flow"
          value="flow"
          bg={itemBgColor}
          style={{
            borderColor: 'transparent',
          }}
        >
          <Accordion.Control>
            <Group gap={8}>
              <Avatar
                size={24}
                styles={{
                  placeholder: {
                    background: 'rgba(73, 81, 235, 1)',
                    color: 'white',
                  },
                }}
              >
                <RiFlowChart size={12} />
              </Avatar>
              <Text fz={12} fw={700} lh="18px">
                工作流
              </Text>
            </Group>
          </Accordion.Control>
          <Accordion.Panel>
            <Stack gap={10}>
              {/* 渲染实际的工作流节点 */}
              {nodes.map((node) => {
                // 获取节点类型和标题 - 从process_data或其他属性中提取
                const processData = (node.process_data as WorkflowNodeData) || {};
                const nodeType = processData.type || 'default';

                // 尝试从workflow_publish中获取节点名称
                let nodeTitle = processData.title;
                if (!nodeTitle && workflow_publish?.graph?.nodes) {
                  // 根据节点ID在workflow_publish中查找对应节点
                  const publishNode = workflow_publish.graph.nodes.find((n) => n.id === node.node_id);
                  if (publishNode?.data?.title) {
                    nodeTitle = publishNode.data.title;
                  }
                }
                // 如果仍然没有找到标题，使用默认值
                nodeTitle = nodeTitle || `节点 ${node.id || '未知'}`;
                const nodeStatus = node.status || 'pending';
                return (
                  <Stack key={node.id} gap={4}>
                    <Group h={36} px={16} bg={panelItemBgColor}>
                      <Avatar
                        className="flex-none"
                        size={16}
                        styles={{
                          placeholder: {
                            background: 'rgba(73, 81, 235, 1)',
                            color: 'white',
                          },
                        }}
                      >
                        {getNodeIcon(nodeType)}
                      </Avatar>
                      <Text className="flex-auto" fz={12} fw={500} lh="18px">
                        {nodeTitle}
                      </Text>
                      {getStatusIcon(nodeStatus)}
                    </Group>
                    {/* 显示节点错误信息 */}
                    {node.error && (
                      <Group px={16} py={8} bg={isDark ? 'rgba(212, 48, 48, 0.1)' : 'rgba(255, 235, 235, 1)'}>
                        <RiErrorWarningFill className="flex-none" size={14} color="rgba(212, 48, 48, 1)" />
                        <Text className="flex-auto" fz={11} fw={400} lh="16px" c="rgba(212, 48, 48, 1)">
                          {node.error}
                        </Text>
                      </Group>
                    )}
                  </Stack>
                );
              })}
            </Stack>
          </Accordion.Panel>
        </Accordion.Item>
      </Accordion>
      <div className="prose max-w-none text-sm">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          unwrapDisallowed={false}
          allowedElements={undefined}
          components={{
            a: ({ ...props }) => (
              <a href={props.href} target="_blank" rel="noopener noreferrer">
                {props.children}
              </a>
            ),
          }}
        >
          {msg}
        </ReactMarkdown>
      </div>
    </Stack>
  );
};

export default Desktop;
