import React, { forwardRef, useImperativeHandle } from 'react';
import { Stack, TextInput, Textarea, NumberInput, Select } from '@mantine/core';
import { useForm } from '@mantine/form';
import { UserInputItem } from '@dify_schemas/app/schemas';

// 表单句柄类型定义
export interface WorkflowFormHandles {
  submit: () => {
    isValid: boolean;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    values: Record<string, any> | null; // 允许值为null
  };
}

// 组件属性类型定义
export interface WorkflowFormProps {
  formFields: UserInputItem[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChange?: (values: Record<string, any>) => void;
}

/**
 * 工作流表单组件
 * 根据配置动态生成表单字段
 */
const WorkflowForm = forwardRef<WorkflowFormHandles, WorkflowFormProps>(({ formFields }, ref) => {
  // 创建初始值对象，确保所有字段都有初始值
  const initialValues = formFields.reduce(
    (acc, field) => {
      const input = field['text-input'] || field.number || field.paragraph || field.select_input;
      if (input) {
        // 根据字段类型设置不同的初始值
        if (input.type === 'number') {
          // 数字类型字段使用 null 作为初始值
          acc[input.variable] = null;
        } else {
          // 其他类型字段使用空字符串
          acc[input.variable] = '';
        }
      }
      return acc;
    },
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    {} as Record<string, any>,
  );

  // 创建表单实例
  const form = useForm({
    initialValues,
    validate: (values) => {
      return formFields.reduce(
        (acc, field) => {
          const input = field['text-input'] || field.number || field.paragraph || field.select_input;
          if (input) {
            const value = values[input.variable];

            if (input.required && (value === null || value === '')) {
              acc[input.variable] = '此字段为必填项';
            }
            if (input.max_length && value?.length > input.max_length) {
              acc[input.variable] = `内容过长（最大 ${input.max_length} 字符）`;
            }
          }
          return acc;
        },
        {} as Record<string, string>,
      );
    },
  });

  // 暴露验证方法
  useImperativeHandle(ref, () => ({
    submit: () => {
      const result = form.validate();
      if (result.hasErrors) {
        return { isValid: false, values: null };
      }
      return { isValid: true, values: form.values };
    },
  }));

  // 获取表单字段的通用样式
  const getCommonStyles = (isTextarea = false) => ({
    label: {
      fontSize: 12,
      fontWeight: 400,
      lineHeight: '18px',
      color: 'var(--mantine-color-dimmed)',
    },
    wrapper: {
      marginTop: 8,
    },
    input: {
      height: isTextarea ? 'auto' : 36,
      paddingLeft: 12,
      paddingRight: 12,
      backgroundColor: 'rgba(247, 248, 255, 1)',
    },
  });

  return (
    <form>
      <Stack gap={16}>
        {formFields.map((field) => {
          // 文本输入
          if (field['text-input']) {
            const config = field['text-input'];
            return (
              <TextInput
                key={config.variable}
                variant="unstyled"
                label={config.label}
                placeholder={`${config.label}${config.required ? '' : '（Optional）'}`}
                required={config.required}
                maxLength={config.max_length}
                styles={getCommonStyles()}
                {...form.getInputProps(config.variable)}
              />
            );
          }

          // 下拉选择
          if (field.select_input) {
            const config = field.select_input;
            return (
              <Select
                key={config.variable}
                variant="unstyled"
                label={config.label}
                placeholder={`请选择${config.label}${config.required ? '' : '（Optional）'}`}
                data={config.options || []}
                required={config.required}
                styles={getCommonStyles()}
                {...form.getInputProps(config.variable)}
              />
            );
          }

          // 数字输入
          if (field.number) {
            const config = field.number;
            return (
              <NumberInput
                key={config.variable}
                variant="unstyled"
                label={config.label}
                placeholder={`${config.label}${config.required ? '' : '（Optional）'}`}
                required={config.required}
                allowNegative={false}
                allowDecimal={true}
                styles={getCommonStyles()}
                {...form.getInputProps(config.variable)}
              />
            );
          }

          // 段落输入
          if (field.paragraph) {
            const config = field.paragraph;
            return (
              <Textarea
                key={config.variable}
                variant="unstyled"
                label={config.label}
                placeholder={`${config.label}${config.required ? '' : '（Optional）'}`}
                required={config.required}
                autosize
                minRows={3}
                maxRows={6}
                maxLength={config.max_length}
                styles={getCommonStyles(true)}
                {...form.getInputProps(config.variable)}
              />
            );
          }

          return null;
        })}
      </Stack>
    </form>
  );
});

WorkflowForm.displayName = 'WorkflowForm';

export default WorkflowForm;
