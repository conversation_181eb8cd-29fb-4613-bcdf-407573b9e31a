import React from 'react';
import { Container, Group } from '@mantine/core';
import { CreateForm, ExecutionFlow } from '../components';
import { RunWorkflowContextProvider } from '../contexts/RunWorkflowContext';

const Create = () => {
  return (
    <Container size="1452px" py={72}>
      <Group align="stretch" gap={16}>
        <RunWorkflowContextProvider>
          <CreateForm />
          <ExecutionFlow />
        </RunWorkflowContextProvider>
      </Group>
    </Container>
  );
};

export default Create;
