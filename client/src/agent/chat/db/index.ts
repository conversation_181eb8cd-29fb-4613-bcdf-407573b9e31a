import Dexie, { Table } from 'dexie';
import { Conversation, Message } from '../schemas';

/**
 * 聊天数据库类
 * 用于管理聊天相关的本地数据
 */
export class ChatDatabase extends Dexie {
  // 消息表
  messages!: Table<Message>;
  // 会话表
  conversations!: Table<Conversation>;

  constructor() {
    super('ChatDatabase');

    // 定义数据库结构
    this.version(1).stores({
      messages: '++id, conversation_id, created_at',
      conversations: 'id, name, created_at, updated_at',
    });
  }

  /**
   * 清空所有数据
   * 用于用户退出登录时清理数据
   */
  async clearAll(): Promise<void> {
    try {
      // 清空所有消息
      await this.messages.clear();
      // 清空所有会话
      await this.conversations.clear();
      console.log('已清空所有会话历史数据');
    } catch (error) {
      console.error('清空会话历史数据失败:', error);
      throw error;
    }
  }
}

// 创建数据库实例
export const db = new ChatDatabase();

// 导出数据库实例
export default db;

// interface ChatDatabaseService {
//   saveOrUpdateConversation: (conversation: Conversation) => Promise<void>;
//   saveOrUpdateMessage: (message: Message) => Promise<void>;
// }

// export const chatDatabaseService: ChatDatabaseService = {
//   async saveOrUpdateConversation(conversation) {
//     await db.conversations.put(conversation);
//   },
//   async saveOrUpdateMessage(message) {
//     await db.messages.put(message);
//   },
// };
