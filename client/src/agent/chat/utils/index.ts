import { Message } from '../schemas';
import { v4 as uuidv4 } from 'uuid';
import {
  AgentMessageEvent,
  AgentThoughtEvent,
  ChatMessageEvent,
  ConversationEvent,
  ConversationEventType,
  MessageFileEvent,
} from '@dify_schemas/index';

/**
 * 将事件转换为内容块
 * @param event SSE事件
 * @returns 内容块对象
 */
export const convertEventToContentBlock = (event: ConversationEvent): Message['content'][number] | null => {
  switch (event.event) {
    case ConversationEventType.MESSAGE: {
      // 文本消息事件
      const messageEvent = event as ChatMessageEvent;
      // 只有当answer不为空时才创建内容块
      if (!messageEvent.answer) return null;

      return {
        id: uuidv4(),
        type: 'text',
        content: messageEvent.answer,
      };
    }

    case ConversationEventType.AGENT_MESSAGE: {
      // Agent文本消息事件
      const agentMessageEvent = event as AgentMessageEvent;
      // 只有当answer不为空时才创建内容块
      if (!agentMessageEvent.answer) return null;

      return {
        id: uuidv4(),
        type: 'text',
        content: agentMessageEvent.answer,
      };
    }

    case ConversationEventType.AGENT_THOUGHT: {
      // Agent思考事件
      const agentThoughtEvent = event as AgentThoughtEvent;
      if (!agentThoughtEvent.tool) return null; // 只处理包含工具调用的思考

      return {
        id: uuidv4(),
        type: 'toolcall',
        status: 'running',
        inputs: {
          thought: agentThoughtEvent.thought,
          tool: agentThoughtEvent.tool,
          tool_input: agentThoughtEvent.tool_input,
        },
        outputs: agentThoughtEvent.observation,
      };
    }

    case ConversationEventType.MESSAGE_FILE: {
      // 文件消息事件
      const messageFileEvent = event as MessageFileEvent;
      const url = messageFileEvent.url || '';
      const fileName = url.split('/').pop()?.toLowerCase() || '';

      // 根据文件类型创建不同的内容块
      if (messageFileEvent.type?.startsWith('image/')) {
        // 图片文件
        return {
          id: uuidv4(),
          type: 'image',
          src: url,
          name: 'image',
        };
      }

      // 首先通过文件扩展名判断文件类型
      if (fileName.endsWith('.xlsx') || fileName.endsWith('.xls')) {
        // Excel文档 (基于文件名)
        return {
          id: uuidv4(),
          type: 'document_preview',
          documentType: 'excel',
          url: url,
        };
      }

      if (fileName.endsWith('.csv')) {
        // CSV文档 (基于文件名)
        return {
          id: uuidv4(),
          type: 'document_preview',
          documentType: 'csv',
          url: url,
        };
      }

      if (fileName.endsWith('.docx') || fileName.endsWith('.doc')) {
        // Word文档 (基于文件名)
        return {
          id: uuidv4(),
          type: 'document_preview',
          documentType: 'word',
          url: url,
        };
      }

      if (fileName.endsWith('.pdf')) {
        // PDF文档 (基于文件名)
        return {
          id: uuidv4(),
          type: 'document_preview',
          documentType: 'pdf',
          url: url,
        };
      }

      // 如果文件名不明确，则通过MIME类型判断
      const fileType = messageFileEvent.type || '';

      if (fileType.includes('pdf')) {
        // PDF文档
        return {
          id: uuidv4(),
          type: 'document_preview',
          documentType: 'pdf',
          url: url,
        };
      }

      if (
        fileType.includes('excel') ||
        fileType.includes('spreadsheet') ||
        fileType.includes('xlsx') ||
        fileType.includes('xls')
      ) {
        // Excel文档
        return {
          id: uuidv4(),
          type: 'document_preview',
          documentType: 'excel',
          url: url,
        };
      }

      if (fileType.includes('csv')) {
        // CSV文档
        return {
          id: uuidv4(),
          type: 'document_preview',
          documentType: 'csv',
          url: url,
        };
      }

      // 注意：这里的判断顺序很重要，确保Excel文件不会被错误识别为Word文档
      if (
        fileType.includes('word') ||
        (fileType.includes('document') && !fileType.includes('spreadsheet')) ||
        fileType.includes('docx') ||
        fileType.includes('doc')
      ) {
        // Word文档
        return {
          id: uuidv4(),
          type: 'document_preview',
          documentType: 'word',
          url: url,
        };
      }

      // 其他类型文件，使用文本块表示
      return {
        id: uuidv4(),
        type: 'text',
        content: `[文件: ${messageFileEvent.id || 'file'}](${url})`,
      };
    }
    case ConversationEventType.MESSAGE_END:
      return null;
    default:
      throw new Error(`未处理的事件类型: ${event.event}`);
  }
};

/**
 * 请求用户的麦克风访问权限
 */
export const requestMicrophoneAccess = async (): Promise<boolean> => {
  if ('mediaDevices' in navigator) {
    try {
      await navigator.mediaDevices.getUserMedia({ audio: true });
      return true;
    } catch (error) {
      console.warn('用户拒绝了麦克风权限:', error);
      return false;
    }
  }
  return false;
};

/**
 * 请求用户的摄像头访问权限
 */
export const requestCameraAccess = async (): Promise<boolean> => {
  if ('mediaDevices' in navigator) {
    try {
      await navigator.mediaDevices.getUserMedia({ video: true });
      return true;
    } catch (error) {
      console.warn('用户拒绝了摄像头权限:', error);
      return false;
    }
  }
  return false;
};
