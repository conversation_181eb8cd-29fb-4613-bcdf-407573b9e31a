import { z } from 'zod';
import { AgentDetailSchema, AgentSchema } from '../marketplace/schemas';
export const ContentBlockSchema = z.object({
  id: z.string(),
  type: z.enum(['text', 'image', 'file', 'document_preview', 'toolcall', 'mention', 'error']),
});

export type ContentBlock = z.infer<typeof ContentBlockSchema>;

// TextContentBlock Schema
export const TextContentBlockSchema = ContentBlockSchema.extend({
  type: z.literal('text'),
  content: z.string(),
});
export type TextContentBlock = z.infer<typeof TextContentBlockSchema>;

// ImageContentBlock Schema
export const ImageContentBlockSchema = ContentBlockSchema.extend({
  type: z.literal('image'),
  src: z.string(),
  name: z.string().optional(),
});
export type ImageContentBlock = z.infer<typeof ImageContentBlockSchema>;

// DocumentPreview内容块 - 用于预览PDF、Word、Excel或CSV文档
export const DocumentPreviewSchema = ContentBlockSchema.extend({
  type: z.literal('document_preview'),
  documentType: z.enum(['pdf', 'word', 'excel', 'csv']),
  url: z.string(),
});

export type DocumentPreviewContentBlock = z.infer<typeof DocumentPreviewSchema>;

// Toolcall内容块 - 用于显示工具调用状态
export const ToolcallSchema = ContentBlockSchema.extend({
  type: z.literal('toolcall'),
  inputs: z.record(z.any()).optional(),
  outputs: z.any().optional(),
  status: z.enum(['running', 'success', 'error']),
});

export type ToolcallContentBlock = z.infer<typeof ToolcallSchema>;

// Mention内容块 - 用于提及/引用某个Agent
export const MentionSchema = ContentBlockSchema.extend({
  type: z.literal('mention'),
  agentId: z.string(),
});

export type MentionContentBlock = z.infer<typeof MentionSchema>;

// Error内容块 - 用于显示错误信息
export const ErrorSchema = ContentBlockSchema.extend({
  type: z.literal('error'),
  msg: z.string(),
});

export type ErrorContentBlock = z.infer<typeof ErrorSchema>;
export const LoadingContentBlockSchema = ContentBlockSchema.extend({
  type: z.literal('loading'),
});
export type LoadingContentBlock = z.infer<typeof LoadingContentBlockSchema>;
export const MessageSchema = z.object({
  id: z.string(),
  conversation_id: z.string().optional(),
  parent_id: z.string().optional(),
  created_at: z.number(),
  created_at_str: z.string(),
  role: z.enum(['user', 'assistant', 'timedivider']),
  content: z.array(
    z.discriminatedUnion('type', [
      TextContentBlockSchema,
      ImageContentBlockSchema,
      DocumentPreviewSchema,
      ToolcallSchema,
      MentionSchema,
      ErrorSchema,
      LoadingContentBlockSchema,
    ]),
  ),
  created_at_label: z.string().optional(),
});

export type Message = z.infer<typeof MessageSchema>;
export const ConversationSchema = z.object({
  id: z.string(),
  name: z.string().optional(),
  created_at: z.number().optional(),
  updated_at: z.number().optional(),
  agent: AgentSchema,
});
export type Conversation = z.infer<typeof ConversationSchema>;
export const ChatContextTypeSchema = z.object({
  /**
   * 输入框背景色
   */
  inputBackgroundColor: z.string(),
  /**
   * 输入框边框色
   */
  borderColor: z.string(),
  /**
   * 图标颜色
   */
  iconColor: z.string(),
  /**
   * 图标悬停颜色
   */
  iconHoverColor: z.string(),
  /**
   * 当前输入的消息 - now an array of ContentItems
   */
  inputMessage: MessageSchema.optional(),
  /**
   * 获取按日期分组的会话列表
   */
  getConversationsByDate: z.function().returns(
    z.promise(
      z.array(
        z.object({
          id: z.string(),
          title: z.string(),
          list: z.array(ConversationSchema),
        }),
      ),
    ),
  ),
  /**
   * 删除会话
   */
  deleteConversation: z.function().args(z.string()).returns(z.promise(z.boolean())),

  /**
   * 重命名会话
   */
  renameConversation: z.function().args(z.string(), z.string()).returns(z.promise(z.boolean())),

  /**
   * 设置当前输入的消息
   */
  setInputMessage: z.function().args(MessageSchema.optional()).returns(z.void()),
  /**
   * 是否正在拖拽
   */
  isDragging: z.boolean(),
  /**
   * 设置是否正在拖拽
   */
  setIsDragging: z.function().args(z.boolean()).returns(z.void()),
  /**
   * 是否正在发送消息
   */
  isMessageSending: z.boolean(),
  /**
   * 当前聊天的智能体
   */
  agent: AgentSchema.optional(),
  /**
   * 设置当前聊天的智能体
   */
  setAgent: z.function().args(AgentSchema.optional()).returns(z.void()),
  agentDetail: AgentDetailSchema.optional(),
  /**
   * 当前选中的会话
   */
  conversation: ConversationSchema.optional(),

  /**
   * 设置当前会话
   */
  setConversation: z.function().args(ConversationSchema.optional()).returns(z.void()),

  /**
   * 任务ID
   */
  taskId: z.string().optional(),
  /**
   * 设置任务ID
   */
  setTaskId: z.function().args(z.string().optional()).returns(z.void()),

  /**
   * 消息列表
   */
  messages: z.array(MessageSchema),

  /**
   * 设置消息列表
   */
  setMessages: z
    .function()
    .args(z.array(MessageSchema).or(z.function().args(z.array(MessageSchema)).returns(z.array(MessageSchema))))
    .returns(z.void()),

  /**
   * 发送消息
   */
  sendMessageMutation: z.function().args(MessageSchema).returns(z.void()),

  /**
   * 停止消息生成
   */
  stopMessageMutation: z.function().returns(z.promise(z.void())),
});

export type ChatContextType = z.infer<typeof ChatContextTypeSchema>;
export const OperationResultSchema = z.object({
  /**
   * 操作结果
   * @default "success"
   */
  result: z.string().default('success'),
});

export type OperationResult = z.infer<typeof OperationResultSchema>;
