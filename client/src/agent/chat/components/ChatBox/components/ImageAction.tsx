import React from 'react';
import { ActionIcon } from '@mantine/core';
import { RiImage2Line } from 'react-icons/ri';
import { useChatContext } from '../../../contexts/ChatContext';
import { ChatInputActionProps } from '../types';

const ImageAction: React.FC<ChatInputActionProps> = ({ 'aria-label': ariaLabel = 'Upload image' }) => {
  const { iconColor } = useChatContext();

  const handleClick = () => {
    // TODO: 实现图片上传点击逻辑
    console.log('Image action clicked');
  };

  return (
    <ActionIcon
      variant="subtle" // Desktop.tsx 中使用的是 subtle
      aria-label={ariaLabel}
      color="gray" // Desktop.tsx 中使用的是 gray
      size="input-sm"
      radius={10}
      onClick={handleClick}
    >
      <RiImage2Line size={18} color={iconColor} />
    </ActionIcon>
  );
};

ImageAction.displayName = 'Chatbox.ImageAction';

export default ImageAction;
