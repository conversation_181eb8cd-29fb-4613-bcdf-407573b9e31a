import React from 'react';
import { ActionIcon } from '@mantine/core';
import { FiPaperclip } from 'react-icons/fi';
import { useChatContext } from '../../../contexts/ChatContext';
import { ChatInputActionProps } from '../types';

const AttachmentAction: React.FC<ChatInputActionProps> = ({ 'aria-label': ariaLabel = 'Attach file' }) => {
  const { iconColor } = useChatContext();

  const handleClick = () => {
    // TODO: 实现附件点击逻辑，例如打开文件选择对话框
    console.log('Attachment action clicked');
  };

  return (
    <ActionIcon variant="default" aria-label={ariaLabel} size="input-md" radius={10} onClick={handleClick}>
      <FiPaperclip size={16} color={iconColor} />
    </ActionIcon>
  );
};

AttachmentAction.displayName = 'Chatbox.AttachmentAction';

export default AttachmentAction;
