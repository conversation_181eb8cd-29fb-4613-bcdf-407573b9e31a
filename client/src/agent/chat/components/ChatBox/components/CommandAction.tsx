import React from 'react';
import { Button, Group, Text, Menu, ScrollArea } from '@mantine/core';
import { RiApps2AiLine, RiSendPlaneFill } from 'react-icons/ri';
import { useChatContext } from '../../../contexts/ChatContext';
import { CommandActionProps } from '../types';
import { MessageSchema } from '../../../schemas';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { useLocation, useNavigate } from 'react-router-dom';

const CommandAction: React.FC<CommandActionProps> = () => {
  const { agentDetail, iconColor, inputBackgroundColor, borderColor, sendMessageMutation, conversation } = useChatContext();
  const location = useLocation();
  const navigate = useNavigate();
  // 从智能体详情中获取建议问题列表
  const suggestedQuestions = agentDetail?.model_config?.suggested_questions || [];

  // 处理命令选择
  const handleCommandSelect = (command: string) => {
    // 创建一个新的消息对象
    const message = MessageSchema.parse({
      id: uuidv4(),
      conversation_id: conversation?.id,
      created_at: Date.now(),
      created_at_str: dayjs().format('YYYY-MM-DD HH:mm:ss SSSS'),
      role: 'user',
      content: [
        {
          id: uuidv4(),
          type: 'text',
          content: command,
        },
      ],
    });

    // 发送消息
    sendMessageMutation(message);

    if (location.pathname.endsWith('/chat')) {
      navigate(`/chat/${agentDetail?.id}`);
    }
  };

  // 如果没有建议问题，则不显示命令菜单
  if (!suggestedQuestions || suggestedQuestions.length === 0) {
    return null;
  }

  return (
    <Menu shadow="md" width={200} position="top-start">
      <Menu.Target>
        <Button variant="default" px={8} size="md" radius={10}>
          <Group gap={4}>
            <RiApps2AiLine size={18} color={iconColor} />
            <Text c={iconColor} fz="sm">
              命令
            </Text>
          </Group>
        </Button>
      </Menu.Target>

      <Menu.Dropdown
        style={{
          width: 240,
          padding: '12px',
          borderRadius: '10px',
          border: `1px solid ${borderColor}`,
          boxShadow: '0px 4px 24px rgba(0, 0, 0, 0.1)',
          backgroundColor: inputBackgroundColor,
        }}
      >
        <ScrollArea>
          {suggestedQuestions.map((command: string, index: number) => (
            <Menu.Item key={index} onClick={() => handleCommandSelect(command)}>
              <Group align="center" gap={8} justify="space-between">
                <Text c={iconColor} fz="sm" style={{ flex: 1 }}>
                  {command}
                </Text>
                <RiSendPlaneFill size={16} color={iconColor} />
              </Group>
            </Menu.Item>
          ))}
        </ScrollArea>
      </Menu.Dropdown>
    </Menu>
  );
};

CommandAction.displayName = 'Chatbox.CommandAction';

export default CommandAction;
