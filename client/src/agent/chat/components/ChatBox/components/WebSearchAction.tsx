import React from 'react';
import { Button, Group, Text } from '@mantine/core';
import { RiGlobalLine } from 'react-icons/ri';
import { useChatContext } from '../../../contexts/ChatContext';
import { ChatInputActionProps } from '../types';

const WebSearchAction: React.FC<ChatInputActionProps> = () => {
  const { iconColor } = useChatContext();

  const handleClick = () => {
    // TODO: 实现联网搜索点击逻辑
    console.log('Web Search action clicked');
  };

  return (
    <Button variant="default" px={8} size="md" radius={10} onClick={handleClick}>
      <Group gap={4}>
        <RiGlobalLine size={18} color={iconColor} />
        <Text c={iconColor} fz="sm">
          联网搜索
        </Text>
      </Group>
    </Button>
  );
};

WebSearchAction.displayName = 'Chatbox.WebSearchAction';

export default WebSearchAction;
