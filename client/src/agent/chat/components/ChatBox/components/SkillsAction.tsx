import React from 'react';
import { Button, Group, Text, Menu } from '@mantine/core';
import { RiApps2AiLine, RiAiGenerateText, RiImageAiLine, RiVideoAiLine, RiVoiceAiLine } from 'react-icons/ri';
import { useChatContext } from '../../../contexts/ChatContext';
import { ChatInputSkillsActionProps } from '../types'; // 使用特定类型

const SkillsAction: React.FC<ChatInputSkillsActionProps> = () => {
  const { iconColor, inputBackgroundColor, borderColor } = useChatContext();

  // TODO: 技能菜单项的点击处理逻辑
  const handleSkillSelect = (skill: string) => {
    console.log(`Skill selected: ${skill}`);
  };

  return (
    <Menu shadow="md" width={200} position="top-start">
      <Menu.Target>
        <Button variant="default" px={8} size="md" radius={10}>
          <Group gap={4}>
            <RiApps2AiLine size={18} color={iconColor} />
            <Text c={iconColor} fz="sm">
              技能
            </Text>
          </Group>
        </Button>
      </Menu.Target>

      <Menu.Dropdown
        style={{
          width: 240,
          padding: '12px',
          borderRadius: '10px',
          border: `1px solid ${borderColor}`,
          boxShadow: '0px 4px 24px rgba(0, 0, 0, 0.1)',
          backgroundColor: inputBackgroundColor,
        }}
      >
        <Menu.Item onClick={() => handleSkillSelect('write')}>
          <Group align="center" gap={8}>
            <RiAiGenerateText size={16} color={iconColor} />
            <Text c={iconColor} fz="sm">
              帮我写作
            </Text>
          </Group>
        </Menu.Item>
        <Menu.Item onClick={() => handleSkillSelect('image')}>
          <Group align="center" gap={8}>
            <RiImageAiLine size={16} color={iconColor} />
            <Text c={iconColor} fz="sm">
              图像生成
            </Text>
          </Group>
        </Menu.Item>
        <Menu.Item onClick={() => handleSkillSelect('video')}>
          <Group align="center" gap={8}>
            <RiVideoAiLine size={16} color={iconColor} />
            <Text c={iconColor} fz="sm">
              视频生成
            </Text>
          </Group>
        </Menu.Item>
        <Menu.Item onClick={() => handleSkillSelect('meeting')}>
          <Group align="center" gap={8}>
            <RiVoiceAiLine size={16} color={iconColor} />
            <Text c={iconColor} fz="sm">
              记录会议
            </Text>
          </Group>
        </Menu.Item>
      </Menu.Dropdown>
    </Menu>
  );
};

SkillsAction.displayName = 'Chatbox.SkillsAction';

export default SkillsAction;
