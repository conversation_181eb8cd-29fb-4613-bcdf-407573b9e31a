import React from 'react';
import { Button, Group, Text } from '@mantine/core';
import { RiReactjsLine } from 'react-icons/ri';
import { useChatContext } from '../../../contexts/ChatContext';
import { ChatInputActionProps } from '../types';

const DeepThinkAction: React.FC<ChatInputActionProps> = () => {
  const { iconColor } = useChatContext();

  const handleClick = () => {
    // TODO: 实现深度思考点击逻辑
    console.log('Deep Think action clicked');
  };

  return (
    <Button variant="default" px={8} size="md" radius={10} onClick={handleClick}>
      <Group gap={4}>
        <RiReactjsLine size={18} color={iconColor} />
        <Text c={iconColor} fz="sm">
          深度思考
        </Text>
      </Group>
    </Button>
  );
};

DeepThinkAction.displayName = 'Chatbox.DeepThinkAction';

export default DeepThinkAction;
