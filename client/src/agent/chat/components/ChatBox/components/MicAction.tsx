import React, { useState } from 'react';
import { ActionIcon } from '@mantine/core';
import { notifications } from '@mantine/notifications';
import { RiMicLine } from 'react-icons/ri';
import { motion } from 'motion/react';
import { useChatContext } from '../../../contexts/ChatContext';
import { ChatInputActionProps } from '../types';
import { transcribeAudio } from '../../../api';
import { v4 as uuidv4 } from 'uuid';
import dayjs from 'dayjs';
import { MessageSchema } from '../../../schemas';

// 定义录音器接口
interface RecordingInterface {
  start: () => void;
  stop: () => Promise<void>;
}

const MicAction: React.FC<ChatInputActionProps> = ({ 'aria-label': ariaLabel = 'Record voice' }) => {
  const { iconColor, setInputMessage, conversation } = useChatContext();
  const [isRecording, setIsRecording] = useState(false);
  const [recorder, setRecorder] = useState<RecordingInterface | null>(null);

  // 录音逻辑 - 完全使用老版本代码
  const recordAudio = (): Promise<RecordingInterface | undefined> =>
    new Promise((resolve) => {
      try {
        navigator.mediaDevices
          .getUserMedia({ audio: true })
          .then((stream) => {
            const mediaRecorder = new MediaRecorder(stream);
            const audioChunks: Blob[] = [];

            mediaRecorder.addEventListener('dataavailable', (event) => {
              audioChunks.push(event.data);
            });

            const start = () => {
              audioChunks.length = 0;
              mediaRecorder.start();
              setIsRecording(true);
            };

            const stop = (): Promise<void> =>
              new Promise<void>((resolve) => {
                mediaRecorder.addEventListener('stop', () => {
                  const audioBlob = new Blob(audioChunks);
                  handleVoiceData(audioBlob);
                  setIsRecording(false);
                  resolve();
                });
                mediaRecorder.stop();
              });

            resolve({ start, stop });
          })
          .catch((error) => {
            console.error('麦克风访问失败:', error);
            resolve(undefined);
          });
      } catch (error) {
        console.error('麦克风访问失败:', error);
        resolve(undefined);
      }
    });

  // 处理语音输入 - 完全使用老版本代码
  const startRecording = async () => {
    const recording = await recordAudio();
    if (recording) {
      setRecorder(recording);
      recording.start();
    }
  };

  const stopRecording = async () => {
    if (recorder) {
      await recorder.stop();
      setRecorder(null);
    }
  };

  const handleVoiceData = async (audioBlob: Blob) => {
    try {
      // 创建音频文件 - 使用老版本代码
      const audioFile = new File([audioBlob], 'recording.wav', {
        type: 'audio/wav',
      });

      // 发送到后端进行转换
      const result = await transcribeAudio(audioFile);

      if (result.data) {
        // 将转换后的文本添加到输入框
        const newMessage = MessageSchema.parse({
          id: uuidv4(),
          conversation_id: conversation?.id,
          created_at: Date.now(),
          created_at_str: dayjs().format('YYYY-MM-DD HH:mm:ss SSSS'),
          role: 'user',
          content: [
            {
              id: `text-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
              type: 'text',
              content: result.data,
            },
          ],
        });

        setInputMessage(newMessage);
      }
    } catch (error) {
      console.error('语音转文字失败:', error);
      notifications.show({
        title: '语音转文字失败',
        message: error instanceof Error ? error.message : '未知错误',
        color: 'red',
      });
    }
  };

  return (
    <ActionIcon
      variant="subtle"
      aria-label={ariaLabel}
      color={isRecording ? 'red' : 'gray'}
      size="input-sm"
      radius={10}
      onMouseDown={startRecording}
      onMouseUp={stopRecording}
      onTouchStart={startRecording}
      onTouchEnd={stopRecording}
    >
      <motion.div
        animate={{
          scale: isRecording ? [1, 1.1] : 1,
        }}
        transition={{
          duration: 0.5,
          repeat: isRecording ? Infinity : 0,
          repeatType: 'reverse',
        }}
      >
        <RiMicLine size={18} color={isRecording ? '#ff4d4f' : iconColor} />
      </motion.div>
    </ActionIcon>
  );
};

MicAction.displayName = 'Chatbox.MicAction';

export default MicAction;
