import React from 'react';
import { ActionIcon } from '@mantine/core';
import { RiArrowUpLine } from 'react-icons/ri';
import { useChatContext } from '../../../contexts/ChatContext';
import { useLocation, useNavigate } from 'react-router-dom';

interface SendActionProps {
  'aria-label'?: string;
}

const SendAction: React.FC<SendActionProps> = ({ 'aria-label': ariaLabel = 'Send Message' }) => {
  const { agent } = useChatContext();
  const location = useLocation();
  const navigate = useNavigate();

  const handleSend = () => {
    // 检查是否在智能体构建器环境中
    const isInBuilder = location.pathname.includes('/agent/builder');

    if (agent) {
      if (isInBuilder) {
        // 在构建器环境中，先触发发布，然后发送消息
        document.dispatchEvent(new CustomEvent('builder:publish-and-send'));
      } else {
        // 在普通聊天页面，直接发送消息
        document.dispatchEvent(new CustomEvent('chatbox:send'));
        if (location.pathname.endsWith('/chat')) {
          navigate(`/chat/${agent.id}`);
        }
      }
    }
  };

  return (
    <ActionIcon
      variant="filled"
      aria-label={ariaLabel}
      size="input-md"
      radius={'50%'}
      color="rgba(73, 81, 235, 1)" // Desktop.tsx 中的颜色
      onClick={handleSend}
      disabled={!agent} // 当没有选择智能体时禁用
    >
      <RiArrowUpLine size={24} color="white" />
    </ActionIcon>
  );
};

SendAction.displayName = 'Chatbox.SendAction';

export default SendAction;
