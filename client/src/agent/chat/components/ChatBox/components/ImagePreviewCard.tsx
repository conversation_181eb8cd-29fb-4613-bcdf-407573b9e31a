import React from 'react';
import { Box, ActionIcon, Text, Group, Image } from '@mantine/core';
import { RiCloseCircleLine } from 'react-icons/ri';

interface ImagePreviewCardProps {
  id: string; // 用于移除操作的唯一标识
  src: string;
  name: string;
  onRemove: (id: string) => void;
  // 可以根据需要添加其他属性，例如上传状态
}

const ImagePreviewCard: React.FC<ImagePreviewCardProps> = ({ id, src, name, onRemove }) => {
  return (
    <Box
      component="span" // 使用 span 以便更好地融入文本流，但需要配合 contentEditable div 的处理
      style={{
        display: 'inline-flex', // 使用 inline-flex 来对齐内部元素
        alignItems: 'center',
        padding: '4px 8px',
        margin: '2px 4px', // 上下左右边距
        border: '1px solid var(--mantine-color-gray-4)', // 使用 Mantine 变量
        borderRadius: 'var(--mantine-radius-sm)',
        backgroundColor: 'var(--mantine-color-gray-0)',
        verticalAlign: 'middle', // 确保与文本垂直对齐
        maxWidth: '200px', // 限制最大宽度
      }}
      // data-id 和 data-type 用于在 contentEditable div 中识别和处理
      data-id={id}
      data-type="image-preview"
      contentEditable={false} // 卡片本身不可编辑
    >
      <Group gap="xs" align="center" wrap="nowrap">
        <Image src={src} alt={name} w={32} h={32} fit="cover" radius="sm" />
        <Text size="sm" truncate style={{ flexGrow: 1 }}>
          {name}
        </Text>
        <ActionIcon
          size="sm"
          variant="transparent"
          color="gray"
          onClick={() => onRemove(id)}
          title="Remove image"
          style={{ cursor: 'pointer' }}
        >
          <RiCloseCircleLine size={16} />
        </ActionIcon>
      </Group>
    </Box>
  );
};

ImagePreviewCard.displayName = 'ImagePreviewCard';

export default ImagePreviewCard;
