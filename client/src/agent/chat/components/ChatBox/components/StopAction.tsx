import React from 'react';
import { ActionIcon } from '@mantine/core';
import { RiStopLine } from 'react-icons/ri';
import { useChatContext } from '../../../contexts/ChatContext';

interface StopActionProps {
  'aria-label'?: string;
}

const StopAction: React.FC<StopActionProps> = ({ 'aria-label': ariaLabel = 'Stop Generation' }) => {
  const { agent, taskId, isMessageSending, stopMessageMutation } = useChatContext();

  const handleStop = async () => {
    if (!agent || !taskId || !isMessageSending) {
      return;
    }

    try {
      await stopMessageMutation();
    } catch (error) {
      console.error('停止任务失败:', error);
    }
  };

  return (
    <ActionIcon
      variant="filled"
      aria-label={ariaLabel}
      size="input-md"
      radius={'50%'}
      color="red" // 使用红色表示停止
      onClick={handleStop}
      disabled={!agent || !taskId || !isMessageSending}
    >
      <RiStopLine size={24} color="white" />
    </ActionIcon>
  );
};

StopAction.displayName = 'Chatbox.StopAction';

export default StopAction;
