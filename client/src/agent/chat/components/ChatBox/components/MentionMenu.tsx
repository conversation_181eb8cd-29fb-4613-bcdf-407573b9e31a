import React, { useState, useEffect, useRef } from 'react';
import { Box, Paper, Text, Group, Avatar, Loader, Stack } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import { getOwnedAgents } from '~/agent/marketplace/api';
import { Agent } from '~/agent/marketplace/schemas';

interface MentionMenuProps {
  visible: boolean;
  onSelect: (agent: Agent) => void;
  onClose: () => void;
  searchText?: string;
}

/**
 * 智能体提及菜单组件
 * 用于在聊天框中@智能体时显示可选的智能体列表
 */
const MentionMenu: React.FC<MentionMenuProps> = ({ visible, onSelect, onClose, searchText = '' }) => {
  const [selectedIndex, setSelectedIndex] = useState(0);
  const menuRef = useRef<HTMLDivElement>(null);

  // 获取用户拥有的智能体列表
  const { data: ownedAgentsResponse, isLoading } = useQuery({
    queryKey: ['ownedAgents'],
    queryFn: getOwnedAgents,
    enabled: visible, // 只在菜单可见时加载数据
    staleTime: 1000 * 60 * 5, // 5分钟内不重新获取
  });

  const ownedAgents = ownedAgentsResponse?.data || [];

  // 根据搜索文本过滤智能体
  const filteredAgents = searchText
    ? ownedAgents.filter((agent) => agent.name.toLowerCase().includes(searchText.toLowerCase()))
    : ownedAgents;

  // 当过滤结果变化时，重置选中索引
  useEffect(() => {
    setSelectedIndex(0);
  }, [filteredAgents.length]);

  // 处理键盘导航
  useEffect(() => {
    if (!visible) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      if (!visible) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex((prev) => (prev < filteredAgents.length - 1 ? prev + 1 : prev));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex((prev) => (prev > 0 ? prev - 1 : 0));
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredAgents.length > 0) {
            onSelect(filteredAgents[selectedIndex]);
          }
          break;
        case 'Escape':
          e.preventDefault();
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [visible, filteredAgents, selectedIndex, onSelect, onClose]);

  // 点击外部关闭菜单
  useEffect(() => {
    if (!visible) return;

    const handleClickOutside = (e: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(e.target as Node)) {
        onClose();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [visible, onClose]);

  if (!visible) return null;

  return (
    <Paper
      ref={menuRef}
      shadow="md"
      p="md"
      radius="md"
      style={{
        position: 'absolute',
        bottom: '100%',
        left: 0,
        width: '300px',
        maxHeight: '300px',
        overflowY: 'auto',
        zIndex: 1000,
      }}
    >
      <Text size="xs" c="dimmed" mb="xs" ta="center">
        选择要@的智能体
      </Text>

      {isLoading ? (
        <Box py="md" ta="center">
          <Loader size="sm" />
          <Text size="sm" mt="xs">
            加载中...
          </Text>
        </Box>
      ) : filteredAgents.length === 0 ? (
        <Text size="sm" ta="center" py="md">
          {searchText ? `没有找到匹配"${searchText}"的智能体` : '您没有可用的智能体'}
        </Text>
      ) : (
        <Stack gap="xs">
          {filteredAgents.map((agent, index) => (
            <Box
              key={agent.id}
              p="xs"
              style={{
                cursor: 'pointer',
                borderRadius: '4px',
                backgroundColor: selectedIndex === index ? 'rgba(0, 0, 0, 0.05)' : 'transparent',
              }}
              onClick={() => onSelect(agent)}
              onMouseEnter={() => setSelectedIndex(index)}
            >
              <Group gap="sm">
                <Avatar src={agent.icon} radius="xl" size="sm" alt={agent.name} />
                <Text size="sm">{agent.name}</Text>
              </Group>
            </Box>
          ))}
        </Stack>
      )}
    </Paper>
  );
};

export default MentionMenu;
