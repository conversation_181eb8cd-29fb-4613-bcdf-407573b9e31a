import React, { useState } from 'react';
import ReactMarkdown from 'react-markdown';
import rehypeRaw from 'rehype-raw';
import remarkGfm from 'remark-gfm';
import { useRef } from 'react';
import type { Components } from 'react-markdown';
import {
  Box,
  Group,
  Title,
  Text,
  Image,
  ActionIcon,
  List,
  Anchor,
  Code,
  Blockquote,
  Table,
  Space,
  useMantineTheme,
} from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiDownload2Line, RiEyeLine, RiFileTextLine, RiFilePdfLine, RiFileExcelLine, RiFileWordLine } from 'react-icons/ri';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { oneDark, prism } from 'react-syntax-highlighter/dist/esm/styles/prism';
import Lightbox from 'yet-another-react-lightbox';
import 'yet-another-react-lightbox/styles.css';
import { DocumentPreviewContentBlock, ImageContentBlock, Message, TextContentBlock } from '~/agent/chat/schemas';
import { motion } from 'motion/react';

interface MarkdownRendererProps {
  content: Message['content'];
}
export const contentBlocksToString = (content: Message['content']) => {
  const md = content
    .map((block) => {
      switch (block.type) {
        case 'text':
          return (block as TextContentBlock).content;
        case 'document_preview': {
          const docPreview = block as DocumentPreviewContentBlock;
          return `<doc-preview data-document-type="${docPreview.documentType}" data-url="${docPreview.url}" > </doc-preview>`;
        }
        case 'loading':
          return '<loading></loading>';
        case 'image':
          return `<img-preview data-src="${(block as ImageContentBlock).src}" data-alt="${(block as ImageContentBlock).name}"  > </img-preview>`;
        default:
          return '';
      }
    })
    .join('');
  //console.log('转换后的md:', md);
  return md;
};
const MarkdownRenderer = React.memo(({ content }: MarkdownRendererProps) => {
  return (
    <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }}>
      <MarkdownRendererInternal content={content} />
    </motion.div>
  );
});

function MarkdownRendererInternal({ content }: MarkdownRendererProps) {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  // 图片预览状态
  const [lightboxOpen, setLightboxOpen] = useState(false);
  const [currentImage, setCurrentImage] = useState<string | null>(null);

  const robotMsgTextColor = isDark ? 'rgba(255, 255, 255, 0.85)' : 'rgba(0, 0, 0, 0.85)';
  const robotMsgTitleColor = isDark ? 'white' : 'black';
  const robotMsgCodeColor = isDark ? theme.colors.dark[6] : theme.colors.gray[2];
  const tableHeadBgColor = isDark ? theme.colors.dark[6] : theme.colors.gray[1];
  const tableHeadTextColor = isDark ? 'white' : 'black';
  const tableBgColor = isDark ? 'black' : 'white';
  const tableBorderColor = isDark ? theme.colors.dark[5] : theme.colors.gray[2];
  // const codeAreaHeadBgColor = isDark ? theme.colors.gray[8] : 'rgba(243, 244, 246, 1)';
  // const codeAreaIconColor = isDark ? 'white' : 'black';
  const codeAreaBgColor = isDark ? 'rgba(40, 44, 52, 1)' : theme.colors.gray[0];
  const codeAreaBorderColor = isDark ? theme.colors.gray[7] : theme.colors.gray[2];

  const markdownRef = useRef<HTMLDivElement>(null);

  // 下载图片函数
  const downloadImage = async (imageUrl: string) => {
    try {
      // 获取图片数据
      const response = await fetch(imageUrl);
      const blob = await response.blob();

      // 创建一个临时URL
      const blobUrl = URL.createObjectURL(blob);

      // 创建下载链接
      const link = document.createElement('a');
      link.href = blobUrl;

      // 设置文件名
      const filename = imageUrl.split('/').pop() || 'image';
      link.download = filename;

      // 模拟点击下载
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      setTimeout(() => URL.revokeObjectURL(blobUrl), 100);
    } catch (error) {
      console.error('下载图片失败:', error);
    }
  };

  // Apply styles to first child after rendering
  // useEffect(() => {
  //     if (markdownRef.current) {
  //         const firstChild = markdownRef.current.querySelector('.prose > *:first-child');
  //         if (firstChild) {
  //             (firstChild as HTMLElement).style.marginTop = '0';
  //         }
  //     }
  // }, [content]);

  // Define custom components for ReactMarkdown
  const components: Components = {
    h1: ({ children }) => (
      <Title order={1} fz={26} fw={600} lh="36px" mb={12} c={robotMsgTitleColor}>
        {children}
      </Title>
    ),
    h2: ({ children }) => (
      <Title order={2} fz={20} fw={700} lh="32px" mt={40} mb={12} c={robotMsgTitleColor}>
        {children}
      </Title>
    ),
    h3: ({ children }) => (
      <Title order={3} fz={18} fw={700} lh="28px" mt={28} mb={12} c={robotMsgTitleColor}>
        {children}
      </Title>
    ),
    h4: ({ children }) => (
      <Title order={4} fz={16} fw={700} lh="28px" mt={24} mb={8} c={robotMsgTitleColor}>
        {children}
      </Title>
    ),
    h5: ({ children }) => (
      <Title order={5} fz={16} fw={700} lh="28px" mt={24} mb={8} c={robotMsgTitleColor}>
        {children}
      </Title>
    ),
    h6: ({ children }) => (
      <Title order={6} fz={16} fw={700} lh="28px" mt={24} mb={8} c={robotMsgTitleColor}>
        {children}
      </Title>
    ),
    p: ({ children }) => (
      <Text component="div" fz={16} fw={400} lh="28px" c={robotMsgTextColor}>
        {children}
      </Text>
    ),
    ul: ({ children }) => (
      <List
        listStyleType="disc"
        my={8}
        styles={{
          root: {
            // '--list-fz': 16,
            '--list-lh': '28px',
          },
          item: {
            marginTop: '4px',
          },
          itemLabel: {
            color: robotMsgTextColor,
          },
        }}
      >
        {children}
      </List>
    ),
    ol: ({ children }) => (
      <List
        type="ordered"
        listStyleType="decimal"
        my={8}
        styles={{
          root: {
            // '--list-fz': 16,
            '--list-lh': '28px',
          },
          item: {
            marginTop: '4px',
          },
          itemWrapper: {
            paddingLeft: '4px',
          },
          itemLabel: {
            color: robotMsgTextColor,
          },
        }}
      >
        {children}
      </List>
    ),
    li: ({ children }) => <List.Item>{children}</List.Item>,
    a: ({ children, href }) => (
      <Anchor href={href} target="_blank" c="rgba(0, 102, 255, 1)">
        {children}
      </Anchor>
    ),
    img: ({ src, alt }) => (
      <Box className="relative rounded-[8px] overflow-hidden">
        <Image
          src={src}
          alt={alt}
          style={{ cursor: 'pointer' }}
          onClick={() => {
            if (src) {
              setCurrentImage(src);
              setLightboxOpen(true);
            }
          }}
        />
        <Group className="absolute top-[8px] right-[8px]" gap={8}>
          <ActionIcon
            variant="white"
            aria-label="Preview"
            color="rgba(25, 33, 61, 1)"
            style={{
              boxShadow: '0px 0px 2px rgba(155, 32, 47, 0.1)',
            }}
            onClick={() => {
              if (src) {
                setCurrentImage(src);
                setLightboxOpen(true);
              }
            }}
          >
            <RiEyeLine size={16} />
          </ActionIcon>
          <ActionIcon
            variant="white"
            aria-label="Download"
            color="rgba(25, 33, 61, 1)"
            style={{
              boxShadow: '0px 0px 2px rgba(155, 32, 47, 0.1)',
            }}
            onClick={() => downloadImage(src || '')}
          >
            <RiDownload2Line size={16} />
          </ActionIcon>
        </Group>
      </Box>
    ),
    br: () => <Space h="1em" />,
    code: ({ className, children }) => {
      // 处理行内代码 <code>
      if (!className) {
        return (
          <Code fz={16} color={robotMsgCodeColor}>
            {children}
          </Code>
        );
      }

      // 处理代码块 <pre><code>
      const match = /language-(\w+)/.exec(className || '');

      return (
        <SyntaxHighlighter
          language={match?.[1] || 'text'}
          style={isDark ? oneDark : prism}
          showLineNumbers
          customStyle={{
            margin: 0,
            background: 'transparent',
            fontSize: theme.fontSizes.sm,
            fontFamily: theme.fontFamilyMonospace,
          }}
        >
          {String(children).replace(/\n$/, '')}
        </SyntaxHighlighter>
      );
    },
    pre: ({ children }) => {
      //FIXME 启用这个会导致最大更新深度错误
      // 从子元素中提取语言类型
      // const language =
      //   React.Children.toArray(children).reduce<string | null>((lang, child) => {
      //     if (lang) return lang;
      //     if (
      //       React.isValidElement(child) &&
      //       child.props &&
      //       typeof child.props === 'object' &&
      //       'className' in child.props &&
      //       typeof child.props?.className === 'string'
      //     ) {
      //       const match = child.props.className.match(/language-(\w+)/);
      //       return match?.[1] || null;
      //     }
      //     return null;
      //   }, null) || 'Code';

      return (
        <Box
          style={{
            borderRadius: '8px',
            background: codeAreaBgColor,
            border: `1px solid ${codeAreaBorderColor}`,
            overflow: 'hidden',
          }}
        >
          {/* <Group h={32} px={16} align="center" justify="space-between" bg={codeAreaHeadBgColor}>
            <Text>{language}</Text>
            <Group gap={8}>
              <Tooltip arrowSize={6} label="复制" withArrow position="top">
                <ActionIcon
                  variant="subtle"
                  aria-label="Copy"
                  color="gray"
                  size="sm"
                  onClick={() => {
                    // 从子元素中提取代码内容
                    let codeContent = '';

                    // 遍历子元素获取实际代码内容
                    React.Children.forEach(children, (child) => {
                      if (React.isValidElement(child) && child.props) {
                        // 如果是SyntaxHighlighter组件
                        const childProps = child.props as React.HTMLProps<HTMLElement>;
                        if (childProps.children) {
                          codeContent =
                            typeof childProps.children === 'string'
                              ? childProps.children
                              : String(childProps.children).replace(/\n$/, '');
                        }
                      }
                    });

                    // 如果没有找到代码内容，尝试直接从第一个子元素获取
                    if (!codeContent) {
                      const firstChild = React.Children.toArray(children)[0];
                      if (firstChild && typeof firstChild === 'string') {
                        codeContent = firstChild;
                      } else if (firstChild && typeof firstChild === 'object') {
                        codeContent = JSON.stringify(firstChild);
                      }
                    }

                    navigator.clipboard
                      .writeText(codeContent)
                      .then(() => {
                        notifications.show({
                          title: '复制成功',
                          message: '代码已复制到剪贴板',
                          color: 'green',
                        });
                      })
                      .catch((error) => {
                        console.error('复制失败:', error);
                        notifications.show({
                          title: '复制失败',
                          message: '无法复制代码',
                          color: 'red',
                        });
                      });
                  }}
                >
                  <RiFileCopyLine size={12} color={codeAreaIconColor} />
                </ActionIcon>
              </Tooltip>
              <Tooltip arrowSize={6} label="下载" withArrow position="top">
                <ActionIcon
                  variant="subtle"
                  aria-label="Download"
                  color="gray"
                  size="sm"
                  onClick={() => {
                    // 从子元素中提取代码内容
                    let codeContent = '';

                    // 遍历子元素获取实际代码内容
                    React.Children.forEach(children, (child) => {
                      if (React.isValidElement(child) && child.props) {
                        // 如果是SyntaxHighlighter组件
                        const childProps = child.props as React.HTMLProps<HTMLElement>;
                        if (childProps.children) {
                          codeContent =
                            typeof childProps.children === 'string'
                              ? childProps.children
                              : String(childProps.children).replace(/\n$/, '');
                        }
                      }
                    });

                    // 如果没有找到代码内容，尝试直接从第一个子元素获取
                    if (!codeContent) {
                      const firstChild = React.Children.toArray(children)[0];
                      if (firstChild && typeof firstChild === 'string') {
                        codeContent = firstChild;
                      } else if (firstChild && typeof firstChild === 'object') {
                        codeContent = JSON.stringify(firstChild);
                      }
                    }

                    const blob = new Blob([codeContent], { type: 'text/plain' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `code.${language.toLowerCase()}`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    setTimeout(() => URL.revokeObjectURL(url), 100);

                    notifications.show({
                      title: '下载成功',
                      message: '代码已下载',
                      color: 'green',
                    });
                  }}
                >
                  <RiDownload2Line size={12} color={codeAreaIconColor} />
                </ActionIcon>
              </Tooltip>
            </Group>
          </Group> */}
          {children}
        </Box>
      );
    },
    blockquote: ({ children }) => (
      <Blockquote
        color="rgba(0, 0, 0, 0.3)"
        fz={16}
        styles={{
          root: {
            padding: 0,
            paddingLeft: '10px',
            lineHeight: '28px',
            background: 'transparent',
            borderLeftWidth: '2px',
            color: 'rgba(87, 96, 106, 1)',
          },
        }}
      >
        {children}
      </Blockquote>
    ),
    table: ({ children }) => (
      <Box
        style={{
          display: 'inline-block',
          maxWidth: '100%',
          background: tableBgColor,
          border: `1px solid ${tableBorderColor}`,
          borderRadius: '12px',
          overflow: 'hidden',
        }}
      >
        <Table
          withColumnBorders
          styles={{
            thead: {
              backgroundColor: tableHeadBgColor,
            },
            th: {
              padding: '12px 18px',
              fontSize: '16px',
              fontWeight: 600,
              color: tableHeadTextColor,
            },
            td: {
              padding: '12px 18px',
              fontSize: '16px',
              color: robotMsgTextColor,
            },
          }}
        >
          {children}
        </Table>
      </Box>
    ),
    thead: ({ children }) => <Table.Thead>{children}</Table.Thead>,
    tbody: ({ children }) => <Table.Tbody>{children}</Table.Tbody>,
    tr: ({ children }) => <Table.Tr>{children}</Table.Tr>,
    th: ({ children }) => <Table.Th>{children}</Table.Th>,
    td: ({ children }) => <Table.Td>{children}</Table.Td>,

    //@ts-expect-error 自定义 'doc-preview' 组件未在类型定义中
    'doc-preview': ({ ...props }) => {
      const url = props['data-url'] as string | undefined;
      const documentType = props['data-type'] as string | undefined;
      const fileName = url?.split('/').pop() || 'document';

      // 根据文档类型设置不同的背景色和图标
      let bgColor = '#E3F2FD'; // 默认蓝色背景
      let IconComponent = RiFileTextLine;
      let iconColor = '#607D8B';

      // 根据文件类型设置不同的图标和背景色
      if (documentType === 'pdf' || url?.toLowerCase().endsWith('.pdf')) {
        bgColor = '#FFEBEE'; // 红色背景
        IconComponent = RiFilePdfLine;
        iconColor = '#F44336';
      } else if (documentType === 'excel' || url?.toLowerCase().endsWith('.xlsx') || url?.toLowerCase().endsWith('.xls')) {
        bgColor = '#E8F5E9'; // 绿色背景
        IconComponent = RiFileExcelLine;
        iconColor = '#4CAF50';
      } else if (documentType === 'csv' || url?.toLowerCase().endsWith('.csv')) {
        bgColor = '#E8F5E9'; // 绿色背景 (与Excel相同)
        IconComponent = RiFileExcelLine;
        iconColor = '#4CAF50';
      } else if (documentType === 'word' || url?.toLowerCase().endsWith('.docx') || url?.toLowerCase().endsWith('.doc')) {
        bgColor = '#E3F2FD'; // 蓝色背景
        IconComponent = RiFileWordLine;
        iconColor = '#2196F3';
      }

      return (
        <Box
          component="span"
          className="inline-flex items-center p-1 bg-gray-100 rounded-lg mx-0.5 align-middle overflow-hidden whitespace-nowrap cursor-pointer"
          style={{ display: 'inline-flex' }}
          onClick={() => {
            if (url) {
              window.open(url, '_blank');
            }
          }}
        >
          <Box component="span" className="inline-flex justify-start items-center gap-2">
            <Box
              component="div"
              className="w-12 h-12 flex items-center justify-center rounded-md"
              style={{ backgroundColor: bgColor }}
            >
              <IconComponent size={24} color={iconColor} />
            </Box>
            <Box component="div" className="inline-flex flex-col justify-center items-start gap-0.5">
              <Text
                component="div"
                className="text-black text-sm font-medium"
                style={{
                  maxWidth: '150px',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {fileName}
              </Text>
            </Box>
          </Box>
        </Box>
      );
    },
    'img-preview': ({ ...props }) => {
      const url = props['data-src'] as string | undefined;
      const imageName = (props['data-alt'] as string) || url?.split('/').pop() || 'image';

      return (
        <Box
          component="span"
          className="inline-flex items-center p-1 bg-gray-100 rounded-lg mx-0.5 align-middle overflow-hidden whitespace-nowrap cursor-pointer"
          style={{ display: 'inline-flex' }}
          onClick={() => {
            if (url) {
              setCurrentImage(url);
              setLightboxOpen(true);
            }
          }}
        >
          <Box component="span" className="inline-flex justify-start items-center gap-2">
            <Image
              src={url}
              alt={imageName}
              className="w-12 h-12 object-cover rounded-md bg-neutral-200"
              style={{ width: '48px', height: '48px' }}
            />
            <Box component="div" className="inline-flex flex-col justify-center items-start gap-0.5">
              <Text
                component="div"
                className="text-black text-sm font-medium"
                style={{
                  maxWidth: '150px',
                  whiteSpace: 'nowrap',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                }}
              >
                {imageName}
              </Text>
            </Box>
          </Box>
        </Box>
      );
    },
    loading: () => {
      return (
        <Box py={8}>
          <motion.div animate={{ opacity: [0.4, 1, 0.4] }} transition={{ duration: 1.5, repeat: Infinity, ease: 'easeInOut' }}>
            <Text fz={16} fw={400} lh="28px" c={robotMsgTextColor}>
              思考中...
            </Text>
          </motion.div>
        </Box>
      );
    },
  };

  return (
    <>
      <div ref={markdownRef} className="prose max-w-none text-sm">
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          rehypePlugins={[rehypeRaw]}
          unwrapDisallowed={false}
          allowedElements={undefined}
          components={components}
        >
          {contentBlocksToString(content)}
        </ReactMarkdown>
      </div>

      {/* 图片预览灯箱 */}
      {currentImage && (
        <Lightbox
          open={lightboxOpen}
          close={() => setLightboxOpen(false)}
          slides={[{ src: currentImage }]}
          controller={{ closeOnBackdropClick: true }}
          render={{
            buttonPrev: () => null,
            buttonNext: () => null,
          }}
          toolbar={{
            buttons: [
              <ActionIcon
                key="download"
                onClick={() => downloadImage(currentImage)}
                variant="transparent"
                color="white"
                size="lg"
              >
                <RiDownload2Line size={24} />
              </ActionIcon>,
            ],
          }}
        />
      )}
    </>
  );
}
export default MarkdownRenderer;
