import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { Message as MessageType } from '../../schemas';

export interface MessageProps {
  message: MessageType;
}

/**
 * 消息内容组件
 * 根据设备类型自动选择桌面版或移动版
 */
const MessageContent: React.FC<MessageProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // 根据设备类型动态选择适当的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default MessageContent;
MessageContent.displayName = 'MessageContent';
