import React, { useState } from 'react';
import { ActionIcon, Tooltip } from '@mantine/core';
import { RiShareLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';
import { notifications } from '@mantine/notifications';
import { Message } from '../../../schemas';
import { contentBlocksToString } from './Copy';
import { API_BASE_URL } from '~/core/constants';
import { useUserStore } from '~/user/core/store';

interface ShareProps {
  message?: Message;
  onShare?: () => void;
}

/**
 * Share button component for sharing message content to Feishu
 */
const Share: React.FC<ShareProps> = ({ message, onShare }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const iconColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';
  const [isSharing, setIsSharing] = useState(false);

  const handleShare = async () => {
    if (!message || isSharing) return;

    setIsSharing(true);

    try {
      // 获取用户token
      const token = useUserStore.getState().token;

      if (!token) {
        throw new Error('未找到用户 token，请先登录');
      }

      // 将消息内容转换为纯文本
      const textContent = contentBlocksToString(message.content);

      // 生成文档标题（使用当前时间）
      const now = new Date();
      const title = `智能体对话记录 - ${now.toLocaleString('zh-CN')}`;

      // 调用后端API分享到飞书
      const response = await fetch(`${API_BASE_URL}/agents/share/feishu`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          title,
          content: textContent,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.detail || '分享失败');
      }

      const result = await response.json();
      const documentUrl = result.data.url;

      // 复制URL到剪贴板
      await navigator.clipboard.writeText(documentUrl);

      // 显示成功通知
      notifications.show({
        title: '分享成功',
        message: '已复制分享链接到剪贴板',
        color: 'green',
      });

      // 调用外部传入的onShare回调
      if (onShare) {
        onShare();
      }
    } catch (error) {
      console.error('分享失败:', error);
      notifications.show({
        title: '分享失败',
        message: error instanceof Error ? error.message : '无法分享消息内容',
        color: 'red',
      });
    } finally {
      setIsSharing(false);
    }
  };

  return (
    <Tooltip arrowSize={6} label="分享到飞书" withArrow position="top">
      <ActionIcon
        variant="subtle"
        aria-label="Share to Feishu"
        color="gray"
        size="input-sm"
        radius={10}
        onClick={handleShare}
        loading={isSharing}
        disabled={isSharing}
      >
        <RiShareLine size={16} color={iconColor} />
      </ActionIcon>
    </Tooltip>
  );
};

export default Share;
