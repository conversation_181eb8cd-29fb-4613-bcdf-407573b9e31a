import React from 'react';
import { ActionIcon, Tooltip } from '@mantine/core';
import { RiRefreshLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';

interface RegenerateProps {
  onRegenerate?: () => void;
}

/**
 * Regenerate button component for regenerating AI responses
 */
const Regenerate: React.FC<RegenerateProps> = ({ onRegenerate }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const iconColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';

  return (
    <Tooltip arrowSize={6} label="重新生成" withArrow position="top">
      <ActionIcon variant="subtle" aria-label="Refresh" color="gray" size="input-sm" radius={10} onClick={onRegenerate}>
        <RiRefreshLine size={16} color={iconColor} />
      </ActionIcon>
    </Tooltip>
  );
};

export default Regenerate;
