import React from 'react';
import { ActionIcon, Tooltip } from '@mantine/core';
import { RiFileCopyLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';
import { notifications } from '@mantine/notifications';
import { DocumentPreviewContentBlock, ImageContentBlock, Message, TextContentBlock } from '../../../schemas';

interface CopyProps {
  message?: Message;
  onCopy?: () => void;
}
export const contentBlocksToString = (content: Message['content']) => {
  const md = content
    .map((block) => {
      switch (block.type) {
        case 'text':
          return (block as TextContentBlock).content;
        case 'document_preview': {
          const docPreview = block as DocumentPreviewContentBlock;
          return `<doc-preview data-document-type="${docPreview.documentType}" data-url="${docPreview.url}" > </doc-preview>`;
        }
        case 'loading':
          return '<loading></loading>';
        case 'image':
          return `<img-preview data-src="${(block as ImageContentBlock).src}" data-alt="${(block as ImageContentBlock).name}"  > </img-preview>`;
        default:
          return '';
      }
    })
    .join('');
  //console.log('转换后的md:', md);
  return md;
};
/**
 * Copy button component for copying message content
 */
const Copy: React.FC<CopyProps> = ({ message, onCopy }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const iconColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';

  const handleCopy = () => {
    if (message) {
      // 将消息内容转换为纯文本
      const textContent = contentBlocksToString(message.content);

      // 复制到剪贴板
      navigator.clipboard
        .writeText(textContent)
        .then(() => {
          // 显示成功通知
          notifications.show({
            title: '复制成功',
            message: '消息内容已复制到剪贴板',
            color: 'green',
          });

          // 调用外部传入的onCopy回调
          if (onCopy) {
            onCopy();
          }
        })
        .catch((error) => {
          console.error('复制失败:', error);
          notifications.show({
            title: '复制失败',
            message: '无法复制消息内容',
            color: 'red',
          });
        });
    }
  };

  return (
    <Tooltip arrowSize={6} label="复制" withArrow position="top">
      <ActionIcon variant="subtle" aria-label="Copy" color="gray" size="input-sm" radius={10} onClick={handleCopy}>
        <RiFileCopyLine size={16} color={iconColor} />
      </ActionIcon>
    </Tooltip>
  );
};

export default Copy;
