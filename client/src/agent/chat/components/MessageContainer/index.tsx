import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { Message } from '../../schemas';

export interface MessageContainerProps {
  children?: React.ReactNode;
  data?: Message;
}

/**
 * 消息容器组件
 * 根据设备类型自动选择桌面版或移动版
 */
const MessageContainer: React.FC<MessageContainerProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // 根据设备类型动态选择适当的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default MessageContainer;
MessageContainer.displayName = 'MessageContainer';
