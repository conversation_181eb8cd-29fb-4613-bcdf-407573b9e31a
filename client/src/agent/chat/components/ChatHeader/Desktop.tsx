/** @jsxImportSource @emotion/react */
//import { css } from '@emotion/react';
import React from 'react';
import { Group, Avatar, Text, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { ossUrl } from '~/core/utils';
//import { RiShareBoxLine, RiMoreFill, RiSidebarFoldLine } from 'react-icons/ri';

import { ChatHeaderProps } from './types';

const Desktop: React.FC<ChatHeaderProps> = ({ title }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  //const iconColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  //const iconHoverColor = isDark ? 'white' : 'black';
  const headerBorderColor = isDark ? theme.colors.dark[6] : 'rgba(240, 242, 245, 1)';
  const titleColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  return (
    <Group
      className="flex-none w-full"
      h={70}
      px={32}
      justify="space-between"
      style={{
        borderBottom: `1px solid ${headerBorderColor}`,
      }}
    >
      <Group gap={12}>
        <Avatar src={ossUrl('/images/logo_icon.png')} size={32} bg="white" />
        <Text fz={16} fw={500} lh="24px" c={titleColor}>
          {title}
        </Text>
      </Group>

      {/*      <Group>
        <RiShareBoxLine
          className="cursor-pointer"
          size={20}
          color={iconColor}
          css={css`
            &:hover {
              color: ${iconHoverColor} !important;
            }
          `}
        />
        <RiMoreFill
          className="cursor-pointer"
          size={20}
          color={iconColor}
          css={css`
            &:hover {
              color: ${iconHoverColor} !important;
            }
          `}
        />
        <RiSidebarFoldLine
          className="cursor-pointer"
          size={20}
          color={iconColor}
          css={css`
            &:hover {
              color: ${iconHoverColor} !important;
            }
          `}
        />
      </Group> */}
    </Group>
  );
};

export default Desktop;
