import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Center } from '@mantine/core';
import { MessageContainer, Message, Actions, Copy, Share } from '../MessageContainer/components';
import { MessageListProps } from '.';
import { Message as MessageType } from '../../schemas';
import { useChatContext } from '../../contexts';
import { useMessageLoader } from '../../hooks';
import { useMemo } from 'react';

const Desktop: React.FC<MessageListProps> = () => {
  const { messages } = useChatContext();
  const { isLoading, hasMore, scrollAreaRef, handleScroll } = useMessageLoader();

  // 渲染消息
  const renderMessage = useMemo(
    () => (message: MessageType) => {
      // 处理时间分隔线类型的消息
      if (message.role === 'timedivider') {
        return <Divider key={message.id} label={message.created_at_label} labelPosition="center" />;
      }

      // 处理普通消息
      return (
        <MessageContainer key={message.id} data={message}>
          <Message message={message} />
          {message.role === 'assistant' && (
            <Actions>
              {/* <Regenerate onRegenerate={() => console.log('重新生成')} /> */}
              <Copy message={message} onCopy={() => console.log('消息已复制')} />
              <Share message={message} onShare={() => console.log('消息已分享')} />
            </Actions>
          )}
        </MessageContainer>
      );
    },
    [],
  );

  return (
    <Container
      className="flex-auto"
      style={{
        overflow: 'hidden',
        display: 'flex',
        flexDirection: 'column',
        minWidth: 400, // 最小宽度防止塌缩，可根据需要调整
        maxWidth: 904, // 最大宽度保持原有视觉
        width: '100%',
      }}
    >
      <ScrollArea
        type="never"
        style={{ flex: 1 }}
        viewportRef={scrollAreaRef}
        onScrollPositionChange={({ y }) => {
          // 当滚动到顶部时触发加载更多
          if (y === 0 && hasMore) {
            handleScroll({ currentTarget: { scrollTop: 0 } } as React.UIEvent<HTMLDivElement>);
          }
        }}
      >
        {isLoading && hasMore && (
          <Center py="md">
            <Loader size="sm" />
          </Center>
        )}
        <Stack py={30} gap={30}>
          {messages.map(renderMessage)}
        </Stack>
      </ScrollArea>
    </Container>
  );
};

export default Desktop;
