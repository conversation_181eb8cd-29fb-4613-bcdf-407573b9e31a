import { ChatPayloads, ConversationEvent, ConversationEventSchema, RunWorkflowPayloads } from '@dify_schemas/index';
import { API_BASE_URL } from '~/core/constants';
import { ResponsePayloads } from '~/core/schemas';
import { useUserStore } from '~/user/core/store';
import { OperationResult } from './schemas';

// 文档提取响应类型
export interface DocumentExtractResponse {
  text: (string | number)[];
  images: string[];
  file_name: string;
}

async function sse(
  url: string,
  payloads: ChatPayloads | RunWorkflowPayloads,
  onMessage: (event: ConversationEvent) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
) {
  // 从全局状态获取用户token
  const token = useUserStore.getState().token;

  // 检查用户是否已登录
  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  try {
    // 根据端点类型构造请求体：chat端点需要完整配置，其他端点只需要inputs

    // 发起SSE请求
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: 'text/event-stream', // 声明接受事件流格式
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payloads),
    });

    // 处理HTTP错误状态
    if (!response.ok) {
      const respText = (await response.json()) as ResponsePayloads<unknown>;
      throw new Error(respText.error?.message || '发送消息失败');
    }

    // 确保响应体存在
    if (!response.body) {
      throw new Error('未接收到响应数据');
    }

    // 准备流式读取器
    const reader = response.body.getReader();
    const decoder = new TextDecoder(); // 用于将字节流转换为字符串

    let buffer = ''; // 缓冲区用于存储不完整的数据块

    // 持续读取流数据
    while (true) {
      const { done, value } = await reader.read();
      if (done) {
        // 流结束
        onComplete?.();
        break;
      }

      // 解码并追加到缓冲区
      buffer += decoder.decode(value, { stream: true });

      // 按换行符分割处理完整事件
      const lines = buffer.split('\n');

      // 保留未处理完的部分到缓冲区
      buffer = lines.pop() || '';

      // 处理每个完整的事件行
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          // 过滤有效事件数据
          let eventData;
          let parsedEvent;
          try {
            // 解析JSON数据并添加格式化时间戳
            eventData = JSON.parse(line.slice(6));
            parsedEvent = ConversationEventSchema.parse(eventData);
            onMessage(parsedEvent);
          } catch (error) {
            console.log(eventData);
            console.error('解析消息失败:', error);
          }
        }
      }
    }
  } catch (error) {
    // 统一错误处理
    onError?.(error instanceof Error ? error : new Error(String(error)));
  }
}
/**
 * 聊天
 * @param appId 应用ID
 * @param config 聊天请求配置
 * @param onMessage 接收到消息时的回调函数
 * @param onError 发生错误时的回调函数
 * @param onComplete SSE流关闭时的回调函数
 */
export async function chat(
  appId: string,
  payloads: ChatPayloads,
  onMessage: (event: ConversationEvent) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
): Promise<void> {
  sse(`${API_BASE_URL}/agents/chat/${appId}`, payloads, onMessage, onError, onComplete);
}

/**
 * 停止指定任务
 * @param appId 应用ID
 * @param taskId 任务ID
 */
export async function stopTask(appId: string, taskId: string): Promise<ResponsePayloads<OperationResult>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const queryParams = new URLSearchParams();
  queryParams.append('app_id', appId);
  queryParams.append('task_id', taskId);

  const response = await fetch(`${API_BASE_URL}/agents/tasks/stop?${queryParams.toString()}`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`停止任务失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 语音转文本
 * @param audioFile 音频文件
 * @returns 转换后的文本
 */
export async function transcribeAudio(audioFile: File): Promise<ResponsePayloads<string>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const formData = new FormData();
  formData.append('audio_file', audioFile);

  const response = await fetch(`${API_BASE_URL}/agents/transcribe`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`语音转文字失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 提取文档内容
 * @param file 文档文件（PDF或Word）
 * @param path 图片存储路径
 * @returns 提取的文本内容和图片URL
 */
export async function extractDocument(
  file: File,
  path: string = 'documents/images/',
): Promise<ResponsePayloads<DocumentExtractResponse>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const formData = new FormData();
  formData.append('file', file);
  formData.append('path', path);

  const response = await fetch(`${API_BASE_URL}/files/extract_document`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`文档内容提取失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 上传图片到Dify平台
 * @param file 图片文件
 * @returns 上传结果，包含文件ID
 */
export async function uploadImageToDify(file: File): Promise<ResponsePayloads<{ id: string }>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const formData = new FormData();
  formData.append('file', file);

  const response = await fetch(`${API_BASE_URL}/agents/upload_file`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`图片上传失败: ${response.statusText}`);
  }

  return response.json();
}
/**
 * 删除对话
 * @param appId 应用ID
 * @param conversationId 对话ID
 * @returns 操作结果
 */
export async function deleteConversation(appId: string, conversationId: string): Promise<ResponsePayloads<OperationResult>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const queryParams = new URLSearchParams();
  queryParams.append('app_id', appId);
  queryParams.append('conversation_id', conversationId);

  const response = await fetch(`${API_BASE_URL}/agents/conversation?${queryParams.toString()}`, {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`删除对话失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 运行工作流
 * @param appId 应用ID
 * @param config 聊天请求配置
 * @param onMessage 接收到消息时的回调函数
 * @param onError 发生错误时的回调函数
 * @param onComplete SSE流关闭时的回调函数
 */
export async function runWorkflow(
  appId: string,
  payloads: RunWorkflowPayloads,
  onMessage: (event: ConversationEvent) => void,
  onError?: (error: Error) => void,
  onComplete?: () => void,
): Promise<void> {
  sse(`${API_BASE_URL}/agents/run/${appId}`, payloads, onMessage, onError, onComplete);
}
