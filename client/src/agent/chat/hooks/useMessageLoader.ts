import { useEffect, useRef, useState } from 'react';
import { useChatContext } from '../contexts';
import db from '../db';
import { Message } from '../schemas';

/**
 * 消息加载钩子
 * 用于处理消息的加载、分页和滚动加载
 */
export const useMessageLoader = () => {
  const { conversation, messages, setMessages } = useChatContext();
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const scrollAreaRef = useRef<HTMLDivElement>(null);
  const prevMessagesLengthRef = useRef(0);

  // 初始加载最近的10条消息
  useEffect(() => {
    if (conversation?.id) {
      loadRecentMessages();
    }
  }, [conversation?.id]);

  // 加载最近的消息
  const loadRecentMessages = async () => {
    if (!conversation?.id) return;

    setIsLoading(true);
    try {
      // 从数据库加载最近的10条消息
      const recentMessages = await db.messages.where('conversation_id').equals(conversation.id).reverse().sortBy('created_at');

      // 只取最近的10条
      const latestMessages = recentMessages.slice(0, 10).reverse();

      setMessages(latestMessages);
      setHasMore(recentMessages.length > 10);
    } catch (error) {
      console.error('加载最近消息失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 加载更多历史消息
  const loadMoreMessages = async () => {
    if (!conversation?.id || isLoading || !hasMore) return;

    setIsLoading(true);
    try {
      // 获取当前最早的消息的创建时间
      const earliestMessage = messages[0];
      if (!earliestMessage) {
        setHasMore(false);
        return;
      }

      // 加载比当前最早消息更早的10条消息
      const olderMessages = await db.messages
        .where('conversation_id')
        .equals(conversation.id)
        .and((item) => (item.created_at || 0) < (earliestMessage.created_at || 0))
        .reverse()
        .limit(10)
        .sortBy('created_at');

      if (olderMessages.length === 0) {
        setHasMore(false);
        return;
      }

      // 记录当前滚动位置
      const scrollArea = scrollAreaRef.current;
      const scrollHeight = scrollArea?.scrollHeight || 0;

      // 更新消息列表，将新加载的消息添加到列表前面
      setMessages((prev: Message[]) => [...olderMessages.reverse(), ...prev]);

      // 恢复滚动位置，保持用户当前查看的位置不变
      if (scrollArea) {
        const newScrollHeight = scrollArea.scrollHeight;
        scrollArea.scrollTop = newScrollHeight - scrollHeight;
      }

      setHasMore(olderMessages.length === 10);
    } catch (error) {
      console.error('加载更多消息失败:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // 处理滚动事件，检测是否滚动到顶部
  const handleScroll = (event: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop } = event.currentTarget;

    // 当滚动到顶部时，加载更多消息
    if (scrollTop === 0 && !isLoading && hasMore) {
      loadMoreMessages();
    }
  };

  // 滚动到底部
  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      setTimeout(() => {
        const scrollArea = scrollAreaRef.current;
        if (scrollArea) {
          scrollArea.scrollTop = scrollArea.scrollHeight;
        }
      }, 50);
    }
  };

  // 监听消息变化，当有新消息时自动滚动到底部
  useEffect(() => {
    // 只有当消息数量增加时才滚动到底部（避免加载历史消息时触发）
    if (messages.length > prevMessagesLengthRef.current) {
      scrollToBottom();
    }
    prevMessagesLengthRef.current = messages.length;
  }, [messages]);

  return {
    isLoading,
    hasMore,
    scrollAreaRef,
    handleScroll,
    loadMoreMessages,
    loadRecentMessages,
    scrollToBottom,
  };
};
