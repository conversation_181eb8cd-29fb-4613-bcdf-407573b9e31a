import { Model, ModelConfigSchema, Tool1Schema, ToolProviderSchema } from '@dify_schemas/index';
import { KnowledgeDataset } from '~/knowledge/schemas';
import { z } from 'zod';

/**
 * 工具选择数据结构，包含工具和提供者信息
 */
export const SelectedToolSchema = z.object({
  tool: Tool1Schema.describe('工具信息'),
  provider: ToolProviderSchema.describe('工具提供者信息'),
});

export type SelectedTool = z.infer<typeof SelectedToolSchema>;

/**
 * 创建智能体请求参数
 */
export const SaveOrUpdateAgentPayloadsSchema = z.object({
  id: z.string().optional().describe('智能体ID，如果提供且存在则更新，否则创建'),
  name: z.string().describe('智能体名称'),
  description: z.string().describe('智能体描述'),
  icon_url: z.string().optional().describe('智能体图标URL'),
  tags: z.array(z.string()).optional().describe('智能体标签'),
  is_public: z.boolean().default(false).describe('是否公开'),
  monthly_price: z.number().default(0).describe('月付价格'),
  yearly_price: z.number().default(0).describe('年付价格'),
  config: ModelConfigSchema.describe('模型配置'),
});

export type SaveOrUpdateAgentPayloads = z.infer<typeof SaveOrUpdateAgentPayloadsSchema>;
const CompletionParamsSchema = z
  .object({
    temperature: z.number().min(0).max(2).optional(),
    max_tokens: z.number().positive().optional(),
    top_p: z.number().min(0).max(1).optional(),
    frequency_penalty: z.number().min(-2).max(2).optional(),
    presence_penalty: z.number().min(-2).max(2).optional(),
    stop: z.union([z.string(), z.array(z.string())]).optional(),
  })
  .catchall(z.unknown()); // 允许其他未定义的参数

/**
 * 智能体构建表单Schema
 */
export const AgentBuilderFormValuesSchema = z.object({
  name: z.string().describe('智能体名称'),
  description: z.string().describe('智能体描述'),
  avatar: z.string().optional().describe('智能体头像'),
  model: z.custom<Model>().describe('使用的模型').optional(),
  completionParams: CompletionParamsSchema.describe('模型完成参数'),
  modelProvider: z.string().describe('模型提供商'),
  prompt: z.string().describe('系统提示词'),
  tools: z.array(SelectedToolSchema).default([]).describe('选择的工具列表'),
  knowledges: z.array(z.custom<KnowledgeDataset>()).default([]).describe('选择的知识库列表'),
  enableVision: z.boolean().default(false).describe('是否启用视觉能力'),
  enableDocument: z.boolean().default(false).describe('是否启用文档能力'),
  isPublic: z.boolean().default(false).describe('是否公开'),
});

export type AgentBuilderFormValues = z.infer<typeof AgentBuilderFormValuesSchema>;
