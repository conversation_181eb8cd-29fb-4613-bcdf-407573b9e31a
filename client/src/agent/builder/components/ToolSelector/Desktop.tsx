import React, { useState } from 'react';
import { Stack, Group, Text, UnstyledButton, Divider, useMantineTheme, Box, ScrollArea, ActionIcon, Modal } from '@mantine/core';
import { RiQuestionLine, RiDeleteBinLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';
import ToolProviders from '../ToolProviders';
import { ToolSelectorProps } from './types';
import { SelectedTool } from '../../schemas';

const Desktop: React.FC<ToolSelectorProps> = ({ value = [], onChange, error }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const [toolDialogOpened, setToolDialogOpened] = useState(false);

  const itemBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const tipTextColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';

  // 处理工具选择确认
  const handleToolDialogConfirm = (tools: SelectedTool[] = []) => {
    setToolDialogOpened(false);
    onChange?.(tools);
  };

  // 处理工具删除
  const handleRemoveTool = (toolName: string) => {
    const updatedTools = value.filter((selectedTool) => selectedTool.tool.name !== toolName);
    onChange?.(updatedTools);
  };

  // 获取工具图标
  const getToolIcon = (selectedTool: SelectedTool) => {
    const tool = selectedTool.tool;
    // 根据工具名称或标签返回相应的图标
    const toolName = tool.name.toLowerCase();
    if (toolName.includes('dalle') || toolName.includes('image')) return '🎨';
    if (toolName.includes('code') || toolName.includes('python')) return '</>';
    if (toolName.includes('audio') || toolName.includes('speech')) return '🎵';
    if (toolName.includes('search') || toolName.includes('web')) return '🔍';
    if (toolName.includes('weather')) return '🌤️';
    if (toolName.includes('calculator') || toolName.includes('math')) return '🧮';
    return '🔧'; // 默认工具图标
  };

  return (
    <>
      <Stack className="rounded-[6px]" px={16} py={14} bg={itemBgColor} gap={0}>
        <Group align="center" justify="space-between">
          <Group align="center" gap={4}>
            <Text fz={16} fw={700} lh="24px" c={textColor}>
              工具
            </Text>
            <RiQuestionLine size={12} color="rgba(204, 204, 204, 1)" />
          </Group>

          <Group>
            <Text fz={14} fw={400} lh="20px" c={tipTextColor}>
              {value.length} 个工具
            </Text>
            <UnstyledButton onClick={() => setToolDialogOpened(true)}>
              <Text fz={12} fw={700} lh="18px" c="rgba(73, 81, 235, 1)">
                + 添加
              </Text>
            </UnstyledButton>
          </Group>
        </Group>

        <Divider my={8} />

        {value.length === 0 ? (
          <Box
            style={{
              height: 36,
              backgroundColor: itemBgColor,
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              paddingLeft: '12px',
              paddingRight: '12px',
            }}
          >
            <Text c={tipTextColor} fz={14}>
              暂无工具，点击"+ 添加"来添加工具
            </Text>
          </Box>
        ) : (
          <ScrollArea h={value.length > 5 ? 200 : undefined} offsetScrollbars>
            <Stack gap={8} p={4}>
              {value.map((selectedTool) => (
                <Group
                  key={selectedTool.tool.name}
                  justify="space-between"
                  p={8}
                  className="tool-item"
                  style={{
                    borderRadius: '6px',
                    backgroundColor: isDark ? 'rgba(40, 40, 40, 1)' : 'rgba(240, 240, 240, 1)',
                  }}
                  onMouseEnter={(e) => {
                    const deleteButton = e.currentTarget.querySelector('.delete-button') as HTMLElement;
                    if (deleteButton) deleteButton.style.opacity = '0.7';
                  }}
                  onMouseLeave={(e) => {
                    const deleteButton = e.currentTarget.querySelector('.delete-button') as HTMLElement;
                    if (deleteButton) deleteButton.style.opacity = '0';
                  }}
                >
                  <Group gap={8}>
                    <Text>{getToolIcon(selectedTool)}</Text>
                    <Text c={textColor}>{selectedTool.tool.label?.zh_Hans || selectedTool.tool.label?.en_US || selectedTool.tool.name}</Text>
                  </Group>

                  <Box
                    className="delete-button"
                    style={{
                      opacity: 0,
                      transition: 'opacity 0.2s',
                    }}
                  >
                    <ActionIcon variant="subtle" color="gray" onClick={() => handleRemoveTool(selectedTool.tool.name)} title="删除">
                      <RiDeleteBinLine size={16} />
                    </ActionIcon>
                  </Box>
                </Group>
              ))}
            </Stack>
          </ScrollArea>
        )}

        {error && (
          <Text c="red" fz={12} mt={4}>
            {error}
          </Text>
        )}
      </Stack>

      {/* 工具选择弹窗 */}
      <Modal
        opened={toolDialogOpened}
        withCloseButton={false}
        closeOnClickOutside={true}
        onClose={() => setToolDialogOpened(false)}
        centered
        styles={{
          content: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'transparent',
            boxShadow: 'none',
          },
          body: {
            width: '510px',
            padding: 0,
          },
        }}
      >
        <ToolProviders
          onCancel={() => setToolDialogOpened(false)}
          onConfirm={handleToolDialogConfirm}
          initialSelected={value}
          tool_type="builtin" // 获取所有类型的工具
        />
      </Modal>
    </>
  );
};

export default Desktop;
