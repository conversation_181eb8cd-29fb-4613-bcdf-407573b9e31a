import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { ToolSelectorProps } from './types';

const ToolSelector: React.FC<ToolSelectorProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default ToolSelector;
export { ToolSelector };
export type { ToolSelectorProps };
ToolSelector.displayName = 'ToolSelector';
