import { Stack, Group, Text, Textarea, TextInput, Divider, Switch, useMantineTheme } from '@mantine/core';
import { RiQuestionLine, RiEyeFill, RiFileTextLine, RiGlobalLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';
import { useAgentBuilderContext } from '../../contexts';
import { ToolSelector, KnowledgeSelector, ModelSelector, AvatarSelector } from '../index';
import { useUserStore } from '~/user/core/store';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const { form, hasVisionCapability, hasDocumentCapability } = useAgentBuilderContext();
  const selectedModel = form.values.model;
  const { userInfo } = useUserStore();

  const containerBgColor = isDark ? theme.colors.dark[9] : 'white';
  const itemBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  return (
    <>
      <Stack
        className="flex-none"
        w={900}
        p={64}
        style={{
          background: containerBgColor,
          boxShadow: '0px 2px 4px  rgba(0, 0, 0, 0.1)',
        }}
        gap={24}
      >
        <Stack className="rounded-[6px]" px={16} py={14} bg={itemBgColor} gap={0}>
          <Group align="center" justify="space-between">
            <Group align="center" gap={4}>
              <Text fz={16} fw={700} lh="24px" c={textColor}>
                名称
              </Text>
              <RiQuestionLine size={12} color="rgba(204, 204, 204, 1)" />
            </Group>
          </Group>

          <Divider my={8} />

          <TextInput
            variant="unstyled"
            placeholder="请输入智能体名称"
            {...form.getInputProps('name')}
            styles={{
              input: {
                height: 36,
                backgroundColor: itemBgColor,
              },
            }}
          />
        </Stack>

        <Stack className="rounded-[6px]" px={16} py={14} bg={itemBgColor} gap={0}>
          <Group align="center" justify="space-between">
            <Group align="center" gap={4}>
              <Text fz={16} fw={700} lh="24px" c={textColor}>
                描述
              </Text>
              <RiQuestionLine size={12} color="rgba(204, 204, 204, 1)" />
            </Group>
          </Group>

          <Divider my={8} />

          <Textarea
            variant="unstyled"
            placeholder="请输入智能体描述"
            {...form.getInputProps('description')}
            styles={{
              input: {
                height: 80,
                backgroundColor: itemBgColor,
              },
            }}
          />
        </Stack>

        <AvatarSelector {...form.getInputProps('avatar')} />
        <Stack className="rounded-[6px]" px={16} py={14} bg={itemBgColor} gap={0}>
          <Group align="center" justify="space-between">
            <Group align="center" gap={4}>
              <Text fz={16} fw={700} lh="24px" c={textColor}>
                模型
              </Text>
              <RiQuestionLine size={12} color="rgba(204, 204, 204, 1)" />
            </Group>
          </Group>
          <Divider my={8} />
          <ModelSelector {...form.getInputProps('model')} />
        </Stack>
        <Stack className="rounded-[6px]" px={16} py={14} bg={itemBgColor} gap={0}>
          <Group align="center" justify="space-between">
            <Group align="center" gap={4}>
              <Text fz={16} fw={700} lh="24px" c={textColor}>
                提示词
              </Text>
              <RiQuestionLine size={12} color="rgba(204, 204, 204, 1)" />
            </Group>
            {/* 暂时隐藏生成按钮，如需启用请添加功能实现 */}
          </Group>

          <Divider my={8} />

          <Textarea
            variant="unstyled"
            placeholder="在这里写你的提示词"
            {...form.getInputProps('prompt')}
            styles={{
              input: {
                height: 130,
                backgroundColor: itemBgColor,
              },
            }}
          />
        </Stack>

        <ToolSelector {...form.getInputProps('tools')} />

        <KnowledgeSelector {...form.getInputProps('knowledges')} />
        {/* 视觉功能 - 只在模型支持视觉功能时显示 */}
        {hasVisionCapability(selectedModel) && (
          <Stack className="rounded-[6px]" px={16} py={14} bg={itemBgColor} gap={0}>
            <Group align="center" justify="space-between">
              <Group align="center" gap={4}>
                <RiEyeFill size={12} color="rgba(73, 81, 235, 1)" />
                <Text fz={16} fw={700} lh="24px" c={textColor}>
                  视觉
                </Text>
                <RiQuestionLine size={12} color="rgba(204, 204, 204, 1)" />
              </Group>

              {/* 添加开关控件 */}
              <Switch
                checked={form.values.enableVision}
                onChange={(event) => {
                  form.setFieldValue('enableVision', event.currentTarget.checked);
                }}
                color="rgba(73, 81, 235, 1)"
                size="sm"
              />
            </Group>
          </Stack>
        )}
        {/* 文档功能 - 只在模型支持文档功能时显示 */}
        {hasDocumentCapability(selectedModel) && (
          <Stack className="rounded-[6px]" px={16} py={14} bg={itemBgColor} gap={0}>
            <Group align="center" justify="space-between">
              <Group align="center" gap={4}>
                <RiFileTextLine size={12} color="rgba(73, 81, 235, 1)" />
                <Text fz={16} fw={700} lh="24px" c={textColor}>
                  文档
                </Text>
                <RiQuestionLine size={12} color="rgba(204, 204, 204, 1)" />
              </Group>

              {/* 添加开关控件 */}
              <Switch
                checked={form.values.enableDocument}
                onChange={(event) => {
                  form.setFieldValue('enableDocument', event.currentTarget.checked);
                }}
                color="rgba(73, 81, 235, 1)"
                size="sm"
              />
            </Group>
          </Stack>
        )}

        {/* 公开选项 - 只在用户允许公开智能体时显示 */}
        {userInfo?.is_allow_public_agent && (
          <Stack className="rounded-[6px]" px={16} py={14} bg={itemBgColor} gap={0}>
            <Group align="center" justify="space-between">
              <Group align="center" gap={4}>
                <RiGlobalLine size={12} color="rgba(73, 81, 235, 1)" />
                <Text fz={16} fw={700} lh="24px" c={textColor}>
                  公开智能体
                </Text>
                <RiQuestionLine size={12} color="rgba(204, 204, 204, 1)" />
              </Group>

              {/* 添加开关控件 */}
              <Switch
                checked={form.values.isPublic}
                onChange={(event) => {
                  form.setFieldValue('isPublic', event.currentTarget.checked);
                }}
                color="rgba(73, 81, 235, 1)"
                size="sm"
              />
            </Group>
          </Stack>
        )}
      </Stack>
    </>
  );
};

export default Desktop;
