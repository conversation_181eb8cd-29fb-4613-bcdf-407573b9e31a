import React, { useState } from 'react';
import { Stack, Group, Text, UnstyledButton, Divider, useMantineTheme, Box, ScrollArea, ActionIcon, Modal } from '@mantine/core';
import { RiQuestionLine, RiDeleteBinLine, RiFileTextLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';
import Knowledge from '../Knowledge';
import { KnowledgeSelectorProps } from './types';
import { KnowledgeDataset } from '~/knowledge/schemas';

const Desktop: React.FC<KnowledgeSelectorProps> = ({ value = [], onChange, error }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const [knowledgeDialogOpened, setKnowledgeDialogOpened] = useState(false);

  const itemBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const tipTextColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';

  // 处理知识库选择确认
  const handleKnowledgeDialogConfirm = (knowledges: KnowledgeDataset[] = []) => {
    setKnowledgeDialogOpened(false);
    onChange?.(knowledges);
  };

  // 处理知识库删除
  const handleRemoveKnowledge = (knowledgeId: string) => {
    const updatedKnowledges = value.filter((knowledge) => knowledge.id !== knowledgeId);
    onChange?.(updatedKnowledges);
  };

  return (
    <>
      <Stack className="rounded-[6px]" px={16} py={14} bg={itemBgColor} gap={0}>
        <Group align="center" justify="space-between">
          <Group align="center" gap={4}>
            <Text fz={16} fw={700} lh="24px" c={textColor}>
              知识库
            </Text>
            <RiQuestionLine size={12} color="rgba(204, 204, 204, 1)" />
          </Group>

          <Group>
            <Text fz={14} fw={400} lh="20px" c={tipTextColor}>
              {value.length} 个知识库
            </Text>
            <UnstyledButton onClick={() => setKnowledgeDialogOpened(true)}>
              <Text fz={12} fw={700} lh="18px" c="rgba(73, 81, 235, 1)">
                + 添加
              </Text>
            </UnstyledButton>
          </Group>
        </Group>

        <Divider my={8} />

        {value.length === 0 ? (
          <Box
            style={{
              height: 36,
              backgroundColor: itemBgColor,
              borderRadius: '4px',
              display: 'flex',
              alignItems: 'center',
              paddingLeft: '12px',
              paddingRight: '12px',
            }}
          >
            <Text c={tipTextColor} fz={14}>
              暂无知识库，点击"+ 添加"来添加知识库
            </Text>
          </Box>
        ) : (
          <ScrollArea h={value.length > 5 ? 200 : undefined} offsetScrollbars>
            <Stack gap={8} p={4}>
              {value.map((knowledge) => (
                <Group
                  key={knowledge.id}
                  justify="space-between"
                  p={8}
                  className="knowledge-item"
                  style={{
                    borderRadius: '6px',
                    backgroundColor: isDark ? 'rgba(40, 40, 40, 1)' : 'rgba(240, 240, 240, 1)',
                  }}
                  onMouseEnter={(e) => {
                    const deleteButton = e.currentTarget.querySelector('.delete-button') as HTMLElement;
                    if (deleteButton) deleteButton.style.opacity = '0.7';
                  }}
                  onMouseLeave={(e) => {
                    const deleteButton = e.currentTarget.querySelector('.delete-button') as HTMLElement;
                    if (deleteButton) deleteButton.style.opacity = '0';
                  }}
                >
                  <Group gap={8}>
                    <RiFileTextLine size={16} color={isDark ? 'rgba(73, 81, 235, 0.8)' : 'rgba(73, 81, 235, 1)'} />
                    <Text c={textColor}>{knowledge.name}</Text>
                  </Group>

                  <Box
                    className="delete-button"
                    style={{
                      opacity: 0,
                      transition: 'opacity 0.2s',
                    }}
                  >
                    <ActionIcon variant="subtle" color="gray" onClick={() => handleRemoveKnowledge(knowledge.id)} title="删除">
                      <RiDeleteBinLine size={16} />
                    </ActionIcon>
                  </Box>
                </Group>
              ))}
            </Stack>
          </ScrollArea>
        )}

        {error && (
          <Text c="red" fz={12} mt={4}>
            {error}
          </Text>
        )}
      </Stack>

      {/* 知识库选择弹窗 */}
      <Modal
        opened={knowledgeDialogOpened}
        withCloseButton={false}
        closeOnClickOutside={true}
        onClose={() => setKnowledgeDialogOpened(false)}
        centered
        styles={{
          content: {
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'transparent',
            boxShadow: 'none',
          },
          body: {
            width: '510px',
            padding: 0,
          },
        }}
      >
        <Knowledge
          onCancel={() => setKnowledgeDialogOpened(false)}
          onConfirm={handleKnowledgeDialogConfirm}
          initialSelected={value}
        />
      </Modal>
    </>
  );
};

export default Desktop;
