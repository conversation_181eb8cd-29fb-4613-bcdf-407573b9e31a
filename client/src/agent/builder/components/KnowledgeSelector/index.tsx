import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { KnowledgeSelectorProps } from './types';

const KnowledgeSelector: React.FC<KnowledgeSelectorProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default KnowledgeSelector;
export { KnowledgeSelector };
export type { KnowledgeSelectorProps };
KnowledgeSelector.displayName = 'KnowledgeSelector';
