/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useState } from 'react';
import { Box, Group, Stack, Button, ScrollArea, Text, useMantineTheme, Checkbox, Input, Loader, Center } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiSearchLine, RiFolderLine } from 'react-icons/ri';
import { useQuery } from '@tanstack/react-query';

import { KnowledgeProps } from './types';
import { getKnowledgeList } from '~/knowledge/api';
import { KnowledgeDataset } from '~/knowledge/schemas';

const Mobile: React.FC<KnowledgeProps> = ({ onCancel, onConfirm, initialSelected = [] }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const containerBgColor = isDark ? theme.colors.dark[9] : 'white';
  const itemBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const tipTextColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';

  const [searchKeyword, setSearchKeyword] = useState('');
  const [selectedKnowledges, setSelectedKnowledges] = useState<KnowledgeDataset[]>(initialSelected);

  // 获取知识库列表
  const { data, isLoading, error } = useQuery({
    queryKey: ['knowledgeList', searchKeyword],
    queryFn: () =>
      getKnowledgeList({
        page: 1,
        page_size: 100, // 获取足够多的知识库
        keyword: searchKeyword || undefined,
      }),
  });

  // 处理知识库选择/取消选择
  const handleToggleKnowledge = (knowledge: KnowledgeDataset) => {
    setSelectedKnowledges((prev) => {
      const isSelected = prev.some((k) => k.id === knowledge.id);
      if (isSelected) {
        return prev.filter((k) => k.id !== knowledge.id);
      } else {
        return [...prev, knowledge];
      }
    });
  };

  // 处理确认按钮点击
  const handleConfirm = () => {
    onConfirm(selectedKnowledges);
  };

  // 检查知识库是否被选中
  const isKnowledgeSelected = (knowledge: KnowledgeDataset) => {
    return selectedKnowledges.some((k) => k.id === knowledge.id);
  };

  return (
    <Stack
      w={430}
      h={572}
      py={16}
      bg={containerBgColor}
      gap={0}
      style={{
        borderRadius: '6px',
        boxShadow: '0px 2px 4px  rgba(0, 0, 0, 0.1)',
      }}
    >
      <Box px={16}>
        <Text fz={18} fw={700} lh="24px" c={textColor} mb={8}>
          选择引用知识库
        </Text>

        <Input
          placeholder="搜索知识库..."
          leftSection={<RiSearchLine size={16} />}
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.currentTarget.value)}
          styles={{
            input: {
              backgroundColor: itemBgColor,
            },
          }}
        />

        <Text fz={14} fw={400} lh="20px" c={tipTextColor} mt={8} mb={4}>
          {selectedKnowledges.length} 个知识库被选中
        </Text>
      </Box>

      <ScrollArea className="flex-auto" px={16} pt={8} pb={16}>
        <Stack gap={8}>
          {isLoading ? (
            <Center h={200}>
              <Loader size="md" color="blue" />
            </Center>
          ) : error ? (
            <Text c="red" ta="center">
              加载失败: {error instanceof Error ? error.message : '未知错误'}
            </Text>
          ) : data?.data?.data?.length === 0 ? (
            <Text c={tipTextColor} ta="center" py={20}>
              没有找到知识库
            </Text>
          ) : (
            data?.data?.data?.map((knowledge: KnowledgeDataset) => (
              <Group
                key={knowledge.id}
                className="cursor-pointer"
                align="center"
                justify="space-between"
                h={36}
                px={8}
                onClick={() => handleToggleKnowledge(knowledge)}
                style={{
                  borderRadius: '6px',
                  backgroundColor: isKnowledgeSelected(knowledge)
                    ? isDark
                      ? 'rgba(73, 81, 235, 0.2)'
                      : 'rgba(73, 81, 235, 0.1)'
                    : 'transparent',
                }}
                css={css`
                  &:hover {
                    background-color: ${isKnowledgeSelected(knowledge)
                      ? isDark
                        ? 'rgba(73, 81, 235, 0.3)'
                        : 'rgba(73, 81, 235, 0.15)'
                      : itemBgColor} !important;
                  }
                `}
              >
                <Group className="flex-auto" align="center" justify="flex-start" gap={8}>
                  <Checkbox
                    checked={isKnowledgeSelected(knowledge)}
                    onChange={() => {}} // 点击Group时会处理
                    color="rgba(73, 81, 235, 1)"
                    size="sm"
                  />
                  <RiFolderLine size={20} color={isDark ? 'rgba(73, 81, 235, 0.8)' : 'rgba(73, 81, 235, 1)'} />
                  <Text className="flex-auto" fz={16} fw={500} lh="24px" c={textColor}>
                    {knowledge.name}
                  </Text>
                </Group>
                <Group className="flex-none" align="center" justify="flex-end" gap={8}>
                  <Text fz={14} fw={400} lh="18px" c={tipTextColor}>
                    {knowledge.document_count || 0} 文档
                  </Text>
                </Group>
              </Group>
            ))
          )}
        </Stack>
      </ScrollArea>

      <Group align="center" justify="flex-end" px={16} gap={8}>
        <Button variant="default" onClick={onCancel}>
          取消
        </Button>
        <Button variant="filled" color="rgba(73, 81, 235, 1)" onClick={handleConfirm} disabled={selectedKnowledges.length === 0}>
          添加
        </Button>
      </Group>
    </Stack>
  );
};

export default Mobile;
