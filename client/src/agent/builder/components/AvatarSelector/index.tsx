import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { AvatarSelectorProps } from './types';

const AvatarSelector: React.FC<AvatarSelectorProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default AvatarSelector;
export { AvatarSelector };
export type { AvatarSelectorProps };
AvatarSelector.displayName = 'AvatarSelector';
