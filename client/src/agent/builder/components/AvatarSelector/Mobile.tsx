import React from 'react';
import { Stack, Group, Text, Divider, useMantineTheme, Avatar, FileInput, ActionIcon } from '@mantine/core';
import { RiQuestionLine } from 'react-icons/ri';
import { IoClose, IoImage } from 'react-icons/io5';
import { useTheme } from '~/core/features/mantine';
import { notifications } from '@mantine/notifications';
import { uploadFileToMinio } from '~/core/utils';
import { AvatarSelectorProps } from './types';

const Mobile: React.FC<AvatarSelectorProps> = ({ value, onChange, error }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const itemBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  // 处理头像上传
  const handleIconUpload = async (file: File | null) => {
    if (!file) return;

    // 检查文件类型
    if (!file.type.match(/image\/(png|jpeg|gif|webp)/)) {
      notifications.show({
        title: '错误',
        message: '请上传PNG、JPEG、GIF或WEBP格式的图片',
        color: 'red',
      });
      return;
    }

    // 检查文件大小（限制为2MB）
    if (file.size > 2 * 1024 * 1024) {
      notifications.show({
        title: '错误',
        message: '图片大小不能超过2MB',
        color: 'red',
      });
      return;
    }

    try {
      // 显示上传中提示
      const uploadingNotification = notifications.show({
        title: '上传中',
        message: '正在上传头像，请稍候...',
        loading: true,
        autoClose: false,
      });

      // 上传文件到MinIO
      const fileUrl = await uploadFileToMinio(file, 'agent/avatars/');

      // 关闭上传中提示
      notifications.hide(uploadingNotification);

      // 显示上传成功提示
      notifications.show({
        title: '成功',
        message: '头像上传成功',
        color: 'green',
      });

      // 设置头像URL
      onChange?.(fileUrl);
    } catch (error) {
      // 显示上传失败提示
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '头像上传失败',
        color: 'red',
      });
    }
  };

  // 处理头像删除
  const handleIconRemove = () => {
    onChange?.('');
  };

  return (
    <Stack className="rounded-[6px]" px={12} py={12} bg={itemBgColor} gap={0}>
      <Group align="center" justify="space-between">
        <Group align="center" gap={4}>
          <Text fz={14} fw={700} lh="20px" c={textColor}>
            头像
          </Text>
          <RiQuestionLine size={10} color="rgba(204, 204, 204, 1)" />
        </Group>
      </Group>

      <Divider my={6} />

      <Group align="center" gap={12}>
        {value ? (
          <Group align="center" gap={6}>
            <Avatar src={value} size={48} radius="xl" />
            <ActionIcon variant="subtle" color="gray" size="sm" onClick={handleIconRemove}>
              <IoClose size={14} />
            </ActionIcon>
          </Group>
        ) : (
          <FileInput
            placeholder="上传头像"
            accept="image/png,image/jpeg,image/gif,image/webp"
            onChange={handleIconUpload}
            leftSection={<IoImage size={14} />}
            error={error}
            styles={{
              input: {
                width: '100%',
                fontSize: '14px',
              },
            }}
          />
        )}
      </Group>
    </Stack>
  );
};

export default Mobile;
