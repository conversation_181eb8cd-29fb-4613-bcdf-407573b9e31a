import { useRef, useEffect, useState } from 'react';
import { Stack, Group, Text, Button, useMantineTheme, ScrollArea, Box } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { useChatContext } from '~/agent/chat/contexts/ChatContext';
import { useAgentBuilderContext } from '../../contexts/AgentBuilderContextProvider';
import { Agent } from '~/agent/marketplace/schemas';
import { ChatBox, Message } from '~/agent/chat/components';
import { saveAgent } from '../../api';
import { SaveOrUpdateAgentPayloads } from '../../schemas';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';
import { CompletionParamsSelector } from '../CompletionParamsSelector';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const containerBgColor = isDark ? theme.colors.dark[9] : 'white';
  const itemBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const textColor = isDark ? 'white' : 'black';

  // 使用聊天上下文中的messages和相关方法
  const { messages, setAgent, setConversation, setMessages } = useChatContext();
  const { form, llms, debugAgentId, setDebugAgentId } = useAgentBuilderContext();
  const queryClient = useQueryClient();
  const selectedModel = form.values.model;
  const completionParams = form.values.completionParams;

  // 消息相关
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 跟踪是否是通过"先发布再发送"触发的发布
  const [shouldSendAfterPublish, setShouldSendAfterPublish] = useState(false);

  // 构建模型配置
  const buildModelConfig = () => {
    if (!selectedModel) {
      return {
        supportAnnotation: true,
        supportCitationHitInfo: true,
      };
    }

    // 找到包含当前模型的提供商
    const provider = llms.find((p) => p.models.some((m) => m.model === selectedModel.model));
    const providerName = provider?.provider || 'openai';

    return {
      supportAnnotation: true,
      supportCitationHitInfo: true,
      model: {
        provider: providerName,
        name: selectedModel.model,
        mode: 'chat',
        completion_params: completionParams,
      },
      pre_prompt: form.values.prompt || '',
      suggested_questions: [],
      suggested_questions_after_answer: {
        enabled: false,
      },
      speech_to_text: {
        enabled: false,
      },
      text_to_speech: {
        enabled: false,
        language: 'zh-CN',
        voice: 'alloy',
      },
      agent_mode: {
        enabled: true,
        max_iteration: 5,
        strategy: 'function_call',
        tools:
          form.values.tools?.map((selectedTool) => ({
            enabled: true,
            notAuthor: false,
            provider_id: selectedTool.provider.id,
            provider_type: selectedTool.provider.type,
            provider_name: selectedTool.provider.name,
            tool_name: selectedTool.tool.name,
            tool_label: selectedTool.tool.label?.zh_Hans || selectedTool.tool.name,
            tool_parameters: {},
          })) || [],
      },
      dataset_configs: {
        retrieval_model: 'single',
        datasets: {
          datasets:
            form.values.knowledges?.map((k) => ({
              dataset: {
                enabled: true,
                id: k.id,
              },
            })) || [],
        },
        reranking_enable: false,
        top_k: 4,
        reranking_mode: 'reranking_model',
        reranking_model: {
          reranking_provider_name: 'langgenius/tongyi/tongyi',
          reranking_model_name: 'gte-rerank',
        },
      },
      file_upload: {
        enabled: form.values.enableVision || form.values.enableDocument,
        number_limits: 3,
        allowed_file_types: ['image', 'document'],
        allowed_file_extensions: ['.jpg', '.jpeg', '.png', '.gif', '.pdf', '.doc', '.docx', '.xls', '.xlsx'],
        allowed_file_upload_methods: ['local_file', 'remote_url'],
        fileUploadConfig: {
          image_file_size_limit: 10,
          batch_count_limit: 5,
          file_size_limit: 15,
          video_file_size_limit: 100,
          audio_file_size_limit: 50,
          workflow_file_upload_limit: 10,
        },
        image: {
          enabled: form.values.enableVision,
          number_limits: 3,
          detail: 'high',
          transfer_methods: ['remote_url', 'local_file'],
        },
        audio: {
          enabled: false,
          number_limits: 1,
          transfer_methods: ['local_file'],
        },
        video: {
          enabled: false,
          number_limits: 1,
          transfer_methods: ['local_file'],
        },
        file: {
          enabled: form.values.enableDocument,
          number_limits: 3,
          transfer_methods: ['local_file'],
        },
      },
    };
  };

  // 构建保存智能体的数据
  const buildSavePayloads = (): SaveOrUpdateAgentPayloads => {
    const config = buildModelConfig();

    return {
      id: debugAgentId, // 如果有ID则更新，否则创建
      name: form.values.name || '调试智能体',
      description: form.values.description || '正在调试的智能体',
      icon_url: form.values.avatar || undefined,
      is_public: form.values.isPublic,
      monthly_price: 0,
      yearly_price: 0,
      config,
    };
  };

  // 创建或更新智能体的mutation
  const { mutate: createOrUpdateAgentMutation, isPending: isPublishing } = useMutation({
    mutationFn: (payloads: SaveOrUpdateAgentPayloads) => saveAgent(payloads),
    onSuccess: (response) => {
      if (!response.error && response.data) {
        // 设置调试智能体ID
        setDebugAgentId(response.data.id);

        // 创建Agent对象并设置到聊天上下文
        const debugAgent: Agent = {
          id: response.data.id,
          name: response.data.name || '调试智能体',
          description: response.data.description || '正在调试的智能体',
          icon: response.data.icon || null,
        };

        setAgent(debugAgent);
        // 清空之前的对话和消息
        setConversation(undefined);
        setMessages([]);

        notifications.show({
          title: '成功',
          message: debugAgentId ? '智能体更新成功' : '智能体创建成功',
          color: 'green',
        });

        // 刷新 agentDetail 查询缓存，确保聊天时使用最新配置
        queryClient.invalidateQueries({
          queryKey: ['agentDetail', response.data.id],
        });

        // 如果是通过"先发布再发送"触发的，则在发布成功后自动发送消息
        if (shouldSendAfterPublish) {
          setShouldSendAfterPublish(false);
          setTimeout(() => {
            document.dispatchEvent(new CustomEvent('chatbox:send'));
          }, 200); // 延迟确保查询缓存刷新完成
        }


      } else {
        throw new Error(response.error?.message || '操作失败');
      }
    },
    onError: (error) => {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '创建或更新智能体失败',
        color: 'red',
      });
    },
  });

  // 处理发布按钮点击
  const handlePublish = () => {
    // 验证表单必填字段
    if (!form.values.name) {
      notifications.show({
        title: '错误',
        message: '请输入智能体名称',
        color: 'red',
      });
      return;
    }

    if (!form.values.description) {
      notifications.show({
        title: '错误',
        message: '请输入智能体描述',
        color: 'red',
      });
      return;
    }

    if (!selectedModel) {
      notifications.show({
        title: '错误',
        message: '请选择模型',
        color: 'red',
      });
      return;
    }

    // 构建保存数据并发起请求
    const payloads = buildSavePayloads();
    createOrUpdateAgentMutation(payloads);
  };

  // 监听"先发布再发送"事件
  useEffect(() => {
    const handlePublishAndSend = () => {
      // 验证表单必填字段
      if (!form.values.name || !form.values.description || !selectedModel) {
        // 如果验证失败，直接发送消息（使用现有配置）
        document.dispatchEvent(new CustomEvent('chatbox:send'));
        return;
      }

      // 设置标志位，表示发布成功后需要发送消息
      setShouldSendAfterPublish(true);

      // 先发布智能体
      const payloads = buildSavePayloads();
      createOrUpdateAgentMutation(payloads);
    };

    document.addEventListener('builder:publish-and-send', handlePublishAndSend);

    return () => {
      document.removeEventListener('builder:publish-and-send', handlePublishAndSend);
    };
  }, [form.values.name, form.values.description, selectedModel, createOrUpdateAgentMutation, setShouldSendAfterPublish]);

  // 自动滚动到底部
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <Stack
      className="flex-auto"
      w={500}
      py={64}
      px={32}
      style={{
        background: containerBgColor,
        boxShadow: '0px 2px 4px  rgba(0, 0, 0, 0.1)',
      }}
      gap={0}
    >
      <Group justify="flex-end" gap={8}>
        <CompletionParamsSelector {...form.getInputProps('completionParams')} />
        <Button
          variant="filled"
          size="xs"
          w={80}
          loading={isPublishing}
          onClick={handlePublish}
          styles={{
            root: {
              backgroundColor: 'rgba(73, 81, 235, 1)',
              color: 'white',
            },
          }}
        >
          发布
        </Button>
      </Group>
      <Stack className="rounded-[6px]" h={612} px={16} py={14} bg={itemBgColor} mt={8} mb={16} gap={0}>
        <Group align="center" justify="space-between" mb={16}>
          <Group align="center" gap={4}>
            <Text fz={16} fw={700} lh="24px" c={textColor}>
              调试
            </Text>
          </Group>
        </Group>

        {/* 聊天消息区域 */}
        <ScrollArea h={480} mb={16}>
          <Stack gap={16}>
            {messages.length === 0 ? (
              <Box ta="center" py={40}>
                <Text c="dimmed" fz={14}>
                  开始与智能体对话吧！
                </Text>
              </Box>
            ) : (
              messages.map((message) => <Message key={message.id} message={message} />)
            )}
            <div ref={messagesEndRef} />
          </Stack>
        </ScrollArea>
      </Stack>
      {/* 聊天输入框 */}
      <ChatBox />
    </Stack>
  );
};

export default Desktop;
