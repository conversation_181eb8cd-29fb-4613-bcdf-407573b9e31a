import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { ModelSelectorProps } from './types';

const ModelSelector: React.FC<ModelSelectorProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default ModelSelector;
export { ModelSelector };
export type { ModelSelectorProps };
ModelSelector.displayName = 'ModelSelector';
