import React, { useState, useMemo, useCallback, useEffect } from 'react';
import { Group, Popover, useMantineTheme, Text, TextInput } from '@mantine/core';
import { useTheme } from '~/core/features';
import { useAgentBuilderContext } from '../../contexts';
import { RiEyeLine, RiSearchLine } from 'react-icons/ri';
import { ModelSelectorProps } from './types';
import { Model, LLM } from '@dify_schemas/index';

const Desktop: React.FC<ModelSelectorProps> = ({ value, onChange }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const textColor = isDark ? 'white' : 'black';

  // 模型搜索
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const { llms, hasVisionCapability } = useAgentBuilderContext();

  // 防抖搜索
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // 处理模型选择 - 使用 useCallback 避免重复创建
  const handleModelSelect = useCallback(
    (model: Model) => {
      if (!model) return;
      onChange?.(model);
    },
    [onChange],
  );

  // 根据搜索过滤提供商和模型 - 使用 useMemo 避免重复计算
  const filteredProviders = useMemo(() => {
    return llms.filter((provider) => {
      // 如果没有搜索词，返回所有提供商
      if (!debouncedSearchQuery) return true;

      // 检查提供商名称是否匹配
      if (provider.label.zh_Hans?.toLowerCase().includes(debouncedSearchQuery.toLowerCase())) return true;

      // 检查提供商的模型是否匹配
      return provider.models.some(
        (model) =>
          model.label.zh_Hans?.toLowerCase().includes(debouncedSearchQuery.toLowerCase()) ||
          model.model.toLowerCase().includes(debouncedSearchQuery.toLowerCase()),
      );
    });
  }, [llms, debouncedSearchQuery]);

  // 获取当前选中模型的显示名称 - 使用 useMemo 避免重复计算
  const selectedModelDisplayName = useMemo(() => {
    if (!value) return '选择模型';
    return value.label.zh_Hans || value.model;
  }, [value]);

  // 获取当前选中模型的提供商图标 - 使用 useMemo 避免重复查找
  const selectedModelIcon = useMemo(() => {
    if (value) {
      const provider = llms.find((p) => p.models.some((m) => m.model === value.model));
      if (provider?.icon_small?.zh_Hans) {
        return `${import.meta.env.VITE_DIFY_BASE_URL}${provider.icon_small.zh_Hans}`;
      }
    }
    // 如果没有选中模型或找不到图标，使用第一个提供商的图标
    if (llms.length > 0 && llms[0].icon_small?.zh_Hans) {
      return `${import.meta.env.VITE_DIFY_BASE_URL}${llms[0].icon_small.zh_Hans}`;
    }
    return null;
  }, [value, llms]);

  // 为提供商图标创建一个 memoized 的渲染函数
  const renderProviderIcon = useCallback((provider: LLM) => {
    if (provider.icon_small?.zh_Hans) {
      return (
        <img
          src={`${import.meta.env.VITE_DIFY_BASE_URL}${provider.icon_small.zh_Hans}`}
          alt={provider.label.zh_Hans || provider.provider}
          width="16"
          height="16"
        />
      );
    }
    // 如果没有提供商图标，使用默认图标
    return (
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
        <circle cx="12" cy="12" r="10" stroke="#10A37F" strokeWidth="2" fill="none" />
      </svg>
    );
  }, []);

  return (
    <Popover
      position="bottom"
      width={400}
      shadow="md"
      styles={{
        dropdown: {
          padding: '16px',
          backgroundColor: isDark ? theme.colors.dark[6] : 'white',
          border: isDark ? '1px solid rgba(60, 60, 60, 1)' : '1px solid rgba(230, 230, 230, 1)',
          borderRadius: '12px',
          maxHeight: '400px',
          overflow: 'hidden',
        },
      }}
    >
      <Popover.Target>
        <div className="cursor-pointer rounded-md p-3 bg-gray-50 dark:bg-gray-800 flex items-center model-selector">
          <Group align="center" justify="space-between" style={{ width: '100%', flexWrap: 'nowrap' }}>
            <Group align="center" gap={8} style={{ flexWrap: 'nowrap', overflow: 'hidden' }}>
              <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center bg-blue-100 dark:bg-blue-900/30 rounded-md">
                {selectedModelIcon ? (
                  <img src={selectedModelIcon} alt={selectedModelDisplayName} width="16" height="16" />
                ) : (
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="12" cy="12" r="10" stroke="#10A37F" strokeWidth="2" fill="none" />
                  </svg>
                )}
              </div>
              <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', maxWidth: '200px' }}>
                <Text fz={14} fw={500} c={textColor} style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
                  {selectedModelDisplayName}
                </Text>
              </div>
              <div className="flex-shrink-0 px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs text-gray-600 dark:text-gray-400">
                CHAT
              </div>
            </Group>
            {value && hasVisionCapability(value) && (
              <div className="flex-shrink-0 flex items-center justify-center w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded-full">
                <RiEyeLine size={12} color={isDark ? '#aaa' : '#666'} />
              </div>
            )}
          </Group>
        </div>
      </Popover.Target>
      <Popover.Dropdown>
        <Text fz={16} fw={600} c={textColor} mb={8}>
          模型
        </Text>
        <TextInput
          placeholder="搜索模型"
          leftSection={<RiSearchLine size={16} />}
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.currentTarget.value)}
          onClick={(e) => e.stopPropagation()}
          mb={16}
        />
        <div style={{ maxHeight: '300px', overflowY: 'auto' }}>
          {filteredProviders.map((provider) => (
            <div key={provider.provider} className="mb-4">
              {provider.provider !== 'OpenAI-API-compatible' && (
                <Text fz={14} fw={600} c={textColor} mb={8}>
                  {provider.label.zh_Hans || provider.provider}
                </Text>
              )}
              {provider.models.map((model) => (
                <div
                  key={model.model}
                  className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md cursor-pointer mb-1"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleModelSelect(model);
                  }}
                >
                  <Group align="center" justify="space-between" style={{ flexWrap: 'nowrap' }}>
                    <Group align="center" gap={8} style={{ flexWrap: 'nowrap', overflow: 'hidden' }}>
                      <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center bg-blue-100 dark:bg-blue-900/30 rounded-md">
                        {renderProviderIcon(provider)}
                      </div>
                      <div
                        style={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          maxWidth: '200px',
                        }}
                      >
                        <Text fz={14} fw={500} c={textColor} style={{ overflow: 'hidden', textOverflow: 'ellipsis' }}>
                          {model.label.zh_Hans || model.model}
                        </Text>
                      </div>
                      <div className="flex-shrink-0 px-1.5 py-0.5 bg-gray-100 dark:bg-gray-700 rounded text-xs text-gray-600 dark:text-gray-400">
                        CHAT
                      </div>
                    </Group>
                    {hasVisionCapability(model) && (
                      <div className="flex-shrink-0 flex items-center justify-center w-5 h-5 bg-gray-200 dark:bg-gray-700 rounded-full">
                        <RiEyeLine size={12} color={isDark ? '#aaa' : '#666'} />
                      </div>
                    )}
                  </Group>
                </div>
              ))}
            </div>
          ))}
        </div>
      </Popover.Dropdown>
    </Popover>
  );
};

export default Desktop;
