import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { CompletionParamsSelectorProps } from './types';

const CompletionParamsSelector: React.FC<CompletionParamsSelectorProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default CompletionParamsSelector;
export { CompletionParamsSelector };
export type { CompletionParamsSelectorProps };
CompletionParamsSelector.displayName = 'CompletionParamsSelector';
