import React from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  useMantineTheme,
  Text,
  Group,
  Tooltip,
  Slider,
  NumberInput,
  Select,
  TextInput,
} from '@mantine/core';
import { useTheme } from '~/core/features';
import { useAgentBuilderContext } from '../../contexts';
import { RiEqualizerLine, RiSettings4Line } from 'react-icons/ri';
import { useQuery } from '@tanstack/react-query';
import { getModelParameterRules } from '../../api';
import { ModelParameterRule } from '@dify_schemas/index';
import { CompletionParamsSelectorProps } from './types';

const Desktop: React.FC<CompletionParamsSelectorProps> = ({ value = {}, onChange, error }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const textColor = isDark ? 'white' : 'black';
  const { llms, form, getModelDisplayName } = useAgentBuilderContext();

  const selectedModel = form.values.model;
  const modelProvider = form.values.modelProvider;

  // 获取模型参数规则
  const { data: modelParameterRulesResponse, isLoading: isLoadingParameterRules } = useQuery({
    queryKey: ['modelParameterRules', form.values.model?.model, form.values.modelProvider],
    queryFn: async () => {
      if (!selectedModel?.model || !modelProvider) return { data: { data: [] } };
      return getModelParameterRules(modelProvider, selectedModel.model);
    },
    enabled: !!selectedModel?.model && !!modelProvider,
  });

  // 提取参数规则数据
  const modelParameterRules = modelParameterRulesResponse?.data?.data || [];

  // 渲染参数控件
  const renderParameterControl = (rule: ModelParameterRule) => {
    const paramValue = value[rule.name] ?? rule.default;

    // 通用的参数标题和帮助文本渲染
    const renderLabelAndHelp = (rule: ModelParameterRule) => (
      <>
        <Group justify="space-between" mb={4}>
          <Text fz={14} fw={500} c={textColor}>
            {rule.label.zh_Hans || rule.name}
          </Text>
          {rule.help?.zh_Hans && (
            <Tooltip label={rule.help.zh_Hans} position="top" withArrow>
              <Text fz={12} c="dimmed">
                <span
                  style={{
                    display: 'inline-flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: '16px',
                    height: '16px',
                    borderRadius: '50%',
                    border: '1px solid #ccc',
                    fontSize: '10px',
                  }}
                >
                  ?
                </span>
              </Text>
            </Tooltip>
          )}
        </Group>
      </>
    );

    // 处理数值类型参数（int和float）
    const renderNumberControl = () => {
      const isInt = rule.type === 'int';
      const min = rule.min !== null && rule.min !== undefined ? rule.min : 0;
      const max = rule.max !== null && rule.max !== undefined ? rule.max : isInt ? 100 : 1;
      const step =
        rule.precision !== null && rule.precision !== undefined ? (isInt ? 1 : Math.pow(10, -rule.precision)) : isInt ? 1 : 0.01;
      const defaultValue =
        typeof paramValue === 'number' ? paramValue : typeof rule.default === 'number' ? rule.default : isInt ? 0 : 0.0;

      return (
        <div key={rule.name} className="mb-4">
          {renderLabelAndHelp(rule)}
          <Group align="center" grow>
            <Slider
              min={min}
              max={max}
              step={step}
              value={defaultValue}
              onChange={(newValue) => {
                const newParams = { ...value, [rule.name]: newValue };
                onChange?.(newParams);
              }}
              style={{ flexGrow: 1 }}
              thumbSize={14}
              size="sm"
              color="blue"
            />
            <NumberInput
              min={min}
              max={max}
              step={step}
              value={defaultValue}
              onChange={(newValue) => {
                if (newValue !== undefined && newValue !== '') {
                  const newParams = { ...value, [rule.name]: newValue };
                  onChange?.(newParams);
                }
              }}
              style={{ width: '80px' }}
              hideControls
              rightSection={null}
            />
          </Group>
        </div>
      );
    };

    // 处理布尔类型参数
    const renderBooleanControl = () => {
      return (
        <div key={rule.name} className="mb-4">
          <Group justify="space-between" align="center">
            {renderLabelAndHelp(rule)}
            <Group gap={8}>
              <Button
                variant={!paramValue ? 'filled' : 'outline'}
                size="xs"
                onClick={() => {
                  const newParams = { ...value, [rule.name]: false };
                  onChange?.(newParams);
                }}
              >
                False
              </Button>
              <Button
                variant={paramValue ? 'filled' : 'outline'}
                size="xs"
                onClick={() => {
                  const newParams = { ...value, [rule.name]: true };
                  onChange?.(newParams);
                }}
              >
                True
              </Button>
            </Group>
          </Group>
        </div>
      );
    };

    // 处理字符串类型参数
    const renderStringControl = () => {
      // 如果有选项则使用下拉框，否则使用文本输入框
      if (rule.options && rule.options.length > 0) {
        return (
          <div key={rule.name} className="mb-4">
            {renderLabelAndHelp(rule)}
            <Select
              data={rule.options.map((option) => ({ value: option, label: option }))}
              value={typeof paramValue === 'string' ? paramValue : typeof rule.default === 'string' ? rule.default : ''}
              onChange={(newValue) => {
                if (newValue !== null) {
                  const newParams = { ...value, [rule.name]: newValue };
                  onChange?.(newParams);
                }
              }}
              placeholder="请选择"
            />
          </div>
        );
      } else {
        return (
          <div key={rule.name} className="mb-4">
            {renderLabelAndHelp(rule)}
            <TextInput
              value={typeof paramValue === 'string' ? paramValue : typeof rule.default === 'string' ? rule.default : ''}
              onChange={(event) => {
                const newParams = { ...value, [rule.name]: event.currentTarget.value };
                onChange?.(newParams);
              }}
              placeholder="请输入"
            />
          </div>
        );
      }
    };

    // 根据参数类型渲染不同的控件
    switch (rule.type) {
      case 'int':
      case 'float':
        return renderNumberControl();
      case 'boolean':
        return renderBooleanControl();
      case 'string':
        return renderStringControl();
      default:
        return (
          <div key={rule.name} className="mb-4">
            <Text fz={14} fw={500} c={textColor} mb={4}>
              {rule.label.zh_Hans || rule.name}: 不支持的参数类型 {rule.type}
            </Text>
          </div>
        );
    }
  };

  return (
    <Popover
      position="bottom"
      shadow="md"
      width={400}
      styles={{
        dropdown: {
          padding: '16px',
          backgroundColor: isDark ? theme.colors.dark[6] : 'white',
          border: isDark ? '1px solid rgba(60, 60, 60, 1)' : '1px solid rgba(230, 230, 230, 1)',
          borderRadius: '12px',
        },
        arrow: {
          backgroundColor: isDark ? theme.colors.dark[6] : 'white',
          border: isDark ? '1px solid rgba(60, 60, 60, 1)' : '1px solid rgba(230, 230, 230, 1)',
        },
      }}
    >
      <Popover.Target>
        <Button
          leftSection={(() => {
            // 动态获取当前选中模型所属的提供商图标
            if (selectedModel) {
              const provider = llms.find((p) => p.models.some((m) => m.model === selectedModel.model));
              if (provider?.icon_small?.zh_Hans) {
                return (
                  <img
                    src={`${import.meta.env.VITE_DIFY_BASE_URL}${provider.icon_small.zh_Hans}`}
                    alt={selectedModel.label.zh_Hans || selectedModel.model}
                    width="14"
                    height="14"
                  />
                );
              }
            }
            // 如果没有选中模型或找不到图标，使用第一个提供商的图标
            else if (llms.length > 0 && llms[0].icon_small?.zh_Hans) {
              return (
                <img
                  src={`${import.meta.env.VITE_DIFY_BASE_URL}${llms[0].icon_small.zh_Hans}`}
                  alt={llms[0].label.zh_Hans || llms[0].provider}
                  width="14"
                  height="14"
                />
              );
            }
            // 如果都没有，使用默认图标
            return <RiSettings4Line size={14} />;
          })()}
          rightSection={<RiEqualizerLine size={14} />}
          variant="outline"
          size="xs"
          color="rgba(166, 166, 166, 1)"
          styles={{
            inner: {
              color: 'rgba(128, 128, 128, 1)',
            },
          }}
        >
          {getModelDisplayName()}
        </Button>
      </Popover.Target>
      <Popover.Dropdown>
        <Stack>
          {/* 动态参数行 */}
          <div className="border border-gray-200 dark:border-gray-700 rounded-md p-4 bg-gray-50 dark:bg-gray-900">
            {isLoadingParameterRules ? (
              <Text fz={14} c={textColor} ta="center">
                加载参数规则中...
              </Text>
            ) : modelParameterRules.length > 0 ? (
              modelParameterRules.map((rule) => renderParameterControl(rule))
            ) : (
              <Text fz={14} c={textColor} ta="center">
                暂无可配置参数
              </Text>
            )}
          </div>
          {error && (
            <Text c="red" fz={12} mt={4}>
              {error}
            </Text>
          )}
        </Stack>
      </Popover.Dropdown>
    </Popover>
  );
};

export default Desktop;
