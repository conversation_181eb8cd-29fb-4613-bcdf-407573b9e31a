import React from 'react';
import { Modal } from '@mantine/core';
import Plugin from '../ToolProviders';
import Knowledge from '../Knowledge';
import { Tool1 } from '@dify_schemas/index';
import { KnowledgeDataset } from '~/knowledge/schemas';

import { CreateDialogProps } from './types';

const Mobile: React.FC<CreateDialogProps> = ({ opened = false, type = 'plugin', onCancel, onConfirm, initialSelected }) => {
  return  <></>
};

export default Mobile;
