import React from 'react';
import { Modal } from '@mantine/core';
import ToolProviders from '../ToolProviders';
import Knowledge from '../Knowledge';
import { KnowledgeDataset } from '~/knowledge/schemas';
import { SelectedTool } from '../../schemas';

import { CreateDialogProps } from './types';

const Desktop: React.FC<CreateDialogProps> = ({ opened = false, type = 'plugin', onCancel, onConfirm, initialSelected }) => {
  return (
    <Modal
      opened={opened}
      withCloseButton={false}
      closeOnClickOutside={true}
      onClose={onCancel}
      centered
      styles={{
        content: {
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          backgroundColor: 'transparent',
          boxShadow: 'none',
        },
        body: {
          width: '510px',
          padding: 0,
        },
      }}
    >
      {type === 'plugin' && (
        <ToolProviders
          onCancel={onCancel}
          onConfirm={onConfirm}
          initialSelected={type === 'plugin' && initialSelected ? (initialSelected as SelectedTool[]) : []}
          tool_type="builtin" // 获取所有类型的工具
        />
      )}
      {type === 'knowledge' && (
        <Knowledge
          onCancel={onCancel}
          onConfirm={onConfirm}
          initialSelected={type === 'knowledge' && initialSelected ? (initialSelected as KnowledgeDataset[]) : []}
        />
      )}
    </Modal>
  );
};

export default Desktop;
