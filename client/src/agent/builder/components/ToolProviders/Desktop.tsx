/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React, { useState, useEffect } from 'react';
import { Box, Group, Stack, Input, ScrollArea, Text, useMantineTheme, Button, Checkbox, Loader, Center } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiArrowRightSLine } from 'react-icons/ri';
import { useQuery } from '@tanstack/react-query';
import { getToolsList } from '~/agent/builder/api/tool';
import { ToolProvidersProps } from './types';
import { ToolProvider, Tool1 } from '@dify_schemas/index';
import { SelectedTool } from '../../schemas';

const Desktop: React.FC<ToolProvidersProps> = ({ onCancel, onConfirm, initialSelected = [], tool_type = 'builtin' }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const containerBgColor = isDark ? theme.colors.dark[9] : 'white';
  const itemBgColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const tipTextColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';

  const [expandedCategory, setExpandedCategory] = useState<string | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  // 使用统一的 SelectedTool 类型
  const getIcon = (provider: ToolProvider) => {
    //icon如果是一个字符串 ， 则是图片， 使用 DIFY_BASE_URL + icon.content作为src
    //如果是一个对象，则它是一个emoji
    if (typeof provider.icon === 'string') {
      return (
        <img
          src={`${import.meta.env.VITE_DIFY_BASE_URL}${provider.icon}`}
          alt={provider.label.zh_Hans || provider.label.en_US || provider.name}
          width="16"
          height="16"
        />
      );
    } else if (provider.icon && typeof provider.icon === 'object') {
      return <span>{provider.icon.content}</span>;
    }

    return null;
  };
  const [selectedTools, setSelectedTools] = useState<SelectedTool[]>([]);

  // 使用React Query获取工具列表
  const {
    data: toolProviders,
    error,
    isLoading,
  } = useQuery({
    queryKey: ['tools', tool_type],
    queryFn: async () => {
      const response = await getToolsList(tool_type);
      return response.data;
    },
  });

  // 处理初始选中的工具
  useEffect(() => {
    if (toolProviders && initialSelected.length > 0) {
      const initialSelectedTools: SelectedTool[] = [];

      for (const selectedTool of initialSelected) {
        // 在 toolProviders 中查找对应的 provider 和 tool
        for (const provider of toolProviders) {
          const foundTool = provider.tools.find((t) => t.name === selectedTool.tool.name);
          if (foundTool) {
            initialSelectedTools.push({ provider, tool: foundTool });
            break;
          }
        }
      }

      setSelectedTools(initialSelectedTools);
    }
  }, [toolProviders, initialSelected]);

  // 处理工具选择/取消选择
  const handleToggleTool = (provider: ToolProvider, tool: Tool1) => {
    setSelectedTools((prev) => {
      const isSelected = prev.some((item) => item.provider.id === provider.id && item.tool.name === tool.name);
      if (isSelected) {
        return prev.filter((item) => !(item.provider.id === provider.id && item.tool.name === tool.name));
      } else {
        return [...prev, { provider, tool }];
      }
    });
  };

  // 检查工具是否被选中
  const isToolSelected = (provider: ToolProvider, tool: Tool1) => {
    return selectedTools.some((item) => item.provider.id === provider.id && item.tool.name === tool.name);
  };

  return (
    <Stack
      w={430}
      h={572}
      bg={containerBgColor}
      gap={0}
      style={{
        borderRadius: '6px',
        boxShadow: '0px 2px 4px  rgba(0, 0, 0, 0.1)',
      }}
    >
      <Box px={16} pt={16} pb={8}>
        <Input
          className="rounded-[6px]"
          variant="unstyled"
          placeholder="搜索工具..."
          bg={itemBgColor}
          px={8}
          value={searchKeyword}
          onChange={(e) => setSearchKeyword(e.currentTarget.value)}
        />
      </Box>

      <ScrollArea className="flex-auto" px={16} pt={8} pb={16} style={{ whiteSpace: 'nowrap' }}>
        {isLoading ? (
          <Center h={200}>
            <Loader size="md" color="rgba(73, 81, 235, 1)" />
          </Center>
        ) : error ? (
          <Center h={200}>
            <Text c="red">{error.message || '加载失败'}</Text>
          </Center>
        ) : (
          <Stack gap={8}>
            {/* 过滤工具提供者 */}
            {toolProviders
              ?.filter(
                (provider: ToolProvider) =>
                  searchKeyword === '' ||
                  provider.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
                  provider.tools.some(
                    (tool: Tool1) =>
                      tool.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
                      (tool.description.zh_Hans &&
                        tool.description.zh_Hans.toLowerCase().includes(searchKeyword.toLowerCase())) ||
                      (tool.description.en_US && tool.description.en_US.toLowerCase().includes(searchKeyword.toLowerCase())),
                  ),
              )
              .map((provider: ToolProvider) => (
                <React.Fragment key={provider.id}>
                  {/* 工具提供者 */}
                  <Group
                    className="cursor-pointer"
                    align="center"
                    justify="space-between"
                    h={36}
                    px={8}
                    onClick={() => setExpandedCategory(expandedCategory === provider.id ? null : provider.id)}
                    style={{
                      borderRadius: '6px',
                    }}
                    css={css`
                      &:hover {
                        background-color: ${itemBgColor} !important;
                      }
                    `}
                  >
                    <Group className="flex-auto" align="center" justify="flex-start" gap={8}>
                      {getIcon(provider)}
                      <Text fz={16} fw={700} lh="24px" c={textColor}>
                        {provider.label.zh_Hans || provider.label.en_US || provider.name}
                      </Text>
                    </Group>
                    <Group className="flex-none" align="center" justify="flex-end" gap={8}>
                      <Text fz={14} fw={500} lh="18px" c={tipTextColor}>
                        {provider.author}
                      </Text>
                      <RiArrowRightSLine
                        size={20}
                        color={tipTextColor}
                        style={{
                          transform: expandedCategory === provider.id ? 'rotate(90deg)' : 'none',
                          transition: 'transform 0.2s ease-in-out',
                        }}
                      />
                    </Group>
                  </Group>

                  {/* 子工具列表 */}
                  {expandedCategory === provider.id && (
                    <Box pl={16}>
                      <Stack gap={8}>
                        {provider.tools
                          .filter(
                            (tool: Tool1) =>
                              searchKeyword === '' ||
                              tool.name.toLowerCase().includes(searchKeyword.toLowerCase()) ||
                              (tool.description.zh_Hans &&
                                tool.description.zh_Hans.toLowerCase().includes(searchKeyword.toLowerCase())) ||
                              (tool.description.en_US &&
                                tool.description.en_US.toLowerCase().includes(searchKeyword.toLowerCase())),
                          )
                          .map((tool: Tool1) => {
                            return (
                              <Group
                                key={`${provider.id}-${tool.name}`}
                                className="cursor-pointer"
                                align="center"
                                justify="space-between"
                                h={36}
                                px={8}
                                onClick={() => handleToggleTool(provider, tool)}
                                style={{
                                  borderRadius: '6px',
                                  backgroundColor: isToolSelected(provider, tool)
                                    ? isDark
                                      ? 'rgba(73, 81, 235, 0.2)'
                                      : 'rgba(73, 81, 235, 0.1)'
                                    : 'transparent',
                                }}
                                css={css`
                                  &:hover {
                                    background-color: ${isToolSelected(provider, tool)
                                      ? isDark
                                        ? 'rgba(73, 81, 235, 0.3)'
                                        : 'rgba(73, 81, 235, 0.15)'
                                      : itemBgColor} !important;
                                  }
                                `}
                              >
                                <Group className="flex-auto" align="center" justify="flex-start" gap={8}>
                                  <Checkbox
                                    checked={isToolSelected(provider, tool)}
                                    onChange={() => {}} // 点击Group时会处理
                                    color="rgba(73, 81, 235, 1)"
                                    size="sm"
                                  />
                                  <Text className="flex-auto" fz={16} fw={500} lh="24px" c={textColor}>
                                    {tool.label.zh_Hans || tool.label.en_US || tool.name}
                                  </Text>
                                </Group>
                                <Group className="flex-none" align="center" justify="flex-end" gap={8}>
                                  <Text fz={14} fw={400} lh="18px" c={tipTextColor}>
                                    {provider.type}
                                  </Text>
                                </Group>
                              </Group>
                            );
                          })}
                      </Stack>
                    </Box>
                  )}
                </React.Fragment>
              ))}
          </Stack>
        )}
      </ScrollArea>

      <Group align="center" justify="space-between" px={16} py={12} gap={8}>
        {/* 右侧操作按钮 */}
        <Group gap={8}>
          <Button variant="default" onClick={onCancel}>
            取消
          </Button>
          <Button
            variant="filled"
            color="rgba(73, 81, 235, 1)"
            onClick={() => onConfirm(selectedTools)}
            disabled={selectedTools.length === 0}
          >
            添加
          </Button>
        </Group>
      </Group>
    </Stack>
  );
};

export default Desktop;
