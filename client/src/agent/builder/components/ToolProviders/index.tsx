import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

import { ToolProvidersProps } from './types';

const ToolProviders: React.FC<ToolProvidersProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default ToolProviders;
ToolProviders.displayName = 'ToolProviders';
