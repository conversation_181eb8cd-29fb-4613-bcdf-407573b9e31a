import { ToolProvider } from 'packages/dify_schema/src/tool/schems';
import { API_BASE_URL } from '~/core/constants';
import { ResponsePayloads } from '~/core/schemas';
import { useUserStore } from '~/user/core/store';

/**
 * 获取工具列表
 * @param tool_type 工具类型，默认为 'builtin'
 * @returns 工具列表
 */
export async function getToolsList(tool_type: string = 'builtin'): Promise<ResponsePayloads<ToolProvider[]>> {
  const token = useUserStore.getState().token;
  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  // 构建查询参数
  const params = new URLSearchParams();
  params.append('tool_type', tool_type);

  // 构建完整的URL
  const url = `${API_BASE_URL}/agents/tools?${params.toString()}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });
  if (!response.ok) {
    throw new Error(`获取工具列表失败: ${response.statusText}`);
  }

  const data = await response.json();

  return data;
}
