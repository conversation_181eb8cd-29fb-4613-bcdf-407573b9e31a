import { AgentDetail } from '~/agent/marketplace/schemas';
import { API_BASE_URL } from '~/core/constants';
import { ResponsePayloads } from '~/core/schemas';
import { useUserStore } from '~/user/core/store';
import { SaveOrUpdateAgentPayloads } from '../schemas';

export * from './llm';
export * from './tool';

/**
 * 保存智能体（创建或更新）
 * 如果payload中的ID不存在则创建，存在则更新
 * @param payloads 智能体参数（包含可选的id字段）
 * @returns 创建或更新后的智能体详情
 */
export async function saveAgent(payloads: SaveOrUpdateAgentPayloads): Promise<ResponsePayloads<AgentDetail>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }
  const response = await fetch(`${API_BASE_URL}/agents/save`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(payloads),
  });

  if (!response.ok) {
    const respText = (await response.json()) as ResponsePayloads<unknown>;
    throw new Error(respText.error?.message || '创建或更新智能体失败');
  }

  return response.json();
}
