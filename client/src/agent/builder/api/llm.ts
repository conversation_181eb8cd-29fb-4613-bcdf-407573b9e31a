import { API_BASE_URL } from '~/core/constants';
import { ResponsePayloads } from '~/core/schemas';
import { useUserStore } from '~/user/core/store';
import { LLMList, ModelParameterRuleList } from '@dify_schemas/index';

/**
 * 获取LLM模型列表
 * @returns LLM模型列表
 */
export async function getLLMs(): Promise<ResponsePayloads<LLMList>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/agents/llms`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取LLM模型列表失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取模型参数规则
 * @param provider 模型提供者，例如 'langgenius'
 * @param model 模型名称，例如 'openai/gpt-4.1'
 * @returns 模型参数规则列表
 */
export async function getModelParameterRules(provider: string, model: string): Promise<ResponsePayloads<ModelParameterRuleList>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const params = new URLSearchParams({
    provider,
    model,
  });

  const response = await fetch(`${API_BASE_URL}/agents/model-parameter-rules?${params}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取模型参数规则失败: ${response.statusText}`);
  }

  return response.json();
}
