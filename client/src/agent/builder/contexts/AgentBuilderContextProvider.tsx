import { useForm, UseFormReturnType, zodResolver } from '@mantine/form';
import { useQuery } from '@tanstack/react-query';
import { createContext, useContext, useState } from 'react';
import { AgentBuilderFormValues, AgentBuilderFormValuesSchema, SelectedTool } from '../schemas';
import { getLLMs } from '../api';
import { getToolsList } from '../api/tool';
import { LLM, Model } from '@dify_schemas/index';
import { useParams, useSearchParams } from 'react-router-dom';
import { getDetail } from '~/agent/marketplace/api';
import { AgentDetail } from '~/agent/marketplace';
import { useChatContext } from '~/agent/chat/contexts';
import { getKnowledgeDetail } from '~/knowledge/api';
import { KnowledgeDataset } from '~/knowledge/schemas';

type AgentBuilderContextType = {
  form: UseFormReturnType<AgentBuilderFormValues>;
  llms: LLM[];
  hasVisionCapability: (model?: Model) => boolean;
  hasDocumentCapability: (model?: Model) => boolean;
  getModelDisplayName: () => string;
  debugAgentId: string | undefined;
  setDebugAgentId: (id: string | undefined) => void;
};
export const AgentBuilderContext = createContext<AgentBuilderContextType | undefined>(undefined);
export const useAgentBuilderContext = () => {
  const context = useContext(AgentBuilderContext);
  if (!context) {
    throw new Error('useAgentBuilderContext must be used within a AgentBuilderContextProvider');
  }
  return context;
};
export const AgentBuilderContextProvider = ({ children }: { children: React.ReactNode }) => {
  const { id: pathId } = useParams<{ id: string }>();
  const [searchParams] = useSearchParams();
  const queryId = searchParams.get('id');

  // 优先使用查询参数中的ID，如果没有则使用路径参数中的ID
  const id = queryId || pathId;

  const [debugAgentId, setDebugAgentId] = useState<string | undefined>(id);
  const { setAgent, setConversation, setMessages } = useChatContext();

  // 先定义表单
  const form = useForm<AgentBuilderFormValues>({
    validate: zodResolver(AgentBuilderFormValuesSchema),
    initialValues: {
      name: '',
      description: '',
      avatar: '',
      model: undefined,
      completionParams: {},
      modelProvider: '',
      prompt: '',
      tools: [],
      knowledges: [],
      enableVision: false,
      enableDocument: false,
      isPublic: false,
    },
  });

  // 获取模型列表
  const { data: llms } = useQuery({
    queryKey: ['llms'],
    queryFn: async () => {
      const llmList = (await getLLMs()).data;
      // 设置默认模型提供商和模型
      if (!id) {
        form.setFieldValue('modelProvider', llmList.data[0].provider);
        form.setFieldValue('model', llmList.data[0].models[0]);
      }
      return llmList;
    },
    refetchOnWindowFocus: false,
  });
  // 将工具配置转换为 SelectedTool 格式
  const convertToolsToSelectedTools = (agentTools: any[], toolProviders: any[]): SelectedTool[] => {
    if (!agentTools || !toolProviders) return [];

    const selectedTools: SelectedTool[] = [];

    for (const agentTool of agentTools) {
      // 查找对应的工具提供者
      for (const provider of toolProviders) {
        const tool = provider.tools?.find((t: any) =>
          t.name === agentTool.tool_name &&
          provider.id === agentTool.provider_id
        );

        if (tool) {
          selectedTools.push({
            tool,
            provider: provider // 直接使用完整的 provider 对象
          });
          break;
        }
      }
    }

    return selectedTools;
  };

  // 从LLM列表中查找模型
  const findModelFromLLMs = (provider: string, modelName: string): Model | undefined => {
    if (!llms?.data) {
      return undefined;
    }

    const llmProvider = llms.data.find(llm => llm.provider === provider);
    if (!llmProvider) {
      return undefined;
    }

    const foundModel = llmProvider.models.find(model => model.model === modelName);
    return foundModel;
  };

  // 获取知识库详情列表
  const fetchKnowledgeDetails = async (datasetIds: string[]): Promise<KnowledgeDataset[]> => {
    if (!datasetIds.length) return [];

    try {
      const knowledgePromises = datasetIds.map(id => getKnowledgeDetail(id));
      const knowledgeResponses = await Promise.all(knowledgePromises);
      return knowledgeResponses.map(response => response.data);
    } catch (error) {
      console.error('获取知识库详情失败:', error);
      return [];
    }
  };

  // 将agent详情转换为表单值
  const convertAgentDetailToFormValues = async (detail: AgentDetail, toolProviders?: any[]): Promise<Partial<AgentBuilderFormValues>> => {
    const modelConfig = detail.model_config;
    const fileUpload = modelConfig?.file_upload;
    const agentMode = modelConfig?.agent_mode;

    // 提取知识库ID列表
    const datasets = modelConfig?.dataset_configs?.datasets?.datasets || [];

    const datasetIds = datasets
      .map((item: unknown) => {
        const datasetItem = item as { dataset?: { id?: string; enabled?: boolean } };
        return datasetItem.dataset?.id;
      })
      .filter((id): id is string => !!id);


    // 获取完整的知识库信息
    const knowledges = await fetchKnowledgeDetails(datasetIds);

    // 提取工具数据 - 转换为 SelectedTool 格式
    const agentTools = agentMode?.tools || [];
    const tools = convertToolsToSelectedTools(agentTools, toolProviders || []);

    // 从LLM列表中查找对应的模型
    let model = undefined;
    if (modelConfig?.model) {

      model = findModelFromLLMs(modelConfig.model.provider, modelConfig.model.name);
    }

    return {
      name: detail.name || '',
      description: detail.description || '',
      avatar: detail.icon || '',
      model,
      completionParams: modelConfig?.model?.completion_params || {},
      modelProvider: modelConfig?.model?.provider || '',
      prompt: modelConfig?.pre_prompt || '',
      tools,
      knowledges,
      enableVision: fileUpload?.allowed_file_types?.includes('image') || false,
      enableDocument: fileUpload?.allowed_file_types?.includes('document') || false,
      isPublic: detail.is_public || false,
    };
  };

  useQuery({
    queryKey: ['agentDetail', id],
    queryFn: async () => {
      // 同时获取智能体详情和工具列表
      const [agentDetailResponse, toolsResponse] = await Promise.all([
        getDetail(id!),
        getToolsList('all') // 获取所有类型的工具用于匹配
      ]);

      const agentDetail = agentDetailResponse.data;
      const toolProviders = toolsResponse.data;

      // 使用工具列表转换表单值
      const formValues = await convertAgentDetailToFormValues(agentDetail, toolProviders);
      form.setValues(formValues);

      setAgent({
        id: agentDetail.id,
        name: agentDetail.name,
        description: agentDetail.description,
        icon: agentDetail.icon,
      });

      // 清空之前的对话和消息
      setConversation(undefined);
      setMessages([]);

      return agentDetail;
    },
    enabled: !!id && !!llms?.data, // 确保ID存在且LLM数据已加载
    refetchOnWindowFocus: false,
  });

  // 检查模型是否支持视觉功能
  const hasVisionCapability = (model?: Model) => {
    if (!model) return false;
    return model.features.includes('vision');
  };

  // 检查模型是否支持文档功能
  const hasDocumentCapability = (model?: Model) => {
    if (!model) return false;
    return model.features.includes('document');
  };

  // 获取模型显示名称
  const getModelDisplayName = () => {
    const selectedModel = form.values.model;
    if (!selectedModel) {
      return '选择模型';
    }
    return selectedModel.label.zh_Hans || selectedModel.model;
  };

  const contextValue: AgentBuilderContextType = {
    form,
    llms: llms?.data ?? [],
    hasVisionCapability,
    hasDocumentCapability,
    getModelDisplayName,
    debugAgentId,
    setDebugAgentId,
  };
  return <AgentBuilderContext.Provider value={contextValue}>{children} </AgentBuilderContext.Provider>;
};
