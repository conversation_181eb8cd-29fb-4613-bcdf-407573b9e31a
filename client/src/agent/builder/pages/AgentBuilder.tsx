import { Container, Group } from '@mantine/core';
import { CreateForm, DebugPreView } from '../components';
import { AgentBuilderContextProvider } from '../contexts';
import { ChatContextProvider } from '~/agent/chat/contexts';

const AgentBuilder = () => {
  return (
    <ChatContextProvider>
      <AgentBuilderContextProvider>
        <Container size="1452px" py={72}>
          <Group align="stretch" gap={16}>
            <CreateForm />
            <DebugPreView />
          </Group>
        </Container>
      </AgentBuilderContextProvider>
    </ChatContextProvider>
  );
};

export default AgentBuilder;
