import { ModelConfigSchema } from '@dify_schemas/app/schemas';
import { z } from 'zod';
import { AssetBaseSchema, AssetQueryBaseSchema } from '~/core/schemas';

/**
 * 智能体类型
 */
export enum AgentType {
  CHAT = 'chat',
  AGENT_CHAT = 'agent-chat',
  WORKFLOW = 'workflow',
  COMPLETION = 'completion',
  BUNDLE = 'bundle', // 智能体捆绑包类型
}

/**
 * 智能体评级分布项
 */
export const RatingDistributionSchema = z.object({
  stars: z.number().min(1).max(5).describe('星级数量'),
  percentage: z.number().min(0).max(100).describe('该星级的百分比'),
});

/**
 * 智能体统计信息模型
 */
export const AgentStatsSchema = z.object({
  rating: z.number().min(0).max(5).describe('智能体平均评级'),
  rating_count: z.string().describe("评级数量，例如：'50K+'"),
  rank: z.string().describe("排名，例如：'#13'"),
  category: z.string().describe('所属类别'),
  conversation_count: z.string().describe("对话数量，例如：'3M+'"),
  rating_distribution: z.array(RatingDistributionSchema).default([]).describe('各星级评分的分布情况'),
});

/**
 * 简化的智能体信息，用于标签分组展示
 * 继承自 AssetBaseSchema 以保持类型一致性
 */
export const AgentSchema = AssetBaseSchema.extend({
  type: z.nativeEnum(AgentType).nullable().optional().describe('智能体类型'),
  owner_id: z.number().optional().nullable().describe('创建者ID'),
});

/**
 * 按标签分组的智能体列表
 */
export const AgentsByTagSchema = z.object({
  data: z.record(z.string(), z.array(AgentSchema)).describe('按标签分组的智能体列表'),
});

/**
 * 智能体详情模式
 * 包含完整的智能体信息，用于详情页展示
 */
export const AgentDetailSchema = AgentSchema.extend({
  // 基本信息
  type: z.nativeEnum(AgentType).optional().describe('智能体类型'),
  mode: z.nativeEnum(AgentType).optional().describe('智能体类型'),
  created_by: z.string().optional().describe('创建者'),
  created_at: z.number().optional().describe('创建时间'),
  updated_at: z.number().optional().describe('更新时间'),

  // 捆绑包信息
  bundle_agent_ids: z.array(z.string()).default([]).describe('捆绑包包含的智能体ID列表'),

  // 状态信息
  is_public: z.boolean().default(false).describe('是否公开'),

  // 标签
  tags: z.array(z.string()).default([]).describe('标签列表'),

  // 模型配置
  model_config: ModelConfigSchema.optional().describe('模型配置'),

  // 工作流配置（仅工作流类型智能体有效）
  workflow_publish: z.record(z.unknown()).optional().describe('工作流发布配置'),

  // 统计信息
  stats: AgentStatsSchema.optional().describe('智能体统计信息'),

  // 功能列表
  features: z.array(z.string()).default([]).describe('智能体支持的功能列表'),

  // 购买状态
  has_purchased: z.boolean().default(false).describe('用户是否已购买该智能体'),
});

/**
 * 智能体搜索参数
 * 继承自 AssetQueryBaseSchema 并添加智能体特有的 mode 字段和 excludeIds 字段
 */
export const AgentQuerySchema = AssetQueryBaseSchema.extend({
  mode: z.nativeEnum(AgentType).optional().describe('智能体类型'),
  excludeIds: z.array(z.string()).optional().describe('排除的智能体ID列表'),
  owner_id: z.number().optional().describe('创建者ID'),
});

// 导出类型
export type Agent = z.infer<typeof AgentSchema>;
export type AgentDetail = z.infer<typeof AgentDetailSchema>;
export type AgentsByTag = z.infer<typeof AgentsByTagSchema>;
export type AgentQuery = z.infer<typeof AgentQuerySchema>;
export type RatingDistribution = z.infer<typeof RatingDistributionSchema>;
export type AgentStats = z.infer<typeof AgentStatsSchema>;
