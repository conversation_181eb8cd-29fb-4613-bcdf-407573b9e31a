import { ResponsePayloads, Pagination } from '~/core/schemas';
import { useUserStore } from '~/user/core/store';
import { AgentsByTag, AgentQuery, Agent, AgentDetail } from './schemas';
import { API_BASE_URL } from '~/core/constants';
/**
 * 获取按标签分组的智能体列表
 * @param types 智能体类型列表，用于过滤特定类型的智能体
 * @returns 按标签分组的智能体列表
 */
export async function getAgentsByTags(types?: string[]): Promise<ResponsePayloads<AgentsByTag>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  // 构建查询参数
  const params = new URLSearchParams();
  if (types && types.length > 0) {
    types.forEach(type => params.append('types', type));
  }

  const url = `${API_BASE_URL}/agents/by_tags${params.toString() ? `?${params.toString()}` : ''}`;

  const response = await fetch(url, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取智能体列表失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 搜索智能体
 * @param params 搜索参数
 * @returns 分页的智能体列表
 */
export async function searchAgents(params: AgentQuery): Promise<ResponsePayloads<Pagination<Agent>>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (params.keyword) queryParams.append('keyword', params.keyword);
  if (params.tags && params.tags.length > 0) {
    params.tags.forEach((tag) => queryParams.append('tags', tag));
  }
  if (params.page) queryParams.append('page', params.page.toString());
  if (params.page_size) queryParams.append('page_size', params.page_size.toString());
  if (params.mode) queryParams.append('mode', params.mode);
  // 添加排除ID列表
  if (params.excludeIds && params.excludeIds.length > 0) {
    params.excludeIds.forEach((id) => queryParams.append('exclude_ids', id));
  }
  // 添加创建者ID
  if (params.owner_id) queryParams.append('owner_id', params.owner_id.toString());

  const response = await fetch(`${API_BASE_URL}/agents/search?${queryParams.toString()}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`搜索智能体失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取由同一创建者创建的其他智能体
 * @param ownerId 创建者ID
 * @param excludeAgentId 要排除的当前智能体ID
 * @param limit 返回结果数量限制
 * @returns 分页的智能体列表
 */
export async function getAgentsByCreator(
  ownerId: number,
  excludeAgentId?: string,
  limit: number = 3,
): Promise<ResponsePayloads<Pagination<Agent>>> {
  // 使用搜索API，将创建者ID作为过滤条件
  const params: AgentQuery = {
    owner_id: ownerId,
    page: 1,
    page_size: limit,
    excludeIds: excludeAgentId ? [excludeAgentId] : [],
  };

  return searchAgents(params);
}

/**
 * 根据ID获取智能体详情
 * @param id 智能体ID
 * @returns 智能体详情
 */
export async function getDetail(id: string): Promise<ResponsePayloads<AgentDetail>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/agents/detail?agent_id=${id}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取智能体详情失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取用户拥有的智能体列表
 * @returns 用户拥有的智能体列表
 */
export async function getOwnedAgents(): Promise<ResponsePayloads<Agent[]>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/agents/owned`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取用户拥有的智能体失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 删除智能体
 * @param id 智能体ID
 * @returns 操作结果
 */
export async function deleteAgent(id: string): Promise<ResponsePayloads<{ result: string }>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/agents/delete/${id}`, {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`删除智能体失败: ${response.statusText}`);
  }

  return response.json();
}
