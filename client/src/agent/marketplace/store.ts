import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';

/**
 * 智能体商店状态
 */
interface AgentMarketplaceState {
  // 当前选中的分类
  activeCategory: string;
  // 设置当前选中的分类
  setActiveCategory: (category: string) => void;
  // 搜索关键词
  searchKeyword: string;
  // 设置搜索关键词
  setSearchKeyword: (keyword: string) => void;
}

/**
 * 智能体商店状态管理
 */
export const useAgentMarketplaceStore = create<AgentMarketplaceState>()(
  devtools(
    persist(
      (set) => ({
        // 默认选中"精选推荐"分类
        activeCategory: '精选推荐',
        searchKeyword: '',

        // 设置当前选中的分类
        setActiveCategory: (category) => set({ activeCategory: category }, false, 'setActiveCategory'),
        setSearchKeyword: (keyword) => set({ searchKeyword: keyword }, false, 'setSearchKeyword'),
      }),
      {
        name: 'agent-marketplace-storage',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          // 只持久化这些状态
          activeCategory: state.activeCategory,
          searchKeyword: state.searchKeyword,
        }),
      },
    ),
    {
      name: 'agent-marketplace-store',
    },
  ),
);

/**
 * 获取智能体商店状态
 */
export const getAgentMarketplaceStore = () => useAgentMarketplaceStore.getState();
