import { Image, Skeleton } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import React, { useEffect } from 'react';
import { getAgentsByCreator } from '../../api';
import { AgentDetail } from '../../schemas';

interface DesktopRelatedAgentsProps {
  /**
   * 智能体详情数据
   */
  agentDetail?: AgentDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 当前智能体ID
   */
  agentId: string;
  /**
   * 关闭弹框的回调函数
   */
  onClose: () => void;
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
}

/**
 * 相关智能体组件 - 桌面版
 */
const Desktop: React.FC<DesktopRelatedAgentsProps> = ({ agentDetail, isDark, agentId, onClose, isLoading: parentIsLoading }) => {
  // 获取同一创建者的其他智能体
  const {
    data: creatorAgentsResponse,
    isLoading: isLoadingCreatorAgents,
    refetch: refetchCreatorAgents,
  } = useQuery({
    queryKey: ['creatorAgents', agentDetail?.owner_id, agentId],
    queryFn: () => getAgentsByCreator(agentDetail?.owner_id || 0, agentId),
    enabled: !!agentDetail?.owner_id,
    refetchOnWindowFocus: false,
  });

  // 当创建者信息加载完成后，重新获取创建者的其他智能体
  useEffect(() => {
    if (agentDetail?.owner_id) {
      refetchCreatorAgents();
    }
  }, [agentDetail?.owner_id, refetchCreatorAgents]);

  // 如果父组件正在加载，显示骨架屏
  if (parentIsLoading) {
    return (
      <div className="flex flex-col">
        <div className="mb-2">
          <div className="font-bold mt-6">相关智能体</div>
        </div>
        <div className="flex flex-col space-y-2">
          {Array(3)
            .fill(0)
            .map((_, index) => (
              <div key={index} className={`bg-token-main-surface-secondary h-fit w-full rounded-xl px-3 py-4`}>
                <div className="flex w-full grow items-center gap-4 overflow-hidden">
                  <Skeleton height={48} circle />
                  <div className="overflow-hidden break-words text-ellipsis w-full">
                    <Skeleton height={20} width="80%" mb={8} />
                    <Skeleton height={12} width="100%" mb={4} />
                    <Skeleton height={12} width="90%" mb={4} />
                    <Skeleton height={12} width="60%" />
                  </div>
                </div>
              </div>
            ))}
        </div>
      </div>
    );
  }

  // 如果没有创建者ID或者没有关联智能体，则不显示组件
  if (!agentDetail?.owner_id || !creatorAgentsResponse?.data?.data || creatorAgentsResponse.data.data.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col">
      <div className="mb-2">
        <div className="font-bold mt-6">由 {agentDetail.created_by} 创建的更多项</div>
      </div>
      <div className="flex flex-col space-y-2">
        {isLoadingCreatorAgents
          ? // 加载状态显示骨架屏
            Array(3)
              .fill(0)
              .map((_, index) => (
                <div key={index} className={`bg-token-main-surface-secondary h-fit w-full rounded-xl px-3 py-4`}>
                  <div className="flex w-full grow items-center gap-4 overflow-hidden">
                    <Skeleton height={48} circle />
                    <div className="overflow-hidden break-words text-ellipsis w-full">
                      <Skeleton height={20} width="80%" mb={8} />
                      <Skeleton height={12} width="100%" mb={4} />
                      <Skeleton height={12} width="90%" mb={4} />
                      <Skeleton height={12} width="60%" />
                    </div>
                  </div>
                </div>
              ))
          : // 显示创建者的其他智能体
            creatorAgentsResponse?.data?.data?.map((agent) => (
              <div
                key={agent.id}
                className={`bg-token-main-surface-secondary h-fit w-full rounded-xl px-3 py-4 cursor-pointer ${
                  isDark ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
                }`}
                onClick={() => {
                  // 关闭当前模态框，然后打开新的智能体详情
                  onClose();
                  // 延迟一下再打开新的模态框，避免闪烁
                  setTimeout(() => {
                    window.dispatchEvent(new CustomEvent('openAgentDetail', { detail: { agentId: agent.id } }));
                  }, 100);
                }}
              >
                <div className="flex w-full grow items-center gap-4 overflow-hidden">
                  <div className="h-12 w-12 shrink-0">
                    <div className="gizmo-shadow-stroke overflow-hidden rounded-full">
                      <Image
                        className="flex-none"
                        radius={48}
                        w={48}
                        h={48}
                        src={agent.icon || 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743067298-ai.jpg'}
                      />
                    </div>
                  </div>
                  <div className="overflow-hidden break-words text-ellipsis">
                    <span className="line-clamp-2 text-sm leading-tight font-semibold">{agent.name}</span>
                    <span className="line-clamp-3 text-xs">{agent.description}</span>
                    <div className="text-token-text-tertiary mt-1 flex items-center gap-1 pe-1 text-xs text-ellipsis whitespace-nowrap">
                      <div className="flex flex-row items-center space-x-1">
                        <div className="text-token-text-tertiary text-xs">创建者：{agentDetail?.created_by || 'AI助手'}</div>
                      </div>
                      <span className="text-[8px]">•</span>
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-3 w-3"
                      >
                        <path
                          fillRule="evenodd"
                          clipRule="evenodd"
                          d="M12 4C7.58172 4 4 7.58172 4 12C4 14.1941 4.88193 16.1802 6.31295 17.6265C6.6343 17.9513 6.69466 18.4526 6.45959 18.8443L5.76619 20H12C16.4183 20 20 16.4183 20 12C20 7.58172 16.4183 4 12 4ZM2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12C22 17.5228 17.5228 22 12 22H4C3.63973 22 3.30731 21.8062 3.1298 21.4927C2.95229 21.1792 2.95715 20.7944 3.14251 20.4855L4.36137 18.4541C2.88894 16.7129 2 14.4595 2 12Z"
                          fill="currentColor"
                        />
                      </svg>
                      3M+
                    </div>
                  </div>
                </div>
              </div>
            ))}
      </div>
    </div>
  );
};

export default Desktop;
