import React from 'react';
import { AgentDetail } from '../../schemas';

interface MobileRelatedAgentsProps {
  /**
   * 智能体详情数据
   */
  agentDetail?: AgentDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 当前智能体ID
   */
  agentId: string;
  /**
   * 关闭弹框的回调函数
   */
  onClose: () => void;
}

/**
 * 相关智能体组件 - 移动版
 * 注意：当前版本为占位实现，实际功能暂未实现
 */
const Mobile: React.FC<MobileRelatedAgentsProps> = () => {
  return <></>;
};

export default Mobile;
