import React from 'react';
import { AgentDetail } from '../../schemas';
import { Skeleton } from '@mantine/core';

interface DesktopAgentFeaturesProps {
  /**
   * 智能体详情数据
   */
  agentDetail?: AgentDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
}

/**
 * 智能体功能列表组件 - 桌面版
 */
const Desktop: React.FC<DesktopAgentFeaturesProps> = ({ agentDetail, isLoading }) => {
  // 如果正在加载，显示骨架屏
  if (isLoading) {
    return (
      <div className="flex flex-col">
        <div className="font-bold mt-6 mb-2">功能</div>
        {Array(3)
          .fill(0)
          .map((_, index) => (
            <div className="flex flex-row items-start gap-2 py-1" key={index}>
              <Skeleton circle height={24} width={24} className="mt-0.5" />
              <Skeleton height={16} width={200} radius="md" />
            </div>
          ))}
      </div>
    );
  }

  // 使用智能体详情中的功能列表，如果没有则不显示此组件
  const features = agentDetail?.features || [];

  // 如果没有功能列表，则不显示此组件
  if (features.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col">
      <div className="font-bold mt-6 mb-2">功能</div>
      {features.map((feature, index) => (
        <div className="flex flex-row items-start gap-2 py-1 text-sm" key={index}>
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="mt-0.5 text-green-600"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M18.0633 5.67387C18.5196 5.98499 18.6374 6.60712 18.3262 7.06343L10.8262 18.0634C10.6585 18.3095 10.3898 18.4679 10.0934 18.4957C9.79688 18.5235 9.50345 18.4178 9.29289 18.2072L4.79289 13.7072C4.40237 13.3167 4.40237 12.6835 4.79289 12.293C5.18342 11.9025 5.81658 11.9025 6.20711 12.293L9.85368 15.9396L16.6738 5.93676C16.9849 5.48045 17.607 5.36275 18.0633 5.67387Z"
              fill="currentColor"
            />
          </svg>
          <div>{feature}</div>
        </div>
      ))}
    </div>
  );
};

export default Desktop;
