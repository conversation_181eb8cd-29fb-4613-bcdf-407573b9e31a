/* 渐变背景 */
.bg-linear-to-b {
  background-image: linear-gradient(to bottom, var(--from-color), var(--to-color));
}

.bg-linear-to-t {
  background-image: linear-gradient(to top, var(--from-color), var(--to-color));
}

.from-white {
  --from-color: white;
}

.from-gray-900 {
  --from-color: #1a202c;
}

.to-transparent {
  --to-color: transparent;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  font-weight: 500;
  transition: all 0.2s;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary {
  background-color: #3182ce;
  color: white;
}

.btn-primary:hover {
  background-color: #2c5282;
}

/* 评分条样式 */
.rating-bar {
  height: 0.625rem;
  overflow: hidden;
  border-radius: 9999px;
}

.rating-bar-fill {
  height: 100%;
}

/* 对话开场白卡片样式 */
.prompt-card {
  position: relative;
  margin-left: 0.5rem;
  height: 3.5rem;
  min-width: 100%;
  flex-grow: 1;
  border-radius: 0.75rem;
  padding: 0 1rem;
  display: flex;
  align-items: center;
  transition: background-color 0.2s;
}

.prompt-card-text {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  font-size: 0.875rem;
  word-break: break-all;
}

/* 图标阴影效果 */
.gizmo-shadow-stroke {
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
}

.dark .gizmo-shadow-stroke {
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.1);
}

/* 文本截断样式 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 无滚动条样式 */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Token 相关样式 */
.bg-token-main-surface-secondary {
  background-color: rgba(247, 247, 248, 0.8);
}

.dark .bg-token-main-surface-secondary {
  background-color: rgba(52, 53, 65, 0.8);
}

.text-token-text-tertiary {
  color: rgba(142, 142, 160, 1);
}

/* 响应式调整 */
@media (max-width: 640px) {
  .grid-cols-2 {
    grid-template-columns: 1fr;
  }
}
