/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { ScrollArea, Box, UnstyledButton } from '@mantine/core';
import { useTheme } from '~/core/features/mantine/theme-context';

import { DialogTabsProps } from './types';

/**
 * Desktop version of the FilterTabs component
 */
const Desktop: React.FC<DialogTabsProps> = ({ tabs, selected, onChange }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const defaultTextColor = isDark ? 'white' : 'rgba(92, 92, 92, 1)';
  const activeTextColor = 'white';
  const activeBgColor = 'rgba(73, 81, 235, 1)';

  const controls = tabs.map((tab) => (
    <UnstyledButton
      className="relative"
      key={tab}
      onClick={() => onChange(tab)}
      mod={{ active: tab === selected }}
      mx={8}
      px={8}
      fz={16}
      fw={500}
      lh="24px"
      style={{
        borderRadius: '6px',
        color: defaultTextColor,
        transition: 'color 100ms ease',
      }}
      css={css`
        &[data-active] {
          background: ${activeBgColor}!important;
          color: ${activeTextColor} !important;
        }
      `}
    >
      <span className="relative z-1">{tab}</span>
    </UnstyledButton>
  ));

  return (
    <Box className="w-full">
      <ScrollArea type="never" style={{ whiteSpace: 'nowrap' }}>
        {controls}
      </ScrollArea>
    </Box>
  );
};

export default Desktop;
