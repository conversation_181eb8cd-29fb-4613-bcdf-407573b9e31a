import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

import { DialogTabsProps } from './types';

/**
 * Responsive DialogTabs component that loads Desktop or Mobile version
 * based on the device type
 */
const DialogTabs: React.FC<DialogTabsProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default DialogTabs;
