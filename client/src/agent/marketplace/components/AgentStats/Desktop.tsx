import React from 'react';
import { AgentDetail } from '../../schemas';
import { Skeleton } from '@mantine/core';

interface DesktopAgentStatsProps {
  /**
   * 智能体详情数据
   */
  agentDetail?: AgentDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
}

/**
 * 智能体统计信息组件 - 桌面版
 */
const Desktop: React.FC<DesktopAgentStatsProps> = ({ agentDetail, isLoading }) => {
  // 如果正在加载，显示骨架屏
  if (isLoading) {
    return (
      <div className="flex justify-center">
        <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
          <Skeleton height={30} width={80} radius="md" />
          <Skeleton height={16} width={100} radius="md" />
        </div>
        <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
          <Skeleton height={30} width={80} radius="md" />
          <Skeleton height={16} width={100} radius="md" />
        </div>
        <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
          <Skeleton height={30} width={80} radius="md" />
          <Skeleton height={16} width={100} radius="md" />
        </div>
      </div>
    );
  }

  if (!agentDetail?.stats) {
    return null;
  }

  const stats = agentDetail.stats;

  return (
    <div className="flex justify-center">
      <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
        <div className="flex flex-row items-center gap-1.5 pt-1 text-xl font-semibold text-center leading-none">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M9.8221 2.07733C10.7784 0.380718 13.2216 0.380718 14.1779 2.07733L16.2635 5.77769C16.3349 5.90428 16.4578 5.99357 16.6002 6.02231L20.764 6.86243C22.6731 7.24763 23.4281 9.57124 22.11 11.005L19.2352 14.1321C19.1369 14.239 19.09 14.3835 19.1066 14.5279L19.5943 18.7475C19.8179 20.6821 17.8413 22.1182 16.0704 21.3077L12.2081 19.54C12.0759 19.4795 11.924 19.4795 11.7919 19.54L7.92953 21.3077C6.15864 22.1182 4.18206 20.6821 4.40565 18.7475L4.89332 14.5279C4.91 14.3835 4.86306 14.239 4.76472 14.1321L1.88997 11.005C0.571908 9.57124 1.32689 7.24763 3.23598 6.86243L7.39974 6.02231C7.54218 5.99357 7.66507 5.90428 7.73642 5.77769L9.8221 2.07733Z"
              fill="currentColor"
            />
          </svg>
          {stats.rating}
        </div>
        <div className="text-xs text-gray-500">评级 ({stats.rating_count})</div>
      </div>
      <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
        <div className="flex flex-row items-center gap-1.5 pt-1 text-xl font-semibold text-center leading-none">{stats.rank}</div>
        <div className="text-xs text-gray-500">属于{stats.category} (全球)</div>
      </div>
      <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
        <div className="flex flex-row items-center gap-1.5 pt-1 text-xl font-semibold text-center leading-none">
          {stats.conversation_count}
        </div>
        <div className="text-xs text-gray-500">对话</div>
      </div>
    </div>
  );
};

export default Desktop;
