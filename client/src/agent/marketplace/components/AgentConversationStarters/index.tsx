import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import { AgentDetail } from '../../schemas';
import Desktop from './Desktop';
import Mobile from './Mobile';

interface AgentConversationStartersProps {
  /**
   * 智能体详情数据
   */
  agentDetail?: AgentDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
}

/**
 * 智能体对话开场白组件
 * 根据设备类型自动选择桌面版或移动版
 */
const AgentConversationStarters: React.FC<AgentConversationStartersProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // 根据设备类型动态选择适当的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default AgentConversationStarters;
