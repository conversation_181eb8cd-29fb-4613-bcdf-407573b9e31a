import React from 'react';
import { AgentDetail } from '../../schemas';
import { Skeleton } from '@mantine/core';

interface DesktopAgentConversationStartersProps {
  /**
   * 智能体详情数据
   */
  agentDetail?: AgentDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
}

/**
 * 智能体对话开场白组件 - 桌面版
 */
const Desktop: React.FC<DesktopAgentConversationStartersProps> = ({ agentDetail, isDark, isLoading }) => {
  // 如果正在加载，显示骨架屏
  if (isLoading) {
    return (
      <div className="flex flex-col">
        <div className="font-bold mt-6">对话开场白</div>
        <div className="mt-4 grid grid-cols-2 gap-x-1.5 gap-y-2">
          {Array(4)
            .fill(0)
            .map((_, index) => (
              <div className="flex" key={index}>
                <Skeleton height={56} radius="xl" className="ms-2 min-w-full" />
              </div>
            ))}
        </div>
      </div>
    );
  }

  // 从智能体配置中获取建议问题，如果没有则使用空数组
  const suggestedQuestions = agentDetail?.model_config?.suggested_questions || [];

  // 如果没有建议问题，则不显示此组件
  if (suggestedQuestions.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col">
      <div className="font-bold mt-6">对话开场白</div>
      <div className="mt-4 grid grid-cols-2 gap-x-1.5 gap-y-2">
        {suggestedQuestions.map((prompt, index) => (
          <div className="flex" key={index} tabIndex={0}>
            <div
              className={`group relative ms-2 h-14 min-w-full grow rounded-xl border px-4 ${
                isDark ? 'border-gray-700 bg-gray-900 hover:bg-gray-800' : 'border-gray-200 bg-white hover:bg-gray-50'
              }`}
            >
              <div className="flex h-full items-center">
                <div className="line-clamp-2 text-sm break-all">{prompt}</div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Desktop;
