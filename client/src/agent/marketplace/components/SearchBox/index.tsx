import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

interface AgentSearchBoxProps {
  /**
   * Placeholder text for the search input
   */
  placeholder?: string;
  /**
   * Callback function when search value changes
   */
  onSearch?: (value: string) => void;
  /**
   * Initial search value
   */
  initialValue?: string;
  /**
   * Width of the search box (default: 682px for desktop, 100% for mobile)
   */
  width?: string | number;
}

/**
 * Responsive AgentSearchBox component that loads Desktop or Mobile version
 * based on the device type
 */
const AgentSearchBox: React.FC<AgentSearchBoxProps> = props => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default AgentSearchBox;
