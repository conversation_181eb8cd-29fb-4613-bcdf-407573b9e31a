import React, { useState } from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';
import { IoSearch } from 'react-icons/io5';
import './styles.css';

interface DesktopAgentSearchBoxProps {
  /**
   * Placeholder text for the search input
   */
  placeholder?: string;
  /**
   * Callback function when search value changes
   */
  onSearch?: (value: string) => void;
  /**
   * Initial search value
   */
  initialValue?: string;
  /**
   * Width of the search box (default: 682px)
   */
  width?: string | number;
}

/**
 * Desktop version of the AgentSearchBox component
 */
const DesktopAgentSearchBox: React.FC<DesktopAgentSearchBoxProps> = ({
  placeholder = '搜索 GPT ...',
  onSearch,
  initialValue = '',
  width = '682px'
}) => {
  const { actualColorScheme } = useTheme();
  const [searchValue, setSearchValue] = useState(initialValue);

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchValue(value);
    if (onSearch) {
      onSearch(value);
    }
  };

  // Determine background and shadow colors based on theme
  const bgColor = actualColorScheme === 'dark' ? '#1A1B1E' : 'white';
  const shadowColor =
    actualColorScheme === 'dark'
      ? 'rgba(0, 0, 0, 0.5)'
      : 'rgba(232, 239, 252, 0.5)';
  const iconColor = actualColorScheme === 'dark' ? '#5C5F66' : '#A8A29E';

  return (
    <div
      style={{
        width,
        height: '64px',
        position: 'relative',
        overflow: 'hidden',
        borderRadius: '12px',
        backgroundColor: bgColor,
        boxShadow: `0px 6px 6px 0px ${shadowColor}`
      }}
    >
      <div style={{ position: 'absolute', left: '28px', top: '22px' }}>
        <IoSearch size={20} color={iconColor} />
      </div>

      <input
        type="text"
        placeholder={placeholder}
        value={searchValue}
        onChange={handleSearchChange}
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          outline: 'none',
          background: 'transparent',
          paddingLeft: '57px',
          paddingRight: '16px',
          fontSize: '16px',
          fontFamily: 'Source Han Sans SC',
          lineHeight: '1.2',
          color: actualColorScheme === 'dark' ? '#C1C2C5' : '#000000'
        }}
        className="agent-search-input"
      />
    </div>
  );
};

export default DesktopAgentSearchBox;
