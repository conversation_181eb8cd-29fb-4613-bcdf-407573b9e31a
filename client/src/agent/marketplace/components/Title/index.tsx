import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

interface AgentTitleProps {
  /**
   * Main title text
   */
  title: string;
  /**
   * Subtitle text
   */
  subtitle: string;
}

/**
 * Responsive AgentTitle component that loads Desktop or Mobile version
 * based on the device type
 */
const AgentTitle: React.FC<AgentTitleProps> = props => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default AgentTitle;
