import React from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';

interface MobileAgentTitleProps {
  /**
   * Main title text
   */
  title: string;
  /**
   * Subtitle text
   */
  subtitle: string;
}

/**
 * Mobile version of the AgentTitle component
 */
const MobileAgentTitle: React.FC<MobileAgentTitleProps> = ({
  title,
  subtitle
}) => {
  const { actualColorScheme } = useTheme();

  // Determine text colors based on theme
  const titleColor = actualColorScheme === 'dark' ? '#8C9EFF' : '#5668FF';
  const subtitleColor = actualColorScheme === 'dark' ? '#C1C2C5' : '#404040';

  return (
    <div className="flex flex-col gap-2 items-center text-center px-4">
      <div
        className="text-2xl font-bold font-['Source_Han_Sans_SC'] leading-[36px]"
        style={{ color: titleColor }}
      >
        {title}
      </div>
      <div
        className="text-sm font-['Source_Han_Sans_SC'] leading-6"
        style={{ color: subtitleColor }}
      >
        {subtitle}
      </div>
    </div>
  );
};

export default MobileAgentTitle;
