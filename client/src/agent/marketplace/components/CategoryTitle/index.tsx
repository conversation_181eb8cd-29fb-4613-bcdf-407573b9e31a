import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

interface CategoryTitleProps {
  /**
   * Main title text
   */
  title: string;
  /**
   * Subtitle text
   */
  subtitle: string;
}

/**
 * Responsive CategoryTitle component that loads Desktop or Mobile version
 * based on the device type
 */
const CategoryTitle: React.FC<CategoryTitleProps> = props => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default CategoryTitle;
