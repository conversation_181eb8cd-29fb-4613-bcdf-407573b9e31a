import React from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';
import { useMantineTheme } from '@mantine/core';

interface MobileCategoryTitleProps {
  /**
   * Main title text
   */
  title: string;
  /**
   * Subtitle text
   */
  subtitle: string;
}

/**
 * Mobile version of the CategoryTitle component
 */
const MobileCategoryTitle: React.FC<MobileCategoryTitleProps> = ({
  title,
  subtitle
}) => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();
  const isDark = actualColorScheme === 'dark';

  // Determine text colors based on theme
  const titleColor = isDark ? theme.colors.indigo[3] : theme.colors.indigo[6];
  const subtitleColor = isDark ? theme.colors.gray[4] : theme.colors.gray[6];

  return (
    <div className="w-full px-4 mb-4">
      <div
        className="text-xl font-bold font-['Source_Han_Sans_SC'] leading-[36px] mb-1"
        style={{ color: titleColor }}
      >
        {title}
      </div>
      <div
        className="text-sm font-['Source_Han_Sans_SC'] leading-6"
        style={{ color: subtitleColor }}
      >
        {subtitle}
      </div>
    </div>
  );
};

export default MobileCategoryTitle;
