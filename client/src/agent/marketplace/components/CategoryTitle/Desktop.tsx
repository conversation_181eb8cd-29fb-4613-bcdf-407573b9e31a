import React from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';
import { useMantineTheme } from '@mantine/core';

interface DesktopCategoryTitleProps {
  /**
   * Main title text
   */
  title: string;
  /**
   * Subtitle text
   */
  subtitle: string;
}

/**
 * Desktop version of the CategoryTitle component
 */
const DesktopCategoryTitle: React.FC<DesktopCategoryTitleProps> = ({
  title,
  subtitle
}) => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();
  const isDark = actualColorScheme === 'dark';

  // Determine text colors based on theme
  const titleColor = isDark ? theme.colors.indigo[3] : theme.colors.indigo[6];
  const subtitleColor = isDark ? theme.colors.gray[4] : theme.colors.gray[6];

  return (
    <div className="w-full max-w-[543px] h-20 relative overflow-hidden">
      <div
        className="left-0 top-0 absolute justify-start text-2xl font-bold font-['Source_Han_Sans_SC'] leading-[48.39px]"
        style={{ color: titleColor }}
      >
        {title}
      </div>
      <div
        className="left-0 top-[47px] absolute justify-start text-base font-['Source_Han_Sans_SC'] leading-9"
        style={{ color: subtitleColor }}
      >
        {subtitle}
      </div>
    </div>
  );
};

export default DesktopCategoryTitle;
