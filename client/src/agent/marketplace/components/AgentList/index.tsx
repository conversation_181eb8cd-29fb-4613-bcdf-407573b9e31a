import React, { ReactNode } from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

interface AgentListProps {
  /**
   * Children components (AgentCard components)
   */
  children: ReactNode;
  /**
   * Number of columns in the grid (desktop only)
   * @default 3
   */
  columns?: number;
  /**
   * Gap between grid items
   * @default "md"
   */
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * Responsive AgentList component that loads Desktop or Mobile version
 * based on the device type
 */
const AgentList: React.FC<AgentListProps> = ({
  children,
  columns = 3,
  gap = 'md'
}) => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile gap={gap}>{children}</Mobile>;
  }

  return (
    <Desktop columns={columns} gap={gap}>
      {children}
    </Desktop>
  );
};

export default AgentList;
