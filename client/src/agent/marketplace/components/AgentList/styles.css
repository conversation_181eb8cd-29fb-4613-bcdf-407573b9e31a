/* Custom styles for AgentList component */

.agent-list-grid {
  width: 100%;
  margin: 0 auto;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .agent-list-grid {
    grid-template-columns: 1fr !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .agent-list-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

/* Animation for items */
.agent-list-grid > * {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
