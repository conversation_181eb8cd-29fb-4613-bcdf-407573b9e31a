import React, { ReactNode } from 'react';
import './styles.css';

interface MobileAgentListProps {
  /**
   * Children components (AgentCard components)
   */
  children: ReactNode;
  /**
   * Gap between grid items
   * @default "md"
   */
  gap?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
}

/**
 * Mobile version of the AgentList component
 */
const MobileAgentList: React.FC<MobileAgentListProps> = ({
  children,
  gap = 'md'
}) => {
  // Map gap size to pixel values
  const gapSizeMap = {
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem'
  };

  return (
    <div
      className="agent-list-grid"
      style={{
        display: 'grid',
        gridTemplateColumns: '1fr',
        gap: gapSizeMap[gap]
      }}
    >
      {children}
    </div>
  );
};

export default MobileAgentList;
