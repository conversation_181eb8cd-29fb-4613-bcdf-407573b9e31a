import React from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';
import './styles.css';

interface MobileFilterTabsProps {
  /**
   * Array of category names to display as tabs
   */
  categories: string[];
  /**
   * Currently selected category
   */
  selectedCategory: string;
  /**
   * Callback function when a category is selected
   */
  onCategoryChange: (category: string) => void;
}

/**
 * Mobile version of the FilterTabs component
 */
const MobileFilterTabs: React.FC<MobileFilterTabsProps> = ({
  categories,
  selectedCategory,
  onCategoryChange
}) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  return (
    <div className="sticky top-0 z-10 w-full py-2 bg-token-main-surface-primary">
      <div className="flex flex-col">
        <div className="flex gap-1.5 overflow-x-auto pb-1 no-scrollbar">
          {categories.map(category => (
            <div
              key={category}
              className={`
                cursor-pointer px-3 py-2 whitespace-nowrap select-none
                first:ms-4 last:me-4
                ${
                  category === selectedCategory
                    ? isDark
                      ? 'text-token-text-primary border-b-2 border-token-text-primary'
                      : 'text-token-text-primary border-b-2 border-token-text-primary'
                    : isDark
                      ? 'text-token-text-tertiary hover:text-token-text-primary'
                      : 'text-token-text-tertiary hover:text-token-text-primary'
                }
              `}
              onClick={() => onCategoryChange(category)}
            >
              {category}
            </div>
          ))}
        </div>
        <div className="h-0.5 w-full bg-token-main-surface-secondary"></div>
      </div>
    </div>
  );
};

export default MobileFilterTabs;
