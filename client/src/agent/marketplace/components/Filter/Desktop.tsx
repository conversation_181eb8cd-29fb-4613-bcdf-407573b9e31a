import React from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';
import './styles.css';

interface DesktopFilterTabsProps {
  /**
   * Array of category names to display as tabs
   */
  categories: string[];
  /**
   * Currently selected category
   */
  selectedCategory: string;
  /**
   * Callback function when a category is selected
   */
  onCategoryChange: (category: string) => void;
}

/**
 * Desktop version of the FilterTabs component
 */
const DesktopFilterTabs: React.FC<DesktopFilterTabsProps> = ({
  categories,
  selectedCategory,
  onCategoryChange
}) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  return (
    <div className="sticky top-14 z-10 -ms-4 w-screen py-2 text-sm md:ms-0 md:w-full md:pb-0">
      <div className="no-scrollbar flex scroll-m-5 gap-1.5 overflow-x-auto md:overflow-hidden">
        {categories.map(category => (
          <div
            key={category}
            className={`
              md:text-token-text-primary cursor-pointer scroll-mx-5 rounded-3xl px-3 py-2
              whitespace-nowrap select-none first:ms-4 last:me-4 md:px-2 md:first:ms-0 md:last:me-0
              ${
                category === selectedCategory
                  ? isDark
                    ? 'border-token-text-primary text-token-main-surface-primary bg-token-text-primary md:rounded-none md:border-b-2 md:bg-transparent'
                    : 'border-token-text-primary text-token-main-surface-primary bg-token-text-primary md:rounded-none md:border-b-2 md:bg-transparent'
                  : isDark
                    ? 'bg-token-main-surface-secondary hover:text-token-text-primary md:text-token-text-tertiary hover:bg-gray-700/50 md:rounded-lg md:hover:bg-gray-700/70 md:bg-transparent'
                    : 'bg-token-main-surface-secondary hover:text-token-text-primary md:text-token-text-tertiary hover:bg-gray-100 md:rounded-lg md:hover:bg-gray-50 md:bg-transparent'
              }
            `}
            onClick={() => onCategoryChange(category)}
          >
            {category}
          </div>
        ))}
      </div>
    </div>
  );
};

export default DesktopFilterTabs;
