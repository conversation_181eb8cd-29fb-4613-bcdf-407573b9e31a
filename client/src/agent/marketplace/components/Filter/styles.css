/* Custom styles for FilterTabs component */

/* Hide scrollbar but allow scrolling */
.no-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

/* Ensure the last character is fully visible */
.whitespace-nowrap {
  padding-right: 2px;
}

/* Hover effect - Light mode */
.hover\:bg-gray-100:hover {
  background-color: rgba(243, 244, 246, 0.5);
  transition: background-color 0.2s ease;
}

.hover\:bg-gray-50:hover {
  background-color: rgba(249, 250, 251, 0.7);
  transition: background-color 0.2s ease;
}

/* Hover effect - Dark mode */
.dark .dark\:hover\:bg-white\/20:hover,
.hover\:bg-gray-700\/50:hover {
  background-color: rgba(255, 255, 255, 0.2);
  transition: background-color 0.2s ease;
}

.hover\:bg-gray-700\/70:hover {
  background-color: rgba(55, 65, 81, 0.7);
  transition: background-color 0.2s ease;
}

/* Selected tab underline */
.md\:border-b-2 {
  border-bottom-width: 2px;
  border-bottom-style: solid;
}

/* Dark mode specific styles */
.dark .dark\:md\:bg-transparent,
.md\:bg-transparent {
  background-color: transparent;
}

.dark .dark\:md\:hover\:bg-gray-700:hover {
  background-color: rgba(55, 65, 81, 0.7);
}
