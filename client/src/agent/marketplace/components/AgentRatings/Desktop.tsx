import React from 'react';
import { AgentDetail } from '../../schemas';
import { Skeleton } from '@mantine/core';

interface DesktopAgentRatingsProps {
  /**
   * 智能体详情数据
   */
  agentDetail?: AgentDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
}

/**
 * 智能体评级组件 - 桌面版
 */
const Desktop: React.FC<DesktopAgentRatingsProps> = ({ agentDetail, isLoading }) => {
  // 如果正在加载，显示骨架屏
  if (isLoading) {
    return (
      <div className="flex flex-col">
        <div className="mb-2">
          <div className="font-bold mt-6">评级</div>
        </div>
        {Array(5)
          .fill(0)
          .map((_, index) => (
            <div className="flex flex-row items-center gap-2 py-1" key={index}>
              <Skeleton circle height={24} width={24} />
              <Skeleton height={10} radius="xl" style={{ width: '100%' }} />
            </div>
          ))}
      </div>
    );
  }

  // 使用实际数据或默认数据
  const ratings = agentDetail?.stats?.rating_distribution;
  if (!ratings) {
    return null;
  }

  // 确保评级按星级从高到低排序
  const sortedRatings = [...ratings].sort((a, b) => b.stars - a.stars);

  return (
    <div className="flex flex-col">
      <div className="mb-2">
        <div className="font-bold mt-6">评级</div>
      </div>
      {sortedRatings.map((rating) => (
        <div className="flex flex-row items-center gap-2 py-1 text-xl font-semibold" key={rating.stars}>
          <div className="relative">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-green-500"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M9.8221 2.07733C10.7784 0.380718 13.2216 0.380718 14.1779 2.07733L16.2635 5.77769C16.3349 5.90428 16.4578 5.99357 16.6002 6.02231L20.764 6.86243C22.6731 7.24763 23.4281 9.57124 22.11 11.005L19.2352 14.1321C19.1369 14.239 19.09 14.3835 19.1066 14.5279L19.5943 18.7475C19.8179 20.6821 17.8413 22.1182 16.0704 21.3077L12.2081 19.54C12.0759 19.4795 11.924 19.4795 11.7919 19.54L7.92953 21.3077C6.15864 22.1182 4.18206 20.6821 4.40565 18.7475L4.89332 14.5279C4.91 14.3835 4.86306 14.239 4.76472 14.1321L1.88997 11.005C0.571908 9.57124 1.32689 7.24763 3.23598 6.86243L7.39974 6.02231C7.54218 5.99357 7.66507 5.90428 7.73642 5.77769L9.8221 2.07733Z"
                fill="currentColor"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center text-[11px] text-white">{rating.stars}</div>
          </div>
          <div className="h-2.5 grow overflow-hidden rounded-full bg-gray-100 dark:bg-gray-700">
            <div className="h-full bg-green-500" style={{ width: `${rating.percentage}%` }}></div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default Desktop;
