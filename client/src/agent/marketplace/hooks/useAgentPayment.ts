import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { notifications } from '@mantine/notifications';
import { getPaymentPlans } from '~/paymentPlan/api';
import { PaymentPlan } from '~/paymentPlan/schemas';
import { AgentDetail } from '../schemas';
import { usePaymentMutation, usePaymentMessageListener } from '~/core/utils/payment';

/**
 * 智能体支付Hook
 * 用于处理智能体购买支付流程
 */
export const useAgentPayment = (agentId?: string) => {
  const navigate = useNavigate();
  // 支付方式
  const [paymentMethod, setPaymentMethod] = useState<'alipay' | 'wechatpay'>('alipay');
  // 订单ID
  const [orderId, setOrderId] = useState<string>('');
  // 选中的付费计划ID
  const [selectedPlanId, setSelectedPlanId] = useState<number | undefined>(undefined);

  // 获取付费计划列表
  const { data: paymentPlansResponse, isLoading: isPaymentPlansLoading } = useQuery({
    queryKey: ['paymentPlans', 'app', agentId],
    queryFn: () => getPaymentPlans('app', agentId!),
    enabled: !!agentId,
  });

  // 提取付费计划数据
  const paymentPlans = paymentPlansResponse?.data || [];

  // 获取选中的付费计划
  const selectedPlan = paymentPlans.find((plan) => plan.id === selectedPlanId);

  // 处理选择付费计划
  const handleSelectPlan = (plan: PaymentPlan) => {
    setSelectedPlanId(plan.id);
  };

  // 支付成功处理
  const handlePaymentSuccess = () => {
    // 可以在这里添加跳转到智能体详情页或其他逻辑
    navigate('/agent/marketplace');
  };

  // 支付处理逻辑
  const handlePayment = usePaymentMutation(setOrderId, navigate, handlePaymentSuccess);

  // 支付消息监听（支持支付宝和微信支付）
  const [isListeningEnabled, setIsListeningEnabled] = useState(false);

  usePaymentMessageListener(
    orderId,
    isListeningEnabled,
    () => {
      setIsListeningEnabled(false);
      // 显示购买成功通知，替代原来的支付成功通知
      notifications.show({
        title: '购买成功',
        message: '智能体购买成功，您现在可以使用该智能体了！',
        color: 'green',
      });
      handlePaymentSuccess();
    },
    () => {
      setIsListeningEnabled(false);
    }
  );

  // 处理智能体购买
  const handleAgentPurchase = (agent: AgentDetail, paymentMethodOverride?: 'alipay' | 'wechatpay') => {
    const currentPaymentMethod = paymentMethodOverride || paymentMethod;

    if (!currentPaymentMethod) {
      notifications.show({
        title: '请选择支付方式',
        message: '请先选择支付宝或微信支付',
        color: 'yellow',
      });
      return;
    }

    if (!selectedPlanId) {
      notifications.show({
        title: '请选择付费计划',
        message: '请先选择一个付费计划',
        color: 'yellow',
      });
      return;
    }

    // 创建订单请求
    handlePayment.mutate({
      product_type: 'app',
      product_id: agent.id,
      payment_plan_id: selectedPlanId,
      quantity: 1,
      selectedPaymentMethod: currentPaymentMethod,
    });

    // 启用消息监听（支付宝通过回调，微信支付通过轮询后的postMessage）
    setIsListeningEnabled(true);
  };



  return {
    // 状态
    paymentMethod,
    orderId,
    isPaymentLoading: handlePayment.isPending,
    paymentPlans,
    selectedPlanId,
    selectedPlan,
    isPaymentPlansLoading,
    isListeningEnabled,

    // 操作方法
    setPaymentMethod,
    handleAgentPurchase,
    handleSelectPlan,
    setSelectedPlanId,
  };
};

export default useAgentPayment;
