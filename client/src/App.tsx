import { BrowserRouter, Route, Routes, Navigate } from 'react-router-dom';
import { Register, Login, ForgetPassword } from '~/user/authentication';
import { AgentMarketplace } from '~/agent/marketplace';
import { MainLayout, SZR, MobileNotSupported } from '~/core/components';
import { UserAgreement, PrivacyPolicy } from '~/core';
import { Welcome, Conversation } from '~/agent/chat';
import { Overview, Account, MyAgent, MyCourse, MyWorkflow, AssetsList, Usage } from '~/user/core';
import { CourseMarketplace, Cart, Payment } from '~/course/marketplace';
import { CourseClassRoom } from '~/course/classroom/pages';
import { KnowledgeBase, KnowledgeDetail } from '~/knowledge/pages';
import { RunWorkflow } from './agent/workflow';
import PaymentCallback from '~/paymentPlan/pages/PaymentCallback';
import PaymentSuccess from '~/paymentPlan/pages/PaymentSuccess';
import PaymentError from '~/paymentPlan/pages/PaymentError';
import { AgentBuilder } from './agent/builder/pages';
import { ChatContextProvider } from '~/agent/chat/contexts';
import { useDeviceDetect } from '~/core/hooks';
import WechatQRCodePage from '~/paymentPlan/pages/WechatQRCodePage.tsx';

function App() {
  const { isMobile } = useDeviceDetect();

  // 如果是手机端，显示不支持提示
  if (isMobile) {
    return <MobileNotSupported />;
  }

  return (
    <BrowserRouter>
      <ChatContextProvider>
        <Routes>
          <Route path="/register" element={<Register />}></Route>
          <Route path="/login" element={<Login />}></Route>
          <Route path="/forget-password" element={<ForgetPassword />}></Route>

          {/* 法律文档 */}
          <Route path="/user-agreement" element={<UserAgreement />}></Route>
          <Route path="/privacy-policy" element={<PrivacyPolicy />}></Route>

          {/* 微信支付二维码页面 */}
          <Route path="/wechat-payment" element={<WechatQRCodePage />}></Route>

          {/* 支付相关页面 */}
          <Route path="/paymentPlan/pages/callback" element={<PaymentCallback />}></Route>
          <Route path="/paymentPlan/pages/success" element={<PaymentSuccess />}></Route>
          <Route path="/paymentPlan/pages/error" element={<PaymentError />}></Route>
          <Route path="/orders/alipay/return" element={<PaymentCallback />}></Route>

          <Route path="/" element={<MainLayout />}>
            {/* 根路径重定向到聊天页面 */}
            <Route index element={<Navigate to="/chat" replace />} />

            {/* 对话 */}
            <Route path="chat">
              <Route index element={<Welcome />} />
              <Route path=":id" element={<Conversation />} />
            </Route>

            {/* 智能体 */}
            <Route path="agent">
              <Route path="marketplace" element={<AgentMarketplace />} />
              <Route path="builder/:id?" element={<AgentBuilder />} />
            </Route>

            {/* 工作流 */}
            <Route path="workflow">
              <Route path="marketplace" element={<AgentMarketplace />} />
              <Route path=":id" element={<RunWorkflow />} />
            </Route>

            {/* 课程 */}
            <Route path="course">
              <Route index element={<CourseMarketplace />} />
              <Route path="cart" element={<Cart />} />
              <Route path=":id" element={<CourseClassRoom />} />
              <Route path="payment" element={<Payment />} />
            </Route>

            {/* 知识库 */}
            <Route path="knowledge">
              <Route index element={<KnowledgeBase />} />
              <Route path=":id" element={<KnowledgeDetail />} />
            </Route>

            {/* 数字人 */}
            <Route path="szr" element={<SZR />} />
          </Route>

          <Route path="ucenter" element={<MainLayout />}>
            <Route index element={<Navigate to="/ucenter/assets/agent" replace />} />
            <Route path="account" element={<Account />} />
            <Route path="agent" element={<MyAgent />} />
            <Route path="course" element={<MyCourse />} />
            <Route path="workflow" element={<MyWorkflow />} />
            <Route path="assets/:type" element={<AssetsList />} />
            <Route path="usage" element={<Usage />} />
            <Route path="overview" element={<Overview />} />
          </Route>
        </Routes>
      </ChatContextProvider>
    </BrowserRouter>
  );
}

export default App;
