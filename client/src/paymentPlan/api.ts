import { API_BASE_URL } from '~/core/constants';
import { ResponsePayloads } from '~/core/schemas';
import { useUserStore } from '~/user/core/store';
import { PaymentPlan } from './schemas';
import { ProductType } from '~/user/schemas';

/**
 * 获取付费计划列表
 * @param productType 产品类型
 * @param productId 产品ID
 * @returns 付费计划列表
 */
export async function getPaymentPlans(productType: ProductType, productId: string): Promise<ResponsePayloads<PaymentPlan[]>> {
  const token = useUserStore.getState().token;
  const response = await fetch(`${API_BASE_URL}/orders/payment-plans?product_type=${productType}&product_id=${productId}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error?.message || '获取付费计划列表失败');
  }

  return response.json();
}
