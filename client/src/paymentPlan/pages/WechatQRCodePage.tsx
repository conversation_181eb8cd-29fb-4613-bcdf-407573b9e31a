import React, { useEffect, useState } from 'react';
import { Container, Paper, Text, Loader, Alert, Button, Stack, Center } from '@mantine/core';
import { MdError, MdRefresh } from 'react-icons/md';
import { QRCodeSVG } from 'qrcode.react';
import { useNavigate } from 'react-router-dom';
import { useWechatPaymentPolling } from '~/core/utils/payment';

interface WechatQRCodePageProps {}

const WechatQRCodePage: React.FC<WechatQRCodePageProps> = () => {
  const navigate = useNavigate();
  const [paymentUrl, setPaymentUrl] = useState<string>('');
  const [orderId, setOrderId] = useState<string>('');
  const [hasError, setHasError] = useState<boolean>(false);
  const [isPollingEnabled, setIsPollingEnabled] = useState<boolean>(false);

  // 从URL参数获取支付信息
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const url = urlParams.get('url');
    const id = urlParams.get('orderId');

    if (url) {
      setPaymentUrl(decodeURIComponent(url));
      setOrderId(id || '');
      setHasError(false);
      setIsPollingEnabled(true); // 启用轮询
      console.info('微信支付二维码页面加载:', decodeURIComponent(url));
    } else {
      setHasError(true);
    }
  }, []);

  // 支付成功处理（与支付宝保持一致）
  const handlePaymentSuccess = () => {
    setIsPollingEnabled(false);

    // 延迟跳转到成功页面（与支付宝回调页面保持一致）
    setTimeout(() => {
      navigate(`/paymentPlan/pages/success?order_id=${orderId}&payment_method=wechatpay&verified=true`);
    }, 2000);
  };

  // 支付失败处理
  const handlePaymentError = () => {
    setIsPollingEnabled(false);
    // 不自动跳转，让用户继续尝试
  };

  // 微信支付轮询
  useWechatPaymentPolling(
    orderId,
    isPollingEnabled,
    handlePaymentSuccess,
    handlePaymentError
  );

  // 返回上一页
  const handleGoBack = () => {
    navigate(-1);
  };

  return (
    <Container size="sm" py="xl">
      <Paper shadow="md" p="xl" radius="md">
        <Stack gap="lg" align="center">
          {/* 标题 */}
          <Text size="xl" fw={700} ta="center" c="green">
            微信支付
          </Text>

          {/* 订单信息 */}
          {orderId && (
            <Text size="sm" c="dimmed" ta="center">
              订单号: {orderId}
            </Text>
          )}

          {/* 二维码区域 */}
          <Paper withBorder p="lg" radius="md" style={{ minHeight: 350, minWidth: 350 }}>
            <Center style={{ height: '100%' }}>
              {hasError && (
                <Stack align="center" gap="md">
                  <Alert icon={<MdError size="1rem" />} title="参数错误" color="red" variant="light">
                    缺少支付链接参数
                  </Alert>
                  <Button leftSection={<MdRefresh size="1rem" />} onClick={handleGoBack} variant="light" color="blue">
                    返回上一页
                  </Button>
                </Stack>
              )}

              {!hasError && paymentUrl && (
                <Stack align="center" gap="md">
                  <QRCodeSVG
                    value={paymentUrl}
                    size={300}
                    level="M"
                    fgColor="#000000"
                    bgColor="#FFFFFF"
                    style={{ border: '1px solid #e9ecef', borderRadius: '8px' }}
                  />
                  <Text size="sm" c="dimmed" ta="center">
                    请使用微信扫描二维码完成支付
                  </Text>
                  {isPollingEnabled && (
                    <Text size="xs" c="blue" ta="center">
                      正在检查支付状态...
                    </Text>
                  )}
                </Stack>
              )}
            </Center>
          </Paper>

          {/* 操作按钮 */}
          <Stack gap="sm" style={{ width: '100%' }}>
            <Text size="xs" c="dimmed" ta="center">
              支付完成后，请返回原页面
            </Text>
            <Button onClick={handleGoBack} variant="light" color="gray" fullWidth>
              返回上一页
            </Button>
          </Stack>
        </Stack>
      </Paper>
    </Container>
  );
};

export default WechatQRCodePage;
