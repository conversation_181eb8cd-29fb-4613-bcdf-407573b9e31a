import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Container, Paper, Title, Text, Button, Group, Alert, Loader } from '@mantine/core';
import { RiCheckLine } from 'react-icons/ri';
import { notifications } from '@mantine/notifications';

/**
 * 支付成功页面
 * 根据 payment-notification-flow.puml，由 API 重定向到此页面
 */
export default function PaymentSuccess() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [orderInfo, setOrderInfo] = useState<any>(null);

  const orderId = searchParams.get('order_id');
  const paymentMethod = searchParams.get('payment_method');

  useEffect(() => {
    if (!orderId) {
      notifications.show({
        title: '参数错误',
        message: '缺少订单信息',
        color: 'red',
      });
      navigate('/');
      return;
    }

    // 只有当页面是通过验证成功跳转过来时才显示通知
    // 避免与轮询或消息监听器的通知重复
    const verified = searchParams.get('verified');
    if (verified === 'true') {
      notifications.show({
        title: '支付成功',
        message: '您的订单已支付成功！',
        color: 'green',
        autoClose: 5000,
      });
    }

    // 模拟加载订单信息
    setTimeout(() => {
      setOrderInfo({
        orderId,
        paymentMethod,
        status: 'success',
        message: '支付成功',
      });
      setLoading(false);
    }, 1000);

    // 通知父窗口支付成功（如果是在弹窗中打开）
    try {
      if (window.opener) {
        window.opener.postMessage({
          type: 'PAYMENT_SUCCESS',
          orderId,
          status: 'success'
        }, '*');
      }
    } catch (e) {
      console.log('无法通知父窗口:', e);
    }
  }, [orderId, paymentMethod, navigate, searchParams]);

  if (loading) {
    return (
      <Container size="sm" py="xl">
        <Paper shadow="md" p="xl" radius="md" style={{ textAlign: 'center' }}>
          <Loader size="lg" />
          <Text mt="md">正在处理支付结果...</Text>
        </Paper>
      </Container>
    );
  }

  return (
    <Container size="sm" py="xl">
      <Paper shadow="md" p="xl" radius="md">
        <div style={{ textAlign: 'center' }}>
          <Title order={2} c="green" mb="md">
            支付成功！
          </Title>

          <Alert icon={<RiCheckLine size={16} />} color="green" mb="xl">
            您的订单已支付成功，相关权限已生效
          </Alert>

          {orderInfo && (
            <div style={{ textAlign: 'left', marginBottom: 24 }}>
              <Text size="sm" c="dimmed" mb="xs">订单信息：</Text>
              <Text size="sm"><strong>订单号：</strong>{orderInfo.orderId}</Text>
              <Text size="sm"><strong>支付方式：</strong>
                {orderInfo.paymentMethod === 'alipay' ? '支付宝' : '微信支付'}
              </Text>
              <Text size="sm"><strong>状态：</strong>支付成功</Text>
            </div>
          )}

          <Group justify="center" gap="md">
            <Button 
              variant="filled" 
              onClick={() => navigate('/agent/marketplace')}
            >
              返回智能体市场
            </Button>
            <Button 
              variant="outline" 
              onClick={() => navigate('/course/marketplace')}
            >
              浏览课程
            </Button>
            <Button 
              variant="light" 
              onClick={() => navigate('/user/orders')}
            >
              查看订单
            </Button>
          </Group>

          <Text size="xs" c="dimmed" mt="xl">
            如果您是在弹窗中看到此页面，可以关闭此窗口返回原页面
          </Text>
        </div>
      </Paper>
    </Container>
  );
}
