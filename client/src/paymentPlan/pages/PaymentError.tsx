import React, { useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Container, Paper, Title, Text, Button, Group, Alert } from '@mantine/core';
import { RiAlertLine } from 'react-icons/ri';
import { notifications } from '@mantine/notifications';

/**
 * 支付错误页面
 * 当支付过程中出现错误时显示
 */
export default function PaymentError() {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();

  const reason = searchParams.get('reason');

  const getErrorMessage = (reason: string | null) => {
    switch (reason) {
      case 'system_error':
        return '系统错误，无法验证支付结果';
      case 'signature_failed':
        return '支付验证失败，请联系客服';
      case 'missing_order_id':
        return '缺少订单信息，请重新发起支付';
      case 'processing_failed':
        return '支付处理失败，请稍后重试';
      default:
        return '支付过程中出现未知错误';
    }
  };

  useEffect(() => {
    // 显示错误通知
    notifications.show({
      title: '支付失败',
      message: getErrorMessage(reason),
      color: 'red',
      autoClose: 5000,
    });

    // 通知父窗口支付失败（如果是在弹窗中打开）
    try {
      if (window.opener) {
        window.opener.postMessage({
          type: 'PAYMENT_FAILED',
          reason,
          status: 'failed'
        }, '*');
      }
    } catch (e) {
      console.log('无法通知父窗口:', e);
    }
  }, [reason]);

  return (
    <Container size="sm" py="xl">
      <Paper shadow="md" p="xl" radius="md">
        <div style={{ textAlign: 'center' }}>
          <Title order={2} c="red" mb="md">
            支付失败
          </Title>

          <Alert icon={<RiAlertLine size={16} />} color="red" mb="xl">
            {getErrorMessage(reason)}
          </Alert>

          <Text size="sm" c="dimmed" mb="xl">
            如果问题持续存在，请联系客服获取帮助
          </Text>

          <Group justify="center" gap="md">
            <Button 
              variant="filled" 
              onClick={() => navigate('/agent/marketplace')}
            >
              返回智能体市场
            </Button>
            <Button 
              variant="outline" 
              onClick={() => navigate('/course/marketplace')}
            >
              浏览课程
            </Button>
            <Button 
              variant="light" 
              onClick={() => window.history.back()}
            >
              返回上一页
            </Button>
          </Group>

          <Text size="xs" c="dimmed" mt="xl">
            错误代码: {reason || 'unknown'}
          </Text>
        </div>
      </Paper>
    </Container>
  );
}
