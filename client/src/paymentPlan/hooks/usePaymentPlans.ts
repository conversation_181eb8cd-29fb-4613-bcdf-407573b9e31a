import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getPaymentPlans } from '../api';
import { PaymentPlan } from '../schemas';
import { ProductType } from '~/user/schemas';

/**
 * 付费计划Hook
 * 用于获取和管理付费计划数据
 */
export const usePaymentPlans = (productType: ProductType, productId: string) => {
  // 选中的付费计划ID
  const [selectedPlanId, setSelectedPlanId] = useState<number | undefined>(undefined);

  // 获取付费计划列表
  const {
    data: paymentPlansResponse,
    isLoading,
    error,
    refetch,
  } = useQuery({
    queryKey: ['paymentPlans', productType, productId],
    queryFn: () => getPaymentPlans(productType, productId),
    enabled: !!productType && !!productId,
  });

  // 提取付费计划数据
  const paymentPlans = paymentPlansResponse?.data || [];

  // 获取选中的付费计划
  const selectedPlan = paymentPlans.find((plan) => plan.id === selectedPlanId);

  // 处理选择付费计划
  const handleSelectPlan = (plan: PaymentPlan) => {
    setSelectedPlanId(plan.id);
  };

  return {
    // 数据
    paymentPlans,
    selectedPlanId,
    selectedPlan,
    isLoading,
    error,

    // 方法
    handleSelectPlan,
    setSelectedPlanId,
    refetch,
  };
};

export default usePaymentPlans;
