import React from 'react';
import { Card, Group, Stack, Text, Badge, useMantineTheme } from '@mantine/core';
import { PaymentPlan } from '../../schemas';

interface DesktopPaymentPlanCardProps {
  /**
   * 付费计划数据
   */
  plan: PaymentPlan;
  /**
   * 是否选中
   */
  isSelected?: boolean;
  /**
   * 点击事件
   */
  onClick?: (plan: PaymentPlan) => void;
  /**
   * 是否为暗色主题
   */
  isDark?: boolean;
}

/**
 * 付费计划卡片组件 - 桌面版
 */
const Desktop: React.FC<DesktopPaymentPlanCardProps> = ({ plan, isSelected = false, onClick, isDark = false }) => {
  const theme = useMantineTheme();

  // 处理点击事件
  const handleClick = () => {
    if (onClick) {
      onClick(plan);
    }
  };

  // 计算有效期显示文本
  const validityText = plan.validity_period > 0 ? `${plan.validity_period}天` : '永久';

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      style={{
        width: '240px',
        height: '160px',
        cursor: 'pointer',
        borderColor: isSelected ? theme.colors.blue[6] : undefined,
        backgroundColor: isSelected ? (isDark ? theme.colors.dark[5] : theme.colors.gray[0]) : undefined,
        transition: 'all 0.2s ease',
      }}
      onClick={handleClick}
    >
      <Stack gap="xs">
        <Group position="apart" align="center">
          <Text fw={700} size="lg" color={isSelected ? theme.colors.blue[6] : undefined}>
            {plan.name}
          </Text>
          {isSelected && (
            <Badge color="blue" variant="filled" size="sm">
              已选择
            </Badge>
          )}
        </Group>

        {plan.description && (
          <Text size="sm" color="dimmed" lineClamp={2}>
            {plan.description}
          </Text>
        )}

        <Group position="apart" mt="md">
          <Stack gap={0}>
            <Group gap={4} align="flex-end">
              <Text size="sm" color={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
                ¥
              </Text>
              <Text size="xl" fw={700} color={isDark ? theme.colors.red[4] : theme.colors.red[6]}>
                {plan.price.toFixed(2)}
              </Text>
            </Group>
            {plan.original_price > 0 && plan.original_price > plan.price && (
              <Text size="xs" color="dimmed" style={{ textDecoration: 'line-through' }}>
                ¥{plan.original_price.toFixed(2)}
              </Text>
            )}
          </Stack>
          <Badge color="green" variant="light">
            {validityText}
          </Badge>
        </Group>
      </Stack>
    </Card>
  );
};

export default Desktop;
