import React from 'react';
import { Group, Stack, Text, Skeleton } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { PaymentPlan } from '../../schemas';
import PaymentPlanCard from '../PaymentPlanCard';
import { PaymentPlanListProps } from './types';

/**
 * 付费计划列表组件 - 桌面版
 */
const Desktop: React.FC<PaymentPlanListProps> = ({ plans, isLoading = false, selectedPlanId, onSelectPlan }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  // 处理选择付费计划
  const handleSelectPlan = (plan: PaymentPlan) => {
    if (onSelectPlan) {
      onSelectPlan(plan);
    }
  };

  // 加载状态下的骨架屏
  if (isLoading) {
    return (
      <Stack gap="md">
        <Text size="lg" fw={600}>
          付费计划
        </Text>
        <Group gap="md">
          {[1, 2, 3].map((i) => (
            <Skeleton key={i} height={160} width={240} radius="md" />
          ))}
        </Group>
      </Stack>
    );
  }

  // 没有付费计划时的提示
  if (plans.length === 0) {
    return (
      <Stack gap="md">
        <Text size="lg" fw={600}>
          可用的付费计划
        </Text>
        <Text c="dimmed">暂无可用的付费计划</Text>
      </Stack>
    );
  }

  return (
    <Stack gap="md" className="mt-6">
      <Text size="lg" fw={600}>
        付费计划
      </Text>
      <Group gap="md" style={{ overflowX: 'auto', paddingBottom: '8px' }}>
        {plans.map((plan) => (
          <PaymentPlanCard
            key={plan.id}
            plan={plan}
            isSelected={selectedPlanId === plan.id}
            onClick={handleSelectPlan}
            isDark={isDark}
          />
        ))}
      </Group>
    </Stack>
  );
};

export default Desktop;
