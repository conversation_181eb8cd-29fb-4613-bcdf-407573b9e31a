import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Title,
  Text,
  Group,
  Button,
  Table,
  Checkbox,
  ActionIcon,
  Menu,
  TextInput,
  Badge,
  Loader,
  Center,
  Stack,
  Modal,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';
import { useQuery } from '@tanstack/react-query';
import { Dropzone } from '@mantine/dropzone';
import {
  RiArrowLeftLine,
  RiMoreLine,
  RiDeleteBin6Line,
  RiEdit2Line,
  RiAddLine,
  RiUploadCloud2Line,
  RiCloseLine,
  RiFileTextLine,
} from 'react-icons/ri';
import {
  getKnowledgeDetail,
  getKnowledgeDocuments,
  deleteDocument,
  renameDocument,
  uploadDocuments,
  batchDeleteDocuments,
} from '../api';
import { KnowledgeDocument } from '../schemas';
import { formatDate } from '~/core/utils';

/**
 * 知识库详情页面
 */
const KnowledgeDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [selectedDocuments, setSelectedDocuments] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false);
  const [documentToDelete, setDocumentToDelete] = useState<string | null>(null);

  // 重命名相关状态
  const [renameModalOpened, { open: openRenameModal, close: closeRenameModal }] = useDisclosure(false);
  const [documentToRename, setDocumentToRename] = useState<KnowledgeDocument | null>(null);
  const [newDocumentName, setNewDocumentName] = useState('');

  // 文件上传相关状态
  const [uploadModalOpened, { open: openUploadModal, close: closeUploadModal }] = useDisclosure(false);
  const [uploadFiles, setUploadFiles] = useState<File[]>([]);
  const [isUploading, setIsUploading] = useState(false);

  // 获取知识库详情
  const { data: knowledgeDetail, isLoading: isLoadingDetail } = useQuery({
    queryKey: ['knowledgeDetail', id],
    queryFn: () => (id ? getKnowledgeDetail(id) : Promise.resolve(null)),
    enabled: !!id,
  });

  // 获取知识库文档列表
  const {
    data: documentsData,
    isLoading: isLoadingDocuments,
    error: documentsError,
    refetch: refetchDocuments,
  } = useQuery({
    queryKey: ['knowledgeDocuments', id],
    queryFn: () =>
      id
        ? getKnowledgeDocuments(id, {
            page: 1,
            page_size: 100,
          })
        : Promise.resolve(null),
    enabled: !!id,
  });

  // 处理返回按钮点击
  const handleBackClick = () => {
    navigate('/knowledge');
  };

  // 处理全选
  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    setSelectAll(checked);
    if (checked) {
      const allDocIds = documentsData?.data?.data?.map((doc) => doc.id) || [];
      setSelectedDocuments(allDocIds);
    } else {
      setSelectedDocuments([]);
    }
  };

  // 处理单个文档选择
  const handleSelectDocument = (docId: string, checked: boolean) => {
    if (checked) {
      setSelectedDocuments((prev) => [...prev, docId]);
    } else {
      setSelectedDocuments((prev) => prev.filter((id) => id !== docId));
    }
  };

  // 处理删除文档
  const handleDeleteDocument = async () => {
    if (!documentToDelete || !id) return;

    try {
      await deleteDocument(id, documentToDelete);
      notifications.show({
        title: '成功',
        message: '文档删除成功',
        color: 'green',
      });
      // 刷新文档列表
      refetchDocuments();
      // 关闭删除确认框
      closeDeleteModal();
      setDocumentToDelete(null);
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '删除文档失败',
        color: 'red',
      });
    }
  };

  // 处理重命名文档
  const handleRenameDocument = async () => {
    if (!documentToRename || !id || !newDocumentName.trim()) return;

    try {
      // 调用重命名文档API
      await renameDocument(id, documentToRename.id, newDocumentName);

      notifications.show({
        title: '成功',
        message: '文档重命名成功',
        color: 'green',
      });

      // 刷新文档列表
      refetchDocuments();
      // 关闭重命名对话框
      closeRenameModal();
      setDocumentToRename(null);
      setNewDocumentName('');
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '重命名文档失败',
        color: 'red',
      });
    }
  };

  // 下载功能已移除

  // 处理批量删除文档
  const handleBatchDelete = async () => {
    if (selectedDocuments.length === 0 || !id) return;

    try {
      // 调用批量删除API
      await batchDeleteDocuments(id, selectedDocuments);

      notifications.show({
        title: '成功',
        message: `已删除 ${selectedDocuments.length} 个文档`,
        color: 'green',
      });
      // 刷新文档列表
      refetchDocuments();
      // 清空选择
      setSelectedDocuments([]);
      setSelectAll(false);
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '批量删除文档失败',
        color: 'red',
      });
    }
  };

  // 处理添加文档
  const handleAddDocument = () => {
    // 打开文件上传模态框
    openUploadModal();
  };

  // 处理文件上传
  const handleFileUpload = (files: File[]) => {
    // 过滤出支持的文件类型
    const supportedFiles = files.filter((file) => {
      const fileType = file.type;
      const fileName = file.name.toLowerCase();
      return (
        fileType.includes('text/plain') ||
        fileType.includes('text/markdown') ||
        fileType.includes('application/pdf') ||
        fileType.includes('application/msword') ||
        fileType.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ||
        fileName.endsWith('.txt') ||
        fileName.endsWith('.md') ||
        fileName.endsWith('.pdf') ||
        fileName.endsWith('.doc') ||
        fileName.endsWith('.docx')
      );
    });

    if (supportedFiles.length !== files.length) {
      notifications.show({
        title: '警告',
        message: '只支持 txt、md、word、pdf 格式的文件',
        color: 'yellow',
      });
    }

    setUploadFiles((prev) => [...prev, ...supportedFiles]);
  };

  // 移除上传文件
  const handleRemoveUploadFile = (index: number) => {
    setUploadFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // 提交文件上传
  const handleSubmitUpload = async () => {
    if (uploadFiles.length === 0 || !id) {
      notifications.show({
        title: '错误',
        message: '请选择至少一个文件上传',
        color: 'red',
      });
      return;
    }

    setIsUploading(true);

    try {
      // 调用上传文件到知识库的API
      await uploadDocuments(id, uploadFiles);

      notifications.show({
        title: '成功',
        message: '文件上传成功',
        color: 'green',
      });

      // 刷新文档列表
      refetchDocuments();
      // 关闭上传对话框
      closeUploadModal();
      // 清空上传文件列表
      setUploadFiles([]);
    } catch (error) {
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '上传文件失败',
        color: 'red',
      });
    } finally {
      setIsUploading(false);
    }
  };

  return (
    <Container size="lg" py={40}>
      {/* 顶部导航和标题 */}
      <Group mb={30}>
        <ActionIcon onClick={handleBackClick} variant="subtle" size="lg">
          <RiArrowLeftLine size={20} />
        </ActionIcon>
        <Title order={2}>{isLoadingDetail ? '加载中...' : knowledgeDetail?.data?.name || '知识库详情'}</Title>
      </Group>

      {/* 描述信息 */}
      {knowledgeDetail?.data?.description && (
        <Text c="dimmed" mb={30}>
          {knowledgeDetail.data.description}
        </Text>
      )}

      {/* 搜索和操作栏 */}
      <Stack mb={20} gap="xs">
        <Group position="apart">
          <Group>
            <Text fw={500}>文档</Text>
            {selectedDocuments.length > 0 && <Badge color="blue">{selectedDocuments.length} 文档被选中</Badge>}
            {selectedDocuments.length > 0 && (
              <Button
                variant="outline"
                color="red"
                leftSection={<RiDeleteBin6Line size={16} />}
                onClick={handleBatchDelete}
                size="xs"
              >
                批量删除
              </Button>
            )}
          </Group>
        </Group>
        <Text size="sm" c="dimmed">
          知识库内所有文件都会在这里显示，整个知识库可以通过 Chat 插件进行引用。
        </Text>
        <Group position="apart" mt={10}>
          <Button leftSection={<RiAddLine size={16} />} onClick={handleAddDocument}>
            添加文件
          </Button>
        </Group>
      </Stack>

      {/* 文档列表 */}
      {isLoadingDocuments ? (
        <Center h={200}>
          <Loader size="md" color="blue" />
        </Center>
      ) : documentsError ? (
        <Text c="red" ta="center">
          加载失败: {documentsError instanceof Error ? documentsError.message : '未知错误'}
        </Text>
      ) : documentsData?.data?.data?.length === 0 ? (
        <Stack align="center" justify="center" h={200} gap="md">
          <Text c="dimmed" ta="center">
            暂无文档，点击"添加文件"按钮上传文件
          </Text>
          <Button variant="outline" leftSection={<RiAddLine size={16} />} onClick={handleAddDocument}>
            添加文件
          </Button>
        </Stack>
      ) : (
        <Table>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>
                <Checkbox checked={selectAll} onChange={handleSelectAll} aria-label="全选" />
              </Table.Th>
              <Table.Th>#</Table.Th>
              <Table.Th>名称</Table.Th>
              <Table.Th>分词模式</Table.Th>
              <Table.Th>字符数</Table.Th>
              <Table.Th>引用次数</Table.Th>
              <Table.Th>上传时间</Table.Th>
              <Table.Th>状态</Table.Th>
              <Table.Th>启用</Table.Th>
              <Table.Th>操作</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {documentsData?.data?.data?.map((doc, index) => (
              <Table.Tr key={doc.id}>
                <Table.Td>
                  <Checkbox
                    checked={selectedDocuments.includes(doc.id)}
                    onChange={(e) => handleSelectDocument(doc.id, e.target.checked)}
                    aria-label={`选择文档 ${doc.name}`}
                  />
                </Table.Td>
                <Table.Td>{index + 1}</Table.Td>
                <Table.Td>{doc.name}</Table.Td>
                <Table.Td>
                  <Badge color="blue" variant="light">
                    {doc.segment_type || '自动'}
                  </Badge>
                </Table.Td>
                <Table.Td>{doc.word_count || 0}</Table.Td>
                <Table.Td>{doc.hit_count || 0}</Table.Td>
                <Table.Td>{formatDate(doc.created_at)}</Table.Td>
                <Table.Td>
                  {(() => {
                    // 根据状态显示不同颜色和文本的Badge
                    // 获取文档的实际状态，检查所有可能的状态字段
                    let status = '';

                    // 检查所有可能的状态字段
                    if (doc.status) {
                      status = doc.status;
                    } else if ('indexing_status' in doc) {
                      status = doc.indexing_status as string;
                    } else if ('processing_status' in doc) {
                      status = doc.processing_status as string;
                    } else if ('state' in doc) {
                      status = doc.state as string;
                    }

                    // 调试信息
                    console.log(`文档 ${doc.name} 的状态: `, status, doc);

                    // 打印文档的所有属性，查找可能的状态字段
                    console.log('文档的所有属性:', Object.keys(doc));

                    // 状态映射表
                    const statusMap: Record<string, { color: string; text: string }> = {
                      // 错误状态
                      error: { color: 'red', text: '错误' },
                      failed: { color: 'red', text: '错误' },
                      failure: { color: 'red', text: '错误' },
                      fail: { color: 'red', text: '错误' },

                      // 完成/可用状态
                      completed: { color: 'green', text: '可用' },
                      complete: { color: 'green', text: '可用' },
                      done: { color: 'green', text: '可用' },
                      available: { color: 'green', text: '可用' },
                      ready: { color: 'green', text: '可用' },
                      success: { color: 'green', text: '可用' },
                      succeeded: { color: 'green', text: '可用' },

                      // 排队状态
                      waiting: { color: 'orange', text: '排队中' },
                      queuing: { color: 'orange', text: '排队中' },
                      queue: { color: 'orange', text: '排队中' },
                      pending: { color: 'orange', text: '排队中' },
                      wait: { color: 'orange', text: '排队中' },

                      // 处理中状态
                      indexing: { color: 'blue', text: '处理中' },
                      parsing: { color: 'blue', text: '处理中' },
                      cleaning: { color: 'blue', text: '处理中' },
                      splitting: { color: 'blue', text: '处理中' },
                      processing: { color: 'blue', text: '处理中' },
                      running: { color: 'blue', text: '处理中' },
                      in_progress: { color: 'blue', text: '处理中' },
                      process: { color: 'blue', text: '处理中' },
                      embedding: { color: 'blue', text: '处理中' },
                    };

                    // 获取状态配置
                    const statusConfig = statusMap[status.toLowerCase()];

                    // 如果状态有匹配的配置，使用配置的颜色和文本
                    if (statusConfig) {
                      return <Badge color={statusConfig.color}>{statusConfig.text}</Badge>;
                    }

                    // 如果状态为空，显示为未知
                    if (!status) {
                      return <Badge color="gray">未知</Badge>;
                    }

                    // 如果状态没有匹配的配置，显示原始状态
                    return <Badge color="gray">{status}</Badge>;
                  })()}
                </Table.Td>
                <Table.Td>
                  <Checkbox checked={doc.enabled !== false} readOnly aria-label={`文档 ${doc.name} 启用状态`} />
                </Table.Td>
                <Table.Td>
                  <Menu position="bottom-end" withinPortal>
                    <Menu.Target>
                      <ActionIcon>
                        <RiMoreLine size={16} />
                      </ActionIcon>
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Item
                        leftSection={<RiEdit2Line size={16} />}
                        onClick={() => {
                          setDocumentToRename(doc);
                          setNewDocumentName(doc.name);
                          openRenameModal();
                        }}
                      >
                        重命名
                      </Menu.Item>

                      <Menu.Item
                        color="red"
                        leftSection={<RiDeleteBin6Line size={16} />}
                        onClick={() => {
                          setDocumentToDelete(doc.id);
                          openDeleteModal();
                        }}
                      >
                        删除
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      )}

      {/* 删除确认对话框 */}
      <Modal opened={deleteModalOpened} onClose={closeDeleteModal} title="确认删除" size="sm">
        <Text mb={20}>确定要删除这个文档吗？此操作不可撤销。</Text>
        <Group position="right">
          <Button variant="default" onClick={closeDeleteModal}>
            取消
          </Button>
          <Button color="red" onClick={handleDeleteDocument}>
            删除
          </Button>
        </Group>
      </Modal>

      {/* 重命名对话框 */}
      <Modal opened={renameModalOpened} onClose={closeRenameModal} title="重命名文档" size="sm">
        <TextInput
          label="文档名称"
          placeholder="请输入新的文档名称"
          value={newDocumentName}
          onChange={(e) => setNewDocumentName(e.target.value)}
          mb={20}
        />
        <Group position="right">
          <Button variant="default" onClick={closeRenameModal}>
            取消
          </Button>
          <Button color="blue" onClick={handleRenameDocument} disabled={!newDocumentName.trim()}>
            确认
          </Button>
        </Group>
      </Modal>

      {/* 文件上传对话框 */}
      <Modal opened={uploadModalOpened} onClose={closeUploadModal} title="上传文件" size="md">
        <Stack>
          <Text size="sm" c="dimmed">
            支持拖拽上传多个 txt、md、word、pdf 格式的文件
          </Text>

          <Dropzone
            onDrop={handleFileUpload}
            accept={{
              'text/plain': ['.txt'],
              'text/markdown': ['.md'],
              'application/pdf': ['.pdf'],
              'application/msword': ['.doc'],
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
            }}
            h={120}
          >
            <Group justify="center" gap="md" style={{ height: '100%', pointerEvents: 'none' }}>
              <Dropzone.Accept>
                <RiUploadCloud2Line size={32} color="green" />
              </Dropzone.Accept>
              <Dropzone.Reject>
                <RiCloseLine size={32} color="red" />
              </Dropzone.Reject>
              <Dropzone.Idle>
                <RiFileTextLine size={32} color="blue" />
              </Dropzone.Idle>

              <Stack gap={0} align="center">
                <Text size="sm" inline>
                  拖拽文件到此处或点击上传
                </Text>
                <Text size="xs" c="dimmed" inline>
                  支持 txt、md、word、pdf 格式
                </Text>
              </Stack>
            </Group>
          </Dropzone>

          {/* 显示已选择的文件列表 */}
          {uploadFiles.length > 0 && (
            <Stack mt="xs">
              {uploadFiles.map((file, index) => (
                <Group
                  key={index}
                  justify="space-between"
                  style={{ border: '1px solid #eee', borderRadius: '4px', padding: '4px 8px' }}
                >
                  <Group gap="xs">
                    <RiFileTextLine size={16} />
                    <Text
                      size="sm"
                      style={{ maxWidth: '250px', overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}
                    >
                      {file.name}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {(file.size / 1024).toFixed(1)} KB
                    </Text>
                  </Group>
                  <ActionIcon size="sm" color="red" variant="subtle" onClick={() => handleRemoveUploadFile(index)}>
                    <RiCloseLine size={16} />
                  </ActionIcon>
                </Group>
              ))}
            </Stack>
          )}

          <Group justify="flex-end" mt="md">
            <Button variant="default" onClick={closeUploadModal}>
              取消
            </Button>
            <Button color="blue" onClick={handleSubmitUpload} loading={isUploading} disabled={uploadFiles.length === 0}>
              上传
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
};

export default KnowledgeDetail;
