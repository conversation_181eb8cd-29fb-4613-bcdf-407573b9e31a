import React, { useState } from 'react';
import { ActionIcon, Button, Group, Stack, Text, useMantineTheme, TextInput, Textarea, Tabs } from '@mantine/core';
import { Dropzone } from '@mantine/dropzone';
import { notifications } from '@mantine/notifications';
import { useForm } from '@mantine/form';
import { IoClose, IoCloudUpload, IoDocument, IoAdd } from 'react-icons/io5';
import { FiAlertCircle, FiCheck } from 'react-icons/fi';
import { createKnowledge, createEmptyKnowledge } from '../api';
import { KnowledgeCreateResponse, CreateEmptyKnowledgeParams } from '../schemas';

interface KnowledgeUploaderProps {
  onKnowledgeCreated?: (data: KnowledgeCreateResponse) => void;
}

/**
 * 知识库上传组件
 */
const KnowledgeUploader: React.FC<KnowledgeUploaderProps> = ({ onKnowledgeCreated }) => {
  const theme = useMantineTheme();
  const [knowledgeFiles, setKnowledgeFiles] = useState<File[]>([]);
  const [isCreatingKnowledge, setIsCreatingKnowledge] = useState(false);
  const [activeTab, setActiveTab] = useState<string | null>('upload');

  // 空知识库表单
  const emptyKnowledgeForm = useForm<CreateEmptyKnowledgeParams>({
    initialValues: {
      name: '',
      description: '',
    },
    validate: {
      name: (value) => (value.trim().length === 0 ? '知识库名称不能为空' : null),
    },
  });

  // 处理知识库文件上传
  const handleKnowledgeFileDrop = (files: File[]) => {
    // 过滤出支持的文件类型
    const supportedFiles = files.filter((file) => {
      const fileType = file.type;
      const fileName = file.name.toLowerCase();
      return (
        fileType.includes('text/plain') ||
        fileType.includes('text/markdown') ||
        fileType.includes('application/pdf') ||
        fileType.includes('application/msword') ||
        fileType.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ||
        fileName.endsWith('.txt') ||
        fileName.endsWith('.md') ||
        fileName.endsWith('.pdf') ||
        fileName.endsWith('.doc') ||
        fileName.endsWith('.docx')
      );
    });

    if (supportedFiles.length !== files.length) {
      notifications.show({
        title: '警告',
        message: '只支持 txt、md、word、pdf 格式的文件',
        color: 'yellow',
        icon: <FiAlertCircle />,
      });
    }

    setKnowledgeFiles((prev) => [...prev, ...supportedFiles]);
  };

  // 移除知识库文件
  const removeKnowledgeFile = (index: number) => {
    setKnowledgeFiles((prev) => prev.filter((_, i) => i !== index));
  };

  // 创建知识库
  const handleCreateKnowledge = async () => {
    if (knowledgeFiles.length === 0) {
      notifications.show({
        title: '错误',
        message: '请上传至少一个文件',
        color: 'red',
        icon: <FiAlertCircle />,
      });
      return;
    }

    setIsCreatingKnowledge(true);

    try {
      const response = await createKnowledge(knowledgeFiles);

      if (response.data) {
        // 如果提供了回调函数，则调用它
        if (onKnowledgeCreated) {
          onKnowledgeCreated(response.data);
        }
      }

      notifications.show({
        title: '成功',
        message: '知识库创建成功',
        color: 'green',
        icon: <FiCheck />,
      });

      // 清空文件列表
      setKnowledgeFiles([]);
    } catch (error) {
      console.error('创建知识库失败:', error);
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '创建知识库失败',
        color: 'red',
        icon: <FiAlertCircle />,
      });
    } finally {
      setIsCreatingKnowledge(false);
    }
  };

  // 创建空知识库
  const handleCreateEmptyKnowledge = async (values: CreateEmptyKnowledgeParams) => {
    setIsCreatingKnowledge(true);

    try {
      console.log('创建空知识库，参数:', values);
      const response = await createEmptyKnowledge(values);
      console.log('创建空知识库响应:', response);

      // 处理响应数据，确保它符合KnowledgeCreateResponse接口
      let knowledgeData: KnowledgeCreateResponse;

      if (response.data) {
        // 检查响应是否包含dataset属性
        if (response.data.dataset) {
          // 标准DataSetCreateResponse格式
          knowledgeData = response.data;
        } else if (response.data.id) {
          // EmptyDataSetCreateResponse格式，需要转换
          knowledgeData = {
            dataset: {
              id: response.data.id || '',
              name: response.data.name || '',
              description: response.data.description,
              // 添加其他必要的属性
              document_count: 0,
              word_count: 0,
              app_count: 0,
            },
            documents: [],
          };
        } else {
          // 未知格式，直接使用
          knowledgeData = response.data;
        }

        // 调用回调函数，确保关闭模态框并刷新列表
        if (onKnowledgeCreated) {
          console.log('调用onKnowledgeCreated回调函数，数据:', knowledgeData);
          onKnowledgeCreated(knowledgeData);
        } else {
          console.warn('未提供onKnowledgeCreated回调函数');
        }
      } else {
        // 即使没有响应数据，也尝试调用回调函数关闭模态框
        if (onKnowledgeCreated) {
          console.log('没有响应数据，但仍调用onKnowledgeCreated回调函数关闭模态框');
          onKnowledgeCreated({} as KnowledgeCreateResponse);
        }
      }

      notifications.show({
        title: '成功',
        message: '空知识库创建成功',
        color: 'green',
        icon: <FiCheck />,
      });

      // 确保调用回调函数关闭模态框
      if (onKnowledgeCreated && !response.data) {
        console.log('在通知后再次尝试调用onKnowledgeCreated回调函数关闭模态框');
        onKnowledgeCreated({} as KnowledgeCreateResponse);
      }

      // 重置表单
      emptyKnowledgeForm.reset();
    } catch (error) {
      console.error('创建空知识库失败:', error);
      notifications.show({
        title: '错误',
        message: error instanceof Error ? error.message : '创建空知识库失败',
        color: 'red',
        icon: <FiAlertCircle />,
      });

      // 即使出错，也尝试调用回调函数关闭模态框
      if (onKnowledgeCreated) {
        console.log('创建失败，但仍尝试调用onKnowledgeCreated回调函数关闭模态框');
        onKnowledgeCreated({} as KnowledgeCreateResponse);
      }
    } finally {
      setIsCreatingKnowledge(false);
    }
  };

  return (
    <Tabs value={activeTab} onChange={setActiveTab}>
      <Tabs.List>
        <Tabs.Tab value="upload" leftSection={<IoCloudUpload size={16} />}>
          上传文件
        </Tabs.Tab>
        <Tabs.Tab value="empty" leftSection={<IoAdd size={16} />}>
          创建空知识库
        </Tabs.Tab>
      </Tabs.List>

      <Tabs.Panel value="upload" pt="md">
        <Stack>
          <Text size="sm" c="dimmed">
            支持拖拽上传多个 txt、md、word、pdf 格式的文件
          </Text>

          <Dropzone
            onDrop={handleKnowledgeFileDrop}
            accept={{
              'text/plain': ['.txt'],
              'text/markdown': ['.md'],
              'application/pdf': ['.pdf'],
              'application/msword': ['.doc'],
              'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
            }}
            h={120}
            styles={{
              root: {
                borderColor: theme.colors.blue[6],
                '&[dataAccept]': { borderColor: theme.colors.green[6] },
                '&[dataReject]': { borderColor: theme.colors.red[6] },
              },
            }}
          >
            <Group justify="center" gap="md" style={{ height: '100%', pointerEvents: 'none' }}>
              <Dropzone.Accept>
                <IoCloudUpload size={32} color={theme.colors.green[6]} />
              </Dropzone.Accept>
              <Dropzone.Reject>
                <IoClose size={32} color={theme.colors.red[6]} />
              </Dropzone.Reject>
              <Dropzone.Idle>
                <IoDocument size={32} color={theme.colors.blue[6]} />
              </Dropzone.Idle>

              <Stack gap={0} align="center">
                <Text size="sm" inline>
                  拖拽文件到此处或点击上传
                </Text>
                <Text size="xs" c="dimmed" inline>
                  支持 txt、md、word、pdf 格式
                </Text>
              </Stack>
            </Group>
          </Dropzone>

          {/* 显示已上传的文件列表 */}
          {knowledgeFiles.length > 0 && (
            <Stack mt="xs">
              {knowledgeFiles.map((file, index) => (
                <Group
                  key={index}
                  justify="space-between"
                  style={{ border: `1px solid ${theme.colors.gray[3]}`, borderRadius: theme.radius.sm, padding: '4px 8px' }}
                >
                  <Group gap="xs">
                    <IoDocument size={16} />
                    <Text size="sm" truncate style={{ maxWidth: '250px' }}>
                      {file.name}
                    </Text>
                    <Text size="xs" c="dimmed">
                      {(file.size / 1024).toFixed(1)} KB
                    </Text>
                  </Group>
                  <ActionIcon size="sm" color="red" variant="subtle" onClick={() => removeKnowledgeFile(index)}>
                    <IoClose size={16} />
                  </ActionIcon>
                </Group>
              ))}

              <Button
                mt="xs"
                color="blue"
                onClick={handleCreateKnowledge}
                loading={isCreatingKnowledge}
                disabled={knowledgeFiles.length === 0}
              >
                创建知识库
              </Button>
            </Stack>
          )}
        </Stack>
      </Tabs.Panel>

      <Tabs.Panel value="empty" pt="md">
        <form onSubmit={emptyKnowledgeForm.onSubmit(handleCreateEmptyKnowledge)}>
          <Stack>
            <TextInput label="知识库名称" placeholder="输入知识库名称" required {...emptyKnowledgeForm.getInputProps('name')} />

            <Textarea
              label="知识库描述"
              placeholder="输入知识库描述（可选）"
              autosize
              minRows={3}
              maxRows={5}
              {...emptyKnowledgeForm.getInputProps('description')}
            />

            <Button mt="md" type="submit" color="blue" loading={isCreatingKnowledge}>
              创建空知识库
            </Button>
          </Stack>
        </form>
      </Tabs.Panel>
    </Tabs>
  );
};

export default KnowledgeUploader;
