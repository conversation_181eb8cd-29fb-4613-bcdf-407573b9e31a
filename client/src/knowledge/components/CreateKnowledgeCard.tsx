import React from 'react';
import { Card, Text, Stack, Box } from '@mantine/core';
import { RiAddLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';

interface CreateKnowledgeCardProps {
  onClick: () => void;
}

/**
 * 创建知识库卡片组件
 */
const CreateKnowledgeCard: React.FC<CreateKnowledgeCardProps> = ({ onClick }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      style={{
        cursor: 'pointer',
        backgroundColor: isDark ? 'var(--mantine-color-dark-6)' : 'var(--mantine-color-gray-0)',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      }}
      onClick={onClick}
    >
      <Stack gap="md" align="center" justify="center" style={{ width: '100%' }}>
        <Box
          style={{
            width: '60px',
            height: '60px',
            borderRadius: '50%',
            backgroundColor: 'var(--mantine-color-blue-1)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'var(--mantine-color-blue-6)',
            marginBottom: '16px',
          }}
        >
          <RiAddLine size={24} />
        </Box>

        <Text fw={500} size="lg" ta="center" color="blue">
          创建知识库
        </Text>

        <Text size="sm" c="dimmed" ta="center" style={{ maxWidth: '90%', marginTop: '8px' }}>
          导入您自己的文本数据或通过 Webhook 实时写入数据以增强 LLM 的上下文。
        </Text>
      </Stack>
    </Card>
  );
};

export default CreateKnowledgeCard;
