import React, { useRef, useEffect } from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';
import { RiSearchLine } from 'react-icons/ri';
import { Group, Input, Box } from '@mantine/core';
import { useDebouncedValue } from '@mantine/hooks';
import './styles.css';

interface SimpleSearchBoxProps {
  /**
   * Placeholder text for the search input
   */
  placeholder?: string;
  /**
   * Callback function when search value changes
   */
  onSearch?: (value: string) => void;
  /**
   * Current search value
   */
  searchKeyword?: string;
  /**
   * Set search keyword function
   */
  setSearchKeyword?: (value: string) => void;
  /**
   * Width of the search box (default: 100%)
   */
  width?: string | number;
}

/**
 * Simple search box component with clean design
 */
const SimpleSearchBox: React.FC<SimpleSearchBoxProps> = ({
  placeholder = '搜索...',
  onSearch,
  searchKeyword = '',
  setSearchKeyword,
  width = '100%',
}) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const inputRef = useRef<HTMLInputElement>(null);

  // 使用防抖处理搜索
  const [debouncedSearchKeyword] = useDebouncedValue(searchKeyword || '', 300);

  // 当防抖后的关键词变化时触发搜索
  useEffect(() => {
    if (debouncedSearchKeyword !== undefined && onSearch) {
      onSearch(debouncedSearchKeyword);
    }
  }, [debouncedSearchKeyword, onSearch]);

  // Handle search input change
  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setSearchKeyword?.(value);
  };

  // Determine background and shadow colors based on theme
  const bgColor = isDark ? '#1A1B1E' : 'white';
  const shadowColor = isDark ? 'rgba(0, 0, 0, 0.3)' : 'rgba(232, 239, 252, 0.5)';
  const iconColor = isDark ? '#5C5F66' : 'rgba(199, 199, 199, 1)';

  return (
    <Box style={{ width }}>
      <Group
        style={{
          position: 'relative',
          height: '64px',
          borderRadius: '12px',
          backgroundColor: bgColor,
          boxShadow: `0px 6px 6px 0px ${shadowColor}`,
          overflow: 'hidden',
        }}
        px={24}
        onClick={() => inputRef.current?.focus()}
      >
        <RiSearchLine size={20} color={iconColor} />
        <Input
          ref={inputRef}
          className="flex-auto"
          variant="unstyled"
          value={searchKeyword}
          placeholder={placeholder}
          onChange={handleSearchChange}
          rightSection={
            searchKeyword !== '' ? <Input.ClearButton size="32px" onClick={() => setSearchKeyword?.('')} /> : undefined
          }
          rightSectionPointerEvents="auto"
          styles={{
            input: {
              fontSize: '16px',
            },
          }}
          style={{
            border: 'none',
            outline: 'none',
            background: 'transparent',
            fontSize: '16px',
            lineHeight: '1.2',
            color: actualColorScheme === 'dark' ? '#C1C2C5' : '#000000',
          }}
        />
      </Group>
    </Box>
  );
};

export default SimpleSearchBox;
