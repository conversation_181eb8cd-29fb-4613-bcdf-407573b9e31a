import React from 'react';
import { Card, Text, Group, ActionIcon, Stack, Badge, Menu, Modal, Button } from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { RiDeleteBin6Line, RiMore2Fill, RiFileTextLine, RiEdit2Line } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';
import { KnowledgeDataset } from '../schemas';

interface KnowledgeCardProps {
  knowledge: KnowledgeDataset;
  onClick: (knowledge: KnowledgeDataset) => void;
  onDelete: (id: string) => void;
  onEdit?: (knowledge: KnowledgeDataset) => void;
}

/**
 * 知识库卡片组件
 */
const KnowledgeCard: React.FC<KnowledgeCardProps> = ({ knowledge, onClick, onDelete, onEdit }) => {
  const { actualColorScheme } = useTheme();
  const [menuOpened, menuHandlers] = useDisclosure(false);
  const isDark = actualColorScheme === 'dark';

  // 添加调试输出，查看完整的知识库对象
  console.log('完整的知识库对象:', JSON.stringify(knowledge, null, 2));

  // 不再使用元数据，而是直接使用标签
  // 移除旧的元数据处理逻辑，不再显示文档数量、字符数和关联应用数
  console.log('知识库标签:', knowledge.tags);

  // 确认删除对话框
  const [deleteModalOpened, { open: openDeleteModal, close: closeDeleteModal }] = useDisclosure(false);

  // 创建一个引用，用于跟踪知识库是否已被删除
  const isDeleted = React.useRef(false);

  // 处理卡片点击事件
  const handleCardClick = () => {
    // 如果知识库已被删除，则不触发点击事件
    if (!isDeleted.current) {
      onClick(knowledge);
    }
  };

  // 修改确认删除函数，标记知识库已被删除
  const handleConfirmDeleteWithFlag = (e: React.MouseEvent) => {
    e.stopPropagation();
    // 标记知识库已被删除
    isDeleted.current = true;
    onDelete(knowledge.id);
    closeDeleteModal();
  };

  return (
    <Card
      shadow="sm"
      padding="lg"
      radius="md"
      withBorder
      style={{
        cursor: 'pointer',
        backgroundColor: isDark ? '#2D333B' : 'white',
        borderColor: isDark ? '#444' : '#eee',
      }}
      onClick={handleCardClick}
    >
      <Card.Section p="md">
        <Group justify="space-between" align="flex-start">
          <Group gap="xs">
            <RiFileTextLine size={24} color={isDark ? '#90CAF9' : '#2196F3'} />
            <Text fw={500} size="lg" lineClamp={1} style={{ flex: 1 }}>
              {knowledge.name}
            </Text>
          </Group>
          <Menu
            opened={menuOpened}
            onOpen={menuHandlers.open}
            onClose={menuHandlers.close}
            position="bottom-end"
            offset={4}
            withArrow
            arrowPosition="center"
          >
            <Menu.Target>
              <ActionIcon
                variant="subtle"
                color="gray"
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  menuHandlers.toggle();
                }}
              >
                <RiMore2Fill size={18} />
              </ActionIcon>
            </Menu.Target>
            <Menu.Dropdown onClick={(e: React.MouseEvent) => e.stopPropagation()}>
              {onEdit && (
                <Menu.Item
                  leftSection={<RiEdit2Line size={16} />}
                  onClick={(e: React.MouseEvent) => {
                    e.stopPropagation();
                    onEdit(knowledge);
                  }}
                >
                  编辑知识库
                </Menu.Item>
              )}

              <Menu.Item
                color="red"
                leftSection={<RiDeleteBin6Line size={16} />}
                onClick={(e: React.MouseEvent) => {
                  e.stopPropagation();
                  openDeleteModal();
                }}
              >
                删除知识库
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </Card.Section>

      <Stack gap="xs" mt="md">
        {knowledge.description && (
          <Text size="sm" c="dimmed" lineClamp={2}>
            {knowledge.description}
          </Text>
        )}

        {/* 显示文档数量、字数统计和关联应用数量 */}
        <Group mt="xs" style={{ width: '100%' }}>
          <Text size="sm" c="dimmed" style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <span>{knowledge.document_count || 0} 文档</span>
            <span style={{ fontSize: '10px', color: 'var(--mantine-color-gray-5)' }}>•</span>
            <span>{Math.round((knowledge.word_count || 0) / 1000)} 千字符</span>
            <span style={{ fontSize: '10px', color: 'var(--mantine-color-gray-5)' }}>•</span>
            <span>{knowledge.app_count || 0} 关联应用</span>
          </Text>
        </Group>

        <Group mt="md" style={{ width: '100%' }}>
          {/* 显示标签 */}
          <Group gap="xs">
            {knowledge.tags && knowledge.tags.length > 0 ? (
              knowledge.tags.map((tag) => (
                <Badge
                  key={tag.id}
                  color="blue"
                  variant="light"
                  style={{
                    display: 'inline-flex',
                    minWidth: '60px',
                    textAlign: 'center',
                    padding: '4px 8px',
                  }}
                >
                  {tag.name}
                </Badge>
              ))
            ) : (
              <Badge
                color="gray"
                variant="light"
                style={{
                  display: 'inline-flex',
                  minWidth: '60px',
                  textAlign: 'center',
                  padding: '4px 8px',
                }}
              >
                暂无标签
              </Badge>
            )}
          </Group>
        </Group>
      </Stack>

      {/* 确认删除模态框 */}
      <Modal
        opened={deleteModalOpened}
        onClose={closeDeleteModal}
        title="删除知识库"
        centered
        onClick={(e: React.MouseEvent) => e.stopPropagation()}
      >
        <Text size="sm" mb="lg">
          确定要删除知识库 "{knowledge.name}" 吗？此操作不可撤销。
        </Text>
        <Group justify="flex-end">
          <Button
            variant="default"
            onClick={(e) => {
              e.stopPropagation();
              closeDeleteModal();
            }}
          >
            取消
          </Button>
          <Button color="red" onClick={handleConfirmDeleteWithFlag}>
            删除
          </Button>
        </Group>
      </Modal>
    </Card>
  );
};

export default KnowledgeCard;
