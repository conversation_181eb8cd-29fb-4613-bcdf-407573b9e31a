import { API_BASE_URL } from '~/core/constants';
import { ResponsePayloads, Pagination } from '~/core/schemas';
import { useUserStore } from '~/user/core/store';
import {
  KnowledgeDataset,
  KnowledgeCreateResponse,
  KnowledgeListParams,
  CreateEmptyKnowledgeParams,
  UpdateKnowledgeParams,
  MetadataField,
  MetadataListResponse,
  CreateMetadataParams,
  UpdateMetadataParams,
  UpdateDocumentMetadataParams,
  KnowledgeDocumentsParams,
  KnowledgeDocument,
} from './schemas';

/**
 * 获取知识库列表
 * @param params 查询参数
 * @returns 知识库列表
 */
export async function getKnowledgeList(params: KnowledgeListParams): Promise<ResponsePayloads<Pagination<KnowledgeDataset>>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const queryParams = new URLSearchParams();
  queryParams.append('page', params.page.toString());
  queryParams.append('page_size', params.page_size.toString());

  if (params.keyword) {
    queryParams.append('keyword', params.keyword);
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/list?${queryParams.toString()}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取知识库列表失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 创建知识库
 * @param files 要上传的文件列表
 * @returns 创建结果，包含数据集信息和文档列表
 */
export async function createKnowledge(files: File[]): Promise<ResponsePayloads<KnowledgeCreateResponse>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const formData = new FormData();

  // 添加文件到表单
  files.forEach((file) => {
    formData.append('files', file);
  });

  const response = await fetch(`${API_BASE_URL}/knowledge/create`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`创建知识库失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 创建空知识库
 * @param params 创建参数
 * @returns 创建结果
 */
export async function createEmptyKnowledge(
  params: CreateEmptyKnowledgeParams,
): Promise<ResponsePayloads<KnowledgeCreateResponse>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/create-empty`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`创建空知识库失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 删除知识库
 * @param id 知识库ID
 * @returns 删除结果
 */
export async function deleteKnowledge(id: string): Promise<ResponsePayloads<{ success: boolean }>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/delete/${id}`, {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`删除知识库失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 更新知识库
 * @param id 知识库ID
 * @param params 更新参数
 * @returns 更新结果
 */
export async function updateKnowledge(id: string, params: UpdateKnowledgeParams): Promise<ResponsePayloads<KnowledgeDataset>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/update/${id}`, {
    method: 'PUT',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`更新知识库失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取知识库元数据列表
 * @param datasetId 知识库ID
 * @returns 元数据列表
 */
export async function getMetadataList(datasetId: string): Promise<ResponsePayloads<MetadataListResponse>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/metadata`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取知识库元数据列表失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 创建知识库元数据字段
 * @param datasetId 知识库ID
 * @param params 创建参数
 * @returns 创建的元数据字段
 */
export async function createMetadata(datasetId: string, params: CreateMetadataParams): Promise<ResponsePayloads<MetadataField>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/metadata`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`创建知识库元数据字段失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 更新知识库元数据字段
 * @param datasetId 知识库ID
 * @param metadataId 元数据字段ID
 * @param params 更新参数
 * @returns 更新后的元数据字段
 */
export async function updateMetadata(
  datasetId: string,
  metadataId: string,
  params: UpdateMetadataParams,
): Promise<ResponsePayloads<MetadataField>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/metadata/${metadataId}`, {
    method: 'PUT',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`更新知识库元数据字段失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 删除知识库元数据字段
 * @param datasetId 知识库ID
 * @param metadataId 元数据字段ID
 * @returns 删除结果
 */
export async function deleteMetadata(datasetId: string, metadataId: string): Promise<ResponsePayloads<{ success: boolean }>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/metadata/${metadataId}`, {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`删除知识库元数据字段失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 启用/禁用知识库元数据内置字段
 * @param datasetId 知识库ID
 * @param action 操作类型，enable表示启用，disable表示禁用
 * @returns 操作结果
 */
export async function toggleBuiltInMetadata(
  datasetId: string,
  action: 'enable' | 'disable',
): Promise<ResponsePayloads<{ success: boolean }>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/metadata/built-in-field`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ action }),
  });

  if (!response.ok) {
    throw new Error(`${action}知识库元数据内置字段失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 更新文档元数据
 * @param datasetId 知识库ID
 * @param params 更新参数
 * @returns 更新结果
 */
export async function updateDocumentMetadata(
  datasetId: string,
  params: UpdateDocumentMetadataParams,
): Promise<ResponsePayloads<{ success: boolean }>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/documents/metadata`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(params),
  });

  if (!response.ok) {
    throw new Error(`更新文档元数据失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取知识库详情
 * @param id 知识库ID
 * @returns 知识库详情
 */
export async function getKnowledgeDetail(id: string): Promise<ResponsePayloads<KnowledgeDataset>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/detail/${id}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取知识库详情失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取知识库文档列表
 * @param datasetId 知识库ID
 * @param params 查询参数
 * @returns 文档列表
 */
export async function getKnowledgeDocuments(
  datasetId: string,
  params: KnowledgeDocumentsParams,
): Promise<ResponsePayloads<Pagination<KnowledgeDocument>>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const queryParams = new URLSearchParams();
  queryParams.append('page', params.page.toString());
  queryParams.append('page_size', params.page_size.toString());

  if (params.keyword) {
    queryParams.append('keyword', params.keyword);
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/documents?${queryParams.toString()}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取知识库文档列表失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 删除知识库文档
 * @param datasetId 知识库ID
 * @param documentId 文档ID
 * @returns 删除结果
 */
export async function deleteDocument(datasetId: string, documentId: string): Promise<ResponsePayloads<{ success: boolean }>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/documents/${documentId}`, {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`删除知识库文档失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 重命名知识库文档
 * @param datasetId 知识库ID
 * @param documentId 文档ID
 * @param newName 新文档名称
 * @returns 重命名结果
 */
export async function renameDocument(
  datasetId: string,
  documentId: string,
  newName: string,
): Promise<ResponsePayloads<KnowledgeDocument>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/documents/${documentId}/rename`, {
    method: 'POST',
    headers: {
      Accept: 'application/json',
      'Content-Type': 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify({ name: newName }),
  });

  if (!response.ok) {
    throw new Error(`重命名知识库文档失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 上传文件到知识库
 * @param datasetId 知识库ID
 * @param files 要上传的文件列表
 * @returns 上传结果
 */
export async function uploadDocuments(
  datasetId: string,
  files: File[],
): Promise<ResponsePayloads<{ success: boolean; result: Record<string, unknown> }>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const formData = new FormData();

  // 添加文件到表单
  files.forEach((file) => {
    formData.append('files', file);
  });

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/documents/upload`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
    },
    body: formData,
  });

  if (!response.ok) {
    throw new Error(`上传文件到知识库失败: ${response.statusText}`);
  }

  return response.json();
}

// 下载功能已移除

/**
 * 批量删除知识库文档
 * @param datasetId 知识库ID
 * @param documentIds 文档ID列表
 * @returns 批量删除结果
 */
export async function batchDeleteDocuments(
  datasetId: string,
  documentIds: string[],
): Promise<ResponsePayloads<{ success: boolean }>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  // 构建查询参数
  const queryParams = new URLSearchParams();
  documentIds.forEach((id) => {
    queryParams.append('document_ids', id);
  });

  const response = await fetch(`${API_BASE_URL}/knowledge/${datasetId}/documents?${queryParams.toString()}`, {
    method: 'DELETE',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`批量删除知识库文档失败: ${response.statusText}`);
  }

  return response.json();
}
