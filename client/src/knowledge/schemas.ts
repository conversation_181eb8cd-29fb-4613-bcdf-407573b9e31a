// 知识库相关的数据模型

/**
 * 知识库文档类型
 */
export interface KnowledgeDocument {
  id: string;
  name: string;
  size?: number;
  created_at: number;
  extension?: string;
  mime_type?: string;
  url?: string;
  status?: string;
  word_count?: number;
  segment_type?: string;
  hit_count?: number;
  enabled?: boolean;
}

/**
 * 文件信息列表
 */
export interface FileInfoList {
  file_ids: string[];
}

/**
 * 数据源信息
 */
export interface DataSourceInfo {
  file_info_list?: FileInfoList;
  [key: string]: unknown;
}

/**
 * 知识库数据集类型
 */
export interface KnowledgeDataset {
  id: string;
  name: string;
  description?: string;
  permission?: string;
  data_source_type?: string;
  data_source_info?: DataSourceInfo;
  indexing_technique?: string;
  created_by?: string;
  created_at?: number;
  updated_by?: string | null;
  updated_at?: number;
  documents?: KnowledgeDocument[];
  document_count?: number;
  character_count?: number; // 字符数
  word_count?: number; // API返回的字数
  app_count?: number; // 关联应用数
  metadata?: string; // 元数据，格式如："2 文档·63 千字符·1 关联应用"
  provider?: string;
  embedding_model?: string;
  embedding_model_provider?: string;
  embedding_available?: boolean;
  retrieval_model_dict?: Record<string, unknown>;
  tags?: Array<{
    id: string;
    name: string;
    type: string;
  }>;
  doc_form?: string;
  external_knowledge_info?: {
    external_knowledge_id: string | null;
    external_knowledge_api_id: string | null;
    external_knowledge_api_name: string | null;
    external_knowledge_api_endpoint: string | null;
  };
  external_retrieval_model?: {
    top_k: number;
    score_threshold: number;
    score_threshold_enabled: boolean;
  };
  doc_metadata?: Record<string, unknown>[];
  built_in_field_enabled?: boolean;
  partial_member_list?: Record<string, unknown>[];
  [key: string]: unknown; // 添加索引签名，允许任意属性
}

/**
 * 知识库创建响应
 */
export interface KnowledgeCreateResponse {
  dataset?: KnowledgeDataset;
  documents?: KnowledgeDocument[];
  id?: string;
  name?: string;
  description?: string;
  [key: string]: unknown; // 允许其他属性，以支持EmptyDataSetCreateResponse
}

/**
 * 知识库列表项
 */
export interface KnowledgeItem {
  id: string;
  name: string;
  description?: string;
  document_count: number;
  created_at: number;
  updated_at?: number;
  icon?: string;
}

/**
 * 知识库查询参数
 */
export interface KnowledgeListParams {
  page: number;
  page_size: number;
  keyword?: string;
}

/**
 * 创建空知识库参数
 */
export interface CreateEmptyKnowledgeParams {
  name: string;
  description?: string;
}

/**
 * 更新知识库参数
 */
export interface UpdateKnowledgeParams {
  name: string;
  description?: string;
}

/**
 * 元数据字段类型
 */
export interface MetadataField {
  id: string;
  type: string;
  name: string;
  use_count?: number;
}

/**
 * 元数据列表响应
 */
export interface MetadataListResponse {
  doc_metadata: MetadataField[];
  built_in_field_enabled: boolean;
}

/**
 * 创建元数据字段参数
 */
export interface CreateMetadataParams {
  type: string;
  name: string;
}

/**
 * 更新元数据字段参数
 */
export interface UpdateMetadataParams {
  name: string;
}

/**
 * 文档元数据项
 */
export interface DocumentMetadataItem {
  id: string;
  value: string;
  name: string;
}

/**
 * 文档元数据操作
 */
export interface DocumentMetadataOperation {
  document_id: string;
  metadata_list: DocumentMetadataItem[];
}

/**
 * 更新文档元数据参数
 */
export interface UpdateDocumentMetadataParams {
  operation_data: DocumentMetadataOperation[];
}

/**
 * 知识库文档查询参数
 */
export interface KnowledgeDocumentsParams {
  page: number;
  page_size: number;
  keyword?: string;
}
