import { API_BASE_URL } from '~/core/constants';
import { ResponsePayloads } from '~/core/schemas';
import { useUserStore } from './core/store';
import { OrderCreateRequest, OrderResult } from './schemas';

/**
 * 创建订单
 * @param data 订单创建请求
 * @returns 订单创建结果
 */
export async function createOrder(data: OrderCreateRequest): Promise<ResponsePayloads<OrderResult>> {
  const token = useUserStore.getState().token;
  const response = await fetch(`${API_BASE_URL}/orders/`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error?.message || '创建订单失败');
  }

  return response.json();
}
