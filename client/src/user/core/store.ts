import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { UserInfo } from './schemas';

// 登录方式类型
export type LoginMethod = 'account' | 'sms';

interface UserState {
  token: string | null;
  userInfo: UserInfo | null;
  isAuthenticated: boolean;
  // 登录相关状态
  loginMethod: LoginMethod;
  loginTitle: string;
  setToken: (token: string) => void;
  setUserInfo: (userInfo: UserInfo) => void;
  clearUser: () => void;
  // 设置登录方式
  setLoginMethod: (method: LoginMethod) => void;
}

export const useUserStore = create<UserState>()(
  devtools(
    persist(
      (set) => ({
        token: null,
        userInfo: null,
        isAuthenticated: false,
        // 默认登录方式为账号登录
        loginMethod: 'account',
        loginTitle: '使用账号登录',
        setToken: (token) => set({ token, isAuthenticated: true }, false, 'setToken'),
        setUserInfo: (userInfo) => set({ userInfo }, false, 'setUserInfo'),
        clearUser: () => set({ token: null, userInfo: null, isAuthenticated: false }, false, 'clearUser'),
        // 设置登录方式，同时更新标题
        setLoginMethod: (method) =>
          set(
            {
              loginMethod: method,
              loginTitle: method === 'account' ? '使用账号登录' : '使用短信登录',
            },
            false,
            'setLoginMethod',
          ),
      }),
      {
        name: 'user-storage',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          token: state.token,
          userInfo: state.userInfo,
          isAuthenticated: state.isAuthenticated,
          // 不需要持久化登录方式和标题
        }),
      },
    ),
    {
      name: 'user-store',
    },
  ),
);

export const getUserStore = () => useUserStore.getState();
