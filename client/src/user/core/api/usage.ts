import { ResponsePayloads } from '~/core/schemas';
import { useUserStore } from '~/user/core/store';
import { API_BASE_URL } from '~/core/constants';

/**
 * 用户活动项
 */
export interface UserActivity {
  id: string;
  key_id: string;
  app_id: string;
  user_id: string;
  service_code: string;
  scope?: string;
  instance_id?: string;
  instance_name?: string;
  type: string; // 活动类型：verify 或 consume
  details?: Record<string, unknown>;
  created_at: string;
  amount: number;
  currency_type?: string;
}

/**
 * 用户活动列表响应
 */
export interface ActivityListResponse {
  activities: UserActivity[];
  total: number;
  page: number;
  limit: number;
}

/**
 * 用户密钥项
 */
export interface UserKey {
  id: string;
  user_id: string;
  service_code: string;
  scope?: string[];
  scope_names: string[];
  credit_limit: number;
  credit_used: number;
  currency_type: string;
  status: string;
  created_at: string;
  expires_at?: string;
}

/**
 * 用户密钥列表响应
 */
export interface KeyListResponse {
  keys: UserKey[];
}

/**
 * 获取指定密钥的活动列表
 * @param keyId 密钥ID
 * @param page 页码，默认为1
 * @param limit 每页数量，默认为100
 * @returns 用户活动列表
 */
export async function getUserActivitiesByKey(
  keyId: string,
  page: number = 1,
  limit: number = 100,
): Promise<ResponsePayloads<ActivityListResponse>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/users/actives?key_id=${keyId}&page=${page}&limit=${limit}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取用户活动列表失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取用户所有密钥的活动列表
 * @returns 用户活动列表
 */
export async function getUserActivities(): Promise<ResponsePayloads<ActivityListResponse>> {
  // 首先获取用户的所有密钥
  const keysResponse = await getUserKeys();
  const keys = keysResponse.data.keys;

  if (keys.length === 0) {
    return {
      data: {
        activities: [],
        total: 0,
        page: 1,
        limit: 100,
      },
    };
  }

  // 为每个密钥获取活动记录
  const allActivities: UserActivity[] = [];

  for (const key of keys) {
    try {
      const activitiesResponse = await getUserActivitiesByKey(key.id);
      allActivities.push(...activitiesResponse.data.activities);
    } catch (error) {
      console.warn(`获取密钥 ${key.id} 的活动记录失败:`, error);
      // 继续处理其他密钥，不中断整个流程
    }
  }

  // 按创建时间倒序排序
  allActivities.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime());

  return {
    data: {
      activities: allActivities,
      total: allActivities.length,
      page: 1,
      limit: 100,
    },
  };
}

/**
 * 获取用户密钥列表
 * @returns 用户密钥列表
 */
export async function getUserKeys(): Promise<ResponsePayloads<KeyListResponse>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/users/keys`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取用户密钥列表失败: ${response.statusText}`);
  }

  return response.json();
}
