import React from 'react';
import { Text, useMantineTheme } from '@mantine/core';
import { Link } from 'react-router-dom';
import { useTheme } from '~/core/features/mantine/theme-context';

const Desktop = () => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();
  const isDark = actualColorScheme === 'dark';

  const textColor = isDark ? theme.colors.indigo[7] : 'rgba(166, 166, 166, 1)';
  const linkColor = isDark ? theme.colors.indigo[7] : 'rgba(70, 92, 231, 1)';

  return (
    <Text fz={12} fw={400} lh="14px" c={textColor}>
      同意并接受{' '}
      <Link to="/user-agreement" style={{ color: linkColor, textDecoration: 'none' }}>
        《用户协议》
      </Link>
      和
      <Link to="/privacy-policy" style={{ color: linkColor, textDecoration: 'none' }}>
        《隐私政策》
      </Link>
    </Text>
  );
};

export default Desktop;
