import React from 'react';
import { Stack, Group, Image, Text, Button, Divider } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';

const Desktop = () => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const titleTextColor = isDark ? 'rgba(204, 204, 204, 1)' : 'rgba(51, 51, 51, 1)';

  return (
    <Group align="flex-start" wrap="nowrap" gap={30}>
      <Image
        className="flex-none rounded-[5px]"
        src="https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668"
        w={285}
        h={160}
      />
      <Stack className="relative flex-auto" h={160}>
        <Text className="line-clamp-1" fz={24} fw={500} lh="24px" c={titleTextColor}>
          多Kafka维度系统精讲，从入门到熟练掌握
        </Text>
        <Group wrap="nowrap" gap={20}>
          <Text className="flex-none" fz={14} fw={500} lh="20px" c="rgba(245, 62, 25, 1)">
            已学2%
          </Text>
          <Text className="flex-none" fz={14} fw={500} lh="20px" c="rgba(102, 102, 102, 1)">
            用时0分
          </Text>
          <Text className="flex-auto line-clamp-1" fz={14} fw={500} lh="20px" c="rgba(102, 102, 102, 1)">
            学习至 1.0.2
            代码初体验，制作我的第一个网页代码初体验，制作我的第一个网页代码初体验，制作我的第一个网页代码初体验，制作我的第一个网页
          </Text>
          <Text className="flex-auto line-clamp-1" fz={14} fw={500} lh="20px" c="rgba(153, 153, 153, 1)">
            课程已下架，无法正常观看，可点击删除课程
          </Text>
        </Group>

        <Button className="absolute bottom-[20px] right-0 rounded-[50px]" variant="filled" color="rgba(73, 81, 235, 1)">
          立即购买
        </Button>

        <Divider className="absolute bottom-[1px] w-full z-[1]" />
      </Stack>
    </Group>
  );
};

export default Desktop;
