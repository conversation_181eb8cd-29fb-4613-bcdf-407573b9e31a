import React from 'react';
import { useTheme } from '~/core/features/mantine/theme-context';
import { Text, useMantineTheme } from '@mantine/core';

import { ModuleTitleProps } from './types';

const Desktop: React.FC<ModuleTitleProps> = ({ title }) => {
  const { actualColorScheme } = useTheme();
  const theme = useMantineTheme();
  const isDark = actualColorScheme === 'dark';

  // Determine text colors based on theme
  const titleColor = isDark ? theme.colors.indigo[7] : 'rgba(73, 81, 235, 1)';

  return (
    <Text className="leading-[38px] fw-700" fz={26} c={titleColor}>
      {title}
    </Text>
  );
};

export default Desktop;
