import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

import { ModuleTitleProps } from './types';

/**
 * Responsive ModuleTitle component that loads Desktop or Mobile version
 * based on the device type
 */
const ModuleTitle: React.FC<ModuleTitleProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default ModuleTitle;
