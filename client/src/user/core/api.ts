import { UserInfo } from './schemas';
import { ResponsePayloads } from '~/core/schemas';

/**
 * 获取当前用户信息
 */
export async function getCurrentUser(token: string): Promise<ResponsePayloads<UserInfo>> {
  const response = await fetch('/api/users/me', {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error('获取用户信息失败');
  }

  return response.json();
}
