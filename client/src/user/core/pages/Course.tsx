import React from 'react';
import { Container, Space, Stack, Pagination, Center } from '@mantine/core';
import { ModuleTitle, CourseItem } from '../components';

const Course = () => {
  return (
    <Container size="950px" pt={72} pb={72}>
      <ModuleTitle title="全部课程" />

      <Space h={24} />

      <Stack gap={32}>
        <CourseItem />
        <CourseItem />
        <CourseItem />
        <CourseItem />
      </Stack>

      <Center mt={48}>
        <Pagination total={10} color="rgba(73, 81, 235, 1)" />
      </Center>
    </Container>
  );
};

export default Course;
