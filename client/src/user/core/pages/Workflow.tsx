import React from 'react';
import { Container, Space } from '@mantine/core';
import { useDeviceDetect } from '~/core/hooks';
import { ModuleList, ModuleCard } from '~/core/components';
import { ModuleTitle } from '../components';

const Workflow = () => {
  const { isMobile } = useDeviceDetect();

  return (
    <Container size="1176px" pt={72} pb={72}>
      <ModuleTitle title="全部工作流" />

      <Space h={24} />

      <ModuleList columns={isMobile ? 1 : 3} gap="md">
        <ModuleCard
          id="g-pmuQfob8d"
          name="image generator"
          description="A GPT specialized in generating and refining images with a mix of professional and friendly tone.image generator"
          iconUrl="https://ai-quantum.oss-cn-hangzhou.aliyuncs.com/images/logo.png"
          emphasized={true}
        />

        <ModuleCard
          id="g-abc123"
          name="AI助手"
          description="一个专业的AI助手，可以帮助你解决各种问题，包括编程、写作、翻译等"
          iconUrl="https://ai-quantum.oss-cn-hangzhou.aliyuncs.com/images/logo.png"
          emphasized={true}
        />

        <ModuleCard
          id="g-NgAcklHd8"
          name="SciSpace"
          description="Do hours worth of research in minutes. Instantly access 287M+ papers, analyze papers at lightning speed, and effortlessly draft content with accurate citations."
          iconUrl="https://ai-quantum.oss-cn-hangzhou.aliyuncs.com/images/logo.png"
          emphasized={true}
        />

        <ModuleCard
          id="g-xyz789"
          name="数据分析专家"
          description="专业的数据分析工具，可以帮助你快速分析和可视化数据，生成专业的报告和图表"
          iconUrl="https://ai-quantum.oss-cn-hangzhou.aliyuncs.com/images/logo.png"
          emphasized={true}
        />
      </ModuleList>
    </Container>
  );
};

export default Workflow;
