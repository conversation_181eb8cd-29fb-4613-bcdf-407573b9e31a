import React, { useState } from 'react';
import { Container, Box, Text, Stack, Group, Avatar, Input, Radio, FileInput, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiAddLargeLine } from 'react-icons/ri';

const Account = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const avatarBgColor = isDark ? theme.colors.dark[7] : 'white';
  const avatarIconColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';
  const avatarTextColor = isDark ? 'rgba(92, 92, 92, 1)' : 'rgba(204, 204, 204, 1)';
  const nicknameTextColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';
  const labelTextColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const sexTextColor = isDark ? 'white' : 'black';

  const [sex, setSex] = useState('');

  return (
    <Container size="520px" pt={72} pb={72}>
      {/* 头像 */}
      <Group align="center" justify="center">
        <Stack
          className="relative rounded-[50%] overflow-hidden cursor-pointer"
          align="center"
          justify="center"
          w={156}
          h={156}
          bg={avatarBgColor}
          gap={14}
        >
          <RiAddLargeLine size={36} color={avatarIconColor} />
          <Text fz={16} fw={400} lh="24px" c={avatarTextColor}>
            设置头像
          </Text>
        </Stack>
        <Box className="relative rounded-[50%] overflow-hidden" w={156} h={156}>
          <Avatar src="avatar.png" alt="it's me" size={156} />
          <Text
            className="absolute bottom-0 flex items-center justify-center w-full bg-black/50 cursor-pointer"
            h={32}
            fz={16}
            fw={500}
            c="white"
          >
            更换
          </Text>
        </Box>

        <FileInput variant="unstyled" />
      </Group>

      {/* 昵称 */}
      <Group align="center" justify="center" mt={10}>
        <Input.Wrapper>
          <Input
            styles={{
              input: {
                textAlign: 'center',
              },
            }}
            variant="unstyled"
            w={156}
            h={30}
            size="20px"
            color={nicknameTextColor}
            placeholder="ID名称"
          />
        </Input.Wrapper>
      </Group>

      {/* 基本信息 */}
      <Stack mt={64}>
        <Text fz={26} fw={700} lh="38px" c={labelTextColor}>
          基本信息
        </Text>
        <Stack mt={40} gap={40}>
          <Group gap={32}>
            <Text className="flex-none" w={64} align="right" fz={20} fw={500} lh="30px" c={labelTextColor}>
              手机号
            </Text>
            <Input
              className="flex-auto"
              placeholder="请输入手机号"
              styles={{
                input: {
                  fontSize: '16px',
                },
              }}
            />
          </Group>
          <Group gap={32}>
            <Text className="flex-none" w={64} align="right" fz={20} fw={500} lh="30px" c={labelTextColor}>
              邮箱
            </Text>
            <Input
              className="flex-auto"
              placeholder="请输入邮箱"
              styles={{
                input: {
                  fontSize: '16px',
                },
              }}
            />
          </Group>
          <Group gap={32}>
            <Text className="flex-none" w={64} align="right" fz={20} fw={500} lh="30px" c={labelTextColor}>
              性别
            </Text>
            <Radio.Group className="flex-auto" value={sex} onChange={setSex}>
              <Group>
                <Radio
                  variant="outline"
                  color="rgba(73, 81, 235, 1)"
                  value="1"
                  label="男"
                  styles={{
                    label: {
                      fontSize: '20px',
                      color: sexTextColor,
                    },
                  }}
                />
                <Radio
                  variant="outline"
                  color="rgba(73, 81, 235, 1)"
                  value="2"
                  label="女"
                  styles={{
                    label: {
                      fontSize: '20px',
                      color: sexTextColor,
                    },
                  }}
                />
              </Group>
            </Radio.Group>
          </Group>
        </Stack>
      </Stack>
    </Container>
  );
};

export default Account;
