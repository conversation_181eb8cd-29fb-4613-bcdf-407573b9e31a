import React, { useState } from 'react';
import {
  Container,
  Box,
  Text,
  Stack,
  Group,
  Avatar,
  useMantineTheme,
  Grid,
  Card,
  Button,
  ActionIcon,
  Modal,
} from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiRobot2Line, RiBookLine, RiPlayLine, RiEditLine, RiDeleteBinLine } from 'react-icons/ri';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { notifications } from '@mantine/notifications';

import { ModuleTitle } from '../components';
import { getOwnedAgents, deleteAgent } from '~/agent/marketplace/api';
import { getOwnedCourses } from '~/course/marketplace/api';
import { Agent, AgentType } from '~/agent/marketplace/schemas';
import { Course } from '~/course/marketplace/schemas';
import { ossUrl } from '~/core/utils';
import { useUserStore } from '../store';
import { getCurrentUser } from '../api';
import { useChatContext } from '~/agent/chat/contexts/ChatContext';
const AssetsList = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const { type } = useParams<{ type: 'agent' | 'course' }>();
  const navigate = useNavigate();
  const isDark = actualColorScheme === 'dark';
  const queryClient = useQueryClient();
  const { userInfo, token, setUserInfo } = useUserStore();
  const [deleteModalOpened, setDeleteModalOpened] = useState(false);
  const [agentToDelete, setAgentToDelete] = useState<Agent | null>(null);
  const { setAgent, setConversation, setMessages } = useChatContext();


  // 如果有token但没有userInfo，则获取用户信息
  useQuery({
    queryKey: ['user-info'],
    queryFn: async () => {
      if (!token) return null;

      try {
        const response = await getCurrentUser(token);
        if (response.data) {
          setUserInfo(response.data);
          return response.data;
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
      return null;
    },
    enabled: !!token && !userInfo,
    retry: 1,
  });

  // 如果没有type参数，默认显示智能体列表
  const currentType = type || 'agent';

  const bgColor = isDark ? theme.colors.dark[7] : 'white';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const cardBgColor = isDark ? theme.colors.dark[6] : 'white';

  // 获取用户拥有的智能体列表
  const { data: agentsResponse } = useQuery({
    queryKey: ['owned-agents'],
    queryFn: getOwnedAgents,
    enabled: currentType === 'agent',
  });

  // 获取用户拥有的课程列表
  const { data: coursesResponse } = useQuery({
    queryKey: ['owned-courses'],
    queryFn: getOwnedCourses,
    enabled: currentType === 'course',
  });

  const agentData = agentsResponse?.data || [];
  const courseData = coursesResponse?.data || [];
  const currentData = currentType === 'agent' ? agentData : courseData;

  const title = currentType === 'agent' ? '我的智能体' : '我的课程';
  const icon = currentType === 'agent' ? RiRobot2Line : RiBookLine;

  // 删除智能体的mutation
  const deleteAgentMutation = useMutation({
    mutationFn: deleteAgent,
    onSuccess: () => {
      notifications.show({
        title: '删除成功',
        message: '智能体已成功删除',
        color: 'green',
      });
      // 刷新智能体列表
      queryClient.invalidateQueries({ queryKey: ['owned-agents'] });
      setDeleteModalOpened(false);
      setAgentToDelete(null);
    },
    onError: (error: Error) => {
      notifications.show({
        title: '删除失败',
        message: error.message || '删除智能体时发生错误',
        color: 'red',
      });
    },
  });

  // 处理智能体点击
  const handleAgentClick = (agent: Agent) => {
      setAgent(agent);
      setConversation(undefined);
      setMessages([]);

    // 根据智能体类型决定跳转路径
    if (agent.type === AgentType.WORKFLOW) {
      // 工作流类型跳转到工作流页面
      navigate(`/workflow/${agent.id}`);
    } else {
      // 其他类型跳转到聊天页面
      navigate(`/chat/${agent.id}`);
    }
  };

  // 处理课程点击
  const handleCourseClick = (course: Course) => {
    navigate(`/course/${course.id}`);
  };

  // 处理编辑智能体
  const handleEditAgent = (agent: Agent) => {
    navigate(`/agent/builder?id=${agent.id}`);
  };

  // 处理删除智能体
  const handleDeleteAgent = (agent: Agent) => {
    setAgentToDelete(agent);
    setDeleteModalOpened(true);
  };

  // 确认删除智能体
  const confirmDeleteAgent = () => {
    if (agentToDelete) {
      deleteAgentMutation.mutate(agentToDelete.id);
    }
  };

  // 检查是否是当前用户创建的智能体
  const isOwner = (agent: Agent) => {
    console.log('权限检查:', {
      userInfo,
      token,
      agentOwnerId: agent.owner_id,
      currentUserId: userInfo?.id,
      isOwner: userInfo && agent.owner_id === userInfo.id,
    });

    return userInfo && agent.owner_id === userInfo.id;
  };

  const renderAgentCard = (agent: Agent) => (
    <Grid.Col span={{ base: 12, sm: 6, md: 4, lg: 3 }} key={agent.id}>
      <Card
        className="rounded-[12px] cursor-pointer transition-all duration-200 hover:shadow-lg"
        bg={cardBgColor}
        p="lg"
        h={280}
        onClick={() => handleAgentClick(agent)}
        style={{ position: 'relative' }}
      >
        {/* 操作按钮 - 只有创建者可见 */}
        {isOwner(agent) && (
          <Group
            gap="xs"
            style={{
              position: 'absolute',
              top: 12,
              right: 12,
              zIndex: 10,
            }}
          >
            <ActionIcon
              variant="filled"
              color="blue"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleEditAgent(agent);
              }}
              style={{
                backgroundColor: 'rgba(73, 81, 235, 0.9)',
                '&:hover': {
                  backgroundColor: 'rgba(73, 81, 235, 1)',
                },
              }}
            >
              <RiEditLine size={14} />
            </ActionIcon>
            <ActionIcon
              variant="filled"
              color="red"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteAgent(agent);
              }}
              style={{
                backgroundColor: 'rgba(239, 68, 68, 0.9)',
                '&:hover': {
                  backgroundColor: 'rgba(239, 68, 68, 1)',
                },
              }}
            >
              <RiDeleteBinLine size={14} />
            </ActionIcon>
          </Group>
        )}

        <Stack gap="md" h="100%" align="center">
          {/* 智能体图标 */}
          <Avatar size={64} src={agent.icon ? ossUrl(agent.icon) : null} bg="rgba(73, 81, 235, 1)" color="white">
            <RiRobot2Line size={32} />
          </Avatar>

          {/* 智能体名称 */}
          <Text fz={18} fw={600} c={textColor} lineClamp={1} ta="center">
            {agent.name}
          </Text>

          {/* 智能体描述 */}
          <Text fz={14} c={isDark ? theme.colors.gray[4] : theme.colors.gray[6]} lineClamp={3} ta="center" style={{ flex: 1 }}>
            {agent.description}
          </Text>

          {/* 立即使用按钮 */}
          <Button
            variant="filled"
            size="sm"
            leftSection={<RiPlayLine size={16} />}
            fullWidth
            onClick={(e) => {
              e.stopPropagation();
              handleAgentClick(agent);
            }}
          >
            立即使用
          </Button>
        </Stack>
      </Card>
    </Grid.Col>
  );

  const renderCourseCard = (course: Course) => (
    <Grid.Col span={{ base: 12, sm: 6, md: 4 }} key={course.id}>
      <Card
        className="rounded-[12px] cursor-pointer transition-all duration-200 hover:shadow-lg"
        bg={cardBgColor}
        p="lg"
        h={320}
        style={{
          border: `1px solid ${isDark ? theme.colors.dark[4] : theme.colors.gray[2]}`,
        }}
        onClick={() => handleCourseClick(course)}
      >
        <Stack gap="md" h="100%">
          {/* 课程封面 */}
          <Box
            className="rounded-[8px] overflow-hidden"
            h={140}
            style={{
              background: course.icon
                ? `url(${course.icon.startsWith('http') ? course.icon : ossUrl(course.icon)}) center/cover`
                : `linear-gradient(135deg, rgba(73, 81, 235, 0.1), rgba(73, 81, 235, 0.05))`,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              position: 'relative',
            }}
          >
            {!course.icon && (
              <Stack align="center" gap="xs">
                <RiBookLine size={32} color="rgba(73, 81, 235, 0.6)" />
                <Text fz={12} c="rgba(73, 81, 235, 0.6)" fw={500}>
                  课程封面
                </Text>
              </Stack>
            )}
          </Box>

          {/* 课程信息 */}
          <Stack gap="xs" style={{ flex: 1 }}>
            <Text fz={16} fw={600} c={textColor} lineClamp={2} lh={1.4}>
              {course.name || '人人都能用好DeepSeek'}
            </Text>

            <Text fz={13} c={isDark ? theme.colors.gray[4] : theme.colors.gray[6]} lineClamp={2} lh={1.4}>
              {course.description || '领跑AI新纪元，小白的智能技术革命与实战代码'}
            </Text>

            {/* 课程元信息 */}
            <Group justify="space-between" mt="xs">
              <Text fz={12} c={isDark ? theme.colors.gray[5] : theme.colors.gray[7]}>
                {course.sections_count || 1} 章节
              </Text>
            </Group>
          </Stack>

          {/* 立即学习按钮 */}
          <Button
            variant="gradient"
            gradient={{ from: 'rgba(86, 104, 255, 1)', to: 'rgba(36, 162, 254, 1)', deg: 90 }}
            size="sm"
            leftSection={<RiPlayLine size={16} />}
            fullWidth
            onClick={(e) => {
              e.stopPropagation();
              handleCourseClick(course);
            }}
            style={{
              borderRadius: '8px',
              fontWeight: 600,
              height: '36px',
            }}
          >
            立即学习
          </Button>
        </Stack>
      </Card>
    </Grid.Col>
  );

  return (
    <Container size="100%" maw="1176px" pt={72} pb={72} px="xl">
      <Group align="center" gap="md" mb={32}>
        <Avatar size={48} bg="rgba(73, 81, 235, 1)" color="white">
          {React.createElement(icon, { size: 24 })}
        </Avatar>
        <ModuleTitle title={title} />
      </Group>

      <Grid gutter="lg">
        {currentData.map((item) => (currentType === 'agent' ? renderAgentCard(item as Agent) : renderCourseCard(item as Course)))}
      </Grid>

      {currentData.length === 0 && (
        <Box className="rounded-[14px] flex items-center justify-center" h={400} bg={bgColor}>
          <Stack align="center" gap="md">
            <Avatar size={64} bg="rgba(73, 81, 235, 0.1)" color="rgba(73, 81, 235, 1)">
              {React.createElement(icon, { size: 32 })}
            </Avatar>
            <Text fz={16} c={isDark ? theme.colors.gray[5] : theme.colors.gray[6]}>
              暂无{currentType === 'agent' ? '智能体' : '课程'}数据
            </Text>
          </Stack>
        </Box>
      )}

      {/* 删除确认模态框 */}
      <Modal opened={deleteModalOpened} onClose={() => setDeleteModalOpened(false)} title="确认删除" centered>
        <Stack gap="md">
          <Text>
            确定要删除智能体 <strong>"{agentToDelete?.name}"</strong> 吗？
          </Text>
          <Text fz="sm" c="dimmed">
            此操作不可撤销，删除后将无法恢复。
          </Text>
          <Group justify="flex-end" gap="sm">
            <Button variant="outline" onClick={() => setDeleteModalOpened(false)} disabled={deleteAgentMutation.isPending}>
              取消
            </Button>
            <Button color="red" onClick={confirmDeleteAgent} loading={deleteAgentMutation.isPending}>
              确认删除
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Container>
  );
};

export default AssetsList;
