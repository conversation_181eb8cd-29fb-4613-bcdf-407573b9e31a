import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';
import { AccountOptionsProps } from './types';

/**
 * 账户选项组件
 *
 * 根据不同场景显示不同的账户操作选项：
 * - 用户名密码登录时显示"忘记密码"
 * - 短信验证码登录时不显示任何选项
 * - 注册和忘记密码页面显示"已有账号，立即登录"
 */
const AccountOptions: React.FC<AccountOptionsProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // 根据设备类型选择合适的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default AccountOptions;
