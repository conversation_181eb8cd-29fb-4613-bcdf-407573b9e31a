import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import { useRegister } from '../../hooks';
import Desktop from './Desktop';
import Mobile from './Mobile';

/**
 * Responsive Register component that loads Desktop or Mobile version
 * based on the device type
 */
const Register: React.FC = () => {
  const { isMobile } = useDeviceDetect();

  // Use the register hook to get shared logic
  const { form, countdown, registerMutation, sendCodeMutation, handleSendCode, handleRegister } = useRegister();

  // Common props for both Desktop and Mobile components
  const registerProps = {
    form,
    countdown,
    registerMutation,
    sendCodeMutation,
    handleSendCode,
    handleRegister,
  };

  // Dynamically choose the appropriate component based on device type
  if (isMobile) {
    return <Mobile {...registerProps} />;
  }

  return <Desktop {...registerProps} />;
};

export default Register;
