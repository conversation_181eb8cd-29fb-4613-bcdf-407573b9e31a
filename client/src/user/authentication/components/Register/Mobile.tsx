import React from 'react';
import { Box, Button, Checkbox, Container, Group, Input, PasswordInput, Stack, Text, Title } from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { UseMutationResult } from '@tanstack/react-query';
import { Link } from 'react-router-dom';
import { RegisterFormValues } from '../../hooks/useRegister';
import AccountOptions from '../AccountOptions';
import { ResponsePayloads } from '~/core/schemas';
import { LoginResult } from '../../schema';

interface MobileRegisterProps {
  form: UseFormReturnType<RegisterFormValues>;
  countdown: number;
  registerMutation: UseMutationResult<ResponsePayloads<LoginResult>, Error, RegisterFormValues>;
  sendCodeMutation: UseMutationResult<ResponsePayloads<string>, Error, string>;
  handleSendCode: () => void;
  handleRegister: (values: RegisterFormValues) => void;
}

/**
 * Mobile Register component for user registration
 */
const MobileRegister: React.FC<MobileRegisterProps> = ({
  form,
  countdown,
  registerMutation,
  sendCodeMutation,
  handleSendCode,
  handleRegister,
}) => {
  return (
    <Container size="xs" py={20}>
      <Box
        style={{
          width: '100%',
          margin: '0 auto',
          padding: '20px 15px',
        }}
      >
        <Title order={2} ta="center" mb={20} fw={500} fz={28}>
          欢迎注册
        </Title>

        {/* 使用新的 AccountOptions 组件 */}
        <AccountOptions scene="register" />

        <form onSubmit={form.onSubmit(handleRegister)}>
          <Stack gap={15}>
            {/* Username input */}
            <Input placeholder="请设置用户名" radius={8} size="md" {...form.getInputProps('username')} />

            {/* Phone input */}
            <Input placeholder="用于登录和找回密码" radius={8} size="md" {...form.getInputProps('phone')} />

            {/* Password input */}
            <PasswordInput placeholder="请设置登录密码" radius={8} size="md" {...form.getInputProps('password')} />

            {/* Verification code input with send button */}
            <Group grow>
              <Input placeholder="验证码" radius={8} size="md" {...form.getInputProps('code')} style={{ flex: 1 }} />
              <Button
                onClick={handleSendCode}
                disabled={countdown > 0 || sendCodeMutation.isPending}
                radius={8}
                size="md"
                variant="outline"
                color="#465CE7"
                style={{ width: '120px' }}
              >
                {countdown > 0 ? `${countdown}秒后重发` : '获取验证码'}
              </Button>
            </Group>

            {/* Agreement checkbox */}
            <Checkbox
              label={
                <Text size="xs" c="dimmed">
                  同意并接受{' '}
                  <Link to="/user-agreement" style={{ color: '#465CE7', textDecoration: 'none' }}>
                    《用户协议》
                  </Link>
                  和
                  <Link to="/privacy-policy" style={{ color: '#465CE7', textDecoration: 'none' }}>
                    《隐私政策》
                  </Link>
                </Text>
              }
              {...form.getInputProps('agreed')}
              radius={2.5}
              size="xs"
              styles={{
                input: {
                  borderColor: '#E5E5E5',
                },
              }}
            />

            {/* Register button */}
            <Button fullWidth radius={8} size="md" type="submit" loading={registerMutation.isPending} bg="#465CE7" mt={10}>
              注 册
            </Button>
          </Stack>
        </form>
      </Box>
    </Container>
  );
};

export default MobileRegister;
