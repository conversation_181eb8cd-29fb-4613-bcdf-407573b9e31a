/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import {
  Button,
  Checkbox,
  Container,
  Input,
  PasswordInput,
  Stack,
  Text,
  UnstyledButton,
  Divider,
  useMantineTheme,
} from '@mantine/core';
import { UseFormReturnType } from '@mantine/form';
import { UseMutationResult } from '@tanstack/react-query';
import { useTheme } from '~/core/features/mantine';
import { RegisterFormValues } from '../../hooks';
import { Protocol } from '~/user/core';
import AccountOptions from '../AccountOptions';
import { ResponsePayloads } from '~/core/schemas';
import { LoginResult } from '../../schema';

interface DesktopRegisterProps {
  form: UseFormReturnType<RegisterFormValues>;
  countdown: number;
  registerMutation: UseMutationResult<ResponsePayloads<LoginResult>, Error, RegisterFormValues>;
  sendCodeMutation: UseMutationResult<ResponsePayloads<string>, Error, string>;
  handleSendCode: () => void;
  handleRegister: (values: RegisterFormValues) => void;
}

/**
 * Desktop Register component for user registration
 */
const DesktopRegister: React.FC<DesktopRegisterProps> = ({
  form,
  countdown,
  registerMutation,
  sendCodeMutation,
  handleSendCode,
  handleRegister,
}) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const containerBgColor = isDark ? theme.colors.dark[8] : 'rgba(243, 245, 255, 1)';
  const titleBgColor = isDark ? 'white' : 'black';
  const dividerColor = isDark ? theme.colors.gray[8] : 'rgba(219, 225, 255, 1)';
  const labelTextColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  const ProtocolLink = <Protocol />;

  return (
    <Container className="flex justify-center" my={100}>
      <Stack align="center" w={664} pt={80} pb={72} bg={containerBgColor} gap={0}>
        <Text fz={36} fw={500} lh="52px" c={titleBgColor}>
          欢迎注册
        </Text>

        <Divider w="100%" mt={40} color={dividerColor} />

        <Stack className="w-full" px={56} align="stretch" justify="center" mt={44} gap={20}>
          <form onSubmit={form.onSubmit(handleRegister)}>
            <Input.Wrapper
              label="用户名"
              styles={{
                label: {
                  fontSize: 16,
                  fontWeight: 400,
                  lineHeight: '18px',
                  color: labelTextColor,
                },
              }}
            >
              <Input
                placeholder="请设置用户名"
                {...form.getInputProps('username')}
                styles={{
                  wrapper: {
                    '--input-fz': '16px',
                    marginTop: 8,
                  },
                  input: {
                    height: 52,
                    paddingLeft: 15,
                    paddingRight: 15,
                    backgroundColor: 'white',
                    borderRadius: 6,
                  },
                }}
              />
            </Input.Wrapper>

            <Input.Wrapper
              label="手机号"
              styles={{
                label: {
                  fontSize: 16,
                  fontWeight: 400,
                  lineHeight: '18px',
                  color: labelTextColor,
                },
              }}
              mt={20}
            >
              <Input
                placeholder="请设置手机号"
                {...form.getInputProps('phone')}
                styles={{
                  wrapper: {
                    '--input-fz': '16px',
                    marginTop: 8,
                  },
                  input: {
                    height: 52,
                    paddingLeft: 15,
                    paddingRight: 15,
                    backgroundColor: 'white',
                    borderRadius: 6,
                  },
                }}
              />
            </Input.Wrapper>

            <Input.Wrapper
              label="密码"
              styles={{
                label: {
                  fontSize: 16,
                  fontWeight: 400,
                  lineHeight: '18px',
                  color: labelTextColor,
                },
              }}
              mt={20}
            >
              <PasswordInput
                placeholder="请设置登录密码"
                {...form.getInputProps('password')}
                styles={{
                  wrapper: {
                    '--input-fz': '16px',
                    marginTop: 8,
                  },
                  input: {
                    height: 52,
                    paddingLeft: 15,
                    paddingRight: 15,
                    backgroundColor: 'white',
                    borderRadius: 6,
                  },
                }}
              />
            </Input.Wrapper>

            <Input.Wrapper
              label="验证码"
              styles={{
                label: {
                  fontSize: 16,
                  fontWeight: 400,
                  lineHeight: '18px',
                  color: labelTextColor,
                },
              }}
              mt={20}
            >
              <Input
                placeholder="短信验证码"
                {...form.getInputProps('code')}
                rightSection={
                  <UnstyledButton onClick={handleSendCode} disabled={countdown > 0 || sendCodeMutation.isPending}>
                    <Text
                      className="cursor-pointer"
                      fz={14}
                      c="rgba(70, 92, 231, 1)"
                      css={css`
                        &:hover {
                          color: rgba(112, 0, 254, 1) !important;
                        }
                      `}
                    >
                      {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                    </Text>
                  </UnstyledButton>
                }
                rightSectionWidth={100}
                rightSectionPointerEvents="auto"
                styles={{
                  wrapper: {
                    '--input-fz': '16px',
                    marginTop: 8,
                  },
                  input: {
                    height: 52,
                    paddingLeft: 15,
                    paddingRight: 15,
                    backgroundColor: 'white',
                    borderRadius: 6,
                  },
                  section: {
                    '--section-end': '15px',
                    justifyContent: 'flex-end',
                  },
                }}
              />
            </Input.Wrapper>

            <Checkbox
              label={ProtocolLink}
              mt={20}
              {...form.getInputProps('agreed')}
              color="rgba(73, 81, 235, 1)"
              styles={{
                root: {
                  '--checkbox-radius': '2px',
                  '--checkbox-size': '14px',
                },
                body: {
                  display: 'flex',
                  alignItems: 'center',
                },
                label: {
                  paddingLeft: '8px',
                },
              }}
            />

            <Button
              variant="filled"
              mt={20}
              color="rgba(70, 92, 231, 1)"
              type="submit"
              loading={registerMutation.isPending}
              styles={{
                root: {
                  width: '100%',
                  '--button-height': '52px',
                  '--button-fz': '16px',
                },
              }}
            >
              注册
            </Button>
          </form>

          {/* 使用新的 AccountOptions 组件 */}
          <AccountOptions scene="register" />
        </Stack>
      </Stack>
    </Container>
  );
};

export default DesktopRegister;
