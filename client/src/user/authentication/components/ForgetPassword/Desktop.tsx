/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import { Button, Checkbox, Container, PasswordInput, Stack, Text, Input, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { useForgetPassword } from '../../hooks';
import AuthTitle from '../AuthTitle';
import { Protocol } from '~/user/core';
import AccountOptions from '../AccountOptions';

const Desktop = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const containerBgColor = isDark ? theme.colors.dark[8] : 'rgba(243, 245, 255, 1)';
  const labelTextColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  const ProtocolLink = <Protocol />;

  // 使用忘记密码Hook
  const { form, countdown, handleSendCode, handleResetPassword, resetPasswordMutation } = useForgetPassword();

  return (
    <Container className="flex justify-center" my={112}>
      <Stack align="center" w={664} pt={80} pb={72} bg={containerBgColor} gap={0}>
        <AuthTitle title="忘记密码" />

        <Stack className="w-full" px={56} align="stretch" justify="center" mt={44} gap={20}>
          <Input.Wrapper
            label="手机号"
            error={form.errors.phone}
            styles={{
              label: {
                fontSize: 16,
                fontWeight: 400,
                lineHeight: '18px',
                color: labelTextColor,
              },
            }}
          >
            <Input
              {...form.getInputProps('phone')}
              radius={8}
              placeholder="请输入手机号"
              styles={{
                wrapper: {
                  '--input-fz': '16px',
                  marginTop: 8,
                },
                input: {
                  height: 52,
                  paddingLeft: 15,
                  paddingRight: 15,
                  backgroundColor: 'white',
                  borderRadius: 6,
                },
              }}
            />
          </Input.Wrapper>

          <Input.Wrapper
            label="验证码"
            error={form.errors.code}
            styles={{
              label: {
                fontSize: 16,
                fontWeight: 400,
                lineHeight: '18px',
                color: labelTextColor,
              },
            }}
          >
            <Input
              {...form.getInputProps('code')}
              placeholder="短信验证码"
              rightSection={
                <Text
                  className={`cursor-pointer ${countdown > 0 ? 'text-gray-400' : ''}`}
                  fz={14}
                  onClick={countdown > 0 ? undefined : handleSendCode}
                  c={countdown > 0 ? 'gray.5' : 'rgba(70, 92, 231, 1)'}
                  css={css`
                    &:hover {
                      color: ${countdown > 0 ? '' : 'rgba(112, 0, 254, 1) !important'};
                    }
                  `}
                >
                  {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
                </Text>
              }
              rightSectionWidth={100}
              rightSectionPointerEvents="auto"
              styles={{
                wrapper: {
                  '--input-fz': '16px',
                  marginTop: 8,
                },
                input: {
                  height: 52,
                  paddingLeft: 15,
                  paddingRight: 15,
                  backgroundColor: 'white',
                  borderRadius: 6,
                },
                section: {
                  '--section-end': '15px',
                  justifyContent: 'flex-end',
                },
              }}
            />
          </Input.Wrapper>

          <Input.Wrapper
            label="设置密码"
            error={form.errors.new_password}
            styles={{
              label: {
                fontSize: 16,
                fontWeight: 400,
                lineHeight: '18px',
                color: labelTextColor,
              },
            }}
          >
            <PasswordInput
              {...form.getInputProps('new_password')}
              radius={8}
              placeholder="请输入新密码"
              styles={{
                wrapper: {
                  '--input-fz': '16px',
                  marginTop: 8,
                },
                input: {
                  height: 52,
                  paddingLeft: 15,
                  paddingRight: 15,
                  backgroundColor: 'white',
                  borderRadius: 6,
                },
              }}
            />
          </Input.Wrapper>
          <Checkbox
            {...form.getInputProps('agreed', { type: 'checkbox' })}
            label={ProtocolLink}
            color="rgba(73, 81, 235, 1)"
            styles={{
              root: {
                '--checkbox-radius': '2px',
                '--checkbox-size': '14px',
              },
              body: {
                display: 'flex',
                alignItems: 'center',
              },
              label: {
                paddingLeft: '8px',
              },
            }}
          />

          <Button
            variant="filled"
            color="rgba(70, 92, 231, 1)"
            onClick={handleResetPassword}
            loading={resetPasswordMutation.isPending}
            styles={{
              root: {
                '--button-height': '52px',
                '--button-fz': '16px',
              },
            }}
          >
            重置密码
          </Button>

          {/* 使用新的 AccountOptions 组件 */}
          <AccountOptions scene="forgetPassword" />
        </Stack>
      </Stack>
    </Container>
  );
};

export default Desktop;
