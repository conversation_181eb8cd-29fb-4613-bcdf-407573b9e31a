import { Button, Checkbox, Input, PasswordInput, Stack } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { Protocol } from '~/user/core';
import { useLogin } from '../../hooks';

const Desktop = () => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';
  const { passwordForm, handleLogin, isLoading } = useLogin();

  const labelTextColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  return (
    <form onSubmit={passwordForm.onSubmit(handleLogin)}>
      <Stack gap="md">
        <Input.Wrapper
          label="用户名"
          styles={{
            label: {
              fontSize: 16,
              fontWeight: 400,
              lineHeight: '18px',
              color: labelTextColor,
            },
          }}
        >
          <Input
            radius={8}
            placeholder="请输入用户名"
            key={passwordForm.key('username')}
            {...passwordForm.getInputProps('username')}
            styles={{
              wrapper: {
                '--input-fz': '16px',
                marginTop: 8,
              },
              input: {
                height: 52,
                paddingLeft: 15,
                paddingRight: 15,
                backgroundColor: 'white',
                borderRadius: 6,
              },
            }}
          />
        </Input.Wrapper>

        <Input.Wrapper
          label="密码"
          styles={{
            label: {
              fontSize: 16,
              fontWeight: 400,
              lineHeight: '18px',
              color: labelTextColor,
            },
          }}
        >
          <PasswordInput
            radius={8}
            placeholder="请输入密码"
            key={passwordForm.key('password')}
            {...passwordForm.getInputProps('password')}
            styles={{
              wrapper: {
                '--input-fz': '16px',
                marginTop: 8,
              },
              input: {
                height: 52,
                paddingLeft: 15,
                paddingRight: 15,
                backgroundColor: 'white',
                borderRadius: 6,
              },
            }}
          />
        </Input.Wrapper>
        <Checkbox
          label={<Protocol />}
          mt={20}
          {...passwordForm.getInputProps('agreed')}
          color="rgba(73, 81, 235, 1)"
          styles={{
            root: {
              '--checkbox-radius': '2px',
              '--checkbox-size': '14px',
            },
            body: {
              display: 'flex',
              alignItems: 'center',
            },
            label: {
              paddingLeft: '8px',
            },
          }}
        />

        <Button
          variant="filled"
          mt={20}
          color="rgba(70, 92, 231, 1)"
          type="submit"
          loading={isLoading}
          styles={{
            root: {
              '--button-height': '52px',
              '--button-fz': '16px',
            },
          }}
        >
          登录
        </Button>
      </Stack>
    </form>
  );
};

export default Desktop;
