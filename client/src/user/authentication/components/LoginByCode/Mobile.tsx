import { Button, Checkbox, Input, Stack, Text } from '@mantine/core';
import { useMutation } from '@tanstack/react-query';
import { sendVerificationCode } from '~/user/authentication/api';
import { notifications } from '@mantine/notifications';
import { useLogin } from '../../hooks';
import { Protocol } from '~/user/core';

const Mobile = () => {
  const { smsForm, countdown, setCountdown, handleLogin, isLoading } = useLogin();

  // Send verification code mutation
  const sendCodeMutation = useMutation({
    mutationFn: sendVerificationCode,
    onSuccess: (response) => {
      if (response.error) {
        notifications.show({
          title: '发送失败',
          message: response.error.message,
          color: 'red',
        });
      } else {
        notifications.show({
          title: '发送成功',
          message: '验证码已发送，请注意查收',
          color: 'green',
        });
        setCountdown(60);
        const timer = setInterval(() => {
          setCountdown((prev) => (prev > 0 ? prev - 1 : 0));
        }, 1000);
        setTimeout(() => clearInterval(timer), 60000);
      }
    },
    onError: (error: Error) => {
      notifications.show({
        title: '发送失败',
        message: error.message,
        color: 'red',
      });
    },
  });

  const sendSmsCode = () => {
    if (countdown > 0) return;
    const phoneError = smsForm.validateField('phone').hasError;
    if (phoneError || !smsForm.values.phone) {
      notifications.show({
        title: '发送失败',
        message: '请输入正确的手机号',
        color: 'red',
      });
      return;
    }
    sendCodeMutation.mutate(smsForm.values.phone);
  };

  return (
    <form onSubmit={smsForm.onSubmit(handleLogin)}>
      <Stack gap="md">
        <Input
          placeholder="请输入手机号"
          key={smsForm.key('phone')}
          {...smsForm.getInputProps('phone')}
          rightSection={
            smsForm.values.phone !== '' ? <Input.ClearButton onClick={() => smsForm.setFieldValue('phone', '')} /> : undefined
          }
          rightSectionPointerEvents="auto"
        />

        <Input
          placeholder="短信验证码"
          key={smsForm.key('code')}
          {...smsForm.getInputProps('code')}
          rightSection={
            <Text className={`cursor-pointer ${countdown > 0 ? 'text-gray-400' : ''}`} fz={14} onClick={sendSmsCode}>
              {countdown > 0 ? `${countdown}秒后重试` : '发送验证码'}
            </Text>
          }
          rightSectionWidth={100}
          rightSectionPointerEvents="auto"
        />

        <Checkbox label={<Protocol />} mt={10} {...smsForm.getInputProps('agreed')} size="xs" />

        <Button variant="filled" color="blue" type="submit" loading={isLoading} fullWidth mt="md">
          登录
        </Button>
      </Stack>
    </form>
  );
};

export default Mobile;
