import { API_BASE_URL } from '~/core/constants';
import { ResponsePayloads } from '~/core/schemas';
import { LoginRequest, LoginResult, RegisterRequest, ResetPasswordRequest, SmsLoginRequest } from './schema';

/**
 * 用户登录
 * @param data 登录请求参数
 * @returns Promise with login result
 */
export const login = async (data: LoginRequest): Promise<ResponsePayloads<LoginResult>> => {
  const response = await fetch(`${API_BASE_URL}/users/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error?.message || '登录失败');
  }

  return response.json();
};

/**
 * 短信登录
 * @param data 短信登录请求参数
 * @returns Promise with login result
 */
export const smsLogin = async (data: SmsLoginRequest): Promise<ResponsePayloads<LoginResult>> => {
  const response = await fetch(`${API_BASE_URL}/users/login/sms`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error?.message || '登录失败');
  }

  return response.json();
};

/**
 * 重置密码
 * @param data 重置密码请求参数
 * @returns Promise with reset result
 */
export const resetPassword = async (data: ResetPasswordRequest): Promise<ResponsePayloads<string>> => {
  const response = await fetch(`${API_BASE_URL}/users/reset-password`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error?.message || '重置密码失败');
  }

  return response.json();
};

/**
 * Register a new user
 * @param data Registration data
 * @returns Promise with registration result
 */
export const register = async (data: RegisterRequest): Promise<ResponsePayloads<LoginResult>> => {
  const response = await fetch(`${API_BASE_URL}/users/register`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      Accept: 'application/json',
    },
    body: JSON.stringify(data),
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error?.message || '注册失败');
  }

  return response.json();
};

/**
 * Send verification code to phone number
 * @param phone Phone number
 * @returns Promise with send result
 */
export const sendVerificationCode = async (phone: string): Promise<ResponsePayloads<string>> => {
  const response = await fetch(`${API_BASE_URL}/users/send_verification_code?phone=${phone}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
    },
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.error?.message || '发送验证码失败');
  }

  return response.json();
};
