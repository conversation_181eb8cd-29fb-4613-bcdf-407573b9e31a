import { z } from 'zod';
import { UserInfoSchema } from '../core/schemas';
const PASSWORD_REGEX = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}$/;

/**
 * 登录结果模式
 */
export const LoginResultSchema = z.object({
  token: z.string().describe('登录令牌'),
  user_info: UserInfoSchema.describe('用户信息'),
});

/**
 * 登录请求模式
 */
export const LoginRequestSchema = z.object({
  username: z.string().min(3, '用户名至少3个字符').describe('用户名'),
  password: z.string().min(6, '最小长度为6').describe('密码'),
  agreed: z
    .boolean()
    .refine((val) => val === true, {
      message: '请先阅读并同意用户协议和隐私政策',
    })
    .describe('是否同意用户协议'),
});

/**
 * 短信登录请求模式
 */
export const SmsLoginRequestSchema = z.object({
  phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, '请输入正确的手机号')
    .describe('手机号'),
  code: z.string().length(6, '请输入6位验证码').describe('验证码'),
  agreed: z
    .boolean()
    .refine((val) => val === true, {
      message: '请先阅读并同意用户协议和隐私政策',
    })
    .describe('是否同意用户协议'),
});

/**
 * 重置密码请求模式
 */
export const ResetPasswordRequestSchema = z.object({
  phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, '请输入正确的手机号')
    .describe('手机号'),
  code: z.string().length(6, '请输入6位验证码').describe('验证码'),
  new_password: z.string().min(6, '最小长度为6').regex(PASSWORD_REGEX, '必须包含大小写字母和数字').describe('新密码'),
  agreed: z
    .boolean()
    .refine((val) => val === true, {
      message: '请先阅读并同意用户协议和隐私政策',
    })
    .describe('是否同意用户协议'),
});

/**
 * 注册请求模式
 */
export const RegisterRequestSchema = z.object({
  username: z.string().min(3, '用户名至少需要3个字符').max(20, '用户名最多20个字符').describe('用户名'),
  phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, '请输入有效的手机号码')
    .describe('手机号'),
  password: z.string().min(6, '最小长度为6').regex(PASSWORD_REGEX, '必须包含大小写字母和数字').describe('密码'),
  code: z.string().length(6, '请输入6位验证码').describe('验证码'),
  agreed: z
    .boolean()
    .refine((val) => val === true, {
      message: '请先阅读并同意用户协议和隐私政策',
    })
    .describe('是否同意用户协议'),
});
export type LoginResult = z.infer<typeof LoginResultSchema>;
export type LoginRequest = z.infer<typeof LoginRequestSchema>;
export type SmsLoginRequest = z.infer<typeof SmsLoginRequestSchema>;
export type ResetPasswordRequest = z.infer<typeof ResetPasswordRequestSchema>;
export type RegisterRequest = z.infer<typeof RegisterRequestSchema>;
