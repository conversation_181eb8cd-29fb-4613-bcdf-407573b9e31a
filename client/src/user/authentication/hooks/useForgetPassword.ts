import { useState } from 'react';
import { useForm, zodResolver } from '@mantine/form';
import { notifications } from '@mantine/notifications';
import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { ResetPasswordRequestSchema } from '../schema';
import { resetPassword, sendVerificationCode } from '../api';

// 忘记密码表单值类型
export type ForgetPasswordFormValues = {
  phone: string;
  code: string;
  new_password: string;
  agreed: boolean;
};

/**
 * 忘记密码Hook
 * 用于处理忘记密码相关的逻辑
 */
export const useForgetPassword = () => {
  const navigate = useNavigate();
  const [countdown, setCountdown] = useState(0);

  // 表单设置
  const form = useForm<ForgetPasswordFormValues>({
    initialValues: {
      phone: '',
      code: '',
      new_password: '',
      agreed: false,
    },
    validate: zodResolver(ResetPasswordRequestSchema),
  });

  // 重置密码mutation
  const resetPasswordMutation = useMutation({
    mutationFn: (values: ForgetPasswordFormValues) => {
      return resetPassword(values);
    },
    onSuccess: () => {
      notifications.show({
        title: '密码重置成功',
        message: '即将跳转到登录页面',
        color: 'green',
      });
      navigate('/login');
    },
    onError: (error: Error) => {
      notifications.show({
        title: '重置失败',
        message: error.message,
        color: 'red',
      });
    },
  });

  // 发送验证码mutation
  const sendCodeMutation = useMutation({
    mutationFn: (phone: string) => sendVerificationCode(phone),
    onSuccess: () => {
      notifications.show({
        title: '发送成功',
        message: '验证码已发送到您的手机',
        color: 'green',
      });

      // 开始倒计时
      setCountdown(60);
      const timer = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    },
    onError: (error: Error) => {
      notifications.show({
        title: '发送失败',
        message: error.message,
        color: 'red',
      });
    },
  });

  // 处理发送验证码
  const handleSendCode = () => {
    const { phone } = form.values;
    const phoneError = form.validateField('phone').hasError;

    if (phoneError || !phone) {
      return;
    }

    sendCodeMutation.mutate(phone);
  };

  // 处理重置密码表单提交
  const handleResetPassword = () => {
    form.validate();
    if (form.isValid()) {
      resetPasswordMutation.mutate(form.values);
    }
  };

  return {
    form,
    countdown,
    resetPasswordMutation,
    sendCodeMutation,
    handleSendCode,
    handleResetPassword,
  };
};

export default useForgetPassword;
