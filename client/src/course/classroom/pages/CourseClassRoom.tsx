import React, { useState, useEffect, useRef } from 'react';
import { Container, Space, Loader, Center, Alert } from '@mantine/core';
import { useParams } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { RiErrorWarningLine } from 'react-icons/ri';
import { SectionList, CommentInput } from '../../marketplace/components';
import { Comment, CourseIntro, CourseInfo, CourseView, FilterTabs } from '../../classroom/components';
import { getCourseDetail } from '../../marketplace/api';
import { CourseItem } from '../../marketplace/components/SectionList/types';
import { type CourseSection } from '../../marketplace/schemas';
import { useCourseProgressStore } from '../store';

const CourseClassRoom: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [selectedTab, setSelectedTab] = useState({ label: '课程介绍', type: 'intro' });
  const [currentSection, setCurrentSection] = useState<CourseSection | null>(null);
  const videoPlayerRef = useRef<HTMLDivElement>(null);
  const lastSavedTimeRef = useRef<number>(0);

  // 使用课程进度store
  const { updateProgress, getCourseProgress, updateLastSection, completeSection } = useCourseProgressStore();

  // 获取课程详情
  const {
    data: courseDetailData,
    isLoading: isLoadingCourseDetail,
    error: courseDetailError,
  } = useQuery({
    queryKey: ['courseDetail', id],
    queryFn: () => getCourseDetail(id!),
    enabled: !!id,
  });

  const courseDetail = courseDetailData?.data;

  // 获取推荐课程
  // const { data: recommendCoursesData, isLoading: isLoadingRecommend } = useQuery({
  //   queryKey: ['recommendCourses', id],
  //   queryFn: () =>
  //     searchCourses({
  //       excludeIds: id ? [id] : [],
  //       page: 1,
  //       page_size: 8,
  //     }),
  //   enabled: !!id,
  // });

  // const recommendCourses = recommendCoursesData?.data?.data || [];

  // 加入购物车
  const onCartClick = () => {
    console.log('onCartClick', id);
  };

  // 立即购买
  const onBuyClick = () => {
    console.log('onBuyClick', id);
  };

  const tabs = [
    {
      label: '课程介绍',
      type: 'intro',
    },
    {
      label: '课程列表',
      type: 'list',
    },
    // {
    //   label: '评论',
    //   type: 'comment',
    // },
  ];

  // 处理视频播放进度更新
  const handleTimeUpdate = (currentTime: number) => {
    if (id && currentSection?.id) {
      const now = Date.now();
      // 每5秒保存一次进度，避免频繁更新
      if (now - lastSavedTimeRef.current > 5000) {
        updateProgress(id, {
          sectionProgress: {
            [currentSection.id.toString()]: currentTime,
          },
        });
        lastSavedTimeRef.current = now;
      }
    }
  };

  // 处理视频播放结束
  const handleVideoEnded = () => {
    if (id && currentSection?.id) {
      // 标记章节为已完成
      completeSection(id, currentSection.id.toString());
    }
  };

  // 滚动视频播放器到屏幕中间
  const scrollVideoToCenter = () => {
    if (videoPlayerRef.current) {
      videoPlayerRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center',
      });
    }
  };

  // 处理课程章节点击
  const onCourseSectionClick = (item: CourseItem) => {
    console.log('onCourseSectionClick', item);
    if (courseDetail?.sections) {
      const section = courseDetail.sections.find((s) => s.id.toString() === item.id);
      if (section) {
        setCurrentSection(section);

        // 更新最后学习的章节
        if (id) {
          updateLastSection(id, section.id.toString());
        }

        // 滚动到视频播放器位置
        setTimeout(() => {
          scrollVideoToCenter();
        }, 100);
      }
    }
  };

  // 初始化选择章节
  useEffect(() => {
    if (courseDetail?.sections && courseDetail.sections.length > 0) {
      if (id) {
        // 获取课程进度
        const progress = getCourseProgress(id);

        // 如果有最后学习的章节，则选择该章节
        if (progress?.lastSectionId) {
          const lastSection = courseDetail.sections.find((s) => s.id.toString() === progress.lastSectionId);
          if (lastSection) {
            setCurrentSection(lastSection);
            return;
          }
        }
      }

      // 默认选择第一个章节
      if (!currentSection) {
        setCurrentSection(courseDetail.sections[0]);
      }
    }
  }, [courseDetail, currentSection, id, getCourseProgress]);

  // 当章节变化时，滚动到视频播放器
  useEffect(() => {
    if (currentSection) {
      // 延迟一点时间，确保视频加载完成
      setTimeout(() => {
        scrollVideoToCenter();
      }, 300);
    }
  }, [currentSection]);

  // 获取当前课程的学习进度
  const courseProgress = id ? getCourseProgress(id) : undefined;

  // 转换课程章节为列表项
  const courseSections =
    courseDetail?.sections?.map((section) => {
      // 获取章节的学习进度
      const sectionId = section.id.toString();
      const progress = courseProgress?.sectionProgress?.[sectionId];

      // 计算进度百分比
      let progressPercent = 0;
      if (progress !== undefined && section.duration) {
        if (progress === -1) {
          // -1表示已完成
          progressPercent = 100;
        } else {
          progressPercent = Math.min(Math.round((progress / section.duration) * 100), 100);
        }
      }

      return {
        id: sectionId,
        title: section.title,
        duration: section.duration ? `${Math.floor(section.duration / 60)}分${section.duration % 60}秒` : '未知时长',
        is_free: section.is_free,
        progress: progressPercent,
        isActive: currentSection?.id === section.id,
      };
    }) || [];

  // 转换推荐课程为列表项
  // const recommendList = recommendCourses.map((course) => ({
  //   id: parseInt(course.id),
  //   linkUrl: `/course/${course.id}`,
  //   title: course.name,
  //   image: course.icon || '',
  //   price: 0, // 价格信息在列表中可能不可用
  //   star: 0, // 评分信息在列表中可能不可用
  // }));

  // 模拟评论数据 (后续可以替换为真实API)
  const commentList = [
    {
      id: '1',
      avatar: 'https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4',
      name: '张三',
      content: '课程内容非常丰富，讲解清晰，很有收获！',
      time: '2023-05-01',
      star: 5,
    },
    {
      id: '2',
      avatar: 'https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4',
      name: '李四',
      content: '老师讲解很专业，但是有些内容可以更详细一些。',
      time: '2023-04-15',
      star: 4,
    },
    {
      id: '3',
      avatar: 'https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4',
      name: '王五',
      content: '课程质量很高，希望能有更多这样的课程。',
      time: '2023-03-20',
      star: 5,
    },
  ];

  if (isLoadingCourseDetail) {
    return (
      <Center style={{ height: '100vh' }}>
        <Loader size="lg" />
      </Center>
    );
  }

  if (courseDetailError) {
    return (
      <Container size="1024px" pt={112}>
        <Alert icon={<RiErrorWarningLine size={16} />} title="加载失败" color="red">
          无法加载课程信息，请稍后再试
        </Alert>
      </Container>
    );
  }

  if (!courseDetail) {
    return (
      <Container size="1024px" pt={112}>
        <Alert icon={<RiErrorWarningLine size={16} />} title="课程不存在" color="yellow">
          未找到该课程信息
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="1024px" pt={112}>
      <CourseInfo courseDetail={courseDetail} onCartClick={onCartClick} onBuyClick={onBuyClick} />

      <Space h={72} />

      <CourseView
        section={currentSection}
        courseTitle={courseDetail.name}
        description={courseDetail.description}
        videoRef={videoPlayerRef}
        onTimeUpdate={handleTimeUpdate}
        onEnded={handleVideoEnded}
      />

      <Space h={72} />

      <FilterTabs tabs={tabs} selected={selectedTab} onChange={setSelectedTab} />

      <Space h={28} />

      {selectedTab.type === 'intro' && <CourseIntro data={courseDetail.description} />}
      {selectedTab.type === 'list' && <SectionList list={courseSections} onClick={onCourseSectionClick} />}
      {selectedTab.type === 'comment' && (
        <>
          <Comment list={commentList} />
          <CommentInput />
        </>
      )}

      <Space h={56} />

      {/* {recommendList.length > 0 && (
        <>
          <Text className="leading-[30px] fw-500 text-center" fz={20} mb={24}>
            推荐好课
          </Text>
          {isLoadingRecommend ? (
            <Center>
              <Loader size="sm" />
            </Center>
          ) : (
            <Recommend list={recommendList} />
          )}
        </>
      )} */}
    </Container>
  );
};

export default CourseClassRoom;
CourseClassRoom.displayName = 'CourseClassRoom';
