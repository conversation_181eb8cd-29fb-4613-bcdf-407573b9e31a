import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';

/**
 * 课程学习进度接口
 */
export interface CourseProgress {
  /** 课程ID */
  courseId: string;
  /** 各章节播放进度（秒） */
  sectionProgress: Record<string, number>;
  /** 最后学习的章节ID */
  lastSectionId?: string;
}

/**
 * 课程进度状态接口
 */
interface CourseProgressState {
  /** 所有课程的学习进度 */
  progresses: CourseProgress[];

  /**
   * 更新或创建课程学习进度
   * @param courseId 课程ID
   * @param progress 进度信息
   */
  updateProgress: (courseId: string, progress: Partial<CourseProgress>) => void;

  /**
   * 标记章节完成
   * @param courseId 课程ID
   * @param sectionId 章节ID
   */
  completeSection: (courseId: string, sectionId: string) => void;

  /**
   * 获取指定课程的学习进度
   * @param courseId 课程ID
   */
  getCourseProgress: (courseId: string) => CourseProgress | undefined;

  /**
   * 更新最后学习的章节
   * @param courseId 课程ID
   * @param sectionId 章节ID
   */
  updateLastSection: (courseId: string, sectionId: string) => void;

  /**
   * 重置课程进度
   * @param courseId 课程ID
   * @param sectionId 可选，指定章节ID则只重置该章节
   */
  resetProgress: (courseId: string, sectionId?: string) => void;
}

/**
 * 课程进度状态管理
 */
export const useCourseProgressStore = create<CourseProgressState>()(
  devtools(
    persist(
      (set, get) => ({
        progresses: [],

        updateProgress: (courseId, progress) =>
          set(
            (state) => {
              const existing = state.progresses.find((p) => p.courseId === courseId);
              if (existing) {
                return {
                  progresses: state.progresses.map((p) =>
                    p.courseId === courseId
                      ? {
                          ...p,
                          sectionProgress: {
                            ...p.sectionProgress,
                            ...progress.sectionProgress,
                          },
                          // 如果提供了lastSectionId，则更新
                          ...(progress.lastSectionId ? { lastSectionId: progress.lastSectionId } : {}),
                        }
                      : p,
                  ),
                };
              }
              return {
                progresses: [
                  ...state.progresses,
                  {
                    courseId,
                    sectionProgress: progress.sectionProgress || {},
                    lastSectionId: progress.lastSectionId,
                  },
                ],
              };
            },
            false,
            'updateProgress',
          ),

        completeSection: (courseId, sectionId) =>
          set(
            (state) => ({
              progresses: state.progresses.map((p) => {
                if (p.courseId === courseId) {
                  const newProgress = { ...p.sectionProgress };
                  // 将章节进度设置为-1表示已完成
                  newProgress[sectionId] = -1;
                  return {
                    ...p,
                    sectionProgress: newProgress,
                  };
                }
                return p;
              }),
            }),
            false,
            'completeSection',
          ),

        updateLastSection: (courseId, sectionId) =>
          set(
            (state) => ({
              progresses: state.progresses.map((p) => (p.courseId === courseId ? { ...p, lastSectionId: sectionId } : p)),
            }),
            false,
            'updateLastSection',
          ),

        getCourseProgress: (courseId) => get().progresses.find((p) => p.courseId === courseId),

        resetProgress: (courseId, sectionId) =>
          set(
            (state) => ({
              progresses: state.progresses.map((p) => {
                if (p.courseId === courseId) {
                  if (sectionId) {
                    const newProgress = { ...p.sectionProgress };
                    delete newProgress[sectionId];
                    return {
                      ...p,
                      sectionProgress: newProgress,
                    };
                  }
                  return {
                    courseId,
                    sectionProgress: {},
                  };
                }
                return p;
              }),
            }),
            false,
            'resetProgress',
          ),
      }),
      {
        name: 'course-progress-storage-v2',
        storage: createJSONStorage(() => localStorage),
      },
    ),
    {
      name: 'course-progress-store-v2',
    },
  ),
);

/**
 * 获取课程进度状态
 */
export const getCourseProgressStore = () => useCourseProgressStore.getState();
