/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { Group, Box, UnstyledButton, Divider } from '@mantine/core';
import { useTheme } from '~/core/features/mantine/theme-context';

import { FilterTabsProps } from './types';

/**
 * Desktop version of the FilterTabs component
 */
const Desktop: React.FC<FilterTabsProps> = ({ tabs, selected, onChange }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const defaultTextColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';
  const activeTextColor = isDark ? 'white' : 'rgba(56, 56, 56, 1)';
  const activeLineColor = 'rgba(255, 235, 59, 1)';

  const controls = tabs.map((tab) => (
    <UnstyledButton
      className="relative leading-[30px] fw-500"
      key={tab.type}
      onClick={() => onChange(tab)}
      mod={{ active: tab.type === selected.type }}
      mx={16}
      fz={20}
      style={{
        height: '100%',
        color: defaultTextColor,
        transition: 'color 100ms ease',
      }}
      css={css`
        &[data-active] {
          color: ${activeTextColor} !important;
        }
        &[data-active]:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          width: calc(100% - 8px);
          height: 3px;
          border-radius: 8px;
          background: ${activeLineColor};
          transform: translateX(-50%);
        }
      `}
    >
      <span className="relative z-1">{tab.label}</span>
    </UnstyledButton>
  ));

  return (
    <Box className="relative sticky w-full z-[2]">
      <Group className="relative z-[2]" align="center" justify="center" style={{ height: '42px', whiteSpace: 'nowrap' }}>
        {controls}
      </Group>

      <Divider className="absolute bottom-[1px] w-full z-[1]" />
    </Box>
  );
};

export default Desktop;
