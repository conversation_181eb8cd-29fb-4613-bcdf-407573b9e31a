/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { Group, Image, Rating, UnstyledButton, Text } from '@mantine/core';
import { Link } from 'react-router-dom';
import { useTheme } from '~/core/features/mantine';
import { Carousel } from '@mantine/carousel';

import { RecommendProps, RecommendItem } from './types';

const Desktop: React.FC<RecommendProps> = ({ list }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const titleTextColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const itemBackgroundColor = isDark ? 'black' : 'white';

  // 将列表分成每组4个
  const groupedList = [];
  for (let i = 0; i < list.length; i += 4) {
    groupedList.push(list.slice(i, i + 4));
  }

  return (
    <Carousel
      slideSize="100%"
      align="start"
      emblaOptions={{ slidesToScroll: 1, loop: true }}
      withControls={false}
      withIndicators
      styles={{
        indicators: {
          bottom: '15px',
        },
        indicator: {
          width: '20px',
          height: '5px',
          borderRadius: '5px',
          background: 'rgba(204, 204, 204, 1)',
          border: 'none',
          opacity: 1,
        },
      }}
      css={css`
        & .mantine-Carousel-indicator[data-active] {
          width: 50px !important;
          background: rgba(73, 81, 235, 1) !important;
        }
      `}
    >
      {groupedList.map((group, index) => (
        <Carousel.Slide key={index}>
          <Group gap={10}>
            {group.map((item: RecommendItem, index) => (
              <UnstyledButton
                key={index}
                className="flex flex-col rounded-[6px] hover:cursor-pointer"
                component={Link}
                to={item.linkUrl}
                w={240}
                h={390}
                px={15}
                pt={8}
                pb={20}
                mb={50}
                bg={itemBackgroundColor}
              >
                <Text fz={16} fw={700} lh="24px" c={titleTextColor}>
                  课程
                </Text>
                <Image className="rounded-[8px]" src={item.image} w={210} h={212} mt={5} />
                <Text fz={24} fw={700} lh="35px" c="rgba(73, 81, 235, 1)">
                  ￥{item.price}
                </Text>
                <Text className="line-clamp-2" fz={16} fw={700} lh="24px" c={titleTextColor} mb={8}>
                  {item.title}
                </Text>
                <Rating value={item.star} size={16} readOnly color="rgba(73, 81, 235, 1)" />
              </UnstyledButton>
            ))}
          </Group>
        </Carousel.Slide>
      ))}
    </Carousel>
  );
};

export default Desktop;
