import React from 'react';
import { Box, Text, Paper } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { CourseIntroProps } from './types';

const Desktop: React.FC<CourseIntroProps> = ({ data }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const backgroundColor = isDark ? 'rgba(25, 25, 25, 1)' : 'rgba(255, 255, 255, 1)';

  // 如果没有数据，显示默认内容
  const introContent = data || '暂无课程介绍内容';

  // 将文本内容按段落分割并渲染
  const paragraphs = introContent.split('\n').filter((p) => p.trim() !== '');

  return (
    <Paper p={24} radius="md" bg={backgroundColor} style={{ minHeight: '300px' }}>
      <Box>
        {paragraphs.length > 0 ? (
          paragraphs.map((paragraph, index) => (
            <Text key={index} fz={16} lh="28px" c={textColor} mb={16}>
              {paragraph}
            </Text>
          ))
        ) : (
          <Text fz={16} lh="28px" c={textColor}>
            {introContent}
          </Text>
        )}
      </Box>
    </Paper>
  );
};

export default Desktop;
