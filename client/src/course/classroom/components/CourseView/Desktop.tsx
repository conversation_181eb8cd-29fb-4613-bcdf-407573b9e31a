import { Box, Stack, Text, Alert } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiErrorWarningLine } from 'react-icons/ri';
import { MediaPlayer, MediaProvider } from '@vidstack/react';
import { defaultLayoutIcons, DefaultVideoLayout } from '@vidstack/react/player/layouts/default';
import { CourseViewProps } from './types';

const Desktop: React.FC<CourseViewProps> = ({ section, courseTitle, description, videoRef, onTimeUpdate, onEnded }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  // 默认视频URL，当没有选择章节或章节没有视频时使用
  const defaultVideoUrl = 'https://files.vidstack.io/sprite-fight/720p.mp4';
  const videoUrl = section?.video_url || defaultVideoUrl;
  const videoTitle = section?.title || courseTitle || '课程视频';

  return (
    <Stack gap={40}>
      <Box
        ref={videoRef}
        style={{
          position: 'relative',
          overflow: 'hidden',
          maxWidth: '100%',
          aspectRatio: '16/9',
        }}
      >
        {!section?.video_url && section && (
          <Alert
            icon={<RiErrorWarningLine size={16} />}
            title="视频未就绪"
            color="yellow"
            style={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              zIndex: 10,
            }}
          >
            该章节视频尚未上传或正在处理中
          </Alert>
        )}

        <MediaPlayer
          title={videoTitle}
          aspectRatio="16/9"
          src={videoUrl}
          style={{
            width: '100%',
            height: '100%',
          }}
          onTimeUpdate={({ currentTime }) => {
            onTimeUpdate?.(currentTime);
          }}
          onEnded={() => {
            onEnded?.();
          }}
          autoPlay
        >
          <MediaProvider />
          <DefaultVideoLayout icons={defaultLayoutIcons} />
        </MediaPlayer>
      </Box>

      <Text fz={18} fw={500} lh="27px" c={textColor}>
        {description ||
          '课程简介：本课程将介绍一些基本的Adobe Photoshop操作并重点结合前端的需求做展开。结合实际例子教会大家从PSD入手到获取所需资源的实际实现方式。'}
      </Text>
    </Stack>
  );
};

export default Desktop;
