import React from 'react';
import { ScrollArea, Group, Avatar, Stack, Text, Rating } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';

import { CommentProps, CommentItem } from './types';

const Desktop: React.FC<CommentProps> = ({ list }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const backgroundColor = isDark ? 'black' : 'white';
  const borderColor = isDark ? 'rgba(51, 51, 51, 1)' : 'rgba(204, 204, 204, 1)';
  const nameTextColor = isDark ? 'white' : 'black';
  const timeTextColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';
  const contentTextColor = 'rgba(166, 166, 166, 1)';

  return (
    <ScrollArea type="always" pr={36} style={{ height: '428px', whiteSpace: 'nowrap' }}>
      <Stack gap={12}>
        {list.map((comment: CommentItem) => (
          <Group align="flex-start" justify="space-between" py={12} pl={20} pr={30} gap={48}>
            <Avatar className="flex-none" src={comment.avatar} alt="it's me" size={104} />
            <Stack
              className="flex-auto rounded-[4px] cursor-pointer"
              mih={104}
              px={24}
              py={12}
              gap={8}
              style={{
                backgroundColor: backgroundColor,
                border: `1px solid ${borderColor}`,
              }}
            >
              <Group align="center" justify="space-between">
                <Group gap={16}>
                  <Text className="leading-[24px] fw-500" fz={18} c={nameTextColor}>
                    {comment.name}
                  </Text>
                  <Text className="leading-[24px] fw-500" fz={12} c={timeTextColor}>
                    {comment.time}
                  </Text>
                </Group>
                <Rating value={comment.star} size={16} readOnly color="rgba(73, 81, 235, 1)" />
              </Group>
              <Text className="leading-[24px] fw-500" fz={14} c={contentTextColor}>
                {comment.content}
              </Text>
            </Stack>
          </Group>
        ))}
      </Stack>
    </ScrollArea>
  );
};

export default Desktop;
