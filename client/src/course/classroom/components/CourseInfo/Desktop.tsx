import React from 'react';
import { Group, Image, Stack, Text, Badge } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { CourseInfoProps } from './types';

const Desktop: React.FC<CourseInfoProps> = ({ courseDetail }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const titleTextColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const descTextColor = 'rgba(166, 166, 166, 1)';

  // 默认图片
  const defaultImage = 'https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668';

  // 格式化日期
  // const formatDate = (timestamp?: number) => {
  //   if (!timestamp) return '';
  //   const date = new Date(timestamp);
  //   return date.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
  // };

  // 计算日期范围
  // const startDate = formatDate(courseDetail?.created_at);
  // const endDate = courseDetail?.updated_at ? formatDate(courseDetail.updated_at) : '';
  // const dateRange = startDate && endDate ? `${startDate} - ${endDate}` : startDate;

  return (
    <Group className="relative" align="flex-start" wrap="nowrap" gap={64}>
      <Image
        className="flex-none"
        w={320}
        h={314}
        src={courseDetail?.cover_image || defaultImage}
        alt={courseDetail?.name || '课程封面'}
      />

      <Stack className="flex-auto" gap={0}>
        <Text className="line-clamp-2" fz={26} fw={700} lh="38px" c={titleTextColor}>
          {courseDetail?.name || '课程标题'}
        </Text>

        <Text className="line-clamp-2" fz={14} fw={500} lh="24px" c={descTextColor} mt={8}>
          {courseDetail?.description?.substring(0, 100) || '课程描述...'}
          {courseDetail?.description && courseDetail.description.length > 100 ? '...' : ''}
        </Text>

        {courseDetail?.tags && courseDetail.tags.length > 0 && (
          <Group mt={8} gap={8}>
            {courseDetail.tags.map((tag, index) => (
              <Badge key={index} color="blue" variant="light">
                {tag}
              </Badge>
            ))}
          </Group>
        )}

        {/* {dateRange && (
          <Text
            className="w-[200px] h-[34px] leading-[34px] text-center rounded-[5px]"
            bg="rgba(235, 235, 235, 1)"
            mt={8}
            fz={14}
            fw={700}
            c="rgba(73, 81, 235, 1)"
          >
            {dateRange}
          </Text>
        )} */}
      </Stack>
    </Group>
  );
};

export default Desktop;
