import React from 'react';
import { Group, Image, Stack, Text, Divider, Button } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';

import { CourseInfoProps } from './types';

const Desktop: React.FC<CourseInfoProps> = ({ onCartClick, onBuyClick }) => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const titleTextColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const descTextColor = 'rgba(166, 166, 166, 1)';
  const cartBtnBackground = isDark ? 'rgba(56, 56, 56, 1)' : 'rgba(56, 56, 56, 1)';
  const buyBtnBackground = isDark ? 'rgba(112, 0, 254, 1)' : 'rgba(112, 0, 254, 1)';

  return (
    <Group className="relative" align="flex-start" wrap="nowrap" gap={64}>
      <Image
        className="flex-none"
        w={320}
        h={314}
        src="https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668"
      />

      <Stack className="flex-auto" gap={0}>
        <Text className="line-clamp-2" fz={26} fw={700} lh="38px" c={titleTextColor}>
          最新AI绘画系统教程（十二）教你精准掌控像素世界！
        </Text>
        <Text className="line-clamp-2" fz={14} fw={500} lh="24px" c={descTextColor} mt={8}>
          在前面我们学习了如果通过 Checkpoint 和 LoRA 模型生成各风格的图像...
        </Text>
        <Text
          className="w-[200px] h-[34px] leading-[34px] text-center rounded-[5px]"
          bg="rgba(235, 235, 235, 1)"
          mt={8}
          fz={14}
          fw={700}
          c="rgba(73, 81, 235, 1)"
        >
          2025.05.28 - 2025.08.18
        </Text>
        <Group align="flex-end" gap={0} mt={20}>
          <Text className="leading-none fw-700" fz={28} c="rgba(73, 81, 235, 1)">
            ￥
          </Text>
          <Text className="leading-none fw-700" fz={48} lh="38px" c="rgba(73, 81, 235, 1)">
            200
          </Text>
        </Group>

        <Divider mt={8} mb={24} />

        <Group className="absolute bottom-0" gap={32}>
          <Button variant="filled" w={240} h={56} fz={18} fw={700} color={cartBtnBackground} onClick={onCartClick}>
            加入购物车
          </Button>
          <Button variant="filled" w={240} h={56} fz={18} fw={700} color={buyBtnBackground} onClick={onBuyClick}>
            立即购买
          </Button>
        </Group>
      </Stack>
    </Group>
  );
};

export default Desktop;
