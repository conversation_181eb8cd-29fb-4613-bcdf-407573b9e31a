import React from 'react';
import { CourseDetail } from '../../schemas';

interface MobileRelatedCoursesProps {
  /**
   * 课程详情数据
   */
  courseDetail?: CourseDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 当前课程ID
   */
  courseId: string;
  /**
   * 关闭弹框的回调函数
   */
  onClose: () => void;
}

/**
 * 相关课程组件 - 移动版
 * 注意：当前版本为占位实现，实际功能暂未实现
 */
const Mobile: React.FC<MobileRelatedCoursesProps> = () => {
  return <></>;
};

export default Mobile;
