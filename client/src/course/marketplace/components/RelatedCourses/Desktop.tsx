import { Image, Skeleton } from '@mantine/core';
import { useQuery } from '@tanstack/react-query';
import React, { useEffect } from 'react';
import { getCoursesByInstructor } from '../../api';
import { CourseDetail } from '../../schemas';

interface DesktopRelatedCoursesProps {
  /**
   * 课程详情数据
   */
  courseDetail?: CourseDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 当前课程ID
   */
  courseId: string;
  /**
   * 关闭弹框的回调函数
   */
  onClose: () => void;
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
}

/**
 * 相关课程组件 - 桌面版
 */
const Desktop: React.FC<DesktopRelatedCoursesProps> = ({
  courseDetail,
  isDark,
  courseId,
  onClose,
  isLoading: parentIsLoading,
}) => {
  // 获取同一讲师的其他课程
  const {
    data: instructorCoursesResponse,
    isLoading: isLoadingInstructorCourses,
    refetch: refetchInstructorCourses,
  } = useQuery({
    queryKey: ['instructorCourses', courseDetail?.instructor, courseId],
    queryFn: () => getCoursesByInstructor(courseDetail?.instructor || '', courseId),
    enabled: !!courseDetail?.instructor,
    refetchOnWindowFocus: false,
  });

  // 当讲师信息加载完成后，重新获取讲师的其他课程
  useEffect(() => {
    if (courseDetail?.instructor) {
      refetchInstructorCourses();
    }
  }, [courseDetail?.instructor, refetchInstructorCourses]);

  // 如果正在加载或没有讲师信息，不显示任何内容
  if (parentIsLoading || !courseDetail?.instructor) {
    return null;
  }

  // 如果正在加载讲师的其他课程，显示骨架屏
  if (isLoadingInstructorCourses) {
    return (
      <div className="flex flex-col">
        <div className="mb-2">
          <div className="font-bold mt-6">由 {courseDetail?.instructor} 创建的更多课程</div>
        </div>
        <div className="grid grid-cols-1 gap-4">
          {Array(3)
            .fill(0)
            .map((_, index) => (
              <div className="flex flex-row items-center gap-4" key={index}>
                <Skeleton circle height={48} width={48} />
                <div className="flex-1">
                  <Skeleton height={16} radius="xl" width="80%" mb={8} />
                  <Skeleton height={12} radius="xl" width="60%" />
                </div>
              </div>
            ))}
        </div>
      </div>
    );
  }

  // 如果没有找到讲师的其他课程，不显示任何内容
  const relatedCourses = instructorCoursesResponse?.data?.data;
  if (!relatedCourses || relatedCourses.length === 0) {
    return null;
  }

  return (
    <div className="flex flex-col">
      <div className="mb-2">
        <div className="font-bold mt-6">由 {courseDetail?.instructor} 创建的更多课程</div>
      </div>
      <div className="grid grid-cols-1 gap-4">
        {relatedCourses.map((course) => (
          <div
            key={course.id}
            className={`bg-token-main-surface-secondary h-fit w-full rounded-xl px-3 py-4 cursor-pointer ${
              isDark ? 'hover:bg-gray-800' : 'hover:bg-gray-100'
            }`}
            onClick={() => {
              // 关闭当前模态框，然后打开新的课程详情
              onClose();
              // 延迟一下再打开新的模态框，避免闪烁
              setTimeout(() => {
                window.dispatchEvent(new CustomEvent('openCourseDetail', { detail: { courseId: course.id } }));
              }, 100);
            }}
          >
            <div className="flex flex-row items-center gap-4">
              <div className="h-12 w-12 shrink-0">
                <div className="gizmo-shadow-stroke overflow-hidden rounded-full">
                  <Image
                    className="bg-token-main-surface-secondary h-full w-full"
                    alt="Course Icon"
                    width="48"
                    height="48"
                    src={course.icon || 'https://ailoveworld.oss-cn-hangzhou.aliyuncs.com/1743067298-ai.jpg'}
                  />
                </div>
              </div>
              <div className="flex flex-col">
                <div className="line-clamp-1 font-semibold">{course.name}</div>
                <span className="line-clamp-2 text-xs">{course.description}</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default Desktop;
