import React from 'react';
import { CourseDetail } from '../../schemas';
import { Skeleton } from '@mantine/core';

interface DesktopCourseRatingsProps {
  /**
   * 课程详情数据
   */
  courseDetail?: CourseDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
}

/**
 * 课程评级组件 - 桌面版
 */
const Desktop: React.FC<DesktopCourseRatingsProps> = ({ courseDetail, isLoading }) => {
  // 如果正在加载，显示骨架屏
  if (isLoading) {
    return (
      <div className="flex flex-col">
        <div className="mb-2">
          <div className="font-bold mt-6">评级</div>
        </div>
        {Array(5)
          .fill(0)
          .map((_, index) => (
            <div className="flex flex-row items-center gap-2 py-1" key={index}>
              <Skeleton circle height={24} width={24} />
              <Skeleton height={10} radius="xl" style={{ width: '100%' }} />
            </div>
          ))}
      </div>
    );
  }

  // 使用实际数据或默认数据
  const ratings = courseDetail?.stats?.rating_distribution;
  if (!ratings) {
    return null;
  }

  // 确保评级按星级从高到低排序
  const sortedRatings = [...ratings].sort((a, b) => b.stars - a.stars);

  return (
    <div className="flex flex-col">
      <div className="mb-2">
        <div className="font-bold mt-6">评级</div>
      </div>
      {sortedRatings.map((rating) => (
        <div className="flex flex-row items-center gap-2 py-1 text-xl font-semibold" key={rating.stars}>
          <div className="relative">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-yellow-500"
            >
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z"
                fill="currentColor"
              />
            </svg>
            <div className="absolute inset-0 flex items-center justify-center text-xs font-bold text-white">{rating.stars}</div>
          </div>
          <div className="flex-1 h-2 bg-gray-200 rounded-full overflow-hidden">
            <div className="h-full bg-yellow-500 rounded-full" style={{ width: `${rating.percentage}%` }}></div>
          </div>
          <div className="text-xs font-normal">{rating.percentage}%</div>
        </div>
      ))}
    </div>
  );
};

export default Desktop;
