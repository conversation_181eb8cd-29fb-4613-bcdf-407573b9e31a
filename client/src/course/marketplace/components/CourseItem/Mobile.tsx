import React from 'react';
import { Group, Image, Stack, Text, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';

import { CourseItemProps } from './types';

const Desktop: React.FC<CourseItemProps> = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const itemBackgroundColor = isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  return (
    <Group className="rounded-[6px]" align="flex-start" px={36} py={32} bg={itemBackgroundColor} wrap="nowrap" gap={22}>
      <Image
        className="flex-none rounded-[4px]"
        w={128}
        h={130}
        src="https://img.js.design/assets/img/6803b25b210a5c79b2473576.png#48643bcca7bfdc40446d892a27005668"
      />
      <Stack className="relative flex-auto" h={130} gap={0}>
        <Text className="line-clamp-1" fz={18} fw={700} lh="26px" c={textColor}>
          最新AI绘画系统教程（十二）教你精准掌控像素世界！
        </Text>
        <Text className="line-clamp-2" fz={12} fw={500} lh="18px" c="rgba(166, 166, 166, 1)" mt={12}>
          在前面我们学习了如果通过 Checkpoint 和 LoRA 模型生成各风格的图像，今天开始我们将了解一种更高级的出图方式...
        </Text>
        <Group className="absolute bottom-0" align="flex-end" gap={0}>
          <Text fz={18} fw={700} lh={1} c="rgba(73, 81, 235, 1)">
            ￥
          </Text>
          <Text fz={28} fw={700} lh={1} c="rgba(73, 81, 235, 1)">
            200
          </Text>
        </Group>
      </Stack>
    </Group>
  );
};

export default Desktop;
