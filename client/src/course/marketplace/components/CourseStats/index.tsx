import React from 'react';
import { useDeviceDetect } from '~/core/hooks';
import { CourseDetail } from '../../schemas';
import Desktop from './Desktop';
import Mobile from './Mobile';

interface CourseStatsProps {
  /**
   * 课程详情数据
   */
  courseDetail?: CourseDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
}

/**
 * 课程统计信息组件
 * 根据设备类型自动选择桌面版或移动版
 */
const CourseStats: React.FC<CourseStatsProps> = (props) => {
  const { isMobile } = useDeviceDetect();

  // 根据设备类型动态选择适当的组件
  if (isMobile) {
    return <Mobile {...props} />;
  }

  return <Desktop {...props} />;
};

export default CourseStats;
