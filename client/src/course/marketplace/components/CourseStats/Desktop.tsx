import React from 'react';
import { CourseDetail } from '../../schemas';
import { Skeleton } from '@mantine/core';

interface DesktopCourseStatsProps {
  /**
   * 课程详情数据
   */
  courseDetail?: CourseDetail;
  /**
   * 是否为暗色主题
   */
  isDark: boolean;
  /**
   * 是否正在加载
   */
  isLoading?: boolean;
}

/**
 * 课程统计信息组件 - 桌面版
 */
const Desktop: React.FC<DesktopCourseStatsProps> = ({ courseDetail, isLoading }) => {
  // 如果正在加载，显示骨架屏
  if (isLoading) {
    return (
      <div className="flex justify-center">
        <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
          <Skeleton height={30} width={80} radius="md" />
          <Skeleton height={16} width={100} radius="md" />
        </div>
        <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
          <Skeleton height={30} width={80} radius="md" />
          <Skeleton height={16} width={100} radius="md" />
        </div>
        <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
          <Skeleton height={30} width={80} radius="md" />
          <Skeleton height={16} width={100} radius="md" />
        </div>
      </div>
    );
  }

  // 如果没有统计信息，不显示任何内容
  if (!courseDetail?.stats) {
    return null;
  }

  // 使用实际数据
  const stats = courseDetail.stats;

  return (
    <div className="flex justify-center">
      <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
        <div className="flex flex-row items-center gap-1.5 pt-1 text-xl font-semibold text-center leading-none">
          {stats.rating.toFixed(1)}
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M12 17.27L18.18 21L16.54 13.97L22 9.24L14.81 8.63L12 2L9.19 8.63L2 9.24L7.46 13.97L5.82 21L12 17.27Z"
              fill="currentColor"
              className="text-yellow-500"
            />
          </svg>
        </div>
        <div className="text-xs text-gray-500">评级 ({stats.rating_count})</div>
      </div>
      <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
        <div className="flex flex-row items-center gap-1.5 pt-1 text-xl font-semibold text-center leading-none">{stats.rank}</div>
        <div className="text-xs text-gray-500">属于{stats.category} (全球)</div>
      </div>
      <div className="flex flex-col justify-center items-center gap-2 w-48 mt-4 px-2">
        <div className="flex flex-row items-center gap-1.5 pt-1 text-xl font-semibold text-center leading-none">
          {stats.student_count}
        </div>
        <div className="text-xs text-gray-500">学习人数</div>
      </div>
    </div>
  );
};

export default Desktop;
