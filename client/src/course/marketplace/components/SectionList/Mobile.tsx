/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import React from 'react';
import { ScrollArea, Group, Stack, Text, useMantineTheme, Progress, Box } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { RiTimeLine, RiCheckboxCircleFill, RiLockLine } from 'react-icons/ri';

import { CourseListProps, CourseItem } from './types';

const Mobile: React.FC<CourseListProps> = ({ list, onClick }) => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const itemBackgroundColor = isDark ? 'black' : 'white';
  const itemHoverBackgroundColor = isDark ? theme.colors.dark[9] : theme.colors.indigo[1];
  const activeBackgroundColor = isDark ? theme.colors.dark[8] : theme.colors.blue[0];
  const titleTextColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';
  const durationTextColor = isDark ? theme.colors.gray[6] : 'rgba(28, 35, 76, 0.4)';

  return (
    <ScrollArea type="always" pr={36} style={{ height: '428px', whiteSpace: 'nowrap' }}>
      <Stack gap={12}>
        {list.map((course: CourseItem) => (
          <Group
            key={course.id}
            className="rounded-[16px] cursor-pointer"
            align="center"
            justify="space-between"
            py={12}
            pl={20}
            pr={30}
            bg={course.isActive ? activeBackgroundColor : itemBackgroundColor}
            onClick={() => onClick(course)}
            css={css`
              &:hover {
                background-color: ${itemHoverBackgroundColor} !important;
              }
            `}
          >
            <Stack gap={8} style={{ width: 'calc(100% - 40px)' }}>
              <Text className="leading-[24px] fw-500" fz={16} c={titleTextColor}>
                {course.title}
              </Text>
              <Group align="center" gap={9}>
                <RiTimeLine size={16} color={durationTextColor} />
                <Text className="leading-[16px] fw-500" fz={12} c={durationTextColor}>
                  {course.duration}
                </Text>
                {course.progress !== undefined && course.progress > 0 && (
                  <Text className="leading-[16px] fw-500" fz={12} c="blue">
                    已学习 {course.progress}%
                  </Text>
                )}
              </Group>
              {course.progress !== undefined && (
                <Progress
                  value={course.progress}
                  color={course.progress === 100 ? 'green' : 'blue'}
                  size="sm"
                  radius="xl"
                  style={{ width: '100%' }}
                />
              )}
            </Stack>
            <Box>
              {!course.is_free ? (
                <RiLockLine size={28} color={isDark ? theme.colors.gray[6] : 'rgba(28, 35, 76, 0.4)'} />
              ) : course.progress === 100 ? (
                <RiCheckboxCircleFill size={28} color="rgba(50, 200, 100, 1)" />
              ) : (
                <RiCheckboxCircleFill size={28} color="rgba(50, 107, 255, 1)" />
              )}
            </Box>
          </Group>
        ))}
      </Stack>
    </ScrollArea>
  );
};

export default Mobile;
