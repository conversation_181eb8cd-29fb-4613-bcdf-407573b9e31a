/** @jsxImportSource @emotion/react */
import { css } from '@emotion/react';
import { useState } from 'react';
import { <PERSON>ing, Button, Stack, Group, Avatar, Divider, Textarea, Text } from '@mantine/core';
import { RiUserSmileLine, RiAtLine, RiImageAddLine } from 'react-icons/ri';
import { useTheme } from '~/core/features/mantine';

const Desktop = () => {
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const textColor = isDark ? 'white' : 'black';
  const inputBackgroundColor = isDark ? 'black' : 'white';
  const borderColor = isDark ? 'rgba(51, 51, 51, 1)' : 'rgba(204, 204, 204, 1)';
  const iconColor = isDark ? 'rgba(163, 163, 163, 1)' : 'rgba(92, 92, 92, 1)';
  const iconHoverColor = isDark ? 'white' : 'black';

  const [star, setStar] = useState(0);

  return (
    <Group align="flex-start" py={12} pl={84} pr={66} gap={32} mt={84}>
      <Stack className="flex-none" align="center" w={72} gap={12}>
        <Avatar
          src="https://avatars.githubusercontent.com/u/115162199?s=400&u=490007c738c07594c6666568296167882768615d&v=4"
          alt="it's me"
          size={72}
        />
        <Text fz={14} fw={500} lh="20px" c={textColor}>
          用户名
        </Text>
      </Stack>
      <Stack className="flex-auto" gap={0}>
        <Group justify="space-between">
          <Text fz={18} fw={500} lh="26px" c={textColor}>
            提交评论
          </Text>
          <Rating value={star} size={16} color="rgba(73, 81, 235, 1)" onChange={setStar} />
        </Group>
        <Stack
          className="rounded-[8px]"
          px={24}
          pt={16}
          mt={16}
          bg={inputBackgroundColor}
          gap={0}
          style={{
            border: `1px solid ${borderColor}`,
          }}
        >
          <Textarea variant="unstyled" placeholder="请输入评论" rows={4} />
          <Divider mt={16} />
          <Group py={16} gap={28}>
            <RiUserSmileLine
              className="cursor-pointer"
              size={18}
              color={iconColor}
              css={css`
                &:hover {
                  color: ${iconHoverColor} !important;
                }
              `}
            />
            <RiAtLine
              className="cursor-pointer"
              size={18}
              color={iconColor}
              css={css`
                &:hover {
                  color: ${iconHoverColor} !important;
                }
              `}
            />
            <RiImageAddLine
              className="cursor-pointer"
              size={18}
              color={iconColor}
              css={css`
                &:hover {
                  color: ${iconHoverColor} !important;
                }
              `}
            />
          </Group>
        </Stack>
        <Group justify="flex-end" mt={24}>
          <Button variant="filled" w={240} h={56} fz={18} fw={700} color="rgba(112, 0, 254, 1)">
            提交评论
          </Button>
        </Group>
      </Stack>
    </Group>
  );
};

export default Desktop;
