import React from 'react';
import { List, Text, Badge, Group, Skeleton } from '@mantine/core';
import { CourseSection } from '../../schemas';
import { CourseSectionListProps } from './types';

/**
 * 课程章节列表组件 - 移动版
 */
const Mobile: React.FC<CourseSectionListProps> = ({
  sections,
  isLoading = false,
  hasPurchased = false,
  maxVisibleSections = 3, // 移动版默认显示更少的章节
  onSectionClick,
}) => {
  // 处理点击章节
  const handleSectionClick = (section: CourseSection) => {
    if (onSectionClick) {
      onSectionClick(section);
    }
  };

  // 加载状态下的骨架屏
  if (isLoading) {
    return (
      <div className="mt-4">
        <Text className="mb-2 font-medium" size="sm">
          课程章节
        </Text>
        <div className="space-y-2">
          <Skeleton height={20} radius="sm" />
          <Skeleton height={20} radius="sm" />
          <Skeleton height={20} radius="sm" />
        </div>
      </div>
    );
  }

  // 没有章节时的提示
  if (!sections || sections.length === 0) {
    return (
      <div className="mt-4">
        <Text className="mb-2 font-medium" size="sm">
          课程章节
        </Text>
        <Text size="xs" c="dimmed">
          暂无章节信息
        </Text>
      </div>
    );
  }

  // 显示章节列表
  const visibleSections = sections.slice(0, maxVisibleSections);
  const remainingSections = sections.length - maxVisibleSections;

  return (
    <div className="mt-4">
      <Text className="mb-2 font-medium" size="sm">
        课程章节
      </Text>
      <List spacing="xs" size="xs" center>
        {visibleSections.map((section) => (
          <List.Item
            key={section.id}
            onClick={() => handleSectionClick(section)}
            className={
              hasPurchased || section.is_free ? 'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-1' : ''
            }
          >
            <Group justify="space-between" wrap="nowrap">
              <Text size="xs">{section.title}</Text>
              <div className="flex items-center gap-1">
                {section.duration && (
                  <Text size="xs" c="dimmed">
                    {Math.floor(section.duration / 60)}:{(section.duration % 60).toString().padStart(2, '0')}
                  </Text>
                )}
                {section.is_free && (
                  <Badge color="green" variant="light" size="xs">
                    免费
                  </Badge>
                )}
                {!hasPurchased && !section.is_free && (
                  <Badge color="gray" variant="light" size="xs">
                    付费
                  </Badge>
                )}
              </div>
            </Group>
          </List.Item>
        ))}
      </List>
      {remainingSections > 0 && (
        <Text size="xs" c="dimmed" ta="center" mt="xs">
          还有 {remainingSections} 个章节...
        </Text>
      )}
    </div>
  );
};

export default Mobile;
