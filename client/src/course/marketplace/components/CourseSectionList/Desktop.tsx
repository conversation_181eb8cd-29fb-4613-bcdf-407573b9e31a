import React from 'react';
import { List, Text, Badge, Group, Skeleton, Stack } from '@mantine/core';
import { CourseSection } from '../../schemas';
import { CourseSectionListProps } from './types';

/**
 * 课程章节列表组件 - 桌面版
 */
const Desktop: React.FC<CourseSectionListProps> = ({
  sections,
  isLoading = false,
  hasPurchased = false,
  maxVisibleSections = 5,
  onSectionClick,
}) => {
  // 处理点击章节
  const handleSectionClick = (section: CourseSection) => {
    if (onSectionClick) {
      onSectionClick(section);
    }
  };

  // 加载状态下的骨架屏
  if (isLoading) {
    return (
      <div className="mt-6">
        <Text className="mb-2 font-medium">课程章节</Text>
        <div className="space-y-2">
          <Skeleton height={24} radius="sm" />
          <Skeleton height={24} radius="sm" />
          <Skeleton height={24} radius="sm" />
        </div>
      </div>
    );
  }

  // 没有章节时的提示
  if (!sections || sections.length === 0) {
    return (
      <Stack gap="md">
        <Text size="lg" fw={600}>
          章节
        </Text>
        <Text c="dimmed">暂无章节信息</Text>
      </Stack>
    );
  }

  // 显示章节列表
  const visibleSections = sections.slice(0, maxVisibleSections);
  const remainingSections = sections.length - maxVisibleSections;

  return (
    <Stack gap="md">
      <Text size="lg" fw={600}>
        章节
      </Text>
      <List spacing="xs" size="sm" center>
        {visibleSections.map((section) => (
          <List.Item
            key={section.id}
            onClick={() => handleSectionClick(section)}
            className={'cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-800 rounded px-2'}
          >
            <Group justify="space-between" wrap="nowrap">
              <Text size="sm">{section.title}</Text>
              <div className="flex items-center gap-2">
                {section.duration && (
                  <Text size="xs" c="dimmed">
                    {Math.floor(section.duration / 60)}:{(section.duration % 60).toString().padStart(2, '0')}
                  </Text>
                )}
                {section.is_free && (
                  <Badge color="green" variant="light" size="xs">
                    免费
                  </Badge>
                )}
                {!hasPurchased && !section.is_free && (
                  <Badge color="gray" variant="light" size="xs">
                    付费
                  </Badge>
                )}
              </div>
            </Group>
          </List.Item>
        ))}
      </List>
      {remainingSections > 0 && (
        <Text size="xs" c="dimmed" ta="center" mt="xs">
          还有 {remainingSections} 个章节...
        </Text>
      )}
    </Stack>
  );
};

export default Desktop;
