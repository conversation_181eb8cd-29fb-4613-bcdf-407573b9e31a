import React, { useState } from 'react';
import { Container, Group, Box, Text, Table, Checkbox, Stack, Button, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { FiShoppingCart } from 'react-icons/fi';

const Cart = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const containerBackground = isDark ? 'black' : 'white';
  const tableHeadBackground = isDark
    ? `linear-gradient(90deg, ${theme.colors.dark[8]} 0%, rgba(247, 248, 255, 0) 100%)`
    : 'linear-gradient(90deg, rgba(247, 248, 255, 1) 0%, rgba(247, 248, 255, 0) 100%)';
  const textColor = isDark ? 'rgba(204, 204, 204, 1)' : 'rgba(51, 51, 51, 1)';

  const [selectedRows, setSelectedRows] = useState<number[]>([]);

  const elements = [
    { position: 6, mass: 12.011, symbol: 'C', name: 'Carbon' },
    { position: 7, mass: 14.007, symbol: 'N', name: 'Nitrogen' },
    { position: 39, mass: 88.906, symbol: 'Y', name: 'Yttrium' },
    { position: 56, mass: 137.33, symbol: 'Ba', name: 'Barium' },
    { position: 58, mass: 140.12, symbol: 'Ce', name: 'Cerium' },
  ];

  // 全选/全不选切换函数
  const toggleAllRows = (checked: boolean) => {
    setSelectedRows(checked ? elements.map((e) => e.position) : []);
  };

  // 判断是否全选
  const isAllSelected = elements.length > 0 && selectedRows.length === elements.length;

  const rows = elements.map((element) => (
    <Table.Tr
      h={120}
      key={element.name}
      bg={selectedRows.includes(element.position) ? 'var(--mantine-color-blue-light)' : undefined}
    >
      <Table.Td>
        <Checkbox
          size="xs"
          color="rgba(73, 81, 235, 1)"
          checked={selectedRows.includes(element.position)}
          onChange={(event) =>
            setSelectedRows(
              event.currentTarget.checked
                ? [...selectedRows, element.position]
                : selectedRows.filter((position) => position !== element.position),
            )
          }
        />
      </Table.Td>
      <Table.Td>
        <Text fz={14} fw={500} lh="24px" c={textColor}>
          Deep seek高手进阶之路
        </Text>
      </Table.Td>
      <Table.Td>
        <Text fz={16} fw={500} lh="24px" c="rgba(255, 80, 0, 1)">
          ¥30
        </Text>
      </Table.Td>
      <Table.Td>
        <Text fz={14} fw={500} lh="24px" c="rgba(128, 128, 128, 1)">
          移出购物车
        </Text>
      </Table.Td>
    </Table.Tr>
  ));

  return (
    <Container size="1024px" pt={112} pb={48}>
      <Group className="rounded-t-[6px]" h={68} px={32} bg="rgba(73, 81, 235, 1)" color="white">
        <FiShoppingCart size={20} />
        <Text fz={22} fw={700} lh="28px" c="">
          购物车
        </Text>
      </Group>

      <Box className="rounded-b-[6px]" p={20} bg={containerBackground}>
        <Table
          variant="vertical"
          styles={{
            thead: {
              background: tableHeadBackground,
              borderRadius: '6px',
              fontSize: '16px',
              fontWeight: '500',
            },
          }}
        >
          <Table.Thead className="rounded-[6px]" h={44}>
            <Table.Tr>
              <Table.Th w={104} bg="transparent">
                <Checkbox
                  label="全选"
                  size="xs"
                  color="rgba(73, 81, 235, 1)"
                  checked={isAllSelected}
                  onChange={(event) => toggleAllRows(event.currentTarget.checked)}
                  styles={{
                    label: {
                      fontSize: '16px',
                      fontWeight: '500',
                    },
                  }}
                />
              </Table.Th>
              <Table.Th bg="transparent">课程名称</Table.Th>
              <Table.Th bg="transparent">价格</Table.Th>
              <Table.Th bg="transparent"></Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>{rows}</Table.Tbody>
        </Table>

        <Group
          align="center"
          justify="flex-end"
          h={126}
          px={48}
          gap={14}
          styles={{
            root: {
              backgroundColor: isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)',
              borderRadius: '10px',
            },
          }}
        >
          <Stack align="flex-end">
            <Group align="flex-end" gap={0}>
              <Text fz={12} fw={500} c={isDark ? theme.colors.gray[3] : 'rgba(51, 51, 51, 1)'} inline>
                合计：
              </Text>
              <Text span fz={18} fw={700} lh="14px" c={'rgba(255, 122, 62, 1)'}>
                ¥{(Number(490) || 0).toFixed(2)}
              </Text>
            </Group>

            <Button w={180} h={44} variant="filled" color="rgba(255, 122, 62, 1)">
              结算
            </Button>
          </Stack>
        </Group>
      </Box>
    </Container>
  );
};

export default Cart;
