import React, { useState } from 'react';
import { Container, Group, CheckIcon, Text, Radio, Stack, Button, useMantineTheme } from '@mantine/core';
import { useTheme } from '~/core/features/mantine';
import { FaAlipay, FaWeixin } from 'react-icons/fa';

import { CourseItem } from '../components';

type PaymentMethod = 'alipay' | 'wechatpay';

const Payment = () => {
  const theme = useMantineTheme();
  const { actualColorScheme } = useTheme();
  const isDark = actualColorScheme === 'dark';

  const containerBackground = isDark ? 'black' : 'white';
  const textColor = isDark ? 'rgba(199, 199, 199, 1)' : 'rgba(56, 56, 56, 1)';

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod>('alipay');

  const paymentType = [
    { label: '支付宝', value: 'alipay' },
    { label: '微信支付', value: 'wechatpay' },
  ];
  const paymentRows = paymentType.map((row) => (
    <Radio.Card h={50} withBorder={false} value={row.value}>
      <Group gap={48}>
        <Radio.Indicator
          size="xs"
          icon={CheckIcon}
          color="rgba(112, 0, 254, 1)"
          style={{
            '--radio-icon-size-xs': '8px',
          }}
        />
        <Group gap={16}>
          {row.value === 'alipay' && <FaAlipay size={24} color="#02A9F1" />}
          {row.value === 'wechatpay' && <FaWeixin size={24} color="#09BB07" />}
          <Text fz={16} fw={500} c={isDark ? theme.colors.gray[3] : theme.colors.dark[6]}>
            {row.label}
          </Text>
        </Group>
      </Group>
    </Radio.Card>
  ));

  return (
    <Container size="1024px" pt={112} pb={48}>
      <Stack className="rounded-[10px]" px={32} py={40} gap={0} bg={containerBackground}>
        <Text fz={22} fw={700} lh="32px" c={textColor}>
          购买商品
        </Text>

        <Stack mt={20} gap={16}>
          <CourseItem />
          <CourseItem />
          <CourseItem />
        </Stack>

        <Text mt={40} fz={22} fw={700} lh="32px" c={textColor}>
          支付方式
        </Text>

        <Radio.Group
          mt={20}
          value={paymentMethod}
          onChange={(value) => {
            setPaymentMethod(value as PaymentMethod);
          }}
        >
          <Stack>{paymentRows}</Stack>
        </Radio.Group>

        <Group
          align="center"
          justify="flex-end"
          h={126}
          px={48}
          mt={20}
          gap={14}
          styles={{
            root: {
              backgroundColor: isDark ? theme.colors.dark[8] : 'rgba(247, 248, 255, 1)',
              borderRadius: '10px',
            },
          }}
        >
          <Stack align="flex-end">
            <Group align="flex-end" gap={0}>
              <Text fz={12} fw={500} c={isDark ? theme.colors.gray[3] : 'rgba(51, 51, 51, 1)'} inline>
                实付金额：
              </Text>
              <Text span fz={18} fw={700} lh="14px" c={'rgba(73, 81, 235, 1)'}>
                ¥{(Number(490) || 0).toFixed(2)}
              </Text>
            </Group>

            <Button w={180} h={44} variant="filled" color="rgba(112, 0, 254, 1)">
              立即支付
            </Button>
          </Stack>
        </Group>
      </Stack>
    </Container>
  );
};

export default Payment;
