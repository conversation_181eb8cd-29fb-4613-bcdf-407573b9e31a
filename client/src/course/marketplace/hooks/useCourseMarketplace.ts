import { useCallback, useMemo, useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getCoursesByTags, searchCourses } from '../api';
import { useCourseMarketplaceStore } from '../store';
import { CourseQuery, CoursesByTag, Course } from '../schemas';

/**
 * 课程商店Hook
 * 用于获取和管理课程商店数据
 */
export const useCourseMarketplace = () => {
  // 从store中获取状态
  const { activeCategory, setActiveCategory, searchKeyword, setSearchKeyword } = useCourseMarketplaceStore();

  // 为每个分类维护独立的加载状态
  const [loadingCategories, setLoadingCategories] = useState<Record<string, boolean>>({});

  // 获取按标签分组的课程列表
  const {
    data: courses,
    isLoading: isCoursesLoading,
    error: coursesError,
    refetch: refetchCourses,
  } = useQuery<CoursesByTag>({
    queryKey: ['coursesByTags'],
    queryFn: async () => (await getCoursesByTags()).data,
  });

  // 从课程数据中提取标签，而不是单独请求
  const tags = useMemo(() => {
    if (!courses || !courses.data) return [];
    // 直接从coursesData中获取所有标签（即对象的键）
    return Object.keys(courses.data);
  }, [courses]);

  // 处理分类切换
  const handleCategoryChange = useCallback(
    (category: string) => {
      setActiveCategory(category);
    },
    [setActiveCategory],
  );

  // 搜索课程
  const {
    data: searchResult,
    isLoading: isSearchLoading,
    error: searchError,
    refetch: research,
  } = useQuery<Course[]>({
    queryKey: ['coursesSearchResult'],
    enabled: false,
    queryFn: async () => {
      if (searchKeyword === '') return [];
      const defaultQuery: CourseQuery = { page: 1, page_size: 10, keyword: searchKeyword };
      return (await searchCourses(defaultQuery)).data.data;
    },
  });

  /**
   * 加载更多课程
   * @param tag 当前标签
   * @param existingIds 已加载的课程ID列表
   * @returns 新加载的课程列表
   */
  const loadMore = useCallback(async (tag: string, existingIds: string[]) => {
    try {
      // 设置当前分类的加载状态为true
      setLoadingCategories((prev) => ({ ...prev, [tag]: true }));

      // 构建查询参数
      const query: CourseQuery = {
        page: 1,
        page_size: 4,
        tags: [tag],
        excludeIds: existingIds,
      };

      // 调用搜索API
      const result = await searchCourses(query);

      // 返回新加载的课程列表
      return result.data.data;
    } catch (error) {
      console.error(`加载更多课程失败: ${error}`);
      throw error;
    } finally {
      // 设置当前分类的加载状态为false
      setLoadingCategories((prev) => ({ ...prev, [tag]: false }));
    }
  }, []);

  return {
    // 数据
    tags,
    courses,
    searchResult,

    // 状态
    activeCategory,
    searchKeyword,
    loadingCategories,

    // 加载状态
    isCoursesLoading,
    isLoading: isCoursesLoading,
    isSearchLoading,

    // 错误状态
    coursesError,
    searchError,

    // 操作方法
    handleCategoryChange,
    refetchCourses,
    research,
    setSearchKeyword,
    loadMore,
  };
};

export default useCourseMarketplace;
