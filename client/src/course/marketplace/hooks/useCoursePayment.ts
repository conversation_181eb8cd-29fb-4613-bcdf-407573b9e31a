import { useState } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { notifications } from '@mantine/notifications';
import { getPaymentPlans } from '~/paymentPlan/api';
import { PaymentPlan } from '~/paymentPlan/schemas';
import { CourseDetail } from '../schemas';
import { usePaymentMutation, usePaymentMessageListener } from '~/core/utils/payment';

/**
 * 课程支付Hook
 * 用于处理课程购买支付流程
 */
export const useCoursePayment = (courseId?: string) => {
  const navigate = useNavigate();
  // 支付方式
  const [paymentMethod, setPaymentMethod] = useState<'alipay' | 'wechatpay'>('alipay');
  // 订单ID
  const [orderId, setOrderId] = useState<string>('');
  // 选中的付费计划ID
  const [selectedPlanId, setSelectedPlanId] = useState<number | undefined>(undefined);

  // 获取付费计划列表
  const { data: paymentPlansResponse, isLoading: isPaymentPlansLoading } = useQuery({
    queryKey: ['paymentPlans', 'course', courseId],
    queryFn: () => getPaymentPlans('course', courseId!),
    enabled: !!courseId,
  });

  // 提取付费计划数据
  const paymentPlans = paymentPlansResponse?.data || [];

  // 获取选中的付费计划
  const selectedPlan = paymentPlans.find((plan) => plan.id === selectedPlanId);

  // 支付成功处理
  const handlePaymentSuccess = () => {
    // 可以在这里添加跳转到课程详情页或其他逻辑
    navigate('/course/marketplace');
  };

  // 支付处理逻辑
  const handlePayment = usePaymentMutation(setOrderId, navigate, handlePaymentSuccess);

  // 支付消息监听（支持支付宝和微信支付）
  const [isListeningEnabled, setIsListeningEnabled] = useState(false);

  usePaymentMessageListener(
    orderId,
    isListeningEnabled,
    () => {
      setIsListeningEnabled(false);
      // 显示购买成功通知，替代原来的支付成功通知
      notifications.show({
        title: '购买成功',
        message: '课程购买成功，您现在可以学习该课程了！',
        color: 'green',
      });
      handlePaymentSuccess();
    },
    () => {
      setIsListeningEnabled(false);
    }
  );

  // 处理课程购买
  const handleCoursePurchase = (course: CourseDetail, paymentMethodOverride?: 'alipay' | 'wechatpay') => {
    const currentPaymentMethod = paymentMethodOverride || paymentMethod;

    if (!currentPaymentMethod) {
      notifications.show({
        title: '请选择支付方式',
        message: '请先选择支付宝或微信支付',
        color: 'yellow',
      });
      return;
    }

    if (!selectedPlanId) {
      notifications.show({
        title: '请选择付费计划',
        message: '请先选择一个付费计划',
        color: 'yellow',
      });
      return;
    }

    // 创建订单请求
    handlePayment.mutate({
      product_type: 'course',
      product_id: course.id.toString(),
      payment_plan_id: selectedPlanId,
      quantity: 1,
      selectedPaymentMethod: currentPaymentMethod,
    });

    // 启用消息监听（支付宝通过回调，微信支付通过轮询后的postMessage）
    setIsListeningEnabled(true);
  };

  // 处理选择付费计划
  const handleSelectPlan = (plan: PaymentPlan) => {
    setSelectedPlanId(plan.id);
  };

  return {
    // 状态
    paymentMethod,
    orderId,
    isPaymentLoading: handlePayment.isPending,
    paymentPlans,
    selectedPlanId,
    selectedPlan,
    isPaymentPlansLoading,
    isListeningEnabled,

    // 操作方法
    setPaymentMethod,
    handleCoursePurchase,
    handleSelectPlan,
    setSelectedPlanId,
  };
};

export default useCoursePayment;
