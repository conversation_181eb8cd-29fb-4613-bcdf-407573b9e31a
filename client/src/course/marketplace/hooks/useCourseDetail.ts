import { useState, useCallback, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { getCourseDetail } from '../api';
import { CourseDetail } from '../schemas';

/**
 * 课程详情Hook
 * 用于管理课程详情模态框的状态和数据
 */
export const useCourseDetail = () => {
  // 状态管理：控制详情模态框
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedCourseId, setSelectedCourseId] = useState<string>('');

  // 获取课程详情数据
  const {
    data: courseDetailResponse,
    isLoading: isCourseDetailLoading,
    error: courseDetailError,
  } = useQuery({
    queryKey: ['courseDetail', selectedCourseId],
    queryFn: () => getCourseDetail(selectedCourseId),
    enabled: isModalOpen && !!selectedCourseId,
    refetchOnWindowFocus: false,
  });

  // 提取课程详情数据
  const courseDetail = courseDetailResponse?.data as CourseDetail;

  // 处理打开详情模态框
  const handleOpenModal = useCallback((courseId: string) => {
    setSelectedCourseId(courseId);
    setIsModalOpen(true);
  }, []);

  // 处理关闭详情模态框
  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
  }, []);

  // 监听自定义事件，用于从其他组件打开课程详情
  useEffect(() => {
    // 定义事件处理函数
    const handleOpenCourseDetailEvent = (event: CustomEvent<{ courseId: string }>) => {
      if (event.detail && event.detail.courseId) {
        handleOpenModal(event.detail.courseId);
      }
    };

    // 添加事件监听器
    window.addEventListener('openCourseDetail', handleOpenCourseDetailEvent as EventListener);

    // 组件卸载时移除事件监听器
    return () => {
      window.removeEventListener('openCourseDetail', handleOpenCourseDetailEvent as EventListener);
    };
  }, [handleOpenModal]);

  return {
    // 状态
    isModalOpen,
    selectedCourseId,
    courseDetail,
    isCourseDetailLoading,
    courseDetailError,

    // 操作方法
    handleOpenModal,
    handleCloseModal,
  };
};

export default useCourseDetail;
