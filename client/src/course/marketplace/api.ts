import { ResponsePayloads, Pagination } from '~/core/schemas';
import { useUserStore } from '~/user/core/store';
import { CoursesByTag, CourseQuery, Course, CourseDetail } from './schemas';
import { API_BASE_URL } from '~/core/constants';

/**
 * 获取按标签分组的课程列表
 * @returns 按标签分组的课程列表
 */
export async function getCoursesByTags(): Promise<ResponsePayloads<CoursesByTag>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/courses/by_tags`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取课程列表失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 搜索课程
 * @param query 搜索参数
 * @returns 课程列表
 */
export async function searchCourses(query: CourseQuery): Promise<ResponsePayloads<Pagination<Course>>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  // 构建查询参数
  const queryParams = new URLSearchParams();
  if (query.keyword) queryParams.append('keyword', query.keyword);
  if (query.page) queryParams.append('page', query.page.toString());
  if (query.page_size) queryParams.append('page_size', query.page_size.toString());
  if (query.teacher_id) queryParams.append('teacher_id', query.teacher_id);
  if (query.instructor) queryParams.append('instructor', query.instructor);

  // 处理标签 - 与智能体搜索保持一致，每个标签单独添加
  if (query.tags && query.tags.length > 0) {
    query.tags.forEach((tag) => queryParams.append('tags', tag));
  }

  // 处理排除的ID - 与智能体搜索保持一致，每个ID单独添加
  if (query.excludeIds && query.excludeIds.length > 0) {
    query.excludeIds.forEach((id) => queryParams.append('exclude_ids', id));
  }

  const response = await fetch(`${API_BASE_URL}/courses/search?${queryParams.toString()}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`搜索课程失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取课程详情
 * @param id 课程ID
 * @returns 课程详情
 */
export async function getCourseDetail(id: string): Promise<ResponsePayloads<CourseDetail>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/courses/details/${id}`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取课程详情失败: ${response.statusText}`);
  }

  // 获取原始数据
  const originalData = await response.json();

  // 添加模拟的统计信息和评级数据
  if (originalData.data) {
    // 生成随机评级（3.5-5.0之间）
    const rating = 3.5 + Math.random() * 1.5;

    // 生成随机评级分布
    const ratingDistribution = [
      { stars: 5, percentage: 60 + Math.floor(Math.random() * 30) },
      { stars: 4, percentage: 20 + Math.floor(Math.random() * 20) },
      { stars: 3, percentage: 5 + Math.floor(Math.random() * 15) },
      { stars: 2, percentage: 1 + Math.floor(Math.random() * 5) },
      { stars: 1, percentage: Math.floor(Math.random() * 5) },
    ];

    // 随机生成评级数量
    const ratingCounts = ['10K+', '25K+', '50K+', '100K+', '250K+', '500K+'];
    const ratingCount = ratingCounts[Math.floor(Math.random() * ratingCounts.length)];

    // 随机生成排名
    const rank = `#${Math.floor(Math.random() * 50) + 1}`;

    // 随机生成学习人数
    const studentCounts = ['500K+', '1M+', '2M+', '3M+', '5M+', '10M+'];
    const studentCount = studentCounts[Math.floor(Math.random() * studentCounts.length)];

    // 获取课程的第一个标签作为类别，如果没有标签则使用"其他"
    const category = originalData.data.tags && originalData.data.tags.length > 0 ? originalData.data.tags[0] : '其他';

    // 添加统计信息
    originalData.data.stats = {
      rating,
      rating_count: ratingCount,
      rank,
      category,
      student_count: studentCount,
      rating_distribution: ratingDistribution,
    };

    // 添加功能列表
    originalData.data.features = ['高清视频', '随时学习', '讲师答疑', '课程资料下载', '学习社区'];

    // 添加创建时间和更新时间
    const now = Date.now();
    originalData.data.created_at = now - 30 * 24 * 60 * 60 * 1000; // 30天前
    originalData.data.updated_at = now - 7 * 24 * 60 * 60 * 1000; // 7天前

    // 模拟付费计划数据
    // 注意：实际应用中，这些数据应该从后端API获取
    // 这里仅作为前端模拟数据使用
    originalData.data.payment_plans = [
      {
        id: 1,
        name: '标准版',
        description: '适合个人学习使用',
        price: originalData.data.price || 99,
        original_price: (originalData.data.price || 99) * 1.2,
        validity_period: 365, // 有效期一年
        scope_type: 'course',
        scope_ids: [id],
        is_active: true,
        created_at: (now - 60 * 24 * 60 * 60 * 1000).toString(), // 60天前
        updated_at: (now - 30 * 24 * 60 * 60 * 1000).toString(), // 30天前
      },
      {
        id: 2,
        name: '高级版',
        description: '包含讲师一对一答疑',
        price: (originalData.data.price || 99) * 1.5,
        original_price: (originalData.data.price || 99) * 1.8,
        validity_period: 730, // 有效期两年
        scope_type: 'course',
        scope_ids: [id],
        is_active: true,
        created_at: (now - 60 * 24 * 60 * 60 * 1000).toString(), // 60天前
        updated_at: (now - 30 * 24 * 60 * 60 * 1000).toString(), // 30天前
      },
      {
        id: 3,
        name: '终身版',
        description: '永久访问，包含所有更新',
        price: (originalData.data.price || 99) * 2.5,
        original_price: (originalData.data.price || 99) * 3,
        validity_period: 0, // 永久有效
        scope_type: 'course',
        scope_ids: [id],
        is_active: true,
        created_at: (now - 60 * 24 * 60 * 60 * 1000).toString(), // 60天前
        updated_at: (now - 30 * 24 * 60 * 60 * 1000).toString(), // 30天前
      },
    ];
  }

  return originalData;
}

/**
 * 获取由同一讲师创建的其他课程
 * @param instructor 讲师名称
 * @param excludeCourseId 要排除的当前课程ID
 * @param limit 返回结果数量限制
 * @returns 分页的课程列表
 */
export async function getCoursesByInstructor(
  instructor: string,
  excludeCourseId?: string,
  limit: number = 3,
): Promise<ResponsePayloads<Pagination<Course>>> {
  // 使用搜索API，将讲师名称作为过滤条件
  const params: CourseQuery = {
    instructor: instructor,
    page: 1,
    page_size: limit,
    excludeIds: excludeCourseId ? [excludeCourseId] : [],
  };

  return searchCourses(params);
}

/**
 * 获取用户拥有的课程列表
 * @returns 用户拥有的课程列表
 */
export async function getOwnedCourses(): Promise<ResponsePayloads<Course[]>> {
  const token = useUserStore.getState().token;

  if (!token) {
    throw new Error('未找到用户 token，请先登录');
  }

  const response = await fetch(`${API_BASE_URL}/courses/owned`, {
    method: 'GET',
    headers: {
      Accept: 'application/json',
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    throw new Error(`获取用户拥有的课程失败: ${response.statusText}`);
  }

  return response.json();
}
