import { z } from 'zod';
import { AssetBaseSchema, AssetQueryBaseSchema } from '~/core/schemas';

/**
 * 课程评级分布项
 */
export const RatingDistributionSchema = z.object({
  stars: z.number().min(1).max(5).describe('星级数量'),
  percentage: z.number().min(0).max(100).describe('该星级的百分比'),
});

/**
 * 课程统计信息模型
 */
export const CourseStatsSchema = z.object({
  rating: z.number().min(0).max(5).describe('课程平均评级'),
  rating_count: z.string().describe("评级数量，例如：'50K+'"),
  rank: z.string().describe("排名，例如：'#13'"),
  category: z.string().describe('所属类别'),
  student_count: z.string().describe("学习人数，例如：'3M+'"),
  rating_distribution: z.array(RatingDistributionSchema).default([]).describe('各星级评分的分布情况'),
});

/**
 * 简化的课程信息，用于标签分组展示
 * 继承自 AssetBaseSchema 以保持类型一致性
 */
export const CourseSchema = AssetBaseSchema.extend({
  sections_count: z.number().optional().describe('课程章节数量'),
});

/**
 * 按标签分组的课程列表
 */
export const CoursesByTagSchema = z.object({
  data: z.record(z.string(), z.array(CourseSchema)).describe('按标签分组的课程列表'),
});

/**
 * 课程章节概要
 */
export const CourseSectionSchema = z.object({
  id: z.number().describe('章节ID'),
  title: z.string().describe('章节标题'),
  duration: z.number().optional().describe('章节时长（秒）'),
  is_free: z.boolean().optional().describe('是否免费'),
  video_url: z.string().optional().describe('视频URL'),
});

/**
 * 课程详情
 */
export const CourseDetailSchema = CourseSchema.extend({
  price: z.number().describe('课程价格'),
  instructor: z.string().optional().describe('讲师'),
  cover_image: z.string().optional().describe('课程封面图片URL'),
  poster_url: z.string().optional().describe('海报图片URL'),
  tags: z.array(z.string()).default([]).describe('标签列表'),
  sections: z.array(CourseSectionSchema).default([]).describe('课程章节列表'),
  has_purchased: z.boolean().default(false).describe('是否已购买'),
  created_at: z.number().optional().describe('创建时间'),
  updated_at: z.number().optional().describe('更新时间'),
  stats: CourseStatsSchema.optional().describe('课程统计信息'),
  features: z.array(z.string()).default([]).describe('课程支持的功能列表'),
});

/**
 * 课程搜索参数
 * 继承自 AssetQueryBaseSchema 并添加课程特有的字段
 */
export const CourseQuerySchema = AssetQueryBaseSchema.extend({
  teacher_id: z.string().optional().describe('讲师ID'),
  instructor: z.string().optional().describe('讲师名称'),
  excludeIds: z.array(z.string()).optional().describe('排除的课程ID列表'),
});

/**
 * 课程搜索参数类型
 */
export type CourseQuery = z.infer<typeof CourseQuerySchema>;

/**
 * 简化的课程信息类型
 */
export type Course = z.infer<typeof CourseSchema>;

/**
 * 课程章节类型
 */
export type CourseSection = z.infer<typeof CourseSectionSchema>;

/**
 * 课程详情类型
 */
export type CourseDetail = z.infer<typeof CourseDetailSchema>;

/**
 * 按标签分组的课程列表类型
 */
export type CoursesByTag = z.infer<typeof CoursesByTagSchema>;
