{"name": "aizy-client", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "compile": "tsc --noEmit", "tauri": "tauri", "tauri:dev": "tauri dev", "tauri:build": "tauri build", "format": "prettier --write \"{src,tests}/**/*.{js,jsx,ts,tsx}\"", "prepare": "cd .. && husky client/.husky"}, "dependencies": {"@emotion/react": "^11.14.0", "@mantine/carousel": "^8.0.0", "@mantine/core": "^8.0.0", "@mantine/dropzone": "^8.0.0", "@mantine/form": "^8.0.0", "@mantine/hooks": "^8.0.0", "@mantine/notifications": "^8.0.0", "@sentry/react": "^9.15.0", "@tanstack/react-query": "^5.75.5", "@tauri-apps/api": "^2.5.0", "@tauri-apps/plugin-opener": "^2.2.6", "@types/react-syntax-highlighter": "^15.5.13", "@vidstack/react": "^1.12.13", "dayjs": "^1.11.13", "dexie": "^4.0.11", "dexie-react-hooks": "^1.1.7", "embla-carousel-react": "^8.6.0", "motion": "^12.10.0", "qrcode.react": "^4.2.0", "quill": "^2.0.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-markdown": "^10.1.0", "react-router-dom": "^7.5.3", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "rehype-react": "^8.0.0", "remark-gfm": "^4.0.1", "remeda": "^2.21.3", "uuid": "^11.1.0", "yet-another-react-lightbox": "^3.23.0", "zod": "^3.24.4", "zustand": "^5.0.4"}, "devDependencies": {"@eslint/js": "^9.26.0", "@tauri-apps/cli": "^2.5.0", "@testing-library/dom": "^10.4.0", "@testing-library/react": "^16.3.0", "@types/node": "^22.15.14", "@types/react": "^19.1.3", "@types/react-dom": "^19.1.3", "@unocss/preset-attributify": "^66.1.0", "@unocss/preset-icons": "^66.1.0", "@unocss/preset-typography": "^66.1.0", "@unocss/preset-uno": "^66.1.0", "@unocss/preset-web-fonts": "^66.1.0", "@unocss/reset": "^66.1.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.3", "eslint-plugin-prettier": "^5.4.0", "husky": "^9.1.7", "lint-staged": "^15.5.2", "prettier": "^3.5.3", "tsx": "^4.19.4", "typescript": "~5.8.3", "typescript-eslint": "^8.32.0", "unocss": "^66.1.0", "vite": "^6.3.5", "vite-plugin-compression": "^0.5.1", "vitest": "^3.2.0"}, "pnpm": {"onlyBuiltDependencies": ["core-js", "core-js-pure", "esbuild"]}, "lint-staged": {"src-v2/**/*.{ts,tsx}": ["eslint --fix", "prettier --write"]}}