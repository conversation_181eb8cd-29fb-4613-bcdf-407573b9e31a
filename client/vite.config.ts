import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import UnoCSS from 'unocss/vite';
import viteCompression from 'vite-plugin-compression';

// https://vitejs.dev/config/
export default defineConfig(async () => ({
  plugins: [UnoCSS(), react(), viteCompression()],
  resolve: {
    alias: {
      '~': __dirname + '/src',
      '@dify_schemas': __dirname + '/packages/dify_schema/src'
    }
  },
  // Vite options tailored for Tauri development and only applied in `tauri dev` or `tauri build`
  //
  // 1. prevent vite from obscuring rust errors
  clearScreen: false,
  // 2. tauri expects a fixed port, fail if that port is not available
  server: {
    port: 1429,
    strictPort: true,
    host: '0.0.0.0',
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/api/, '')
      }
    },
    allowedHosts: ['*', 'return.cruldra.cn', 'notify.cruldra.cn', 'notify.aiquanyu.cn'],
    hmr: {
      protocol: 'ws',
      host: 'localhost',
      port: 1429
    },
    watch: {
      // 3. tell vite to ignore watching `src-tauri`
      ignored: ['**/src-tauri/**']
    }
  }
}));
