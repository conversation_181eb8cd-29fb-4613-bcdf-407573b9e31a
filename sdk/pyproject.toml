[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "hkjx-billing-sdk"
version = "0.1.3"
description = "Huakunjingxiu Billing SDK"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "curlify2>=2.0.0",
    "dotenv>=0.9.9",
    "httpx>=0.28.1",
    "pydantic>=2.10.6",
]
authors = [{ name = "A1 Huakunjingxiu", email = "<EMAIL>" }]
license = { text = "MIT" }
classifiers = [
    "Programming Language :: Python :: 3",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
]

[project.urls]
"Homepage" = "https://github.com/a1huakunjingxiu/billing-sdk"
"Bug Tracker" = "https://github.com/a1huakunjingxiu/billing-sdk/issues"

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-asyncio>=0.22.0",
    "pytest-mock>=3.14.0",
    "ruff>=0.0.254",
    "pre-commit>=3.7.0",
]

[tool.hatch.build.targets.wheel]
packages = ["billing"]

[tool.hatch.build.targets.sdist]
include = ["billing", "README.md", "LICENSE"]
