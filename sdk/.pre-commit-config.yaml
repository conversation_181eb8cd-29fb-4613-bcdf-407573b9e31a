repos:
- repo: https://github.com/pre-commit/pre-commit-hooks
  rev: v4.5.0
  hooks:
    - id: trailing-whitespace
    - id: end-of-file-fixer
    - id: check-yaml
    - id: check-added-large-files
    - id: check-merge-conflict
    - id: check-symlinks
    - id: mixed-line-ending
    - id: debug-statements

- repo: https://github.com/psf/black
  rev: 24.3.0
  hooks:
    - id: black
      language_version: python3.12

- repo: https://github.com/pycqa/isort
  rev: 5.13.2
  hooks:
    - id: isort
      name: isort (python)
      args: ["--profile", "black"]
