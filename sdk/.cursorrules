---
description: Python 库项目开发最佳实践与规范
---

# Python 库项目开发规范

## 依赖管理

- 使用`uv`管理依赖

## 项目结构

- 项目整体结构如下:

    ```markdown
    $PROJECT_ROOT/
    ├── lib_name/                      # 主应用目录
    │   ├── module1/                 # 模块1
    │   │   ├── __init__.py
    │   │   ├── schemas.py         # 模型
    │   │   ├── utils.py          # 工具函数
    ├── tests/                     # 测试目录
    │   ├── test_module1.py
    │   └── __init__.py
    ├── docs/                      # 文档目录
    │   ├── __init__.py
    │   └── index.md              # 主页
    └── examples/                  # 示例目录
        ├── __init__.py
        └── example1.py           # 示例1
    ```
- 采用模块化设计,每个模块位于`$PROJECT_ROOT/lib_name`目录下,其结构如下:

    ```markdown
    module_name
    ├── sub_module1   # 子模块1
    │   ├── __init__.py
    │   ├── schemas.py  # Pydantic模型
    │   └── utils.py    # 工具函数
    ├── sub_module2   # 子模块2
    │   ├── __init__.py
    │   ├── schemas.py  # Pydantic模型
    │   └── utils.py    # 工具函数
    ├── __init__.py
    └── schemas.py      # 模块公共模型
    ```

## 示例

- 在完成一个功能时,询问是否需要添加示例代码
- 示例代码应位于`$PROJECT_ROOT/examples`目录下,结构如下:

    ```markdown
    examples
    ├── module_name   # 模块名
    │   ├── __init__.py
    │   ├── feature_name_example.py   # 功能名示例
    │   └── ...
    └── ...
    ```
- 示例和测试不是一个东西，示例代码不要用`test_`开头
- 在`$PROJECT_ROOT/examples/README.md`文件中记录示例代码的运行方法,内容格式如下:

    ```markdown
    ## $MODULE_NAME
    ### $EXAMPLE_NAME

    ```powershell
    $env:PYTHONPATH=$PROJECT_ROOT
    uv run examples/module_name/feature_name_example.py
    ```
    ```bash
    export PYTHONPATH=$PROJECT_ROOT
    uv run examples/module_name/feature_name_example.py
    ```
    ```

## 自述文件

- 在`$PROJECT_ROOT/README.md`文件中记录项目的重要信息
- 涉及重要变更时,更新`$PROJECT_ROOT/CHANGELOG.md`文件
