"""
管理密钥保护示例

此示例演示了系统如何保护管理密钥，防止被删除或创建。
"""

import asyncio
from datetime import datetime, timedelta

from billing.keys import KeyCreate

from billing import HkJingXiuBilling


async def main():
    # 初始化客户端
    client = HkJingXiuBilling(
        base_url="http://localhost:20000", admin_key="b30c884a639842d6841f18bc41bd34d3"
    )

    print("1. 尝试创建管理密钥（应该失败）")
    try:
        # 尝试创建管理密钥
        key_create = KeyCreate(
            user_id="test_user",
            service_code="ADM",  # 管理密钥服务代码
            currency_type="CNY",
            credit_limit=1000.0,
            expires_at=datetime.now() + timedelta(days=30),
        )
        key = await client.keys.create_key(key_create)
        print(f"创建管理密钥成功（这不应该发生）: ID: {key.id}")
    except Exception as e:
        print(f"创建管理密钥失败（预期行为）: {e}")
        if "不能创建管理密钥" in str(e):
            print("  系统正确阻止了管理密钥的创建")

    print("\n2. 尝试删除管理密钥（应该失败）")
    try:
        # 尝试删除当前使用的管理密钥
        admin_key_id = "b30c884a639842d6841f18bc41bd34d3"  # 替换为实际的管理密钥ID
        result = await client.keys.delete_key(admin_key_id)
        print(f"删除管理密钥成功（这不应该发生）: {result.success}")
    except Exception as e:
        print(f"删除管理密钥失败（预期行为）: {e}")
        if "不能删除管理密钥" in str(e):
            print("  系统正确阻止了管理密钥的删除")

    print("\n3. 尝试删除普通密钥（应该成功）")
    try:
        # 首先创建一个普通密钥
        key_create = KeyCreate(
            user_id="test_user",
            service_code="AGC",  # 非管理密钥服务代码
            currency_type="CNY",
            credit_limit=100.0,
            expires_at=datetime.now() + timedelta(days=7),
        )
        key = await client.keys.create_key(key_create)
        print(f"创建普通密钥成功: ID: {key.id}")

        # 然后尝试删除它
        result = await client.keys.delete_key(key.id)
        print(f"删除普通密钥成功: {result.success}")
    except Exception as e:
        print(f"操作失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
