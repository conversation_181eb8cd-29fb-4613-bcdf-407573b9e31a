"""
数字人视频生成服务消费示例

此示例演示如何使用数字人视频生成服务消费密钥，按每秒0.067元人民币计费。
"""

import asyncio
import json
from datetime import datetime

from billing.keys import KeyConsumeRequest

from billing import HkJingXiuBilling


async def main():
    # 初始化客户端
    client = HkJingXiuBilling(
        base_url="http://localhost:20000",
        admin_key="2362a49044034ed99a5aca2de262223d",  # 替换为实际的管理密钥
    )

    # 要消费的密钥ID
    key_id = "2330fc2b80be47e1af956c870ad04089"  # 替换为实际的密钥ID

    # 数字人视频生成服务消费详情
    video_details = {
        "request_id": f"req_{int(datetime.now().timestamp())}",
        "video_id": "vid_12345",
        "duration": 34,  # 视频时长（秒）
        "resolution": "1080p",
        "user_id": "user_12345",
        "title": "示例视频",
        "tags": ["demo", "video"],
    }

    print("\n数字人视频生成服务消费示例")
    print(f"详细信息: {json.dumps(video_details, ensure_ascii=False, indent=2)}")
    print(f"视频时长: {video_details['duration']}秒")

    # 计算预期费用
    expected_fee = round(video_details["duration"] * 0.067, 2)  # 每秒0.067元
    print(f"预期费用: {expected_fee}元 (按每秒0.067元计费)")

    consume_request = KeyConsumeRequest(
        key_id=key_id,
        amount=None,  # 不需要指定金额，由策略自动计算
        details=video_details,
    )
    await client.keys.consume_key(consume_request)


if __name__ == "__main__":
    asyncio.run(main())
