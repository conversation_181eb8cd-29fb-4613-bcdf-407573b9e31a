"""
删除密钥示例

此示例演示如何删除密钥。
"""

import asyncio

from billing import HkJingXiuBilling


async def main():
    # 初始化客户端
    client = HkJingXiuBilling(
        base_url="http://localhost:20000", admin_key="2362a49044034ed99a5aca2de262223d"
    )

    # 要删除的密钥ID
    key_id = "09201480facf4ec297589835d83df97d"  # 替换为实际要删除的密钥ID

    # 删除密钥
    try:
        result = await client.keys.delete_key(key_id)
        print(f"删除密钥结果: 成功: {result.success}")
        print(f"  密钥ID: {result.key_id}")
    except Exception as e:
        print(f"删除密钥失败: {e}")
        # 检查是否是管理密钥错误
        if "不能删除管理密钥" in str(e):
            print("  注意: 管理密钥不能被删除，这是一个安全限制")


if __name__ == "__main__":
    asyncio.run(main())
