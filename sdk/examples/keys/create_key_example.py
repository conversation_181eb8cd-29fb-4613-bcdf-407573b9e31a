"""
创建应用和密钥示例

此示例演示如何创建应用和密钥，并获取可用密钥列表。
"""

import asyncio
from datetime import datetime, timedelta

from billing.keys import KeyCreate

from billing import HkJingXiuBilling


async def main():
    # 初始化客户端
    client = HkJingXiuBilling(
        base_url="http://localhost:20000", admin_key="2362a49044034ed99a5aca2de262223d"
    )

    # 创建密钥
    key_create = KeyCreate(
        user_id="5",
        service_code="AGC",  # 使用非管理密钥的服务代码
        scope=[
            "b0dab4c4-f765-49bd-80a1-148f8f2c12af",
            "8558c68c-5075-4037-88c7-e3dfa623cf54",
            "8ea12a75-01be-4151-8f71-d279aa39d113",
        ],  # 作用域，JSON数组格式
        currency_type="UTS",
        credit_limit=1000.0,
        # 计算当前时间后30天的时间戳
        expires_at=datetime.now() + timedelta(days=30),
    )
    print(key_create.model_dump_json())
    key = await client.keys.create_key(key_create)
    print(f"创建密钥成功: ID: {key.id}")

    # 获取可用密钥列表
    available_keys = await client.keys.list_available_keys(
        user_id="5",
        service_code="AGC",
        scope=["b0dab4c4-f765-49bd-80a1-148f8f2c12af"],
        currency_type="UTS",
    )
    print(f"可用密钥数量: {len(available_keys.keys)}")

    # 遍历并打印密钥信息
    for i, key in enumerate(available_keys.keys):
        print(f"\n密钥 {i + 1}:")
        print(f"  ID: {key.id}")
        print(f"  用户ID: {key.user_id}")
        print(f"  服务代码: {key.service_code}")
        print(f"  信用货币类型: {key.currency_type}")
        print(f"  作用域: {key.scope}")
        print(f"  额度: {key.credit_used} / {key.credit_limit}")
        print(f"  过期时间: {key.expires_at}")


if __name__ == "__main__":
    asyncio.run(main())
