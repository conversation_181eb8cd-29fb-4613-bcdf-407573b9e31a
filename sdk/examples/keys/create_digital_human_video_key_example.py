"""
创建数字人视频生成服务密钥示例

此示例演示如何创建数字人视频生成服务密钥，用于数字人视频生成服务的计费。
"""

import asyncio
from datetime import datetime, timedelta

from billing.keys import CurrencyType, KeyCreate

from billing import HkJingXiuBilling


async def main():
    # 初始化客户端
    client = HkJingXiuBilling(
        base_url="http://localhost:20000",
        admin_key="2362a49044034ed99a5aca2de262223d",  # 替换为实际的管理密钥
    )

    # 创建数字人视频生成服务密钥
    key_create = KeyCreate(
        user_id="video_user_1",
        service_code="DGV",  # 数字人视频生成服务代码
        instance_id="instance1,instance2",  # 多个实例ID以逗号隔开
        currency_type=CurrencyType.CNY,  # 使用人民币计费
        credit_limit=1000.0,  # 额度限制1000元
        # 计算当前时间后30天的时间戳
        expires_at=datetime.now() + timedelta(days=30),
    )

    print("创建数字人视频生成服务密钥请求:")
    print(key_create.model_dump_json(indent=2))

    try:
        key = await client.keys.create_key(key_create)
        print("\n创建数字人视频生成服务密钥成功:")
        print(f"  密钥ID: {key.id}")
        print(f"  应用ID: {key.app_id}")
        print(f"  用户ID: {key.user_id}")
        print(f"  服务代码: {key.service_code}")
        print(f"  货币类型: {key.currency_type}")
        print(f"  额度限制: {key.credit_limit}")
        print(f"  过期时间: {key.expires_at}")
    except Exception as e:
        print(f"创建数字人视频生成服务密钥失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
