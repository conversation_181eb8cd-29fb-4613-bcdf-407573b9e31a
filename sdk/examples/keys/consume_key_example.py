"""
消费密钥示例

此示例演示如何消费密钥，并查看密钥的活动记录。
"""

import asyncio

from billing.keys import KeyConsumeRequest

from billing import HkJingXiuBilling


async def main():
    # 初始化客户端

    # 初始化客户端，设置管理密钥
    client = HkJingXiuBilling(
        base_url="http://localhost:20000",
        admin_key="b30c884a639842d6841f18bc41bd34d3",  # 替换为实际的管理密钥
    )
    # 消费密钥
    consume_request = KeyConsumeRequest(
        key_id="71f4207c30bc45fd84fead7fe1e0adfa",
        amount=100.0,
        details={
            "request_id": "req_123456",
            "model": "gpt-4",
            "input_tokens": 50,
            "output_tokens": 150,
        },
    )
    consume_result = await client.keys.consume_key(consume_request)
    print(f"消费密钥结果: 成功: {consume_result.success}")
    if consume_result.success:
        print(f"  密钥ID: {consume_result.key_id}")
        print(f"  消费数量: {consume_result.amount}")
        print(f"  已使用额度: {consume_result.credit_used}")
        print(f"  可用额度: {consume_result.credit_available}")


if __name__ == "__main__":
    asyncio.run(main())
