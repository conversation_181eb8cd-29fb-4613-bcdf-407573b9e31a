"""
按货币类型过滤密钥示例

此示例演示如何按货币类型过滤密钥。
"""

import asyncio

from billing import HkJingXiuBilling


async def main():
    # 初始化客户端
    client = HkJingXiuBilling(
        base_url="http://localhost:20000", admin_key="2362a49044034ed99a5aca2de262223d"
    )

    # 根据作用域查询可用密钥
    scope = [2, 3]
    keys_by_scope = await client.keys.list_available_keys(scope=scope)
    print(f"\n作用域为 {scope} 的密钥数量: {len(keys_by_scope.keys)}")

    # 打印密钥信息
    for i, key in enumerate(keys_by_scope.keys):
        print(f"  密钥 {i + 1}:")
        print(f"    ID: {key.id}")
        print(f"    用户ID: {key.user_id}")
        print(f"    服务代码: {key.service_code}")
        print(f"    信用货币类型: {key.currency_type}")
        print(f"    额度: {key.credit_used} / {key.credit_limit}")
        print(f"    作用域: {key.scope}")


if __name__ == "__main__":
    asyncio.run(main())
