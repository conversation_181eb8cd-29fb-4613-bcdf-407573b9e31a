"""
带详细信息的消费密钥示例

此示例演示如何使用详细信息参数消费密钥，并展示不同场景下的详细信息结构。
"""

import asyncio
import json
from datetime import datetime

from billing.keys import KeyConsumeRequest

from billing import HkJingXiuBilling


async def main():
    # 初始化客户端
    client = HkJingXiuBilling(
        base_url="http://localhost:20000",
        admin_key="b30c884a639842d6841f18bc41bd34d3",  # 替换为实际的管理密钥
    )

    # 要消费的密钥ID
    key_id = "71f4207c30bc45fd84fead7fe1e0adfa"  # 替换为实际的密钥ID

    # 示例1: LLM模型调用的详细信息
    llm_details = {
        "request_id": f"req_{int(datetime.now().timestamp())}",
        "model": "gpt-4",
        "input_tokens": 150,
        "output_tokens": 350,
        "total_tokens": 500,
        "prompt": "简要介绍一下人工智能的发展历史",
        "user_id": "user_12345",
        "session_id": "session_67890",
        "tags": ["education", "ai_history"],
    }

    print("\n示例1: LLM模型调用")
    print(f"详细信息: {json.dumps(llm_details, ensure_ascii=False, indent=2)}")

    try:
        consume_request = KeyConsumeRequest(
            key_id=key_id,
            amount=0.5,  # 消费0.5个单位
            details=llm_details,
        )
        result = await client.keys.consume_key(consume_request)
        print(
            f"消费结果: 成功={result.success}, 已使用={result.credit_used}, 可用={result.credit_available}"
        )
    except Exception as e:
        print(f"消费失败: {e}")

    # 示例2: 图像生成的详细信息
    image_details = {
        "request_id": f"req_{int(datetime.now().timestamp())}",
        "model": "stable-diffusion-xl",
        "resolution": "1024x1024",
        "prompt": "一只可爱的猫咪在阳光下玩耍",
        "negative_prompt": "模糊, 低质量",
        "steps": 50,
        "images_count": 4,
        "user_id": "user_12345",
        "tags": ["image_generation", "animals"],
    }

    print("\n示例2: 图像生成")
    print(f"详细信息: {json.dumps(image_details, ensure_ascii=False, indent=2)}")

    try:
        consume_request = KeyConsumeRequest(
            key_id=key_id,
            amount=2.0,  # 消费2.0个单位
            details=image_details,
        )
        result = await client.keys.consume_key(consume_request)
        print(
            f"消费结果: 成功={result.success}, 已使用={result.credit_used}, 可用={result.credit_available}"
        )
    except Exception as e:
        print(f"消费失败: {e}")

    # 示例3: API调用的详细信息
    api_details = {
        "request_id": f"req_{int(datetime.now().timestamp())}",
        "endpoint": "/api/v1/analyze",
        "method": "POST",
        "ip_address": "*************",
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        "response_time_ms": 235,
        "status_code": 200,
        "user_id": "user_12345",
    }

    print("\n示例3: API调用")
    print(f"详细信息: {json.dumps(api_details, ensure_ascii=False, indent=2)}")

    try:
        consume_request = KeyConsumeRequest(
            key_id=key_id,
            amount=0.1,  # 消费0.1个单位
            details=api_details,
        )
        result = await client.keys.consume_key(consume_request)
        print(
            f"消费结果: 成功={result.success}, 已使用={result.credit_used}, 可用={result.credit_available}"
        )
    except Exception as e:
        print(f"消费失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
