# 示例代码运行指南

本目录包含 billing-sdk 的使用示例代码。

## keys
### create_app_key_example

```bash
$env:PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/keys/create_app_key_example.py
```

```bash
export PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/keys/create_app_key_example.py
```

### create_digital_human_video_key_example

```bash
$env:PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/keys/create_digital_human_video_key_example.py
```

```bash
export PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/keys/create_digital_human_video_key_example.py
```

### consume_key_example

```bash
$env:PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/keys/consume_key_example.py
```

```bash
export PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/keys/consume_key_example.py
```

### consume_digital_human_video_example

```bash
$env:PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/keys/consume_digital_human_video_example.py
```

```bash
export PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/keys/consume_digital_human_video_example.py
```

## product
### create_token_example

```bash
$env:PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/product/create_token_example.py
```

```bash
export PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/product/create_token_example.py
```

## transaction
### create_transaction_example

```bash
$env:PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/transaction/create_transaction_example.py
```

```bash
export PYTHONPATH="D:\Sources\HKJX\billing\sdk"
uv run examples/transaction/create_transaction_example.py
```
