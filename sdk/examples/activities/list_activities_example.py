"""
活动列表示例

此示例演示如何获取活动列表，并支持各种过滤条件。
"""

import asyncio
import json

from billing import HkJingXiuBilling


async def main():
    # 初始化客户端
    client = HkJingXiuBilling(
        base_url="http://localhost:20000",
        admin_key="b30c884a639842d6841f18bc41bd34d3",  # 替换为实际的管理密钥
    )

    # 获取所有活动
    print("\n获取所有活动:")
    activities = await client.activities.list_activities(limit=5)
    print(f"总数: {activities.total}")
    print(f"当前页: {activities.page}")
    print(f"每页数量: {activities.limit}")
    print(f"活动数量: {len(activities.activities)}")

    # 打印活动信息
    for i, activity in enumerate(activities.activities):
        print(f"\n活动 {i + 1}:")
        print(f"  ID: {activity.id}")
        print(f"  密钥ID: {activity.key_id}")
        print(f"  应用ID: {activity.app_id}")
        print(f"  用户ID: {activity.user_id}")
        print(f"  服务代码: {activity.service_code}")
        print(f"  活动类型: {activity.type}")
        print(f"  消费数量: {activity.amount}")
        print(f"  创建时间: {activity.created_at}")
        if activity.details:
            print(
                f"  详情: {json.dumps(activity.details, ensure_ascii=False, indent=2)}"
            )

    # 按应用ID过滤
    print("\n\n按应用ID过滤:")
    app_id = "123456"  # 替换为实际的应用ID
    activities = await client.activities.list_activities(app_id=app_id, limit=3)
    print(f"应用ID为 {app_id} 的活动数量: {len(activities.activities)}")

    # 按用户ID过滤
    print("\n\n按用户ID过滤:")
    user_id = "user_12345"  # 替换为实际的用户ID
    activities = await client.activities.list_activities(user_id=user_id, limit=3)
    print(f"用户ID为 {user_id} 的活动数量: {len(activities.activities)}")

    # 按服务代码过滤
    print("\n\n按服务代码过滤:")
    service_code = "AGC"  # 替换为实际的服务代码
    activities = await client.activities.list_activities(
        service_code=service_code, limit=3
    )
    print(f"服务代码为 {service_code} 的活动数量: {len(activities.activities)}")

    # 按活动类型过滤
    print("\n\n按活动类型过滤:")
    activity_type = "consume"  # 消费类型
    activities = await client.activities.list_activities(
        activity_type=activity_type, limit=3
    )
    print(f"活动类型为 {activity_type} 的活动数量: {len(activities.activities)}")

    # 分页示例
    print("\n\n分页示例:")
    page1 = await client.activities.list_activities(page=1, limit=2)
    print(f"第1页活动数量: {len(page1.activities)}")

    page2 = await client.activities.list_activities(page=2, limit=2)
    print(f"第2页活动数量: {len(page2.activities)}")

    # 确认两页的活动不同
    if page1.activities and page2.activities:
        page1_ids = [a.id for a in page1.activities]
        page2_ids = [a.id for a in page2.activities]
        print(f"第1页活动ID: {page1_ids}")
        print(f"第2页活动ID: {page2_ids}")
        print(f"两页活动是否不同: {set(page1_ids).isdisjoint(set(page2_ids))}")


if __name__ == "__main__":
    asyncio.run(main())
