import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  List,
  ShowButton,
  useTable,
} from "@refinedev/antd";
import { useNavigation } from "@refinedev/core";
import {
  Button,
  Form,
  Input,
  Select,
  Space,
  Table,
  Tag,
  InputNumber,
} from "antd";
import React, { useState } from "react";
import { FaArrowDown, FaBoxes } from "react-icons/fa";
import type { BaseRecord, CrudFilters } from "@refinedev/core";
import { ImportModal } from "../components";
import { SearchForm } from "@/common/components";
import { Agent, AgentType } from "../schemas";
import { entityKey, modeMap, modeOptions } from "../consts";
const AgentList: React.FC = () => {
  const { create } = useNavigation();
  const { tableProps, searchFormProps } = useTable<Agent>({
    resource: entityKey,
    syncWithLocation: false,
    defaultSetFilterBehavior: "replace",
    sorters: {
      initial: [
        {
          field: "sort_order",
          order: "asc",
        },
      ],
    },
    onSearch: (params: any) => {
      const filters: CrudFilters = [];
      if (params.name) {
        filters.push({
          field: "name",
          operator: "contains",
          value: params.name,
        });
      }

      if (params.type) {
        filters.push({
          field: "type",
          operator: "eq",
          value: params.type,
        });
      }

      if (params.tags && params.tags.length > 0) {
        filters.push({
          field: "tags",
          operator: "containss",
          value: params.tags,
        });
      }

      if (params.sort_order !== undefined) {
        filters.push({
          field: "sort_order",
          operator: "eq",
          value: params.sort_order,
        });
      }

      if (params.is_public !== undefined) {
        filters.push({
          field: "is_public",
          operator: "eq",
          value: params.is_public,
        });
      }

      return filters;
    },
  });

  const [importModalVisible, setImportModalVisible] = useState(false);

  // 显示导入模态框
  const showImportModal = () => {
    setImportModalVisible(true);
  };

  // 处理导入成功
  const handleImportSuccess = () => {
    setImportModalVisible(false);
    // 刷新列表
    searchFormProps?.onFinish?.({});
  };

  // 关闭导入模态框
  const handleImportCancel = () => {
    setImportModalVisible(false);
  };

  // 创建捆绑包
  const handleCreateBundle = () => {
    // 导航到创建页面，并设置默认类型为捆绑包
    create("agent");
  };

  return (
    <List
      headerButtons={
        <Space>
          <Button
            type="primary"
            icon={<FaArrowDown />}
            onClick={showImportModal}
          >
            从Dify导入
          </Button>
          <Button
            type="primary"
            icon={<FaBoxes />}
            onClick={handleCreateBundle}
          >
            创建捆绑包
          </Button>
        </Space>
      }
    >
      {/* 搜索表单 */}
      <SearchForm
        searchFormProps={searchFormProps}
        useCard={true}
        layout="inline"
      >
        <Form.Item name="name" label="应用名称">
          <Input placeholder="搜索应用名称" allowClear style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="type" label="应用模式">
          <Select
            placeholder="选择应用模式"
            allowClear
            style={{ width: 140 }}
            options={modeOptions}
          />
        </Form.Item>
        <Form.Item name="tags" label="标签">
          <Select
            placeholder="选择标签"
            allowClear
            mode="multiple"
            style={{ width: 200 }}
            options={[
              { label: "教育", value: "教育" },
              { label: "生活", value: "生活" },
              { label: "情感", value: "情感" },
              { label: "编程", value: "编程" },
              { label: "健康", value: "健康" },
              { label: "财经", value: "财经" },
              { label: "旅游", value: "旅游" },
              { label: "娱乐", value: "娱乐" },
              { label: "科技", value: "科技" },
              { label: "艺术", value: "艺术" },
              { label: "工作", value: "工作" },
              { label: "学习", value: "学习" },
              { label: "运动", value: "运动" },
              { label: "美食", value: "美食" },
              { label: "法律", value: "法律" },
              { label: "医疗", value: "医疗" },
            ]}
          />
        </Form.Item>
        <Form.Item name="sort_order" label="排序顺序">
          <InputNumber placeholder="排序顺序" min={0} style={{ width: 120 }} />
        </Form.Item>
        <Form.Item name="is_public" label="是否公开">
          <Select
            placeholder="选择状态"
            allowClear
            style={{ width: 120 }}
            options={[
              { label: "是", value: true },
              { label: "否", value: false },
            ]}
          />
        </Form.Item>
      </SearchForm>

      <Table {...tableProps} rowKey="id">
        <Table.Column title="ID" dataIndex="id" />
        <Table.Column title="应用名称" dataIndex="name" />
        <Table.Column
          title="应用模式"
          dataIndex="type"
          render={(type: AgentType) => {
            return (
              <Tag color={modeMap[type]?.color}>
                {modeMap[type]?.label || type}
              </Tag>
            );
          }}
        />
        <Table.Column
          title="捆绑包内容"
          dataIndex="bundle_agent_ids"
          render={(bundleAgentIds) => {
            if (
              !bundleAgentIds ||
              !Array.isArray(bundleAgentIds) ||
              bundleAgentIds.length === 0
            )
              return "-";
            return `包含 ${bundleAgentIds.length} 个智能体`;
          }}
        />
        <Table.Column
          title="是否公开"
          dataIndex="is_public"
          render={(isPublic) => (
            <Tag color={isPublic ? "green" : "default"}>
              {isPublic ? "是" : "否"}
            </Tag>
          )}
        />
        <Table.Column
          title="标签"
          dataIndex="tags"
          render={(tags) => {
            if (!tags || tags.length === 0) return "-";
            return (
              <>
                {Array.isArray(tags) &&
                  tags.map((tag, index) => (
                    <Tag key={index} color="cyan">
                      {tag}
                    </Tag>
                  ))}
              </>
            );
          }}
        />
        <Table.Column title="排序" dataIndex="sort_order" sorter />

        <Table.Column
          title="操作"
          dataIndex="actions"
          render={(_, record: BaseRecord) => (
            <Space>
              <EditButton hideText size="small" recordItemId={record.id} />
              <DeleteButton hideText size="small" recordItemId={record.id} />
              <ShowButton hideText size="small" recordItemId={record.id} />
            </Space>
          )}
        />
      </Table>

      {/* 导入模态框 */}
      <ImportModal
        visible={importModalVisible}
        onCancel={handleImportCancel}
        onSuccess={handleImportSuccess}
      />
    </List>
  );
};

export default AgentList;
