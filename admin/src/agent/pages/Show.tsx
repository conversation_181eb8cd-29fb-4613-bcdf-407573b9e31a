import { Show } from "@refinedev/antd";
import { Typography, Card, Tag, Space, Descriptions, List } from "antd";
import React from "react";
import { Agent } from "../schemas";
import { entityKey, modeMap } from "../consts";
import { useShow, useMany } from "@refinedev/core";
const { Title } = Typography;

const AgentShow: React.FC = () => {
  const { query } = useShow<Agent>({
    resource: entityKey,
  });
  const { data, isLoading } = query;
  const record = data?.data;

  // 获取捆绑包中包含的智能体详情
  const { data: bundleAgentsData, isLoading: isBundleAgentsLoading } =
    useMany<Agent>({
      resource: entityKey,
      ids: record?.bundle_agent_ids || [],
      queryOptions: {
        enabled:
          !!record?.bundle_agent_ids && record.bundle_agent_ids.length > 0,
      },
    });

  const renderAgentType = (type?: string) => {
    if (!type) return null;
    return (
      <Tag color={modeMap[type]?.color}>{modeMap[type]?.label || type}</Tag>
    );
  };

  return (
    <Show isLoading={isLoading}>
      {record && (
        <Card>
          <Title level={4}>{record.name}</Title>

          <Descriptions bordered column={2}>
            <Descriptions.Item label="应用ID">{record.id}</Descriptions.Item>

            <Descriptions.Item label="应用模式">
              {renderAgentType(record.type)}
            </Descriptions.Item>

            <Descriptions.Item label="描述">
              {record.description || "-"}
            </Descriptions.Item>

            <Descriptions.Item label="API密钥">
              {record.api_key ? `${record.api_key.substring(0, 8)}...` : "-"}
            </Descriptions.Item>

            {record.type === "bundle" && (
              <Descriptions.Item label="捆绑包内容" span={2}>
                {record.bundle_agent_ids &&
                record.bundle_agent_ids.length > 0 ? (
                  <List
                    size="small"
                    bordered
                    dataSource={bundleAgentsData?.data || []}
                    loading={isBundleAgentsLoading}
                    renderItem={(item: Agent) => (
                      <List.Item>
                        <Space>
                          {item.name}
                          {renderAgentType(item.type)}
                        </Space>
                      </List.Item>
                    )}
                  />
                ) : (
                  "无智能体"
                )}
              </Descriptions.Item>
            )}

            <Descriptions.Item label="标签" span={2}>
              {record.tags && record.tags.length > 0 ? (
                <Space>
                  {record.tags.map((tag, index) => (
                    <Tag key={index} color="cyan">
                      {tag}
                    </Tag>
                  ))}
                </Space>
              ) : (
                "-"
              )}
            </Descriptions.Item>

            {record.icon_url && (
              <Descriptions.Item label="图标" span={2}>
                <img
                  src={record.icon_url}
                  alt={record.name}
                  style={{
                    maxWidth: "100px",
                    maxHeight: "100px",
                    objectFit: "contain",
                  }}
                />
              </Descriptions.Item>
            )}

            <Descriptions.Item label="创建时间">
              {record.created_at &&
                new Date(record.created_at).toLocaleString()}
            </Descriptions.Item>

            <Descriptions.Item label="更新时间">
              {record.updated_at &&
                new Date(record.updated_at).toLocaleString()}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      )}
    </Show>
  );
};

export default AgentShow;
