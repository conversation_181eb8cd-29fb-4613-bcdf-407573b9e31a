import { Create, useForm } from "@refinedev/antd";
import {
  Form,
  Input,
  InputNumber,
  Select,
  Upload,
  Switch,
  Transfer,
} from "antd";
import React from "react";
import { entityKey, modeOptions } from "../consts";
import { FaPlus } from "react-icons/fa";
import { useNavigation } from "@refinedev/core";
import { AgentType } from "../schemas";
import { useBundleAgents } from "../hooks";

const AgentCreate: React.FC = () => {
  const { formProps, saveButtonProps } = useForm({
    resource: entityKey,
  });

  const { goBack } = useNavigation();

  // 获取URL参数中的type
  const urlParams = new URLSearchParams(window.location.search);
  const typeFromUrl = urlParams.get('type');

  // 使用捆绑包hook
  const {
    isBundle,
    selectedAgentIds,
    agentItems,
    isLoadingAgents,
    handleTypeChange,
    handleAgentSelectChange,
  } = useBundleAgents({
    form: formProps.form,
    initialType: typeFromUrl || undefined,
  });

  return (
    <Create
      saveButtonProps={saveButtonProps}
      headerButtons={[
        <button key="cancel" onClick={() => goBack()} className="ant-btn">
          取消
        </button>,
      ]}
    >
      <Form<any> {...formProps} layout="vertical">
        <Form.Item name="id" hidden initialValue={crypto.randomUUID()}>
          <Input />
        </Form.Item>
        <Form.Item
          label="应用名称"
          name="name"
          rules={[{ required: true, message: "请输入应用名称" }]}
        >
          <Input placeholder="请输入应用名称" />
        </Form.Item>

        <Form.Item
          label="应用模式"
          name="type"
          rules={[{ required: true, message: "请选择应用模式" }]}
          initialValue={typeFromUrl || undefined}
        >
          <Select
            placeholder="请选择应用模式"
            options={modeOptions}
            onChange={handleTypeChange}
          />
        </Form.Item>

        {isBundle && (
          <Form.Item
            label="捆绑包内容"
            name="bundle_agent_ids"
            rules={[{ required: isBundle, message: "请选择至少一个智能体" }]}
          >
            <Transfer
              dataSource={agentItems}
              titles={["可选智能体", "已选智能体"]}
              targetKeys={selectedAgentIds}
              onChange={handleAgentSelectChange}
              render={(item) => item.title}
              listStyle={{ width: 300, height: 300 }}
            />
            {isLoadingAgents && <div>加载中...</div>}
          </Form.Item>
        )}

        <Form.Item
          label="是否公开"
          name="is_public"
          valuePropName="checked"
          initialValue={false}
        >
          <Switch checkedChildren="是" unCheckedChildren="否" />
        </Form.Item>

        <Form.Item label="图标" name="icon_url">
          <Upload
            name="file"
            action="/api/file/upload"
            listType="picture-card"
            maxCount={1}
            onChange={({ file }) => {
              if (file.status === "done") {
                formProps.form?.setFieldValue(
                  "icon_url",
                  file.response.data.url
                );
              }
            }}
          >
            <div>
              <FaPlus />
              <div style={{ marginTop: 8 }}>上传</div>
            </div>
          </Upload>
        </Form.Item>

        <Form.Item label="功能描述" name="description">
          <Input.TextArea rows={4} placeholder="请输入功能描述" />
        </Form.Item>

        <Form.Item label="API密钥" name="api_key">
          <Input.Password placeholder="请输入API密钥" />
        </Form.Item>

        <Form.Item label="标签" name="tags" initialValue={[]}>
          <Select
            mode="tags"
            style={{ width: "100%" }}
            placeholder="请输入或选择标签"
            options={[
              { value: "教育", label: "教育" },
              { value: "生活", label: "生活" },
              { value: "情感", label: "情感" },
              { value: "编程", label: "编程" },
              { value: "健康", label: "健康" },
              { value: "财经", label: "财经" },
              { value: "旅游", label: "旅游" },
              { value: "娱乐", label: "娱乐" },
              { value: "科技", label: "科技" },
              { value: "艺术", label: "艺术" },
              { value: "工作", label: "工作" },
              { value: "学习", label: "学习" },
              { value: "运动", label: "运动" },
              { value: "美食", label: "美食" },
              { value: "法律", label: "法律" },
              { value: "医疗", label: "医疗" },
            ]}
          />
        </Form.Item>

        <Form.Item label="排序顺序" name="sort_order" initialValue={0}>
          <InputNumber
            min={0}
            style={{ width: "100%" }}
            placeholder="请输入排序顺序，数字越小越靠前"
          />
        </Form.Item>
      </Form>
    </Create>
  );
};

export default AgentCreate;
