import { API_BASE_URL } from "@/common/env";
import { Pagination, ResponsePayloads } from "@/common/schemas";
import { Agent, ImportAppsResponse } from "./schemas";
import { request } from "@/common/utils";
/**
 * 获取Dify应用列表
 * @param page 页码
 * @param limit 每页数量
 * @param name 名称过滤
 * @returns Dify应用列表
 */
export const getDifyApps = async (
    page: number = 1,
    limit: number = 10,
    name: string = ''
): Promise<ResponsePayloads<Pagination<Agent>>> => {
    const response = await request(`${API_BASE_URL}/agents/from_dify?page=${page}&limit=${limit}&name=${name}`);

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "获取Dify应用列表失败");
    }

    return await response.json();
};

/**
 * 从Dify导入应用
 * @param apps 要导入的应用列表
 * @returns 导入结果
 */
export const importAgents = async (
    agents: Agent[]
): Promise<ResponsePayloads<ImportAppsResponse>> => {
    const response = await request(`${API_BASE_URL}/agents/import`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Accept": "application/json"
        },
        body: JSON.stringify(agents)
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "导入应用失败");
    }

    return await response.json();
}; 