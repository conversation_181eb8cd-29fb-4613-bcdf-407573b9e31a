import { useState, useEffect } from "react";
import { useList } from "@refinedev/core";
import { FormInstance } from "antd";
import { AgentType } from "../schemas";

/**
 * 捆绑包智能体项
 */
export interface AgentItem {
  key: string;
  title: string;
  description?: string;
}

/**
 * 捆绑包智能体 Hook 参数
 */
export interface UseBundleAgentsParams {
  /**
   * 表单实例
   */
  form?: FormInstance;
  /**
   * 初始智能体类型
   */
  initialType?: string;
  /**
   * 初始捆绑包智能体ID列表
   */
  initialBundleAgentIds?: string[];
  /**
   * 当前编辑的智能体ID（用于过滤自身）
   */
  currentAgentId?: string;
}

/**
 * 捆绑包智能体 Hook
 * 用于管理捆绑包智能体的选择和状态
 */
export const useBundleAgents = ({
  form,
  initialType,
  initialBundleAgentIds = [],
  currentAgentId,
}: UseBundleAgentsParams) => {
  // 是否为捆绑包类型
  const [isBundle, setIsBundle] = useState<boolean>(initialType === AgentType.BUNDLE);
  // 已选择的智能体ID列表
  const [selectedAgentIds, setSelectedAgentIds] = useState<string[]>(initialBundleAgentIds);
  // 可选择的智能体列表
  const [agentItems, setAgentItems] = useState<AgentItem[]>([]);

  // 获取智能体列表用于捆绑包选择
  const { data: agentData, isLoading: isLoadingAgents } = useList({
    resource: "agent",
    pagination: {
      mode: "off",
      pageSize: 9999,
    },
    queryOptions: {
      enabled: isBundle,
    },
  });

  // 当智能体列表加载完成时，设置可选项
  useEffect(() => {
    if (agentData?.data && isBundle) {
      // 过滤掉当前编辑的智能体，避免自己包含自己
      const filteredAgents = agentData.data.filter(
        (agent) => agent.id !== currentAgentId
      );

      const items = filteredAgents.map((agent) => ({
        key: agent.id || "", // 确保key是字符串
        title: agent.name as string,
        description: agent.description as string,
      }));
      setAgentItems(items as AgentItem[]);
    }
  }, [agentData, isBundle, currentAgentId]);

  // 处理类型变更
  const handleTypeChange = (value: string) => {
    const newIsBundle = value === AgentType.BUNDLE;
    setIsBundle(newIsBundle);
    
    // 如果不是捆绑包，清空已选智能体
    if (!newIsBundle) {
      setSelectedAgentIds([]);
      form?.setFieldValue('bundle_agent_ids', []);
    }
  };

  // 处理智能体选择变更
  const handleAgentSelectChange = (
    newTargetKeys: any,
    _direction: any,
    _moveKeys: any
  ) => {
    // 将 newTargetKeys 转换为字符串数组
    const targetKeysAsStrings = newTargetKeys.map((key: any) => String(key));
    setSelectedAgentIds(targetKeysAsStrings);
    form?.setFieldValue("bundle_agent_ids", targetKeysAsStrings);
  };

  return {
    isBundle,
    selectedAgentIds,
    agentItems,
    isLoadingAgents,
    handleTypeChange,
    handleAgentSelectChange,
  };
};

export default useBundleAgents;
