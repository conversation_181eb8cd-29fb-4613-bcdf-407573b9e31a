import { Button, Input, Modal, Space, Table, message } from "antd";
import React, { useState } from "react";
import type { TablePaginationConfig } from "antd/es/table";
import { Tag } from "antd";
import { getDifyApps, importAgents } from "../api";
import { Agent, AgentType } from "../schemas";
import { modeMap } from "../consts";

interface ImportModalProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess: () => void;
}

const ImportModal: React.FC<ImportModalProps> = ({ visible, onCancel, onSuccess }) => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [selectedAppIds, setSelectedAppIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<TablePaginationConfig>({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 获取Dify应用列表
  const fetchAgents = async (page: number, limit: number, name: string = '') => {
    setLoading(true);
    try {
      const data = await getDifyApps(page, limit, name);
      setAgents(data.data?.data || []);
      setPagination({
        current: data.data?.page_no || 1,
        pageSize: data.data?.page_size || 10,
        total: data.data?.total || 0
      });
    } catch (error) {
      console.error('获取Dify应用列表失败:', error);
      message.error('获取Dify应用列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件显示时加载数据
  React.useEffect(() => {
    if (visible) {
      fetchAgents(1, 10);
    }
  }, [visible]);

  // 处理分页变化
  const handleTableChange = (newPagination: TablePaginationConfig) => {
    fetchAgents(newPagination.current || 1, newPagination.pageSize || 10);
  };

  // 处理选择变化
  const handleSelectChange = (selectedRowKeys: React.Key[]) => {
    setSelectedAppIds(selectedRowKeys as string[]);
  };

  // 搜索Dify应用
  const searchDifyApps = (value: string) => {
    fetchAgents(1, pagination.pageSize as number || 10, value);
  };

  // 处理导入
  const handleImport = async () => {
    if (selectedAppIds.length === 0) {
      message.warning('请至少选择一个应用');
      return;
    }

    setLoading(true);
    try {
      // 获取选中的应用数据
      const selectedApps = agents.filter(app => selectedAppIds.includes(app.id));

      // 调用导入API
      const result = await importAgents(selectedApps);

      // 统计导入结果
      const imported = result.data?.imported_apps?.filter(app => app.status === 'imported').length || 0;
      const alreadyExists = result.data?.imported_apps?.filter(app => app.status === 'already_exists').length || 0;

      let successMessage = `成功导入 ${imported} 个应用`;
      if (alreadyExists > 0) {
        successMessage += `，${alreadyExists} 个应用已存在`;
      }

      message.success(successMessage);
      setSelectedAppIds([]);
      onSuccess();
    } catch (error) {
      console.error('导入应用失败:', error);
      message.error('导入应用失败: ' + (error as Error).message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="从Dify导入应用"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button
          key="import"
          type="primary"
          loading={loading}
          onClick={handleImport}
          disabled={selectedAppIds.length === 0}
        >
          导入选中的应用 ({selectedAppIds.length})
        </Button>,
      ]}
    >
      <div style={{ marginBottom: 16 }}>
        <Input.Search
          placeholder="搜索Dify应用"
          onSearch={searchDifyApps}
          style={{ width: 300 }}
          allowClear
        />
      </div>
      <Table
        rowKey="id"
        dataSource={agents}
        loading={loading}
        pagination={pagination}
        onChange={handleTableChange}
        rowSelection={{
          type: 'checkbox',
          selectedRowKeys: selectedAppIds,
          onChange: handleSelectChange,
        }}
      >
        <Table.Column
          title="应用名称"
          dataIndex="name"
        />
        <Table.Column
          title="描述"
          dataIndex="description"
          ellipsis={true}
        />
        <Table.Column
          title="模式"
          dataIndex="type"
          render={(type) => {
            return <Tag color={modeMap[type as AgentType]?.color || 'default'}>{modeMap[type as AgentType]?.label || type}</Tag>;
          }}
        />
      </Table>
    </Modal>
  );
};

export default ImportModal;