// 智能体类型
export enum AgentType {
  CHAT = "chat",
  AGENT_CHAT = "agent-chat",
  WORKFLOW = "workflow",
  COMPLETION = "completion",
  BUNDLE = "bundle", // 智能体捆绑包类型
}

export interface AppImportStatus {
  id: string;
  status: "imported" | "already_exists";
}

// 导入应用的响应类型
export interface ImportAppsResponse {
  imported_apps: AppImportStatus[];
}

// 智能体类型
export interface Agent {
  id: string;
  name: string;
  bundle_agent_ids: string[];
  icon_url?: string;
  description: string;
  type: AgentType;
  api_key?: string;
  owner_id?: number;
  tags: string[];
  sort_order: number;
  is_public: boolean;
  created_at: Date;
  updated_at: Date;
}
