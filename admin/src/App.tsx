import { Authenticated, Refine } from "@refinedev/core";
import { DevtoolsPanel, DevtoolsProvider } from "@refinedev/devtools";
import { RefineKbar, RefineKbarProvider } from "@refinedev/kbar";
import {
    ErrorComponent,
    ThemedLayoutV2,
    ThemedSiderV2,
    useNotificationProvider,
} from "@refinedev/antd";
import "@refinedev/antd/dist/reset.css";

import routerBindings, {
    CatchAllNavigate,
    DocumentTitleHandler,
    NavigateToResource,
    UnsavedChangesNotifier,
} from "@refinedev/react-router";
import { App as AntdApp } from "antd";
import { HashRouter, Outlet, Route, Routes } from "react-router";
import { authProvider } from "@/admin/providers";
import { Header } from "@/common/components";
import { ColorModeContextProvider } from "@/common/contexts";
import { Login } from "@/admin/pages/Login";
import { customDataProvider } from "@/common/dataProvider";
import { AdminList, AdminCreate, AdminEdit, AdminShow } from "@/admin/pages";
import { AppList, AppCreate, AppEdit, AppShow } from "./app/pages";
import { KeyList, KeyCreate, KeyEdit, KeyShow } from "./key/pages";
import { ServiceList, ServiceCreate, ServiceEdit, ServiceShow } from "./service/pages";
import { ActivityList, ActivityShow, ActivityDashboard } from "./activity/pages";

import { FaUserShield, FaRegWindowRestore, FaKey, FaCogs, FaHistory } from "react-icons/fa";
import { IoLogoOctocat } from "react-icons/io";
import { APP_TITLE } from "./common";

function App() {
    return (
        <HashRouter>
            <RefineKbarProvider>
                <ColorModeContextProvider>
                    <AntdApp>
                        <DevtoolsProvider>
                            <Refine
                                dataProvider={customDataProvider()}
                                notificationProvider={useNotificationProvider}
                                authProvider={authProvider}
                                routerProvider={routerBindings}
                                resources={[
                                    
                                    {
                                        name: "activityDashboard",
                                        list: "/activity-dashboard",
                                        meta: {
                                            label: "Dashboard面板",
                                            icon: <FaHistory />,
                                        },
                                    },
                                    {
                                        name: "admin",
                                        list: "/admin",
                                        show: "/admin/show/:id",
                                        create: "/admin/create",
                                        edit: "/admin/edit/:id",
                                        meta: {
                                            label: "管理员",
                                            icon: <FaUserShield />,
                                        },
                                    },
                                    {
                                        name: "app",
                                        list: "/app",
                                        show: "/app/show/:id",
                                        create: "/app/create",
                                        edit: "/app/edit/:id",
                                        meta: {
                                            label: "应用管理",
                                            icon: <FaRegWindowRestore />,
                                        },
                                    },
                                    {
                                        name: "service",
                                        list: "/service",
                                        show: "/service/show/:id",
                                        create: "/service/create",
                                        edit: "/service/edit/:id",
                                        meta: {
                                            label: "服务管理",
                                            icon: <FaCogs />,
                                        },
                                    },
                                    {
                                        name: "key",
                                        list: "/key",
                                        show: "/key/show/:id",
                                        create: "/key/create",
                                        edit: "/key/edit/:id",
                                        meta: {
                                            label: "密钥管理",
                                            icon: <FaKey />,
                                        },
                                    },
                                    {
                                        name: "activity",
                                        list: "/activity",
                                        show: "/activity/show/:id",
                                        meta: {
                                            label: "活动记录",
                                            icon: <FaHistory />,
                                        },
                                    }
                                ]}
                                options={{
                                    syncWithLocation: true,
                                    warnWhenUnsavedChanges: true,
                                    useNewQueryKeys: true,
                                    projectId: "RUkgr3-hlbLLB-lUpTgX",
                                    title: { text: APP_TITLE, icon: <IoLogoOctocat /> },
                                }}
                            >
                                <Routes>
                                    <Route
                                        element={
                                            <Authenticated
                                                key="authenticated-inner"
                                                fallback={<CatchAllNavigate to="/login" />}
                                            >
                                                <ThemedLayoutV2
                                                    Header={Header}
                                                    Sider={(props) => <ThemedSiderV2 {...props} fixed />}
                                                >
                                                    <Outlet />
                                                </ThemedLayoutV2>
                                            </Authenticated>
                                        }
                                    >
                                        <Route
                                            index
                                            element={<NavigateToResource resource="activityDashboard" />}
                                        />
                                        <Route path="/admin">
                                            <Route index element={<AdminList />} />
                                            <Route path="create" element={<AdminCreate />} />
                                            <Route path="edit/:id" element={<AdminEdit />} />
                                            <Route path="show/:id" element={<AdminShow />} />
                                        </Route>
                                        <Route path="/app">
                                            <Route index element={<AppList />} />
                                            <Route path="create" element={<AppCreate />} />
                                            <Route path="edit/:id" element={<AppEdit />} />
                                            <Route path="show/:id" element={<AppShow />} />
                                        </Route>
                                        <Route path="/service">
                                            <Route index element={<ServiceList />} />
                                            <Route path="create" element={<ServiceCreate />} />
                                            <Route path="edit/:id" element={<ServiceEdit />} />
                                            <Route path="show/:id" element={<ServiceShow />} />
                                        </Route>
                                        <Route path="/key">
                                            <Route index element={<KeyList />} />
                                            <Route path="create" element={<KeyCreate />} />
                                            <Route path="edit/:id" element={<KeyEdit />} />
                                            <Route path="show/:id" element={<KeyShow />} />
                                        </Route>
                                        <Route path="/activity">
                                            <Route index element={<ActivityList />} />
                                            <Route path="show/:id" element={<ActivityShow />} />
                                        </Route>
                                        <Route path="/activity-dashboard">
                                            <Route index element={<ActivityDashboard />} />
                                        </Route>
                                        <Route path="*" element={<ErrorComponent />} />
                                    </Route>
                                    <Route
                                        element={
                                            <Authenticated
                                                key="authenticated-outer"
                                                fallback={<Outlet />}
                                            >
                                                <NavigateToResource />
                                            </Authenticated>
                                        }
                                    >
                                        <Route path="/login" element={<Login />} />
                                    </Route>
                                </Routes>

                                <RefineKbar />
                                <UnsavedChangesNotifier />
                                <DocumentTitleHandler />
                            </Refine>
                            <DevtoolsPanel />
                        </DevtoolsProvider>
                    </AntdApp>
                </ColorModeContextProvider>
            </RefineKbarProvider>
        </HashRouter>
    );
}

export default App;
