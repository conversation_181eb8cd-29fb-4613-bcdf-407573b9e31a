import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>Button,
  List,
  ShowButton,
  useTable,
} from "@refinedev/antd";
import type { BaseRecord, CrudFilters } from "@refinedev/core";
import { Divider, Form, Input, Space, Table } from "antd";
import { SearchForm } from "@/common/components";
import { entityKey } from "../consts";
import { App } from "../schemas";

interface SearchFormValues {
  app_id?: string;
  name?: string;
}

export const AppList = () => {
  const { tableProps, searchFormProps } = useTable<App>({
    resource: entityKey,
    syncWithLocation: false,
    defaultSetFilterBehavior: "replace",
    onSearch: (values) => {
      const filters: CrudFilters = [];
      const data = values as SearchFormValues;

      // 构建搜索过滤器
      if (data.app_id) {
        filters.push({
          field: "app_id",
          operator: "contains",
          value: data.app_id,
        });
      }

      if (data.name) {
        filters.push({
          field: "name",
          operator: "contains",
          value: data.name,
        });
      }

      return filters;
    },
  });

  return (
    <List>
      {/* 使用通用搜索表单组件 */}
      <SearchForm
        searchFormProps={searchFormProps}
        useCard={true}
        layout="inline"
      >
        <Form.Item label="应用ID" name="app_id">
          <Input placeholder="请输入应用ID" allowClear />
        </Form.Item>
        <Form.Item label="应用名称" name="name">
          <Input placeholder="请输入应用名称" allowClear />
        </Form.Item>
      </SearchForm>

      <Divider className="my-4" />

      <Table {...tableProps} rowKey="app_id">
        <Table.Column dataIndex="app_id" title="应用ID" />
        <Table.Column dataIndex="name" title="应用名称" />
        <Table.Column 
          dataIndex="description" 
          title="应用描述" 
          ellipsis
          render={(value) => value || '-'}
        />
        <Table.Column
          dataIndex="created_at"
          title="创建时间"
          render={(value) => new Date(value).toLocaleString()}
        />
        <Table.Column
          dataIndex="updated_at"
          title="更新时间"
          render={(value) => new Date(value).toLocaleString()}
        />
        <Table.Column
          title="操作"
          dataIndex="actions"
          render={(_, record: BaseRecord) => (
            <Space>
              <EditButton hideText size="small" recordItemId={record.app_id} />
              <ShowButton hideText size="small" recordItemId={record.app_id} />
              <DeleteButton hideText size="small" recordItemId={record.app_id} />
            </Space>
          )}
        />
      </Table>
    </List>
  );
};
