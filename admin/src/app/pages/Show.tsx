import React, { useState } from "react";
import { useShow, useResource, useList, useCreate } from "@refinedev/core";
import {
  Show,
  DateField,
  TextField,
} from "@refinedev/antd";
import {
  Typography,
  Tabs,
  Tag,
  Space,
  Table,
  Card,
  Descriptions,
  Row,
  Col,
  Button,
  Modal,
  Form,
  Input,
  Select,
  DatePicker
} from "antd";
import { AppDetail, Key, Activity, KeyType } from "../schemas";
import { entityKey } from "../consts";



export const AppShow: React.FC = () => {
  const { id } = useResource();
  const { queryResult } = useShow<AppDetail>({
    resource: entityKey,
    id,
  });

  // 使用 useList 钩子获取密钥列表
  const { data: keysData, isLoading: keysLoading, refetch: refetchKeys } = useList<Key>({
    resource: "key",
    filters: [
      {
        field: "app_id",
        operator: "eq",
        value: id,
      },
    ],
  });

  // 使用 useCreate 钩子创建密钥
  const { mutate: createKey } = useCreate();

  const [activities, setActivities] = useState<Activity[]>([]);
  const [selectedKeyId, setSelectedKeyId] = useState<string | null>(null);
  const [activitiesLoading, setActivitiesLoading] = useState<boolean>(false);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);

  const [form] = Form.useForm();

  const { data, isLoading } = queryResult;
  const record = data?.data;

  // 从 keysData 中提取密钥列表
  const keys = keysData?.data || [];



  // 获取密钥的活动记录
  const fetchActivities = (keyId: string) => {
    setSelectedKeyId(keyId);
    setActivitiesLoading(true);

    // 使用 fetch 直接请求活动记录
    fetch(`/api/keys/${keyId}/activities`)
      .then(response => response.json())
      .then(data => {
        setActivities(data.data || []);
      })
      .catch(error => {
        console.error("获取活动记录失败:", error);
      })
      .finally(() => {
        setActivitiesLoading(false);
      });
  };

  // 创建密钥
  const handleCreateKey = () => {
    setIsModalVisible(true);
  };

  const handleModalOk = () => {
    form.validateFields()
      .then(values => {
        // 使用 useCreate 钩子创建密钥
        createKey({
          resource: "key",
          values: {
            ...values,
            app_id: id,
          },
        });
        setIsModalVisible(false);
        form.resetFields();
        // 刷新密钥列表
        refetchKeys();
      })
      .catch(info => {
        console.log("表单验证失败:", info);
      });
  };

  const handleModalCancel = () => {
    setIsModalVisible(false);
    form.resetFields();
  };

  if (isLoading) {
    return <div>加载中...</div>;
  }

  if (!record) {
    return <div>未找到应用</div>;
  }

  return (
    <Show
      isLoading={isLoading}
      title={<Typography.Title level={3}>应用详情: {record.name}</Typography.Title>}
      headerButtons={({ defaultButtons }) => (
        <>
          {defaultButtons}
          <Button type="primary" onClick={handleCreateKey}>创建密钥</Button>
        </>
      )}
    >
      <Row gutter={24}>
        <Col span={24}>
          <Card title="基本信息" className="mb-4">
            <Descriptions column={{ xs: 1, sm: 2, md: 3 }}>
              <Descriptions.Item label="应用ID">
                <TextField value={record.app_id} />
              </Descriptions.Item>
              <Descriptions.Item label="应用名称">
                <TextField value={record.name} />
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                <DateField format="YYYY-MM-DD HH:mm:ss" value={record.created_at} />
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                <DateField format="YYYY-MM-DD HH:mm:ss" value={record.updated_at} />
              </Descriptions.Item>
              <Descriptions.Item label="应用描述" span={3}>
                {record.description || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col span={24}>
          <Tabs defaultActiveKey="keys">
            <Tabs.TabPane tab={`密钥 (${keys.length})`} key="keys">
              <Card loading={keysLoading}>
                {keys && keys.length > 0 ? (
                  <Table
                    dataSource={keys}
                    rowKey="id"
                    pagination={{ pageSize: 5 }}
                    columns={[
                      { title: "密钥ID", dataIndex: "id", key: "id" },
                      { title: "用户ID", dataIndex: "user_id", key: "user_id" },
                      {
                        title: "类型",
                        dataIndex: "type",
                        key: "type",
                        render: (type) => {
                          const typeMap: Record<string, { text: string, color: string }> = {
                            [KeyType.AGENT]: { text: "智能体", color: "blue" },
                            [KeyType.CHANJING]: { text: "数字人", color: "green" }
                          };
                          return <Tag color={typeMap[type]?.color || "default"}>{typeMap[type]?.text || type}</Tag>;
                        }
                      },
                      { title: "密钥", dataIndex: "secret", key: "secret", ellipsis: true },
                      {
                        title: "额度",
                        key: "credit",
                        render: (_, record) => `${record.credit_used} / ${record.credit_limit}`
                      },
                      {
                        title: "过期时间",
                        dataIndex: "expires_at",
                        key: "expires_at",
                        render: (text) => text ? new Date(text).toLocaleString() : '永不过期'
                      },
                      {
                        title: "创建时间",
                        dataIndex: "created_at",
                        key: "created_at",
                        render: (text) => new Date(text).toLocaleString()
                      },
                      {
                        title: "操作",
                        key: "action",
                        render: (_, record) => (
                          <Space>
                            <Button size="small" onClick={() => fetchActivities(String(record.id))}>查看活动</Button>
                          </Space>
                        )
                      }
                    ]}
                  />
                ) : (
                  <div>暂无密钥数据</div>
                )}
              </Card>
            </Tabs.TabPane>

            {selectedKeyId && (
              <Tabs.TabPane tab="活动记录" key="activities">
                <Card loading={activitiesLoading}>
                  {activities && activities.length > 0 ? (
                    <Table
                      dataSource={activities}
                      rowKey="id"
                      pagination={{ pageSize: 5 }}
                      columns={[
                        { title: "活动ID", dataIndex: "id", key: "id" },
                        {
                          title: "类型",
                          dataIndex: "type",
                          key: "type",
                          render: (type) => {
                            const typeMap: Record<string, { text: string, color: string }> = {
                              "verify": { text: "验证", color: "blue" },
                              "consume": { text: "消费", color: "green" }
                            };
                            return <Tag color={typeMap[type]?.color || "default"}>{typeMap[type]?.text || type}</Tag>;
                          }
                        },
                        { title: "数量", dataIndex: "amount", key: "amount" },
                        {
                          title: "元数据",
                          dataIndex: "metadata",
                          key: "metadata",
                          render: (metadata) => metadata ? JSON.stringify(metadata) : '-'
                        },
                        {
                          title: "创建时间",
                          dataIndex: "created_at",
                          key: "created_at",
                          render: (text) => new Date(text).toLocaleString()
                        }
                      ]}
                    />
                  ) : (
                    <div>暂无活动记录</div>
                  )}
                </Card>
              </Tabs.TabPane>
            )}
          </Tabs>
        </Col>
      </Row>

      {/* 创建密钥的模态框 */}
      <Modal
        title="创建密钥"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="user_id"
            label="用户ID"
            rules={[{ required: true, message: "请输入用户ID" }]}
          >
            <Input placeholder="请输入用户ID" />
          </Form.Item>
          <Form.Item
            name="service_id"
            label="服务"
            rules={[{ required: true, message: "请选择服务" }]}
          >
            <Select placeholder="请选择服务">
              <Select.Option value={1}>智能助手</Select.Option>
              <Select.Option value={2}>数字人平台</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="credit_limit"
            label="额度限制"
            rules={[{ required: true, message: "请输入额度限制" }]}
          >
            <Input type="number" placeholder="请输入额度限制" />
          </Form.Item>
          <Form.Item
            name="expires_at"
            label="过期时间"
          >
            <DatePicker showTime style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </Show>
  );
};
