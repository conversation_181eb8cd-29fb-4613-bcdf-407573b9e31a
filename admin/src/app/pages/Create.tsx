import React from "react";
import { Create, useForm } from "@refinedev/antd";
import { Form, Input } from "antd";
import { App } from "../schemas";
import { entityKey } from "../consts";

export const AppCreate: React.FC = () => {
  const { formProps, saveButtonProps } = useForm<App>({
    resource: entityKey,
  });

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Form.Item
          label="应用名称"
          name="name"
          rules={[{ required: true, message: "请输入应用名称" }]}
        >
          <Input placeholder="请输入应用名称" />
        </Form.Item>

        <Form.Item
          label="应用描述"
          name="description"
        >
          <Input.TextArea rows={4} placeholder="请输入应用描述" />
        </Form.Item>
      </Form>
    </Create>
  );
};
