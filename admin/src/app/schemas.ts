// 应用相关类型定义

// 应用实体
export interface App {
  app_id: string; // 应用ID，最多6位字符串
  name: string; // 应用名称
  description?: string; // 应用描述
  created_at: Date; // 创建时间
  updated_at: Date; // 更新时间
}

// 应用详情（包含关联数据）
export interface AppDetail extends App {
  keys?: any[]; // 密钥列表
  activities?: any[]; // 活动记录
}

// 应用创建请求
export interface AppCreateRequest {
  name: string; // 应用名称
  description?: string; // 应用描述
}

// 应用更新请求
export interface AppUpdateRequest {
  name?: string; // 应用名称
  description?: string; // 应用描述
}

// 密钥类型
export enum KeyType {
  AGENT = "agent", // 智能体
  CHANJING = "chanjing" // 数字人
}

// 密钥实体
export interface Key {
  id: number; // 密钥ID
  app_id: string; // 应用ID
  user_id: string; // 用户ID
  type: KeyType; // 密钥类型
  secret: string; // 密钥
  credit_limit: number; // 额度限制
  credit_used: number; // 已使用额度
  expires_at?: Date; // 过期时间
  created_at: Date; // 创建时间
  updated_at: Date; // 更新时间
}

// 活动类型
export enum ActivityType {
  VERIFY = "verify", // 验证
  CONSUME = "consume" // 消费
}

// 活动实体
export interface Activity {
  id: number; // 活动ID
  key_id: number; // 密钥ID
  type: ActivityType; // 活动类型
  amount: number; // 消费数量
  metadata?: any; // 元数据
  created_at: Date; // 创建时间
}
