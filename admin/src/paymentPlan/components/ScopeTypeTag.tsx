import React from "react";
import { Tag } from "antd";
import { ScopeType } from "../schemas";
import { scopeTypeMap } from "../consts";

interface ScopeTypeTagProps {
  type: ScopeType;
}

export const ScopeTypeTag: React.FC<ScopeTypeTagProps> = ({ type }) => {
  const config = scopeTypeMap[type] || { label: type, color: "default" };
  
  return (
    <Tag color={config.color}>
      {config.label}
    </Tag>
  );
};
