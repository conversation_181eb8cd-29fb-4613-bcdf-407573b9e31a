import { ScopeType } from "./schemas";

export const entityKey = "paymentPlan";

// 适用范围类型映射
export const scopeTypeMap: Record<string, { label: string; color: string }> = {
  [ScopeType.APP]: { label: "智能体", color: "blue" },
  [ScopeType.COURSE]: { label: "课程", color: "green" },
  [ScopeType.BUNDLE]: { label: "捆绑包", color: "purple" },
};

// 适用范围类型选项
export const scopeTypeOptions = [
  { label: "智能体", value: ScopeType.APP },
  { label: "课程", value: ScopeType.COURSE },
  { label: "捆绑包", value: ScopeType.BUNDLE },
];
