import React, { useState, useEffect } from "react";
import { Create, useForm } from "@refinedev/antd";
import { Form, Input, InputNumber, Select, Switch, Transfer } from "antd";
import type { TransferDirection, TransferProps } from "antd/es/transfer";
import { useGo, useList } from "@refinedev/core";
import { PaymentPlan, ScopeType } from "../schemas";
import { entityKey, scopeTypeOptions } from "../consts";
import TextArea from "antd/es/input/TextArea";

interface ScopeItem {
  key: string;
  title: string;
  description?: string;
}

export const PaymentPlanCreate: React.FC = () => {
  const go = useGo();
  const [scopeType, setScopeType] = useState<ScopeType>(ScopeType.APP);
  const [scopeItems, setScopeItems] = useState<ScopeItem[]>([]);
  const [selectedScopeIds, setSelectedScopeIds] = useState<string[]>([]);

  const { formProps, saveButtonProps } = useForm<PaymentPlan>({
    resource: entityKey,
    redirect: "list",
    onMutationSuccess: () => {
      go({ to: "/paymentPlan" });
    },
    onMutationError: (error) => {
      console.error("Error creating payment plan:", error);
    },
  });

  // 获取智能体列表
  const { data: agentData, isLoading: isLoadingAgents } = useList({
    resource: "agent",
    pagination: {
      mode: "off",
      pageSize: 9999,
    },
    queryOptions: {
      enabled: scopeType === ScopeType.APP || scopeType === ScopeType.BUNDLE,
    },
  });

  // 获取课程列表
  const { data: courseData, isLoading: isLoadingCourses } = useList({
    resource: "course",
    pagination: {
      mode: "off",
      pageSize: 9999,
    },
    queryOptions: {
      enabled: scopeType === ScopeType.COURSE,
    },
  });

  // 当范围类型变化时，更新可选项
  useEffect(() => {
    if (scopeType === ScopeType.APP && agentData?.data) {
      const items = agentData.data.map((agent: any) => ({
        key: String(agent.id), // 确保 key 是字符串
        title: agent.name,
        description: agent.description,
      }));
      setScopeItems(items);
      setSelectedScopeIds([]);
    } else if (scopeType === ScopeType.COURSE && courseData?.data) {
      const items = courseData.data.map((course: any) => ({
        key: String(course.id), // 确保 key 是字符串
        title: course.title,
        description: course.description,
      }));
      setScopeItems(items);
      setSelectedScopeIds([]);
    } else if (scopeType === ScopeType.BUNDLE && agentData?.data) {
      // 捆绑包也使用智能体列表
      const items = agentData.data.map((agent: any) => ({
        key: String(agent.id), // 确保 key 是字符串
        title: agent.name,
        description: agent.description,
      }));
      setScopeItems(items);
      setSelectedScopeIds([]);
    } else {
      setScopeItems([]);
      setSelectedScopeIds([]);
    }
  }, [scopeType, agentData, courseData]);

  const handleScopeChange: TransferProps["onChange"] = (newTargetKeys) => {
    setSelectedScopeIds(newTargetKeys.map((key) => String(key)));
  };

  // 自定义表单提交方法
  const onFinish = (values: any) => {
    // 转换字段名称
    const transformedValues = {
      ...values,
      scope_ids: selectedScopeIds,
    };

    // 调用原始的表单提交方法
    return formProps.onFinish?.(transformedValues);
  };

  // 替换原始的onFinish
  const customFormProps = {
    ...formProps,
    onFinish,
  };

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form<any> {...customFormProps} layout="vertical">
        <Form.Item
          label="计划名称"
          name="name"
          rules={[{ required: true, message: "请输入计划名称" }]}
        >
          <Input placeholder="请输入计划名称" />
        </Form.Item>

        <Form.Item label="计划描述" name="description">
          <TextArea placeholder="请输入计划描述" rows={4} />
        </Form.Item>

        <Form.Item
          label="价格"
          name="price"
          rules={[{ required: true, message: "请输入价格" }]}
          initialValue={0}
        >
          <InputNumber
            min={0}
            step={0.01}
            precision={2}
            style={{ width: "100%" }}
            prefix="¥"
            placeholder="请输入价格"
          />
        </Form.Item>

        <Form.Item label="原价" name="original_price" initialValue={0}>
          <InputNumber
            min={0}
            step={0.01}
            precision={2}
            style={{ width: "100%" }}
            prefix="¥"
            placeholder="请输入原价"
          />
        </Form.Item>

        <Form.Item
          label="有效期（天）"
          name="validity_period"
          initialValue={0}
          tooltip="0表示永久有效"
        >
          <InputNumber
            min={0}
            style={{ width: "100%" }}
            placeholder="请输入有效期（天）"
          />
        </Form.Item>

        <Form.Item
          label="适用范围类型"
          name="scope_type"
          rules={[{ required: true, message: "请选择适用范围类型" }]}
          initialValue={ScopeType.APP}
        >
          <Select
            placeholder="请选择适用范围类型"
            options={scopeTypeOptions}
            onChange={(value) => setScopeType(value as ScopeType)}
          />
        </Form.Item>

        <Form.Item label="适用范围" required>
          {/* Transfer 组件不支持 loading 属性，移除该属性 */}
          <Transfer
            dataSource={scopeItems}
            titles={["可选项", "已选项"]}
            targetKeys={selectedScopeIds}
            onChange={handleScopeChange}
            render={(item) => item.title}
            listStyle={{ width: 300, height: 300 }}
          />
        </Form.Item>

        <Form.Item
          label="是否激活"
          name="is_active"
          valuePropName="checked"
          initialValue={true}
        >
          <Switch />
        </Form.Item>
      </Form>
    </Create>
  );
};
