import React from "react";
import {
  useTable,
  <PERSON>,
  <PERSON><PERSON>ield,
  CreateButton,
  EditButton,
  DeleteButton,
  ShowButton,
} from "@refinedev/antd";
import { BaseRecord, CrudFilters, useGo } from "@refinedev/core";
import { Table, Space, Card, Button, Form, Input, Select, Tag } from "antd";
import { PaymentPlan, ScopeType } from "../schemas";
import { ScopeTypeTag } from "../components";
import { SearchForm } from "@/common/components";
import { entityKey, scopeTypeOptions } from "../consts";

export const PaymentPlanList: React.FC = () => {
  const go = useGo();

  const { tableProps, searchFormProps } = useTable<PaymentPlan>({
    resource: entityKey,
    syncWithLocation: true,
    defaultSetFilterBehavior: "replace",
    onSearch: (params: any) => {
      const filters: CrudFilters = [];

      const { name, scope_type, is_active } = params;

      if (name) {
        filters.push({
          field: "name",
          operator: "contains",
          value: name,
        });
      }

      if (scope_type) {
        filters.push({
          field: "scope_type",
          operator: "eq",
          value: scope_type,
        });
      }

      if (is_active !== undefined) {
        filters.push({
          field: "is_active",
          operator: "eq",
          value: is_active,
        });
      }

      return filters;
    },
  });

  return (
    <List
      headerButtons={[
        <CreateButton key="create" onClick={() => go({ to: "/paymentPlan/create" })} />
      ]}
    >
      <SearchForm
        searchFormProps={searchFormProps}
        useCard={true}
        layout="inline"
      >
        <Form.Item label="计划名称" name="name">
          <Input placeholder="请输入计划名称" allowClear />
        </Form.Item>
        <Form.Item label="适用范围" name="scope_type">
          <Select
            placeholder="请选择适用范围"
            allowClear
            options={scopeTypeOptions}
          />
        </Form.Item>
        <Form.Item label="是否激活" name="is_active">
          <Select
            placeholder="请选择状态"
            allowClear
            options={[
              { label: "已激活", value: true },
              { label: "未激活", value: false },
            ]}
          />
        </Form.Item>
      </SearchForm>

      <Table {...tableProps} rowKey="id">
        <Table.Column
          title="ID"
          dataIndex="id"
          key="id"
          render={(value) => <span className="font-mono">{value}</span>}
        />
        <Table.Column
          title="计划名称"
          dataIndex="name"
          key="name"
        />
        <Table.Column
          title="价格"
          dataIndex="price"
          key="price"
          render={(value) => `¥${value.toFixed(2)}`}
          sorter
        />
        <Table.Column
          title="原价"
          dataIndex="original_price"
          key="original_price"
          render={(value) => value ? `¥${value.toFixed(2)}` : '-'}
        />
        <Table.Column
          title="有效期"
          dataIndex="validity_period"
          key="validity_period"
          render={(value) => value > 0 ? `${value}天` : '永久'}
        />
        <Table.Column
          title="适用范围"
          dataIndex="scope_type"
          key="scope_type"
          render={(value: ScopeType) => <ScopeTypeTag type={value} />}
          filters={scopeTypeOptions.map(option => ({
            text: option.label,
            value: option.value,
          }))}
        />
        <Table.Column
          title="状态"
          dataIndex="is_active"
          key="is_active"
          render={(value) => (
            <Tag color={value ? "green" : "default"}>
              {value ? "已激活" : "未激活"}
            </Tag>
          )}
          filters={[
            { text: "已激活", value: true },
            { text: "未激活", value: false },
          ]}
        />
        <Table.Column
          title="创建时间"
          dataIndex="created_at"
          key="created_at"
          render={(value) => <DateField format="YYYY-MM-DD HH:mm:ss" value={value} />}
          sorter
        />
        <Table.Column
          title="操作"
          dataIndex="actions"
          render={(_, record: BaseRecord) => (
            <Space>
              <EditButton hideText size="small" recordItemId={record.id} />
              <ShowButton hideText size="small" recordItemId={record.id} />
              <DeleteButton hideText size="small" recordItemId={record.id} />
            </Space>
          )}
        />
      </Table>
    </List>
  );
};
