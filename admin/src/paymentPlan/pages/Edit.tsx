import React, { useState, useEffect } from "react";
import { Edit, useForm } from "@refinedev/antd";
import { Form, Input, InputNumber, Select, Switch, Transfer } from "antd";
import { useGo, useList } from "@refinedev/core";
import { PaymentPlan, ScopeType } from "../schemas";
import { entityKey, scopeTypeOptions } from "../consts";
import TextArea from "antd/es/input/TextArea";

interface ScopeItem {
  key: string;
  title: string;
  description?: string;
}

export const PaymentPlanEdit: React.FC = () => {
  const go = useGo();
  const [scopeType, setScopeType] = useState<ScopeType>(ScopeType.APP);
  const [scopeItems, setScopeItems] = useState<ScopeItem[]>([]);
  const [selectedScopeIds, setSelectedScopeIds] = useState<string[]>([]);

  const { formProps, saveButtonProps, queryResult } = useForm<PaymentPlan>({
    resource: entityKey,
    redirect: "list",
    onMutationSuccess: () => {
      go({ to: "/paymentPlan" });
    },
    onMutationError: (error) => {
      console.error("Error updating payment plan:", error);
    },
    warnWhenUnsavedChanges: true,
  });

  // 获取当前编辑的付费计划数据
  const paymentPlan = queryResult?.data?.data;

  // 初始化范围类型和已选范围
  useEffect(() => {
    if (paymentPlan) {
      setScopeType(paymentPlan.scope_type);
      setSelectedScopeIds(paymentPlan.scope_ids || []);
    }
  }, [paymentPlan]);

  // 获取智能体列表
  const { data: agentData, isLoading: isLoadingAgents } = useList({
    resource: "agent",
    pagination: {
      mode: "off",
      pageSize: 9999,
    },
    queryOptions: {
      enabled: scopeType === ScopeType.APP || scopeType === ScopeType.BUNDLE,
    },
  });

  // 获取课程列表
  const { data: courseData, isLoading: isLoadingCourses } = useList({
    resource: "course",
    pagination: {
      mode: "off",
      pageSize: 9999,
    },
    queryOptions: {
      enabled: scopeType === ScopeType.COURSE,
    },
  });

  // 当范围类型变化时，更新可选项
  useEffect(() => {
    if (scopeType === ScopeType.APP && agentData?.data) {
      const items = agentData.data.map((agent: any) => ({
        key: agent.id,
        title: agent.name,
        description: agent.description,
      }));
      setScopeItems(items);
    } else if (scopeType === ScopeType.COURSE && courseData?.data) {
      const items = courseData.data.map((course: any) => ({
        key: course.id,
        title: course.title,
        description: course.description,
      }));
      setScopeItems(items);
    } else if (scopeType === ScopeType.BUNDLE && agentData?.data) {
      // 捆绑包也使用智能体列表
      const items = agentData.data.map((agent: any) => ({
        key: agent.id,
        title: agent.name,
        description: agent.description,
      }));
      setScopeItems(items);
    }
  }, [scopeType, agentData, courseData]);

  // 自定义表单提交方法
  const onFinish = (values: any) => {
    // 转换字段名称
    const transformedValues = {
      ...values,
      scope_ids: selectedScopeIds,
    };

    // 调用原始的表单提交方法
    return formProps.onFinish?.(transformedValues);
  };

  // 替换原始的onFinish
  const customFormProps = {
    ...formProps,
    onFinish,
  };

  return (
    <Edit saveButtonProps={saveButtonProps}>
      <Form<any> {...customFormProps} layout="vertical">
        <Form.Item
          label="计划名称"
          name="name"
          rules={[{ required: true, message: "请输入计划名称" }]}
        >
          <Input placeholder="请输入计划名称" />
        </Form.Item>

        <Form.Item label="计划描述" name="description">
          <TextArea placeholder="请输入计划描述" rows={4} />
        </Form.Item>

        <Form.Item
          label="价格"
          name="price"
          rules={[{ required: true, message: "请输入价格" }]}
        >
          <InputNumber
            min={0}
            step={0.01}
            precision={2}
            style={{ width: "100%" }}
            prefix="¥"
            placeholder="请输入价格"
          />
        </Form.Item>

        <Form.Item label="原价" name="original_price">
          <InputNumber
            min={0}
            step={0.01}
            precision={2}
            style={{ width: "100%" }}
            prefix="¥"
            placeholder="请输入原价"
          />
        </Form.Item>

        <Form.Item
          label="有效期（天）"
          name="validity_period"
          tooltip="0表示永久有效"
        >
          <InputNumber
            min={0}
            style={{ width: "100%" }}
            placeholder="请输入有效期（天）"
          />
        </Form.Item>

        <Form.Item
          label="适用范围类型"
          name="scope_type"
          rules={[{ required: true, message: "请选择适用范围类型" }]}
        >
          <Select
            placeholder="请选择适用范围类型"
            options={scopeTypeOptions}
            onChange={(value) => setScopeType(value as ScopeType)}
          />
        </Form.Item>

        <Form.Item label="适用范围" required>
          {/* 修复 onChange 类型不匹配问题 */}
          <Transfer
            dataSource={scopeItems}
            titles={["可选项", "已选项"]}
            targetKeys={selectedScopeIds}
            onChange={(newTargetKeys) => {
              setSelectedScopeIds(newTargetKeys.map((key) => String(key)));
            }}
            render={(item) => item.title}
            listStyle={{ width: 300, height: 300 }}
          />
        </Form.Item>

        <Form.Item label="是否激活" name="is_active" valuePropName="checked">
          <Switch />
        </Form.Item>
      </Form>
    </Edit>
  );
};
