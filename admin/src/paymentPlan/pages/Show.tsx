import React, { useEffect, useState } from "react";
import { useShow, useList } from "@refinedev/core";
import {
  Show,
  NumberField,
  DateField,
  TextField,
} from "@refinedev/antd";
import { 
  Typography, 
  Card, 
  Descriptions, 
  Divider, 
  Tag, 
  Space, 
  List,
  Avatar
} from "antd";
import { PaymentPlan, ScopeType } from "../schemas";
import { ScopeTypeTag } from "../components";
import { entityKey } from "../consts";

const { Title } = Typography;

interface ScopeItem {
  id: string;
  name: string;
  title?: string;
  description?: string;
  icon_url?: string;
}

export const PaymentPlanShow: React.FC = () => {
  // 获取资源ID
  const { queryResult } = useShow<PaymentPlan>({
    resource: entityKey,
  });

  const { data, isLoading } = queryResult;
  const record = data?.data;

  const [scopeItems, setScopeItems] = useState<ScopeItem[]>([]);
  const [isLoadingScope, setIsLoadingScope] = useState(false);

  // 获取适用范围详情
  useEffect(() => {
    if (record && record.scope_ids && record.scope_ids.length > 0) {
      setIsLoadingScope(true);
      
      const resource = record.scope_type === ScopeType.COURSE ? "course" : "agent";
      
      // 为每个ID获取详情
      Promise.all(
        record.scope_ids.map(async (id) => {
          try {
            const response = await fetch(`/api/admin/${resource}s/${id}`);
            if (!response.ok) throw new Error(`Failed to fetch ${resource} ${id}`);
            const data = await response.json();
            return data.data;
          } catch (error) {
            console.error(`Error fetching ${resource} ${id}:`, error);
            return { id, name: `${resource} ${id}` };
          }
        })
      )
        .then((items) => {
          setScopeItems(items);
          setIsLoadingScope(false);
        })
        .catch((error) => {
          console.error("Error fetching scope items:", error);
          setIsLoadingScope(false);
        });
    }
  }, [record]);

  if (isLoading) {
    return <div>加载中...</div>;
  }

  if (!record) {
    return <div>未找到付费计划</div>;
  }

  return (
    <Show>
      <Card>
        <Title level={4}>{record.name}</Title>
        <Descriptions bordered column={2}>
          <Descriptions.Item label="ID">{record.id}</Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag color={record.is_active ? "green" : "default"}>
              {record.is_active ? "已激活" : "未激活"}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="价格">¥{record.price.toFixed(2)}</Descriptions.Item>
          <Descriptions.Item label="原价">
            {record.original_price ? `¥${record.original_price.toFixed(2)}` : "-"}
          </Descriptions.Item>
          <Descriptions.Item label="有效期">
            {record.validity_period > 0 ? `${record.validity_period}天` : "永久"}
          </Descriptions.Item>
          <Descriptions.Item label="适用范围类型">
            <ScopeTypeTag type={record.scope_type} />
          </Descriptions.Item>
          <Descriptions.Item label="创建时间" span={2}>
            <DateField format="YYYY-MM-DD HH:mm:ss" value={record.created_at} />
          </Descriptions.Item>
          <Descriptions.Item label="更新时间" span={2}>
            <DateField format="YYYY-MM-DD HH:mm:ss" value={record.updated_at} />
          </Descriptions.Item>
          <Descriptions.Item label="描述" span={2}>
            {record.description || "-"}
          </Descriptions.Item>
        </Descriptions>

        <Divider orientation="left">适用范围</Divider>
        
        <List
          loading={isLoadingScope}
          dataSource={scopeItems}
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                avatar={item.icon_url ? <Avatar src={item.icon_url} /> : null}
                title={item.name || item.title}
                description={item.description}
              />
            </List.Item>
          )}
        />
      </Card>
    </Show>
  );
};
