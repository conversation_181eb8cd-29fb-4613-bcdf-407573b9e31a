// 付费计划相关类型定义

// 适用范围类型枚举
export enum ScopeType {
  APP = "app", // 应用
  COURSE = "course", // 课程
  BUNDLE = "bundle", // 捆绑包
}

// 付费计划实体
export interface PaymentPlan {
  id?: number; // 计划ID
  name: string; // 计划名称
  description?: string; // 计划描述
  price: number; // 价格
  original_price: number; // 原价
  validity_period: number; // 有效期（天）
  scope_type: ScopeType; // 适用范围类型
  scope_ids: string[]; // 适用范围ID列表
  is_active: boolean; // 是否激活
  created_at: Date; // 创建时间
  updated_at: Date; // 更新时间
}

// 付费计划创建请求
export interface PaymentPlanCreateRequest {
  name: string;
  description?: string;
  price: number;
  original_price: number;
  validity_period: number;
  scope_type: ScopeType;
  scope_ids: string[];
  is_active: boolean;
}

// 付费计划更新请求
export interface PaymentPlanUpdateRequest {
  name?: string;
  description?: string;
  price?: number;
  original_price?: number;
  validity_period?: number;
  scope_type?: ScopeType;
  scope_ids?: string[];
  is_active?: boolean;
}

// 付费计划查询参数
export interface PaymentPlanQueryParams {
  id?: number;
  name?: string;
  scope_type?: ScopeType;
  is_active?: boolean;
}
