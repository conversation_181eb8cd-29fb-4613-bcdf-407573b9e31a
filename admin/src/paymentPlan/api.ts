import { API_BASE_URL } from "@/common/env";
import { Pagination, QueryPayloads, ResponsePayloads } from "@/common/schemas";
import { PaymentPlan, PaymentPlanCreateRequest, PaymentPlanQueryParams, PaymentPlanUpdateRequest } from "./schemas";

/**
 * 获取付费计划列表
 * @param params 查询参数
 * @returns 分页付费计划列表
 */
export const getPaymentPlans = async (params: QueryPayloads): Promise<ResponsePayloads<Pagination<PaymentPlan>>> => {
  const response = await fetch(`${API_BASE_URL}/admin/payment-plans`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    }
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "获取付费计划列表失败");
  }

  return await response.json();
};

/**
 * 获取付费计划详情
 * @param id 付费计划ID
 * @returns 付费计划详情
 */
export const getPaymentPlan = async (id: number): Promise<ResponsePayloads<PaymentPlan>> => {
  const response = await fetch(`${API_BASE_URL}/admin/payment-plans/${id}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    }
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "获取付费计划详情失败");
  }

  return await response.json();
};

/**
 * 创建付费计划
 * @param data 付费计划创建请求
 * @returns 创建的付费计划
 */
export const createPaymentPlan = async (data: PaymentPlanCreateRequest): Promise<ResponsePayloads<PaymentPlan>> => {
  const response = await fetch(`${API_BASE_URL}/admin/payment-plans`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "创建付费计划失败");
  }

  return await response.json();
};

/**
 * 更新付费计划
 * @param id 付费计划ID
 * @param data 付费计划更新请求
 * @returns 更新后的付费计划
 */
export const updatePaymentPlan = async (id: number, data: PaymentPlanUpdateRequest): Promise<ResponsePayloads<PaymentPlan>> => {
  const response = await fetch(`${API_BASE_URL}/admin/payment-plans/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "更新付费计划失败");
  }

  return await response.json();
};

/**
 * 删除付费计划
 * @param id 付费计划ID
 * @returns 删除结果
 */
export const deletePaymentPlan = async (id: number): Promise<ResponsePayloads<{ success: boolean }>> => {
  const response = await fetch(`${API_BASE_URL}/admin/payment-plans/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    }
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "删除付费计划失败");
  }

  return await response.json();
};
