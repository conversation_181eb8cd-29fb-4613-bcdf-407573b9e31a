import {
  DateField,
  List,
  useTable
} from "@refinedev/antd";
import { CrudFilters, useGo } from "@refinedev/core";
import { <PERSON><PERSON>, Card, DatePicker, Form, Input, Select, Space, Table } from "antd";
import React from "react";
import { FaEdit, FaEye } from "react-icons/fa";
import { SearchForm } from "@/common/components";
import { OrderStatusTag, PaymentMethodTag } from "../components";
import { Order, OrderStatus, PaymentMethod } from "../schemas";

export const OrderList: React.FC = () => {
  const go = useGo();

  const { tableProps, searchFormProps } = useTable<Order>({
    resource: "order",
    syncWithLocation: true,
    defaultSetFilterBehavior: "replace",
    onSearch: (params: any) => {
      const filters: CrudFilters = [];

      const { id, status, payment_method, date_range } = params;

      if (id) {
        filters.push({
          field: "id",
          operator: "eq",
          value: id,
        });
      }

      if (status) {
        filters.push({
          field: "status",
          operator: "eq",
          value: status,
        });
      }

      if (payment_method) {
        filters.push({
          field: "payment_method",
          operator: "eq",
          value: payment_method,
        });
      }

      if (date_range && date_range[0] && date_range[1]) {
        filters.push(
          {
            field: "created_at",
            operator: "gte",
            value: date_range[0].toISOString(),
          },
          {
            field: "created_at",
            operator: "lte",
            value: date_range[1].toISOString(),
          }
        );
      }

      return filters;
    },
  });

  return (
    <List>
      <SearchForm
        searchFormProps={searchFormProps}
        useCard={true}
        layout="vertical"
        formClassName="flex flex-col md:flex-row gap-2"
      >
        <Form.Item name="id">
          <Input
            placeholder="订单ID"
            prefix="ID: "
            className="max-w-xs"
          />
        </Form.Item>

        <Form.Item name="status">
          <Select
            placeholder="订单状态"
            allowClear
            className="w-32"
            options={[
              { label: "待支付", value: OrderStatus.PENDING },
              { label: "已支付", value: OrderStatus.PAID },
              { label: "已取消", value: OrderStatus.CANCELLED },
              { label: "已退款", value: OrderStatus.REFUNDED },
            ]}
          />
        </Form.Item>

        <Form.Item name="payment_method">
          <Select
            placeholder="支付方式"
            allowClear
            className="w-32"
            options={[
              { label: "支付宝", value: PaymentMethod.ALIPAY },
              { label: "微信", value: PaymentMethod.WECHATPAY },
            ]}
          />
        </Form.Item>

        <Form.Item name="date_range">
          <DatePicker.RangePicker
            placeholder={["开始日期", "结束日期"]}
          />
        </Form.Item>
      </SearchForm>

      <Table {...tableProps} rowKey="id">
        <Table.Column
          title="订单ID"
          dataIndex="id"
          key="id"
          render={(value) => <span className="font-mono">{value}</span>}
        />
        <Table.Column
          title="用户ID"
          dataIndex="user_id"
          key="user_id"
        />
        <Table.Column
          title="订单金额"
          dataIndex="amount"
          key="amount"
          render={(value) => `¥${value}`}
          sorter
        />
        <Table.Column
          title="状态"
          dataIndex="status"
          key="status"
          render={(value) => <OrderStatusTag status={value} />}
          filters={[
            { text: "待支付", value: OrderStatus.PENDING },
            { text: "已支付", value: OrderStatus.PAID },
            { text: "已取消", value: OrderStatus.CANCELLED },
            { text: "已退款", value: OrderStatus.REFUNDED },
          ]}
        />
        <Table.Column
          title="支付方式"
          dataIndex="payment_method"
          key="payment_method"
          render={(value) => <PaymentMethodTag method={value} />}
        />
        <Table.Column
          title="支付时间"
          dataIndex="pay_time"
          key="pay_time"
          render={(value) => {
            return value ? <DateField format="YYYY-MM-DD HH:mm:ss" value={value} /> : "-";
          }}
        />
        <Table.Column
          title="创建时间"
          dataIndex="created_at"
          key="created_at"
          render={(value) => <DateField format="YYYY-MM-DD HH:mm:ss" value={value} />}
          sorter
        />
        <Table.Column
          title="操作"
          key="actions"
          render={(_: unknown, record: Order) => (
            <Space>
              <Button
                type="primary"
                size="small"
                icon={<FaEye className="mr-1" />}
                onClick={() => go({ to: `/order/show/${record.id}` })}
              >
                查看
              </Button>
              <Button
                type="default"
                size="small"
                icon={<FaEdit className="mr-1" />}
                onClick={() => go({ to: `/order/edit/${record.id}` })}
              >
                编辑
              </Button>
            </Space>
          )}
        />
      </Table>
    </List>
  );
}; 