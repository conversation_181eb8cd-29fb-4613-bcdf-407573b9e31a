import React from "react";
import { useForm, Edit } from "@refinedev/antd";
import { Form, Input, DatePicker, Card, Row, Col, Select } from "antd";
import { useApiUrl, useCustomMutation, useInvalidate } from "@refinedev/core";
import { Order, OrderStatus, PaymentMethod } from "../schemas";
import { OrderItemsList } from "../components";
import dayjs from "dayjs";

export const OrderEdit: React.FC = () => {
  const apiUrl = useApiUrl();
  const invalidate = useInvalidate();

  const { formProps, saveButtonProps, queryResult } = useForm<Order>({
    resource: "order",
    redirect: "list",
  });

  const order = queryResult?.data?.data;

  if (!order) {
    return <div>加载中...</div>;
  }

  // 格式化支付时间
  const initialValues = {
    ...order,
    pay_time: order.pay_time ? dayjs(order.pay_time) : undefined,
  };

  return (
    <Edit saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical" initialValues={initialValues}>
        <Row gutter={24}>
          <Col span={24} lg="12">
            <Card title="基本信息" className="mb-4">
              <Form.Item
                label="订单ID"
                name="id"
              >
                <Input disabled className="font-mono" />
              </Form.Item>
              
              <Form.Item
                label="用户ID"
                name="user_id"
              >
                <Input disabled />
              </Form.Item>
              
              <Form.Item
                label="订单金额"
                name="amount"
              >
                <Input prefix="¥" disabled />
              </Form.Item>
              
              <Form.Item
                label="订单状态"
                name="status"
                rules={[{ required: true, message: "请选择订单状态" }]}
              >
                <Select
                  options={[
                    {
                      label: "待支付",
                      value: OrderStatus.PENDING,
                    },
                    {
                      label: "已支付",
                      value: OrderStatus.PAID,
                    },
                    {
                      label: "已取消",
                      value: OrderStatus.CANCELLED,
                    },
                    {
                      label: "已退款",
                      value: OrderStatus.REFUNDED,
                    },
                  ]}
                />
              </Form.Item>
              
              <Form.Item
                label="支付方式"
                name="payment_method"
                rules={[{ required: true, message: "请选择支付方式" }]}
              >
                <Select
                  options={[
                    {
                      label: "支付宝",
                      value: PaymentMethod.ALIPAY,
                    },
                    {
                      label: "微信",
                      value: PaymentMethod.WECHATPAY,
                    } 
                  ]}
                />
              </Form.Item>
              
              <Form.Item
                label="支付时间"
                name="pay_time"
              >
                <DatePicker showTime format="YYYY-MM-DD HH:mm:ss" />
              </Form.Item>
            </Card>
          </Col>
          
          <Col span={24} lg="12">
            <Card title="订单项目" className="mb-4">
              <OrderItemsList items={order.items} />
              <div className="text-xs text-gray-500 mt-2">
                注意：订单项目无法通过此界面修改
              </div>
            </Card>
          </Col>
        </Row>
      </Form>
    </Edit>
  );
}; 