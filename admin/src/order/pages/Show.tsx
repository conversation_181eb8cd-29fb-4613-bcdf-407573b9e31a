import React from "react";
import { useShow, useResource } from "@refinedev/core";
import {
  Show,
  NumberField,
  DateField,
  TextField,
} from "@refinedev/antd";
import { 
  Typography, 
  Card, 
  Descriptions, 
  Divider, 
  Tag, 
  Space, 
  Button, 
  Popconfirm,
  Row,
  Col
} from "antd";
import { Order, OrderStatus } from "../schemas";
import { OrderItemsList, OrderStatusTag, PaymentMethodTag } from "../components";
import { cancelOrder, refundOrder } from "../api";
import { FaTimes, FaUndo } from "react-icons/fa";

const { Title } = Typography;

export const OrderShow: React.FC = () => {
  const { id } = useResource();
  const { queryResult } = useShow<Order>({
    resource: "order",
    id,
  });

  const order = queryResult.data?.data;

  if (!order) {
    return <div>加载中...</div>;
  }

  const handleCancel = async () => {
    if (!id) return;
    await cancelOrder(id as string);
  };

  const handleRefund = async () => {
    if (!id) return;
    await refundOrder(id as string);
  };

  return (
    <Show
      title={<Title level={3}>订单详情 #{order.id}</Title>}
      headerButtons={({ defaultButtons }) => {
        return (
          <Space>
            {order.status === OrderStatus.PENDING && (
              <Popconfirm
                title="确定要取消这个订单吗？"
                onConfirm={handleCancel}
                okText="确定"
                cancelText="取消"
              >
                <Button danger icon={<FaTimes className="mr-1" />}>
                  取消订单
                </Button>
              </Popconfirm>
            )}
            {order.status === OrderStatus.PAID && (
              <Popconfirm
                title="确定要退款这个订单吗？"
                onConfirm={handleRefund}
                okText="确定"
                cancelText="取消"
              >
                <Button type="default" icon={<FaUndo className="mr-1" />}>
                  退款订单
                </Button>
              </Popconfirm>
            )}
            {defaultButtons}
          </Space>
        );
      }}
    >
      <Row gutter={24}>
        <Col span={24}>
          <Card title="基本信息" className="mb-4">
            <Descriptions column={{ xs: 1, sm: 2, md: 3 }}>
              <Descriptions.Item label="订单ID">
                <TextField value={order.id} className="font-mono" />
              </Descriptions.Item>
              <Descriptions.Item label="用户ID">
                <TextField value={order.user_id} />
              </Descriptions.Item>
              <Descriptions.Item label="订单金额">
                <NumberField value={order.amount} options={{ style: "currency", currency: "CNY" }} />
              </Descriptions.Item>
              <Descriptions.Item label="订单状态">
                <OrderStatusTag status={order.status} />
              </Descriptions.Item>
              <Descriptions.Item label="支付方式">
                <PaymentMethodTag method={order.payment_method} />
              </Descriptions.Item>
              <Descriptions.Item label="支付时间">
                {order.pay_time ? (
                  <DateField format="YYYY-MM-DD HH:mm:ss" value={order.pay_time} />
                ) : (
                  "-"
                )}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                <DateField format="YYYY-MM-DD HH:mm:ss" value={order.created_at} />
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                <DateField format="YYYY-MM-DD HH:mm:ss" value={order.updated_at} />
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
        
        <Col span={24}>
          <Card title="订单项目">
            <OrderItemsList items={order.items} />
          </Card>
        </Col>
      </Row>
    </Show>
  );
}; 