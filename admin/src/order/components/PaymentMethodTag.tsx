import React from "react";
import { Tag } from "antd";
import { PaymentMethod } from "../schemas";
import { FaAlipay, FaWeixin, FaBuildingColumns, FaQuestion } from "react-icons/fa6";

interface PaymentMethodTagProps {
  method: PaymentMethod;
}

export const PaymentMethodTag: React.FC<PaymentMethodTagProps> = ({ method }) => {
  const methodConfig: Record<PaymentMethod, { color: string; text: string; icon: React.ReactNode }> = {
    [PaymentMethod.ALIPAY]: { 
      color: "#1677FF", 
      text: "支付宝", 
      icon: <FaAlipay className="mr-1" /> 
    },
    [PaymentMethod.WECHATPAY]: { 
      color: "#07C160", 
      text: "微信", 
      icon: <FaWeixin className="mr-1" /> 
    } 
  };

  const config = methodConfig[method];

  return (
    <Tag color={config.color} style={{ display: "flex", alignItems: "center" }}>
      {config.icon} {config.text}
    </Tag>
  );
}; 