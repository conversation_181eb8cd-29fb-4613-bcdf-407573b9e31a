import React from "react";
import { Table, Tag } from "antd";
import { OrderItem, AssetType } from "../schemas";

interface OrderItemsListProps {
  items: OrderItem[];
}

export const OrderItemsList: React.FC<OrderItemsListProps> = ({ items }) => {
  const columns = [
    {
      title: "资产类型",
      dataIndex: "asset_type",
      key: "asset_type",
      render: (type: AssetType) => {
        const colorMap = {
          [AssetType.COURSE]: "blue",
          [AssetType.MATERIAL]: "green",
          [AssetType.OTHER]: "default",
        };
        
        const labelMap = {
          [AssetType.COURSE]: "课程",
          [AssetType.MATERIAL]: "资料",
          [AssetType.OTHER]: "其他",
        };
        
        return <Tag color={colorMap[type]}>{labelMap[type]}</Tag>;
      },
    },
    {
      title: "资产ID",
      dataIndex: "asset_id",
      key: "asset_id",
    },
    {
      title: "数量",
      dataIndex: "quantity",
      key: "quantity",
    },
    {
      title: "单价",
      dataIndex: "unit_price",
      key: "unit_price",
      render: (price: string) => `¥${price}`,
    },
    {
      title: "小计",
      key: "subtotal",
      render: (_: unknown, record: OrderItem) => {
        const subtotal = Number(record.unit_price) * record.quantity;
        return `¥${subtotal.toFixed(2)}`;
      },
    },
  ];

  return (
    <Table
      columns={columns}
      dataSource={items.map((item, index) => ({ ...item, key: index }))}
      pagination={false}
      size="small"
    />
  );
}; 