import React from "react";
import { Tag } from "antd";
import { OrderStatus } from "../schemas";

interface OrderStatusTagProps {
  status: OrderStatus;
}

export const OrderStatusTag: React.FC<OrderStatusTagProps> = ({ status }) => {
  const statusConfig: Record<OrderStatus, { color: string; text: string }> = {
    [OrderStatus.PENDING]: { color: "warning", text: "待支付" },
    [OrderStatus.PAID]: { color: "success", text: "已支付" },
    [OrderStatus.CANCELLED]: { color: "default", text: "已取消" },
    [OrderStatus.REFUNDED]: { color: "error", text: "已退款" },
  };

  const config = statusConfig[status];

  return <Tag color={config.color}>{config.text}</Tag>;
}; 