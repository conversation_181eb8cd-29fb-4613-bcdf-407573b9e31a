// 订单相关类型定义

// 资产类型枚举
export enum AssetType {
  COURSE = "COURSE",
  MATERIAL = "MATERIAL",
  OTHER = "OTHER"
}

// 支付方式枚举
export enum PaymentMethod {
  ALIPAY = "alipay",   
  WECHATPAY = "wechatpay"
}

// 订单状态枚举
export enum OrderStatus {
  PENDING = "PENDING", // 待支付
  PAID = "PAID", // 已支付
  CANCELLED = "CANCELLED", // 已取消
  REFUNDED = "REFUNDED", // 已退款
}

// 订单项目接口
export interface OrderItem {
  asset_type: AssetType;
  asset_id: string;
  quantity: number;
  unit_price: string;
}

// 订单实体接口
export interface Order {
  id?: string; // 订单ID
  user_id: number; // 用户ID
  amount: number; // 订单金额
  status: OrderStatus; // 订单状态
  payment_method: PaymentMethod; // 支付方式
  pay_time?: Date; // 支付时间
  items: OrderItem[]; // 订单项目
  created_at: Date; // 创建时间
  updated_at: Date; // 更新时间
}

// 订单查询参数
export interface OrderQueryParams {
  id?: string;
  user_id?: number;
  status?: OrderStatus;
  payment_method?: PaymentMethod;
  start_time?: Date;
  end_time?: Date;
}

// 订单创建请求
export interface OrderCreateRequest {
  user_id: number;
  amount: number;
  payment_method: PaymentMethod;
  items: OrderItem[];
}

// 订单更新请求
export interface OrderUpdateRequest {
  status?: OrderStatus;
  payment_method?: PaymentMethod;
  pay_time?: Date;
  items?: OrderItem[];
} 