import { API_BASE_URL } from "@/common/env";
import { Pagination, QueryPayloads, ResponsePayloads } from "@/common/schemas";
import { Order, OrderCreateRequest, OrderQueryParams, OrderUpdateRequest } from "./schemas";

/**
 * 获取订单列表
 * @param params 查询参数
 * @returns 分页订单列表
 */
export const getOrders = async (params: QueryPayloads): Promise<ResponsePayloads<Pagination<Order>>> => {
  const response = await fetch(`${API_BASE_URL}/orders`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    },
    body: JSON.stringify(params)
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "获取订单列表失败");
  }

  return await response.json();
};

/**
 * 获取订单详情
 * @param id 订单ID
 * @returns 订单详情
 */
export const getOrder = async (id: string): Promise<ResponsePayloads<Order>> => {
  const response = await fetch(`${API_BASE_URL}/orders/${id}`, {
    method: "GET",
    headers: {
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    }
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "获取订单详情失败");
  }

  return await response.json();
};

/**
 * 创建订单
 * @param data 订单创建数据
 * @returns 创建结果
 */
export const createOrder = async (data: OrderCreateRequest): Promise<ResponsePayloads<Order>> => {
  const response = await fetch(`${API_BASE_URL}/orders`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "创建订单失败");
  }

  return await response.json();
};

/**
 * 更新订单
 * @param id 订单ID
 * @param data 订单更新数据
 * @returns 更新结果
 */
export const updateOrder = async (id: string, data: OrderUpdateRequest): Promise<ResponsePayloads<Order>> => {
  const response = await fetch(`${API_BASE_URL}/orders/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "更新订单失败");
  }

  return await response.json();
};

/**
 * 取消订单
 * @param id 订单ID
 * @returns 取消结果
 */
export const cancelOrder = async (id: string): Promise<ResponsePayloads<Order>> => {
  const response = await fetch(`${API_BASE_URL}/orders/${id}/cancel`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    }
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "取消订单失败");
  }

  return await response.json();
};

/**
 * 退款订单
 * @param id 订单ID
 * @returns 退款结果
 */
export const refundOrder = async (id: string): Promise<ResponsePayloads<Order>> => {
  const response = await fetch(`${API_BASE_URL}/orders/${id}/refund`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Accept": "application/json",
      "Authorization": `Bearer ${localStorage.getItem("auth-token")}`
    }
  });

  if (!response.ok) {
    const errorData = await response.json();
    throw new Error(errorData.message || "退款订单失败");
  }

  return await response.json();
}; 