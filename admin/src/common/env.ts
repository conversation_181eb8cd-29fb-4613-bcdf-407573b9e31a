/**
 * 环境变量配置
 * 所有环境变量都应该在这里定义，以便统一管理
 */

// 应用标题
export const APP_TITLE = import.meta.env.VITE_APP_TITLE || 'IT Assets Management';

// API基础URL
export const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

// 应用基础URL
export const APP_BASE_URL = import.meta.env.VITE_APP_BASE_URL || '/';

// 当前环境
export const APP_ENV = import.meta.env.VITE_APP_ENV || 'development';

// 判断是否为开发环境
export const IS_DEV = APP_ENV === 'development';

// 判断是否为生产环境
export const IS_PROD = APP_ENV === 'production';

// 判断是否为测试环境
export const IS_TEST = APP_ENV === 'test';

// 导出默认配置对象
export default {
  APP_TITLE,
  API_BASE_URL,
  APP_BASE_URL,
  APP_ENV,
  IS_DEV,
  IS_PROD,
  IS_TEST
}; 