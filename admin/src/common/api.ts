import { API_BASE_URL } from "./env";
import { ResponsePayloads, Pagination, QueryPayloads } from "./schemas";
import { request } from "./utils";

/**
 * 查询实体列表
 */
export async function getList(resource: string, payloads: QueryPayloads): Promise<ResponsePayloads<Pagination<any>>> {
  //const token = useUserStore.getState().token;

  // if (!token) {
  //   throw new Error('未找到用户 token，请先登录');
  // }

  const response = await request(`${API_BASE_URL}/entity/${resource}/list`, {
    method: 'POST',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      // 'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(payloads)
  });

  if (!response.ok) {
    throw new Error(`获取应用标签失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 获取单个实体
 */
export async function getOne(resource: string, id: number | string): Promise<ResponsePayloads<any>> {
  const response = await request(`${API_BASE_URL}/entity/${resource}/${id}`, {
    method: 'GET',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      // 'Authorization': `Bearer ${token}`
    }
  });

  if (!response.ok) {
    throw new Error(`获取实体失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 创建实体
 */
export async function create(resource: string, data: any): Promise<ResponsePayloads<any>> {
  const response = await request(`${API_BASE_URL}/entity/${resource}`, {
    method: 'POST',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      // 'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    throw new Error(`创建实体失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 更新实体
 */
export async function update(resource: string, id: number | string, data: any): Promise<ResponsePayloads<any>> {
  const response = await request(`${API_BASE_URL}/entity/${resource}/${id}`, {
    method: 'PUT',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      // 'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    throw new Error(`更新实体失败: ${response.statusText}`);
  }

  return response.json();
}

/**
 * 删除实体
 */
export async function deleteOne(resource: string, id: number | string): Promise<ResponsePayloads<any>> {
  const response = await request(`${API_BASE_URL}/entity/${resource}/${id}`, {
    method: 'DELETE',
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      // 'Authorization': `Bearer ${token}`
    }
  });

  if (!response.ok) {
    throw new Error(`删除实体失败: ${response.statusText}`);
  }

  return response.json();
}