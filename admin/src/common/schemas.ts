// 通用类型定义

// 定义操作符枚举
export enum Operator {
    LIKE = "like", // 模糊匹配
    EQUALS = "=", // 等于
    NOT_EQUALS = "!=", // 不等于
    GREATER_THAN = ">", // 大于
    GREATER_THAN_OR_EQUAL_TO = ">=", // 大于等于
    LESS_THAN = "<", // 小于
    LESS_THAN_OR_EQUAL_TO = "<=", // 小于等于
    IN = "in", // 在...之中
    NOT_IN = "nin", // 不在...之中
    BETWEEN = "between", // 在...之间
    IS_NULL = "isNull", // 为空
    IS_NOT_EMPTY = "isNotEmpty", // 不为空
    CONTAINS = "contains", // 包含
    NOT_CONTAINS = "notContains", // 不包含
    STARTS_WITH = "startsWith", // 以...开始
    ENDS_WITH = "endsWith", // 以...结束
    REGEX = "regex", // 正则表达式
}

// 定义排序方向枚举
export enum SortDirection {
    ASC = "asc",
    DESC = "desc"
}

// 定义过滤器接口
export interface Filter {
    property: string; // 过滤的属性
    operator: Operator; // 操作符
    value?: any; // 过滤的值
}

// 定义排序接口
export interface Sort {
    property: string; // 排序的属性
    direction: SortDirection; // 排序的方向
}

// 定义查询载荷接口
export interface QueryPayloads {
    pageNo?: number; // 页码
    pageSize?: number; // 每页大小
    filters?: Filter[]; // 过滤器列表
    sorts?: Sort[]; // 排序器列表
}

// 定义分页接口
export interface Pagination<T> {
    page_no?: number; // 当前页码
    page_size?: number; // 每页条数
    total?: number; // 总条数
    has_more?: boolean; // 是否有更多数据
    data?: T[]; // 数据列表 
}


// 定义错误信息接口
export interface Error {
    type?: string;
    message?: string;
    code?: number;
}

// 定义响应载荷接口
export interface ResponsePayloads<T> {
    data?: T;
    error?: Error;
}