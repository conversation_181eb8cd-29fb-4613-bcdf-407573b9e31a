import { useAuthStore } from "@/admin/stores";

/**
 * 自定义的 fetch 包装函数，用于处理 401 Unauthorized 响应
 * @param input 请求 URL 或 Request 对象
 * @param init 请求配置
 * @returns Promise<Response>
 */
export const request = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
    const token = useAuthStore.getState().token;
    let headers = init?.headers;
    if (token) {
        headers = {
            ...headers,
            'Authorization': `Bearer ${token}`
        }
    }

    const response = await fetch(input, {
        ...init,
        headers
    });

    if (response.status === 401) {
        try {
            const data = await response.json();
            if (data.error?.type === 'Unauthorized') {
                // 使用 Zustand store 的 clearUser 方法清除用户信息
                //useAuthStore.getState().clearUser();

                // 重定向到登录页面
                window.location.href = '/#/login';
                return Promise.reject(new Error('Unauthorized'));
            }
        } catch (e) {
            // 如果响应不是 JSON 格式，仍然返回原始响应
            return response;
        }
    }

    return response;
};
