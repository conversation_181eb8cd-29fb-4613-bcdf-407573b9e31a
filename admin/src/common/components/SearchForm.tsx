import React from "react";
import { <PERSON><PERSON>, Card, Form, Space } from "antd";
import { FaSearch, FaUndo } from "react-icons/fa";
import { useForm } from "@refinedev/antd";

interface SearchFormProps {
  // useTable 钩子返回的 searchFormProps
  searchFormProps: any;
  // 表单布局，默认为 inline
  layout?: "horizontal" | "vertical" | "inline";
  // 是否使用卡片包裹
  useCard?: boolean;
  // 额外的表单样式
  formClassName?: string;
  // 额外的卡片样式
  cardClassName?: string;
  // 表单内容
  children: React.ReactNode;
  // 是否显示重置按钮
  showReset?: boolean;
  // 自定义重置按钮文本
  resetText?: string;
  // 自定义搜索按钮文本
  searchText?: string;
  // 自定义搜索按钮图标
  searchIcon?: React.ReactNode;
  // 表单初始值
  initialValues?: Record<string, any>;
  // 重置回调
  onReset?: () => void;
}

export const SearchForm: React.FC<SearchFormProps> = ({
  searchFormProps,
  layout = "inline",
  useCard = true,
  formClassName = "mb-2",
  cardClassName = "mb-2",
  children,
  showReset = true,
  resetText = "重置",
  searchText = "搜索",
  searchIcon = <FaSearch />,
  initialValues,
  onReset,
}) => {
  const [form] = Form.useForm();

  // 自定义重置处理函数
  const handleReset = () => {
    form.resetFields();
    if (onReset) {
      onReset();
    } else {
      // 默认重置行为
      searchFormProps?.onFinish?.(initialValues || {});
    }
  };

  const formContent = (
    <Form
      {...searchFormProps}
      form={form}
      layout={layout}
      className={`${formClassName} flex flex-wrap gap-y-2`}
      initialValues={initialValues}
      style={{ rowGap: '0.5rem' }}
    >
      <div className="flex flex-wrap gap-x-2 gap-y-2 w-full">
        {children}
      </div>
      <Form.Item className="mb-2">
        <Space>
          <Button
            type="primary"
            htmlType="submit"
            icon={searchIcon}
          >
            {searchText}
          </Button>
          {showReset && (
            <Button
              onClick={handleReset}
              icon={<FaUndo />}
            >
              {resetText}
            </Button>
          )}
        </Space>
      </Form.Item>
    </Form>
  );

  if (useCard) {
    return <Card className={cardClassName}>{formContent}</Card>;
  }

  return formContent;
};

export default SearchForm; 