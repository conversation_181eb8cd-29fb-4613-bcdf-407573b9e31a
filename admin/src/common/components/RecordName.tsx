import { useOne } from "@refinedev/core";
import { Spin } from "antd";

interface RecordNameCellProps {
    value: any;
    resource: string;  // 资源名称，如 "product" 或 "customer"
    nameField?: string; // 要显示的字段名，默认为 "name"
}

const RecordNameCell: React.FC<RecordNameCellProps> = ({
    value,
    resource,
    nameField = "name"
}) => {
    const { data, isLoading } = useOne({
        resource: resource,
        id: value,
        queryOptions: {
            enabled: value != null && value !== "",
        },
    });

    if (isLoading) {
        return <Spin size="small" />;
    }

    return <span>{data?.data?.[nameField] || "-"}</span>;
};
export default RecordNameCell;
