import { 
  DataProvider, 
  <PERSON>rudFilters, 
  CrudSorting, 
  BaseRecord,
  CreateResponse,
  UpdateResponse,
  DeleteOneResponse,
  GetOneResponse,
  CustomResponse
} from "@refinedev/core";
import { getList as apiGetList, getOne as apiGetOne, create as apiCreate, update as apiUpdate, deleteOne as apiDeleteOne } from "./api";
import { QueryPayloads, Operator, Filter, Sort, SortDirection } from "./schemas";

// 将Refine的过滤器转换为我们的过滤器格式
const mapFilters = (filters?: CrudFilters): Filter[] | undefined => {
  if (!filters) return undefined;
  
  return filters.map(filter => {
    if ('field' in filter) {
      return {
        property: filter.field,
        operator: mapOperator(filter.operator),
        value: filter.value
      };
    }
    return {
      property: 'id',
      operator: Operator.EQUALS,
      value: ''
    };
  });
};

// 将Refine的操作符转换为我们的操作符格式
const mapOperator = (operator: string): Operator => {
  switch (operator) {
    case 'eq': return Operator.EQUALS;
    case 'ne': return Operator.NOT_EQUALS;
    case 'lt': return Operator.LESS_THAN;
    case 'gt': return Operator.GREATER_THAN;
    case 'lte': return Operator.LESS_THAN_OR_EQUAL_TO;
    case 'gte': return Operator.GREATER_THAN_OR_EQUAL_TO;
    case 'in': return Operator.IN;
    case 'nin': return Operator.NOT_IN;
    case 'contains': return Operator.CONTAINS;
    case 'ncontains': return Operator.NOT_CONTAINS;
    case 'startswith': return Operator.STARTS_WITH;
    case 'endswith': return Operator.ENDS_WITH;
    default: return Operator.EQUALS;
  }
};

// 将Refine的排序转换为我们的排序格式
const mapSorters = (sorters?: CrudSorting): Sort[] | undefined => {
  if (!sorters) return undefined;
  
  return sorters.map(sorter => ({
    property: sorter.field,
    direction: sorter.order === 'asc' ? SortDirection.ASC : SortDirection.DESC
  }));
};

export const customDataProvider = (): DataProvider => ({
  getList: async ({ resource, pagination, filters, sorters, meta }) => {
    const payloads: QueryPayloads = {
      pageNo: pagination?.current || 1,
      pageSize: pagination?.pageSize || 10,
      filters: mapFilters(filters),
      sorts: mapSorters(sorters)
    };

    const response = await apiGetList(resource, payloads);
    
    return {
      data: response.data?.data || [],
      total: response.data?.total || 0
    };
  },

  getOne: async ({ resource, id }) => {
    const response = await apiGetOne(resource, id);
    
    return {
      data: response.data || {}
    };
  },

  create: async ({ resource, variables }) => {
    const response = await apiCreate(resource, variables);
    
    return {
      data: response.data || {}
    };
  },
  
  update: async ({ resource, id, variables }) => {
    const response = await apiUpdate(resource, id, variables);
    
    return {
      data: response.data || {}
    };
  },
  
  deleteOne: async ({ resource, id }) => {
    const response = await apiDeleteOne(resource, id);
    
    return {
      data: response.data || {}
    };
  },
  
  getApiUrl: () => {
    return "";
  },
  
  custom: async <TData extends BaseRecord = BaseRecord>() => {
    return { data: {} as TData };
  }
}); 