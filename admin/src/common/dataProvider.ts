import {
  <PERSON><PERSON><PERSON><PERSON>,
  Crud<PERSON>ilt<PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Crud<PERSON>ilter,
  BaseRecord
} from "@refinedev/core";
import { getList as apiGetList, getOne as apiGetOne, create as apiCreate, update as apiUpdate, deleteOne as apiDeleteOne } from "./api";
import { QueryPayloads, Operator, LogicalOperator, Filter, PropertyFilter, LogicalFilter, Sort, SortDirection } from "./schemas";

// 将Refine的过滤器转换为我们的过滤器格式
const mapFilters = (filters?: CrudFilters): Filter[] | undefined => {
  if (!filters) return undefined;

  return filters.map(filter => mapFilter(filter));
};

// 将单个过滤器转换为我们的过滤器格式
const mapFilter = (filter: CrudFilter): Filter => {
  // 处理逻辑过滤器（OR/AND）
  if ('operator' in filter && (filter.operator === 'or' || filter.operator === 'and')) {
    // 处理逻辑过滤器
    const logicalFilter = filter as unknown as { field: string, operator: 'or' | 'and', value: CrudFilter[] };
    return {
      property: logicalFilter.field, // 即使是逻辑操作符，也需要添加property字段
      operator: logicalFilter.operator === 'or' ? LogicalOperator.OR : LogicalOperator.AND,
      value: logicalFilter.value.map((subFilter: CrudFilter) => mapFilter(subFilter))
    } as LogicalFilter;
  }

  // 处理条件过滤器
  if ('field' in filter) {
    // 处理属性过滤器
    return {
      property: filter.field,
      operator: mapOperator(filter.operator),
      value: filter.value
    } as PropertyFilter;
  }
  throw new Error('Invalid filter format');
};

// 将Refine的操作符转换为我们的操作符格式
const mapOperator = (operator: string): Operator => {
  switch (operator) {
    case 'eq': return Operator.EQUALS;
    case 'ne': return Operator.NOT_EQUALS;
    case 'lt': return Operator.LESS_THAN;
    case 'gt': return Operator.GREATER_THAN;
    case 'lte': return Operator.LESS_THAN_OR_EQUAL_TO;
    case 'gte': return Operator.GREATER_THAN_OR_EQUAL_TO;
    case 'in': return Operator.IN;
    case 'nin': return Operator.NOT_IN;
    case 'contains': return Operator.CONTAINS;
    case 'ncontains': return Operator.NOT_CONTAINS;
    case 'startswith': return Operator.STARTS_WITH;
    case 'endswith': return Operator.ENDS_WITH;
    case 'containss': return Operator.TAGS_CONTAINS;
    case 'null': return Operator.NULL;
    case 'or': return Operator.OR;
    case 'and': return Operator.AND;
    default: return Operator.EQUALS;
  }
};

// 将Refine的排序转换为我们的排序格式
const mapSorters = (sorters?: CrudSorting): Sort[] | undefined => {
  if (!sorters) return undefined;

  return sorters.map(sorter => ({
    property: sorter.field,
    direction: sorter.order === 'asc' ? SortDirection.ASC : SortDirection.DESC
  }));
};

export const customDataProvider = (): DataProvider => ({
  getList: async ({ resource, pagination, filters, sorters }) => {
    const payloads: QueryPayloads = {
      pageNo: pagination?.current || 1,
      pageSize: pagination?.pageSize || 10,
      filters: mapFilters(filters),
      sorts: mapSorters(sorters)
    };

    const response = await apiGetList(resource, payloads);

    return {
      data: response.data?.data || [],
      total: response.data?.total || 0
    };
  },

  getOne: async ({ resource, id }) => {
    const response = await apiGetOne(resource, id);

    return {
      data: response.data || {}
    };
  },

  create: async ({ resource, variables }) => {
    const response = await apiCreate(resource, variables);

    return {
      data: response.data || {}
    };
  },

  update: async ({ resource, id, variables }) => {
    const response = await apiUpdate(resource, id, variables);

    return {
      data: response.data || {}
    };
  },

  deleteOne: async ({ resource, id }) => {
    const response = await apiDeleteOne(resource, id);

    return {
      data: response.data || {}
    };
  },

  getApiUrl: () => {
    return "";
  },

  custom: async <TData extends BaseRecord = BaseRecord>() => {
    return { data: {} as TData };
  }
});