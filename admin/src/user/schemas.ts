// 用户相关类型定义

// 密钥状态枚举
export enum KeyStatus {
  ACTIVE = "active",
  INACTIVE = "inactive",
  EXPIRED = "expired",
}

// 货币类型枚举
export enum CurrencyType {
  // 法定货币
  CNY = "CNY", // 人民币
  USD = "USD", // 美元

  // 自定义货币
  LTC = "LTC", // LLM Token Counts
  UTS = "UTS", // Usage Times
}

// 密钥实体
export interface UserKey {
  id: string; // 密钥ID
  app_id: string; // 应用ID
  user_id: string; // 用户ID
  service_code: string; // 服务代码
  currency_type?: CurrencyType; // 信用货币类型，可能为空表示管理密钥
  scope?: string[]; // 作用域列表
  scope_names?: string[]; // 作用域名称列表
  credit_limit: number; // 信用额度
  credit_used: number; // 已使用额度
  expires_at?: string; // 过期时间
  created_at: string; // 创建时间
  updated_at: string; // 更新时间
}

// 用户密钥列表响应
export interface UserKeyList {
  keys: UserKey[]; // 密钥列表
}

// 创建密钥请求
export interface CreateKeyRequest {
  service_code: string; // 服务代码，如 "AGC"
  scope?: Array<string>; // 实例ID，可以是多个以逗号分隔
  currency_type: CurrencyType; // 货币类型
  credit_limit: number; // 信用额度
  expires_at?: string; // 过期时间，可选
}

// 用户实体
export interface User {
  id?: number; // 用户ID
  username: string; // 用户名
  phone: string; // 手机号
  password?: string; // 密码（加密存储）
  nickname: string; // 昵称
  comment: string; // 备注
  tags: string[]; // 标签列表
  can_create_agent: boolean; // 是否允许创建智能体
  is_allow_public_agent: boolean; // 是否允许公开智能体
  created_at: Date; // 创建时间
  updated_at: Date; // 更新时间
}

// 用户详情（包含关联数据）
export interface UserDetail extends User {
  orders?: any[]; // 订单列表
  assets?: any[]; // 用户资产
  agents?: any[]; // 代理
  accounts?: any[]; // 用户账户
}
