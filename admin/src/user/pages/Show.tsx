import React, { useEffect, useState } from "react";
import { useShow, useList, useResourceParams } from "@refinedev/core";
import { Show, DateField, TextField } from "@refinedev/antd";
import {
  Typography,
  Tabs,
  Tag,
  Space,
  Table,
  Card,
  Descriptions,
  Row,
  Col,
  Badge,
  Button,
} from "antd";
import { UserDetail, UserKey, KeyStatus, CurrencyType } from "../schemas";
import { entityKey } from "../consts";
import { getUserKeys, deleteUserKey } from "../api";
import { message, Modal } from "antd";

export const UserShow: React.FC = () => {
  // 获取资源ID
  const { id } = useResourceParams();

  // 获取用户详情
  const { query } = useShow<UserDetail>({
    resource: entityKey,
    id,
  });

  const { data, isLoading } = query;

  // 使用useList主动查询关联的数据
  const { data: ordersData, isLoading: ordersLoading } = useList({
    resource: "order",
    filters: [
      {
        field: "user_id",
        operator: "eq",
        value: id,
      },
    ],
    queryOptions: {
      enabled: !!id,
    },
  });

  const { data: agentsData, isLoading: agentsLoading } = useList({
    resource: "agent",
    filters: [
      {
        field: "owner_id",
        operator: "eq",
        value: id,
      },
    ],
    queryOptions: {
      enabled: !!id,
    },
  });

  // 使用状态来存储密钥数据和加载状态
  const [keysData, setKeysData] = useState<any>(null);
  const [keysLoading, setKeysLoading] = useState<boolean>(false);

  // 获取密钥的函数
  const fetchKeys = async () => {
    if (id) {
      try {
        setKeysLoading(true);
        console.log(`Fetching keys for user ${id}`);

        // 使用API文件中定义的方法
        const data = await getUserKeys(Number(id));
        console.log("Keys data:", data);
        setKeysData(data);
      } catch (error) {
        console.error("Failed to fetch keys:", error);
        message.error("获取密钥失败");
      } finally {
        setKeysLoading(false);
      }
    }
  };

  // 删除密钥的函数
  const handleDeleteKey = async (keyId: string) => {
    if (id) {
      Modal.confirm({
        title: "删除密钥",
        content: "确定要删除该密钥吗？删除后无法恢复。",
        okText: "确定",
        cancelText: "取消",
        onOk: async () => {
          try {
            setKeysLoading(true);
            await deleteUserKey(Number(id), keyId);
            message.success("密钥删除成功");
            // 重新获取密钥列表
            await fetchKeys();
          } catch (error) {
            console.error("Failed to delete key:", error);
            message.error("删除密钥失败");
          } finally {
            setKeysLoading(false);
          }
        },
      });
    }
  };

  // 在组件加载时获取密钥
  useEffect(() => {
    if (id) {
      fetchKeys();
    }
  }, [id]);

  const record = data?.data;

  if (isLoading) {
    return <div>加载中...</div>;
  }

  if (!record) {
    return <div>未找到用户</div>;
  }

  return (
    <Show
      isLoading={isLoading}
      title={
        <Typography.Title level={3}>
          用户详情: {record.username}
        </Typography.Title>
      }
    >
      <Row gutter={24}>
        <Col span={24}>
          <Card title="基本信息" className="mb-4">
            <Descriptions column={{ xs: 1, sm: 2, md: 3 }}>
              <Descriptions.Item label="用户ID">
                <TextField value={record.id} />
              </Descriptions.Item>
              <Descriptions.Item label="用户名">
                <TextField value={record.username} />
              </Descriptions.Item>
              <Descriptions.Item label="昵称">
                <TextField value={record.nickname || "-"} />
              </Descriptions.Item>
              <Descriptions.Item label="手机号">
                <TextField value={record.phone} />
              </Descriptions.Item>
              <Descriptions.Item label="是否允许创建智能体">
                <Tag color={record.can_create_agent ? "success" : "default"}>
                  {record.can_create_agent ? "是" : "否"}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="是否允许公开智能体">
                <Tag
                  color={record.is_allow_public_agent ? "success" : "default"}
                >
                  {record.is_allow_public_agent ? "是" : "否"}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                <DateField
                  format="YYYY-MM-DD HH:mm:ss"
                  value={record.created_at}
                />
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                <DateField
                  format="YYYY-MM-DD HH:mm:ss"
                  value={record.updated_at}
                />
              </Descriptions.Item>
              <Descriptions.Item label="标签" span={3}>
                <Space>
                  {record.tags && record.tags.length > 0 ? (
                    record.tags.map((tag: string) => (
                      <Tag color="blue" key={tag}>
                        {tag}
                      </Tag>
                    ))
                  ) : (
                    <span>-</span>
                  )}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="备注" span={3}>
                {record.comment || "-"}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col span={24}>
          <Tabs defaultActiveKey="orders">
            <Tabs.TabPane tab={`订单 (${ordersData?.total || 0})`} key="orders">
              <Card loading={ordersLoading}>
                {ordersData?.data && ordersData.data.length > 0 ? (
                  <Table
                    dataSource={ordersData.data}
                    rowKey="id"
                    pagination={{ pageSize: 5 }}
                    columns={[
                      { title: "订单号", dataIndex: "id", key: "id" },
                      {
                        title: "金额",
                        dataIndex: "amount",
                        key: "amount",
                        render: (text) => `¥${text?.toFixed(2)}`,
                      },
                      { title: "状态", dataIndex: "status", key: "status" },
                      {
                        title: "创建时间",
                        dataIndex: "created_at",
                        key: "created_at",
                        render: (text) => new Date(text).toLocaleString(),
                      },
                    ]}
                  />
                ) : (
                  <div>暂无订单数据</div>
                )}
              </Card>
            </Tabs.TabPane>

            <Tabs.TabPane tab={`作品 (${agentsData?.total || 0})`} key="agents">
              <Card loading={agentsLoading}>
                {agentsData?.data && agentsData.data.length > 0 ? (
                  <Table
                    dataSource={agentsData.data}
                    rowKey="id"
                    pagination={{ pageSize: 5 }}
                    columns={[
                      { title: "代理ID", dataIndex: "id", key: "id" },
                      { title: "名称", dataIndex: "name", key: "name" },
                      { title: "类型", dataIndex: "type", key: "type" },
                      {
                        title: "创建时间",
                        dataIndex: "created_at",
                        key: "created_at",
                        render: (text) => new Date(text).toLocaleString(),
                      },
                    ]}
                  />
                ) : (
                  <div>暂无代理数据</div>
                )}
              </Card>
            </Tabs.TabPane>

            <Tabs.TabPane
              tab={`密钥 (${keysData?.data?.keys?.length || 0})`}
              key="keys"
            >
              <Card
                loading={keysLoading}
                extra={
                  <Button
                    type="primary"
                    onClick={fetchKeys}
                    loading={keysLoading}
                  >
                    刷新密钥
                  </Button>
                }
              >
                {keysData?.data?.keys && keysData.data.keys.length > 0 ? (
                  <Table
                    dataSource={keysData.data.keys}
                    rowKey="id"
                    pagination={{ pageSize: 5 }}
                    columns={[
                      {
                        title: "密钥ID",
                        dataIndex: "id",
                        key: "id",
                        width: 280,
                      },
                      {
                        title: "服务代码",
                        dataIndex: "service_code",
                        key: "service_code",
                      },
                      {
                        title: "作用域",
                        key: "scope",
                        render: (_, record: UserKey) => {
                          // 如果有作用域名称列表，优先显示名称
                          if (
                            record.scope_names &&
                            record.scope_names.length > 0
                          ) {
                            return (
                              <span>
                                {record.scope_names.map(
                                  (name: string, index: number) => (
                                    <Tag key={`${name}-${index}`} color="blue">
                                      {name}
                                    </Tag>
                                  )
                                )}
                              </span>
                            );
                          }
                          // 如果没有名称但有作用域ID列表，显示ID
                          else if (record.scope && record.scope.length > 0) {
                            return (
                              <span>
                                {record.scope.map(
                                  (id: string, index: number) => (
                                    <Tag key={`${id}-${index}`} color="blue">
                                      {id}
                                    </Tag>
                                  )
                                )}
                              </span>
                            );
                          }
                          // 没有作用域的情况
                          return <span>-</span>;
                        },
                      },
                      {
                        title: "额度使用",
                        key: "credit",
                        render: (_, record: UserKey) => {
                          // 根据货币类型显示不同的格式
                          let prefix = "";
                          let suffix = "";
                          let tagColor = "blue";

                          switch (record.currency_type) {
                            case CurrencyType.CNY:
                              prefix = "¥"; // 人民币符号
                              tagColor = "green";
                              break;
                            case CurrencyType.USD:
                              prefix = "$"; // 美元符号
                              tagColor = "green";
                              break;
                            case CurrencyType.LTC:
                              suffix = " tokens"; // LLM Token Counts
                              tagColor = "purple";
                              break;
                            case CurrencyType.UTS:
                              suffix = " 次"; // Usage Times
                              tagColor = "orange";
                              break;
                            default:
                              // 默认情况
                              break;
                          }

                          return (
                            <span>
                              {prefix}
                              {record.credit_used} / {record.credit_limit}
                              {suffix}
                              <Tag color={tagColor} style={{ marginLeft: 8 }}>
                                {record.currency_type}
                              </Tag>
                            </span>
                          );
                        },
                      },

                      {
                        title: "创建时间",
                        key: "created_at",
                        render: (_, record: UserKey) => (
                          <span>
                            {new Date(record.created_at).toLocaleString()}
                          </span>
                        ),
                      },
                      {
                        title: "过期时间",
                        key: "expires_at",
                        render: (_, record: UserKey) => (
                          <span>
                            {record.expires_at
                              ? new Date(record.expires_at).toLocaleString()
                              : "-"}
                          </span>
                        ),
                      },
                      {
                        title: "操作",
                        key: "action",
                        render: (_, record: UserKey) => (
                          <Button
                            type="link"
                            danger
                            onClick={() => handleDeleteKey(record.id)}
                          >
                            删除
                          </Button>
                        ),
                      },
                    ]}
                  />
                ) : (
                  <div>暂无密钥数据</div>
                )}
              </Card>
            </Tabs.TabPane>
          </Tabs>
        </Col>
      </Row>
    </Show>
  );
};
