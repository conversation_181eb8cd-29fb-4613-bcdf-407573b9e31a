import React from "react";
import { Create, useForm } from "@refinedev/antd";
import { Form, Input, Select, Switch } from "antd";
import { User } from "../schemas";
import { entityKey } from "../consts";

export const UserCreate: React.FC = () => {
  const { formProps, saveButtonProps } = useForm<User>({
    resource: entityKey,
  });

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Form.Item
          label="用户名"
          name="username"
          rules={[{ required: true, message: "请输入用户名" }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>

        <Form.Item
          label="昵称"
          name="nickname"
        >
          <Input placeholder="请输入昵称" />
        </Form.Item>

        <Form.Item
          label="手机号"
          name="phone"
          rules={[
            { required: true, message: "请输入手机号" },
            {
              pattern: /^1[3-9]\d{9}$/,
              message: "请输入正确的手机号码"
            }
          ]}
        >
          <Input placeholder="请输入手机号" />
        </Form.Item>

        <Form.Item
          label="密码"
          name="password"
          rules={[
            { required: true, message: "请输入密码" },
            { min: 6, message: "密码长度至少为6位" }
          ]}
        >
          <Input.Password placeholder="请输入密码" />
        </Form.Item>

        <Form.Item
          label="确认密码"
          name="confirmPassword"
          dependencies={["password"]}
          rules={[
            { required: true, message: "请确认密码" },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("password") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error("两次输入的密码不一致"));
              },
            }),
          ]}
        >
          <Input.Password placeholder="请确认密码" />
        </Form.Item>

        <Form.Item
          label="标签"
          name="tags"
        >
          <Select
            mode="tags"
            style={{ width: '100%' }}
            placeholder="请输入标签"
            tokenSeparators={[',']}
          />
        </Form.Item>

        <Form.Item
          label="备注"
          name="comment"
        >
          <Input.TextArea rows={4} placeholder="请输入备注" />
        </Form.Item>

        <Form.Item
          label="允许创建智能体"
          name="can_create_agent"
          valuePropName="checked"
          initialValue={false}
        >
          <Switch checkedChildren="是" unCheckedChildren="否" />
        </Form.Item>

        <Form.Item
          label="允许公开智能体"
          name="is_allow_public_agent"
          valuePropName="checked"
          initialValue={false}
        >
          <Switch checkedChildren="是" unCheckedChildren="否" />
        </Form.Item>
      </Form>
    </Create>
  );
};
