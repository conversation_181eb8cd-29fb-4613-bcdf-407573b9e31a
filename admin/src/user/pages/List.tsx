import {
  Delete<PERSON><PERSON><PERSON>,
  EditButton,
  List,
  ShowButton,
  useTable,
} from "@refinedev/antd";
import type { BaseRecord, CrudFilters } from "@refinedev/core";
import { Divider, Form, Input, Space, Table, Tag, Select, Button } from "antd";
import { useState } from "react";
import { SearchForm } from "@/common/components";
import { entityKey } from "../consts";
import { User } from "../schemas";
import { CreateKeyModal } from "../components/CreateKey";

interface SearchFormValues {
  username?: string;
  phone?: string;
  nickname?: string;
  can_create_agent?: boolean;
}

export const UserList = () => {
  // 创建密钥相关状态
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [selectedUserName, setSelectedUserName] = useState<string>("");

  // 打开创建密钥对话框
  const showCreateKeyModal = (record: User) => {
    setSelectedUserId(record.id!);
    setSelectedUserName(record.username);
    setIsModalVisible(true);
  };

  // 关闭对话框
  const handleCloseModal = () => {
    setIsModalVisible(false);
  };
  const { tableProps, searchFormProps } = useTable<User>({
    resource: entityKey,
    syncWithLocation: false,
    defaultSetFilterBehavior: "replace",
    onSearch: (values) => {
      const filters: CrudFilters = [];
      const data = values as SearchFormValues;

      // 构建搜索过滤器
      if (data.username) {
        filters.push({
          field: "username",
          operator: "contains",
          value: data.username,
        });
      }

      if (data.phone) {
        filters.push({
          field: "phone",
          operator: "contains",
          value: data.phone,
        });
      }

      if (data.nickname) {
        filters.push({
          field: "nickname",
          operator: "contains",
          value: data.nickname,
        });
      }

      if (data.can_create_agent !== undefined) {
        filters.push({
          field: "can_create_agent",
          operator: "eq",
          value: data.can_create_agent,
        });
      }
      return filters;
    },
  });

  return (
    <>
      <List>
        {/* 使用通用搜索表单组件 */}
        <SearchForm
          searchFormProps={searchFormProps}
          useCard={true}
          layout="inline"
        >
          <Form.Item label="用户名" name="username">
            <Input placeholder="请输入用户名" allowClear />
          </Form.Item>
          <Form.Item label="手机号" name="phone">
            <Input placeholder="请输入手机号" allowClear />
          </Form.Item>
          <Form.Item label="昵称" name="nickname">
            <Input placeholder="请输入昵称" allowClear />
          </Form.Item>
          <Form.Item label="允许创建智能体" name="can_create_agent">
            <Select
              placeholder="请选择"
              allowClear
              options={[
                { label: '是', value: true },
                { label: '否', value: false },
              ]}
            />
          </Form.Item>
          <Form.Item label="允许公开智能体" name="is_allow_public_agent">
            <Select
              placeholder="请选择"
              allowClear
              options={[
                { label: '是', value: true },
                { label: '否', value: false },
              ]}
            />
          </Form.Item>
        </SearchForm>

        <Divider className="my-4" />

        <Table {...tableProps} rowKey="id">
          <Table.Column dataIndex="id" title="ID" />
          <Table.Column dataIndex="username" title="用户名" />
          <Table.Column dataIndex="nickname" title="昵称" />
          <Table.Column dataIndex="phone" title="手机号" />
          <Table.Column
            dataIndex="can_create_agent"
            title="允许创建智能体"
            render={(value) => (
              <Tag color={value ? "success" : "default"}>
                {value ? "是" : "否"}
              </Tag>
            )}
          />
          <Table.Column
            dataIndex="is_allow_public_agent"
            title="允许公开智能体"
            render={(value) => (
              <Tag color={value ? "success" : "default"}>
                {value ? "是" : "否"}
              </Tag>
            )}
          />
          <Table.Column
            dataIndex="tags"
            title="标签"
            render={(tags: string[]) => (
              <>
                {tags && tags.map(tag => (
                  <Tag color="blue" key={tag}>
                    {tag}
                  </Tag>
                ))}
              </>
            )}
          />
          <Table.Column
            dataIndex="comment"
            title="备注"
            ellipsis
            render={(value) => value || '-'}
          />
          <Table.Column
            dataIndex="created_at"
            title="创建时间"
            render={(value) => new Date(value).toLocaleString()}
          />
          <Table.Column
            dataIndex="updated_at"
            title="更新时间"
            render={(value) => new Date(value).toLocaleString()}
          />
          <Table.Column
            title="操作"
            dataIndex="actions"
            render={(_, record: BaseRecord) => (
              <Space>
                <Button
                  type="primary"
                  size="small"
                  onClick={() => showCreateKeyModal(record as User)}
                  title="创建密钥"
                >
                  创建密钥
                </Button>
                <EditButton hideText size="small" recordItemId={record.id} />
                <ShowButton hideText size="small" recordItemId={record.id} />
                <DeleteButton hideText size="small" recordItemId={record.id} />
              </Space>
            )}
          />
        </Table>
      </List>

      {/* 创建密钥对话框 */}
      {selectedUserId && (
        <CreateKeyModal
          userId={selectedUserId}
          userName={selectedUserName}
          visible={isModalVisible}
          onClose={handleCloseModal}
        />
      )}
    </>


  );
};
