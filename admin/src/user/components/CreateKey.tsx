import React, { useState } from "react";
import { Form, Input, Select, DatePicker, message, Modal } from "antd";
import { useList, CrudFilters } from "@refinedev/core";
import dayjs from "dayjs";
import utc from "dayjs/plugin/utc";
import timezone from "dayjs/plugin/timezone";

// 配置dayjs使用时区插件
dayjs.extend(utc);
dayjs.extend(timezone);
import { CurrencyType } from "../schemas";
import { createUserKey } from "../api";

interface CreateKeyProps {
  userId: number;
  userName: string;
  visible: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export const CreateKeyModal: React.FC<CreateKeyProps> = ({
  userId,
  userName,
  visible,
  onClose,
  onSuccess,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 不再需要获取当前管理员信息，因为我们使用选中的用户ID

  // 自定义逻辑过滤器，包含field字段以满足后端要求

  // 获取智能体列表 - 只查询owner_id为null或等于选中用户ID的智能体
  const { data: agentsData } = useList({
    resource: "agent",
    filters: [
      {
        operator: "or",
        value: [
          // 查询owner_id为null的智能体
          {
            field: "owner_id",
            operator: "null",
            value: true
          },
          // 查询owner_id等于选中用户ID的智能体
          {
            field: "owner_id",
            operator: "eq",
            value: userId
          }
        ]
      }
    ],
    pagination: {
      pageSize: 9999
    },
    sorters: [
      {
        field: "sort_order",
        order: "asc" // 按sort_order字段升序排序
      }
    ]
  });

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 处理过期时间
      let expiresAt = undefined;
      if (values.expires_at) {
        // 将选择的时间转换为北京时区的时间
        const beijingTime = dayjs(values.expires_at).tz("Asia/Shanghai");
        // 保持北京时间的年月日时分秒，但使用ISO格式
        expiresAt = beijingTime.format('YYYY-MM-DDTHH:mm:ss.SSS[Z]');
        console.log('Selected Beijing time:', beijingTime.format('YYYY-MM-DD HH:mm:ss'));
        console.log('Formatted expires_at:', expiresAt);
      }

      // 处理实例ID
      let scope = undefined;
      if (values.instance_ids && values.instance_ids.length > 0) {
        scope = values.instance_ids; // 直接使用数组，不需要join
      }
      // 如果没有选择智能体，则不传递scope参数，表示适用于所有智能体

      // 创建密钥
      const result = await createUserKey(userId, {
        service_code: values.service_code,
        scope: scope,
        currency_type: values.currency_type,
        credit_limit: values.credit_limit,
        expires_at: expiresAt
      });

      message.success(`密钥创建成功，ID: ${result.data?.id}`);
      form.resetFields();
      if (onSuccess) {
        onSuccess();
      }
      onClose();
    } catch (error) {
      console.error("创建密钥失败:", error);
      message.error(`创建密钥失败: ${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 当服务类型改变时更新货币类型
  const handleServiceCodeChange = (value: string) => {
    // 如果选择了数字人服务，则强制设置货币类型为人民币
    if (value === 'DGV') {
      form.setFieldsValue({ currency_type: CurrencyType.CNY });
    }
  };

  return (
    <Modal
      title={`为用户 ${userName} 创建密钥`}
      open={visible}
      onOk={handleSubmit}
      onCancel={onClose}
      confirmLoading={loading}
      width={600}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          service_code: "AGC",
          currency_type: CurrencyType.UTS,
          credit_limit: 1000
        }}
        preserve={false}
      >
        <Form.Item
          name="service_code"
          label="服务"
          rules={[{ required: true, message: '请选择服务' }]}
        >
          <Select onChange={handleServiceCodeChange}>
            <Select.Option value="AGC">智能体聊天 (AGC)</Select.Option>
            <Select.Option value="DGV">数字人 (DGV)</Select.Option>
          </Select>
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.service_code !== currentValues.service_code}
        >
          {({ getFieldValue }) => {
            const serviceCode = getFieldValue('service_code');
            return serviceCode === 'AGC' ? (
              <Form.Item
                name="instance_ids"
                label="智能体"
                tooltip="不选择智能体表示适用于所有智能体"
              >
                <Select
                  mode="multiple"
                  placeholder="请选择智能体"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    (option?.children as unknown as string).toLowerCase().includes(input.toLowerCase())
                  }
                >
                  {agentsData?.data?.map((agent: any) => (
                    <Select.Option key={agent.id} value={agent.id}>
                      {agent.name}
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            ) : null;
          }}
        </Form.Item>

        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => prevValues.service_code !== currentValues.service_code}
        >
          {({ getFieldValue }) => {
            const serviceCode = getFieldValue('service_code');
            return (
              <Form.Item
                name="currency_type"
                label="货币类型"
                rules={[{ required: true, message: '请选择货币类型' }]}
              >
                <Select disabled={serviceCode === 'DGV'}>
                  <Select.Option value={CurrencyType.CNY}>人民币 (CNY)</Select.Option>
                  {serviceCode !== 'DGV' && (
                    <>
                      <Select.Option value={CurrencyType.USD}>美元 (USD)</Select.Option>
                      <Select.Option value={CurrencyType.LTC}>LLM Token Counts (LTC)</Select.Option>
                      <Select.Option value={CurrencyType.UTS}>使用次数 (UTS)</Select.Option>
                    </>
                  )}
                </Select>
              </Form.Item>
            );
          }}
        </Form.Item>

        <Form.Item
          name="credit_limit"
          label="信用额度"
          rules={[{ required: true, message: '请输入信用额度' }]}
        >
          <Input type="number" min={1} />
        </Form.Item>

        <Form.Item
          name="expires_at"
          label="过期时间"
        >
          <DatePicker
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择过期时间（北京时间）"
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};
