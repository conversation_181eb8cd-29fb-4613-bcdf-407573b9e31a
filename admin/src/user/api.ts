import { API_BASE_URL } from "@/common/env";
import { ResponsePayloads } from "@/common/schemas";
import { UserKeyList, CreateKeyRequest } from "./schemas";
import { request } from "@/common/utils";


/**
 * 获取用户密钥列表
 * @param userId 用户ID
 * @returns 密钥列表
 */
export const getUserKeys = async (userId: number): Promise<ResponsePayloads<UserKeyList>> => {
  const response = await request(`${API_BASE_URL}/users/${userId}/keys`, {
    method: "GET",
    headers: {
      "Accept": "application/json"
    }
  });

  if (!response.ok) {
    throw new Error(`获取用户密钥失败: ${response.statusText}`);
  }

  return await response.json();
};

/**
 * 删除用户密钥
 * @param userId 用户ID
 * @param keyId 密钥ID
 * @returns 操作结果
 */
export const deleteUserKey = async (userId: number, keyId: string): Promise<ResponsePayloads<{ success: boolean }>> => {
  const response = await request(`${API_BASE_URL}/users/${userId}/keys/${keyId}`, {
    method: "DELETE",
    headers: {
      "Accept": "application/json"
    }
  });

  if (!response.ok) {
    throw new Error(`删除密钥失败: ${response.statusText}`);
  }

  return await response.json();
};

/**
 * 创建用户密钥
 * @param userId 用户ID
 * @param data 密钥创建数据
 * @returns 创建结果
 */
export const createUserKey = async (userId: number, data: CreateKeyRequest): Promise<ResponsePayloads<{ id: string; success: boolean }>> => {
  const response = await request(`${API_BASE_URL}/users/${userId}/keys`, {
    method: "POST",
    headers: {
      "Accept": "application/json",
      "Content-Type": "application/json"
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    throw new Error(`创建密钥失败: ${response.statusText}`);
  }

  return await response.json();
};