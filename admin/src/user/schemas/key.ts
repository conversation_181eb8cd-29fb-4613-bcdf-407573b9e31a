// 用户密钥相关类型定义
 

// 密钥实体
export interface UserKey {
  id: string; // 密钥ID
  user_id: string; // 用户ID
  service_code: string; // 服务代码
  instance_id?: string; // 实例ID（如智能体ID）
  instance_name?: string; // 实例名称（如智能体名称）
  credit_limit: number; // 信用额度
  credit_used: number; // 已使用额度 
  created_at: string; // 创建时间
  expires_at?: string; // 过期时间
}

// 用户密钥列表响应
export interface UserKeyList {
  keys: UserKey[]; // 密钥列表
}
