import React from 'react';
import { Tag } from 'antd';
import { CurrencyType } from '../schemas';

interface CurrencyTypeTagProps {
  value: string;
}

/**
 * 货币类型标签组件
 *
 * 用于在列表和详情页面显示货币类型
 */
export const CurrencyTypeTag: React.FC<CurrencyTypeTagProps> = ({ value }) => {
  const currencyTypeMap: Record<string, { text: string, color: string }> = {
    [CurrencyType.CNY]: { text: '人民币', color: 'green' },
    [CurrencyType.USD]: { text: '美元', color: 'blue' },
    [CurrencyType.LTC]: { text: 'LLM Token Counts', color: 'purple' },
    [CurrencyType.UTS]: { text: '体验次数', color: 'orange' },
  };

  const config = currencyTypeMap[value] || { text: value, color: 'default' };
  return <Tag color={config.color}>{config.text} ({value})</Tag>;
};

export default CurrencyTypeTag;
