import React from 'react';
import { Select } from 'antd';
import { CurrencyType } from '../schemas';

interface CurrencyTypeSelectProps {
  value?: CurrencyType;
  onChange?: (value: CurrencyType) => void;
  placeholder?: string;
  allowClear?: boolean;
  disabled?: boolean;
  style?: React.CSSProperties;
}

/**
 * 货币类型选择器组件
 *
 * 用于在表单中选择货币类型
 */
export const CurrencyTypeSelect: React.FC<CurrencyTypeSelectProps> = ({
  value,
  onChange,
  placeholder = "请选择信用货币类型",
  allowClear = false,
  disabled = false,
  style,
}) => {
  return (
    <Select
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      allowClear={allowClear}
      disabled={disabled}
      style={style}
    >
      <Select.Option value={CurrencyType.CNY}>人民币 (CNY)</Select.Option>
      <Select.Option value={CurrencyType.USD}>美元 (USD)</Select.Option>
      <Select.Option value={CurrencyType.LTC}>LLM Token Counts (LTC)</Select.Option>
      <Select.Option value={CurrencyType.UTS}>体验次数 (UTS)</Select.Option>
    </Select>
  );
};

export default CurrencyTypeSelect;
