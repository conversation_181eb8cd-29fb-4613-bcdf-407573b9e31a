// 密钥相关类型定义

// 信用货币类型枚举
export enum CurrencyType {
  CNY = "CNY", // 人民币
  USD = "USD", // 美元
  LTC = "LTC", // LLM Token Counts
  UTS = "UTS"  // Usage Times
}

// 密钥实体
export interface Key {
  id: number; // 密钥ID
  app_id: string; // 应用ID
  user_id: string; // 用户ID
  service_id: number; // 服务ID
  scope?: string[]; // 作用域，JSON数组格式
  currency_type: CurrencyType; // 信用货币类型
  credit_limit: number; // 额度限制
  credit_used: number; // 已使用额度
  expires_at?: Date; // 过期时间
  created_at: Date; // 创建时间
  updated_at: Date; // 更新时间
}

// 密钥详情（包含关联数据）
export interface KeyDetail extends Key {
  activities?: Activity[]; // 活动记录
  app?: {
    app_id: string;
    name: string;
  }; // 关联的应用信息
  service?: {
    id: number;
    name: string;
    code: string;
  }; // 关联的服务信息
}

// 密钥创建请求
export interface KeyCreateRequest {
  app_id: string; // 应用ID
  user_id: string; // 用户ID
  service_id: number; // 服务ID
  scope?: string[]; // 作用域，JSON数组格式
  currency_type: CurrencyType; // 信用货币类型
  credit_limit: number; // 额度限制
  expires_at?: Date; // 过期时间
}

// 密钥更新请求
export interface KeyUpdateRequest {
  user_id?: string; // 用户ID
  scope?: string[]; // 作用域，JSON数组格式
  currency_type?: CurrencyType; // 信用货币类型
  credit_limit?: number; // 额度限制
  expires_at?: Date; // 过期时间
}

// 活动类型枚举
export enum ActivityType {
  VERIFY = "verify", // 验证
  CONSUME = "consume" // 消费
}

// 活动实体
export interface Activity {
  id: number; // 活动ID
  key_id: number; // 密钥ID
  type: ActivityType; // 活动类型
  amount: number; // 消费数量
  metadata?: any; // 元数据
  created_at: Date; // 创建时间
}
