import React, { useState } from "react";
import { useShow, useResource, useList, useUpdate } from "@refinedev/core";
import {
  Show,
  DateField,
  TextField,
  NumberField,
} from "@refinedev/antd";
import {
  Typography,
  Tabs,
  Tag,
  Space,
  Table,
  Card,
  Descriptions,
  Row,
  Col,
  Button,
  Modal,
  Form,
  InputNumber,
  Input,
  Progress,
  Divider,
} from "antd";
import { KeyDetail, Activity, ActivityType } from "../schemas";
import RecordNameCell from "@/common/components/RecordName";
import { entityKey } from "../consts";
import { CurrencyTypeTag } from "../components";

const { Title } = Typography;
const { TextArea } = Input;

export const KeyShow: React.FC = () => {
  const { id } = useResource();
  const { queryResult } = useShow<KeyDetail>({
    resource: entityKey,
    id,
  });

  // 使用 useList 钩子获取活动记录
  const { data: activitiesData, isLoading: activitiesLoading, refetch: refetchActivities } = useList<Activity>({
    resource: "activity",
    filters: [
      {
        field: "key_id",
        operator: "eq",
        value: id,
      },
    ],
  });

  // 使用 useUpdate 钩子消费密钥
  const { mutate: updateKey } = useUpdate();

  const [isConsumeModalVisible, setIsConsumeModalVisible] = useState<boolean>(false);
  const [consumeLoading, setConsumeLoading] = useState<boolean>(false);
  const [consumeForm] = Form.useForm();

  const { data, isLoading } = queryResult;
  const record = data?.data;

  // 从 activitiesData 中提取活动记录列表
  const activities = activitiesData?.data || [];



  // 消费密钥
  const handleConsume = () => {
    setIsConsumeModalVisible(true);
  };

  const handleConsumeModalOk = () => {
    consumeForm.validateFields()
      .then(values => {
        if (id) {
          setConsumeLoading(true);

          // 使用 fetch 直接请求消费密钥
          fetch(`/api/keys/consume`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              key_id: id,
              amount: values.amount,
              metadata: values.metadata ? JSON.parse(values.metadata) : undefined
            }),
          })
            .then(response => response.json())
            .then(() => {
              // 刷新数据
              queryResult.refetch();
              // 刷新活动记录
              refetchActivities();
              setIsConsumeModalVisible(false);
              consumeForm.resetFields();
            })
            .catch(error => {
              console.error("消费密钥失败:", error);
            })
            .finally(() => {
              setConsumeLoading(false);
            });
        }
      })
      .catch(info => {
        console.log("表单验证失败:", info);
      });
  };

  const handleConsumeModalCancel = () => {
    setIsConsumeModalVisible(false);
    consumeForm.resetFields();
  };

  if (isLoading) {
    return <div>加载中...</div>;
  }

  if (!record) {
    return <div>未找到密钥</div>;
  }

  // 计算可用额度和使用百分比
  const creditAvailable = record.credit_limit - record.credit_used;
  const usagePercentage = (record.credit_used / record.credit_limit) * 100;

  return (
    <Show
      isLoading={isLoading}
      title={<Typography.Title level={3}>密钥详情: {record.id}</Typography.Title>}
      headerButtons={({ defaultButtons }) => (
        <>
          {defaultButtons}
          <Button type="primary" onClick={handleConsume}>消费密钥</Button>
        </>
      )}
    >
      <Row gutter={24}>
        <Col span={24}>
          <Card title="基本信息" className="mb-4">
            <Descriptions column={{ xs: 1, sm: 2, md: 3 }}>
              <Descriptions.Item label="密钥ID">
                <TextField value={record.id} />
              </Descriptions.Item>
              <Descriptions.Item label="应用ID">
                <TextField value={record.app_id} />
              </Descriptions.Item>
              <Descriptions.Item label="用户ID">
                <TextField value={record.user_id} />
              </Descriptions.Item>
              <Descriptions.Item label="服务">
                <RecordNameCell value={record.service_id} resource="service" nameField="name" />
              </Descriptions.Item>
              <Descriptions.Item label="信用货币类型">
                <CurrencyTypeTag value={record.currency_type} />
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                <DateField format="YYYY-MM-DD HH:mm:ss" value={record.created_at} />
              </Descriptions.Item>
              <Descriptions.Item label="过期时间">
                {record.expires_at ? (
                  <DateField format="YYYY-MM-DD HH:mm:ss" value={record.expires_at} />
                ) : (
                  "永不过期"
                )}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col span={24}>
          <Card title="额度信息" className="mb-4">
            <Row gutter={16}>
              <Col span={8}>
                <Statistic title="额度限制" value={record.credit_limit} />
              </Col>
              <Col span={8}>
                <Statistic title="已使用额度" value={record.credit_used} />
              </Col>
              <Col span={8}>
                <Statistic title="可用额度" value={creditAvailable} />
              </Col>
            </Row>
            <Divider />
            <Progress
              percent={usagePercentage}
              status={usagePercentage >= 90 ? "exception" : "active"}
              strokeWidth={20}
              format={percent => `${percent?.toFixed(2)}%`}
            />
          </Card>
        </Col>

        <Col span={24}>
          <Card title="活动记录" loading={activitiesLoading}>
            {activities && activities.length > 0 ? (
              <Table
                dataSource={activities}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                columns={[
                  { title: "活动ID", dataIndex: "id", key: "id" },
                  {
                    title: "类型",
                    dataIndex: "type",
                    key: "type",
                    render: (type) => {
                      const typeMap: Record<string, { text: string, color: string }> = {
                        [ActivityType.VERIFY]: { text: "验证", color: "blue" },
                        [ActivityType.CONSUME]: { text: "消费", color: "green" }
                      };
                      return <Tag color={typeMap[type]?.color || "default"}>{typeMap[type]?.text || type}</Tag>;
                    }
                  },
                  { title: "数量", dataIndex: "amount", key: "amount" },
                  {
                    title: "元数据",
                    dataIndex: "metadata",
                    key: "metadata",
                    render: (metadata) => metadata ? (
                      <pre style={{ maxHeight: '100px', overflow: 'auto' }}>
                        {JSON.stringify(metadata, null, 2)}
                      </pre>
                    ) : '-'
                  },
                  {
                    title: "创建时间",
                    dataIndex: "created_at",
                    key: "created_at",
                    render: (text) => new Date(text).toLocaleString()
                  }
                ]}
              />
            ) : (
              <div>暂无活动记录</div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 消费密钥的模态框 */}
      <Modal
        title="消费密钥"
        open={isConsumeModalVisible}
        onOk={handleConsumeModalOk}
        onCancel={handleConsumeModalCancel}
        confirmLoading={consumeLoading}
      >
        <Form form={consumeForm} layout="vertical">
          <Form.Item
            name="amount"
            label="消费数量"
            rules={[
              { required: true, message: "请输入消费数量" },
              { type: 'number', min: 0.01, message: "消费数量必须大于0" }
            ]}
          >
            <InputNumber
              min={0.01}
              step={0.01}
              style={{ width: '100%' }}
              placeholder="请输入消费数量"
            />
          </Form.Item>
          <Form.Item
            name="metadata"
            label="元数据 (JSON格式)"
          >
            <TextArea
              rows={4}
              placeholder='{"key": "value"}'
            />
          </Form.Item>
        </Form>
      </Modal>
    </Show>
  );
};

// 统计数字组件
const Statistic = ({ title, value }: { title: string, value: number }) => (
  <div style={{ textAlign: 'center' }}>
    <div style={{ fontSize: '14px', color: 'rgba(0, 0, 0, 0.45)' }}>{title}</div>
    <div style={{ fontSize: '24px', fontWeight: 'bold', marginTop: '8px' }}>{value}</div>
  </div>
);
