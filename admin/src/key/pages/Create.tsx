import React from "react";
import { Create, useForm, useSelect } from "@refinedev/antd";
import { Form, Input, Select, DatePicker, InputNumber } from "antd";
import { Key } from "../schemas";
import { entityKey } from "../consts";
import { CurrencyTypeSelect } from "../components";
import dayjs from "dayjs";

export const KeyCreate: React.FC = () => {
  const { formProps, saveButtonProps } = useForm<Key>({
    resource: entityKey,
  });

  // 使用 useSelect 钩子获取应用列表
  const { selectProps: appSelectProps } = useSelect({
    resource: "app",
    optionLabel: "name",
    optionValue: "app_id",
  });

  // 使用 useSelect 钩子获取服务列表
  const { selectProps: serviceSelectProps } = useSelect({
    resource: "service",
    optionLabel: "name",
    optionValue: "id",
  });

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Form.Item
          label="应用"
          name="app_id"
          rules={[{ required: true, message: "请选择应用ID" }]}
        >
          <Select
            {...appSelectProps}
            placeholder="请选择应用ID"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          label="用户ID"
          name="user_id"
        >
          <Input placeholder="请输入用户ID" />
        </Form.Item>

        <Form.Item
          label="作用域"
          name="scope"
        >
          <Select
            mode="tags"
            style={{ width: '100%' }}
            placeholder="请输入作用域（可选，多个值以逗号分隔）"
            tokenSeparators={[',']}
          />
        </Form.Item>

        <Form.Item
          label="信用货币类型"
          name="currency_type"
        >
          <CurrencyTypeSelect />
        </Form.Item>

        <Form.Item
          label="服务"
          name="service_id"
          rules={[{ required: true, message: "请选择服务" }]}
        >
          <Select
            {...serviceSelectProps}
            placeholder="请选择服务"
            style={{ width: '100%' }}
          />
        </Form.Item>

        <Form.Item
          label="额度限制"
          name="credit_limit"
        >
          <InputNumber
            min={0}
            step={1}
            style={{ width: '100%' }}
            placeholder="请输入额度限制"
          />
        </Form.Item>

        <Form.Item
          label="过期时间"
          name="expires_at"
          getValueProps={(value) => ({
            value: value ? dayjs(value) : undefined,
          })}
        >
          <DatePicker
            showTime
            style={{ width: '100%' }}
            placeholder="请选择过期时间（可选）"
          />
        </Form.Item>
      </Form>
    </Create>
  );
};
