// 图片上传相关常量配置

/**
 * 支持的图片文件类型
 */
export const SUPPORTED_IMAGE_TYPES = [
  'image/jpeg',
  'image/jpg', 
  'image/png',
  'image/gif',
  'image/webp'
] as const;

/**
 * 图片文件最大大小（MB）
 */
export const MAX_IMAGE_SIZE = 5;

/**
 * 图片上传配置
 */
export const IMAGE_UPLOAD_CONFIG = {
  maxSize: MAX_IMAGE_SIZE,
  acceptedTypes: SUPPORTED_IMAGE_TYPES,
  helpText: `支持 ${SUPPORTED_IMAGE_TYPES.map(type => type.split('/')[1].toUpperCase()).join('、')} 格式，文件大小不超过 ${MAX_IMAGE_SIZE}MB`
} as const;

/**
 * 支持的视频文件类型
 */
export const SUPPORTED_VIDEO_TYPES = [
  'video/mp4',
  'video/avi',
  'video/mov',
  'video/wmv',
  'video/flv',
  'video/webm'
] as const;

/**
 * 视频文件最大大小（MB）
 */
export const MAX_VIDEO_SIZE = 100;

/**
 * 视频上传配置
 */
export const VIDEO_UPLOAD_CONFIG = {
  maxSize: MAX_VIDEO_SIZE,
  acceptedTypes: SUPPORTED_VIDEO_TYPES,
  helpText: `支持 ${SUPPORTED_VIDEO_TYPES.map(type => type.split('/')[1].toUpperCase()).join('、')} 格式，文件大小不超过 ${MAX_VIDEO_SIZE}MB`
} as const;

/**
 * 课程相关文件上传路径
 */
export const COURSE_UPLOAD_PATHS = {
  // 图片路径
  cover: 'courses/covers/',
  poster: 'courses/posters/',
  // 视频路径
  videos: 'courses/videos/'
} as const;

// 保持向后兼容
export const COURSE_IMAGE_PATHS = {
  cover: COURSE_UPLOAD_PATHS.cover,
  poster: COURSE_UPLOAD_PATHS.poster
} as const;
