import { Show } from "@refinedev/antd";
import { Typography, Card, Tag, Space, Descriptions, Image } from "antd";
import React from "react";
import { Course } from "@/course";
import { entityKey } from "@/course";
import { useShow } from "@refinedev/core";

const { Title } = Typography;

const CourseShow: React.FC = () => {
  const { query } = useShow<Course>({
    resource: entityKey
  });
  const { data, isLoading } = query;
  const record = data?.data;

  return (
    <Show isLoading={isLoading}>
      {record && (
        <Card>
          <Title level={4}>{record.title}</Title>
          
          <Descriptions bordered column={2}>
            <Descriptions.Item label="课程ID">
              {record.id}
            </Descriptions.Item>
            
            <Descriptions.Item label="导师">
              {record.instructor || '-'}
            </Descriptions.Item>

            <Descriptions.Item label="课程描述" span={2}>
              {record.description || '-'}
            </Descriptions.Item>
            
            <Descriptions.Item label="标签" span={2}>
              {record.tags && record.tags.length > 0 ? (
                <Space>
                  {record.tags.map((tag, index) => (
                    <Tag key={index} color="blue">{tag}</Tag>
                  ))}
                </Space>
              ) : '-'}
            </Descriptions.Item>
            
            {record.cover_image && (
              <Descriptions.Item label="课程封面" span={2}>
                <Image 
                  src={record.cover_image} 
                  alt={record.title}
                  style={{ 
                    maxWidth: '200px',
                    maxHeight: '200px',
                    objectFit: 'contain'
                  }}
                />
              </Descriptions.Item>
            )}
            
            {record.poster_url && (
              <Descriptions.Item label="海报图片" span={2}>
                <Image 
                  src={record.poster_url} 
                  alt={record.title}
                  style={{ 
                    maxWidth: '200px',
                    maxHeight: '200px',
                    objectFit: 'contain'
                  }}
                />
              </Descriptions.Item>
            )}
            
            <Descriptions.Item label="创建时间">
              {record.created_at && new Date(record.created_at).toLocaleString()}
            </Descriptions.Item>
            
            <Descriptions.Item label="更新时间">
              {record.updated_at && new Date(record.updated_at).toLocaleString()}
            </Descriptions.Item>
          </Descriptions>
        </Card>
      )}
    </Show>
  );
};

export default CourseShow;
