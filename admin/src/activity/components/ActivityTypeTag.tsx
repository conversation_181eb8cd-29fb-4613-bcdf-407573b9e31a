import React from 'react';
import { Tag } from 'antd';
import { ActivityType } from '../schemas';

interface ActivityTypeTagProps {
  value: string;
}

/**
 * 活动类型标签组件
 *
 * 用于在列表和详情页面显示活动类型
 */
export const ActivityTypeTag: React.FC<ActivityTypeTagProps> = ({ value }) => {
  const activityTypeMap: Record<string, { text: string, color: string }> = {
    [ActivityType.VERIFY]: { text: '验证', color: 'blue' },
    [ActivityType.CONSUME]: { text: '消费', color: 'green' },
  };

  const config = activityTypeMap[value] || { text: value, color: 'default' };
  return <Tag color={config.color}>{config.text} ({value})</Tag>;
};

export default ActivityTypeTag;
