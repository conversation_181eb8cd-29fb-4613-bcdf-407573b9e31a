import {
  List,
  ShowButton,
  useTable,
  useSelect,
} from "@refinedev/antd";
import type { BaseRecord, CrudFilters } from "@refinedev/core";
import { Divider, Form, Input, Space, Table, Select, Typography } from "antd";
import RecordNameCell from "@/common/components/RecordName";
import { SearchForm } from "@/common/components";
import { entityKey } from "../consts";
import { Activity, ActivityType } from "../schemas";
import { ActivityTypeTag } from "../components";
import { CurrencyTypeTag } from "@/key/components";

interface SearchFormValues {
  app_id?: string;
  user_id?: string;
  service_id?: number;
  currency_type?: string;
  activity_type?: ActivityType;
}

export const ActivityList = () => {
  // 使用 useSelect 钩子获取应用列表
  const { selectProps: appSelectProps } = useSelect({
    resource: "app",
    optionLabel: "name",
    optionValue: "app_id",
    onSearch: (value) => [
      {
        field: "name",
        operator: "contains",
        value,
      },
    ],
  });

  // 使用 useSelect 钩子获取服务列表
  const { selectProps: serviceSelectProps } = useSelect({
    resource: "service",
    optionLabel: "name",
    optionValue: "id",
    onSearch: (value) => [
      {
        field: "name",
        operator: "contains",
        value,
      },
    ],
  });

  const { tableProps, searchFormProps } = useTable<Activity>({
    resource: entityKey,
    syncWithLocation: false,
    defaultSetFilterBehavior: "replace",
    onSearch: (values) => {
      const filters: CrudFilters = [];
      const data = values as SearchFormValues;

      // 构建搜索过滤器
      if (data.app_id) {
        filters.push({
          field: "app_id",
          operator: "contains",
          value: data.app_id,
        });
      }

      if (data.user_id) {
        filters.push({
          field: "user_id",
          operator: "contains",
          value: data.user_id,
        });
      }

      if (data.service_id) {
        filters.push({
          field: "service_id",
          operator: "eq",
          value: data.service_id,
        });
      }

      if (data.currency_type) {
        filters.push({
          field: "currency_type",
          operator: "eq",
          value: data.currency_type,
        });
      }

      if (data.activity_type) {
        filters.push({
          field: "type",
          operator: "eq",
          value: data.activity_type,
        });
      }

      return filters;
    },
  });

  return (
    <List title="活动记录">
      {/* 使用通用搜索表单组件 */}
      <SearchForm
        searchFormProps={searchFormProps}
        useCard={true}
        layout="inline"
      >
        <Form.Item label="应用ID" name="app_id">
          <Select
            {...appSelectProps}
            placeholder="请选择应用ID"
            allowClear
            showSearch
            optionFilterProp="label"
            filterOption={(input, option) =>
              (option?.label ? String(option.label).toLowerCase() : '').includes(input.toLowerCase())
            }
            // 自定义选项渲染
            optionRender={(option) => (
              <Space>
                <Typography.Text>{option.label}</Typography.Text>
                <Typography.Text type="secondary">({option.value})</Typography.Text>
              </Space>
            )}
          />
        </Form.Item>
        <Form.Item label="用户ID" name="user_id">
          <Input placeholder="请输入用户ID" allowClear />
        </Form.Item>
        <Form.Item label="服务" name="service_id">
          <Select
            {...serviceSelectProps}
            placeholder="请选择服务"
            allowClear
            showSearch
            optionFilterProp="label"
            filterOption={(input, option) =>
              (option?.label ? String(option.label).toLowerCase() : '').includes(input.toLowerCase())
            }
            // 自定义选项渲染
            optionRender={(option) => (
              <Space>
                <Typography.Text>{option.label}</Typography.Text>
                <Typography.Text type="secondary">({option.value})</Typography.Text>
              </Space>
            )}
          />

        </Form.Item>
        <Form.Item label="活动类型" name="activity_type">
          <Select
            placeholder="请选择活动类型"
            allowClear
            options={[
              { label: '验证', value: ActivityType.VERIFY },
              { label: '消费', value: ActivityType.CONSUME },
            ]}
          />

        </Form.Item>
      </SearchForm>

      <Divider className="my-4" />

      <Table {...tableProps} rowKey="id">
        <Table.Column dataIndex="id" title="活动ID" />
        <Table.Column dataIndex="key_id" title="密钥ID" />
        <Table.Column
          dataIndex="app_id"
          title="应用名称"
          render={(value) => value ? <RecordNameCell value={value} resource="app" nameField="name" /> : '-'}
        />
        <Table.Column dataIndex="user_id" title="用户ID" />
        <Table.Column
          dataIndex="service_id"
          title="服务"
          render={(value) => value ? <RecordNameCell value={value} resource="service" nameField="name" /> : '-'}
        />
        <Table.Column
          dataIndex="type"
          title="活动类型"
          render={(value: string) => <ActivityTypeTag value={value} />}
        />
        <Table.Column
          dataIndex="currency_type"
          title="信用货币类型"
          render={(value: string) => value ? <CurrencyTypeTag value={value} /> : '-'}
        />
        <Table.Column
          dataIndex="amount"
          title="消费数量"
          render={(value) => value.toFixed(2)}
        />
        <Table.Column
          dataIndex="created_at"
          title="创建时间"
          render={(value) => new Date(value).toLocaleString()}
        />
        <Table.Column
          title="操作"
          dataIndex="actions"
          render={(_, record: BaseRecord) => (
            <Space>
              <ShowButton hideText size="small" recordItemId={record.id} />
            </Space>
          )}
        />
      </Table>
    </List>
  );
};
