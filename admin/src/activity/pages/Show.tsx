import React from "react";
import { useShow } from "@refinedev/core";
import {
  Show,
  DateField,
  TextField,
  NumberField,
} from "@refinedev/antd";
import {
  Typography,
  Card,
  Descriptions,
  Row,
  Col,
} from "antd";
import { ActivityDetail } from "../schemas";
import RecordNameCell from "@/common/components/RecordName";
import { entityKey } from "../consts";
import { ActivityTypeTag } from "../components";
import { CurrencyTypeTag } from "@/key/components";

export const ActivityShow: React.FC = () => {
  const { query } = useShow<ActivityDetail>({
    resource: entityKey,
  });

  const { data, isLoading } = query;
  const record = data?.data;

  if (isLoading) {
    return <div>加载中...</div>;
  }

  if (!record) {
    return <div>未找到活动记录</div>;
  }

  return (
    <Show
      isLoading={isLoading}
      title={<Typography.Title level={3}>活动详情: {record.id}</Typography.Title>}
      headerButtons={({ defaultButtons }) => (
        <>
          {defaultButtons}
        </>
      )}
    >
      <Row gutter={24}>
        <Col span={24}>
          <Card title="基本信息" className="mb-4">
            <Descriptions column={{ xs: 1, sm: 2, md: 3 }}>
              <Descriptions.Item label="活动ID">
                <TextField value={record.id} />
              </Descriptions.Item>
              <Descriptions.Item label="密钥ID">
                <TextField value={record.key_id} />
              </Descriptions.Item>
              <Descriptions.Item label="应用ID">
                {
                  record.app_id ? <RecordNameCell value={record.app_id} resource="app" nameField="name" /> : '-'
                }
              </Descriptions.Item>
              <Descriptions.Item label="用户ID">
                <TextField value={record.user_id} />
              </Descriptions.Item>
              <Descriptions.Item label="服务">
                {record.service_id ? <RecordNameCell value={record.service_id} resource="service" nameField="name" /> : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="实例ID">
                <TextField value={record.instance_id || '-'} />
              </Descriptions.Item>
              <Descriptions.Item label="信用货币类型">
                {record.currency_type ? <CurrencyTypeTag value={record.currency_type} /> : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="活动类型">
                <ActivityTypeTag value={record.type} />
              </Descriptions.Item>
              <Descriptions.Item label="消费数量">
                <NumberField value={record.amount} />
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                <DateField format="YYYY-MM-DD HH:mm:ss" value={record.created_at} />
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        {record.details && (
          <Col span={24}>
            <Card title="详细信息" className="mb-4">
              <pre style={{ padding: '12px', borderRadius: '4px', overflow: 'auto', maxHeight: '400px' }}>
                {JSON.stringify(record.details, null, 2)}
              </pre>
            </Card>
          </Col>
        )}

        <Col span={24}>
          <Card title="关联信息" className="mb-4">
            <Descriptions column={{ xs: 1, sm: 2, md: 3 }}>
              <Descriptions.Item label="密钥ID">
                <TextField value={record.key_id} />
              </Descriptions.Item>
              <Descriptions.Item label="应用">
                {record.app_id ? <RecordNameCell value={record.app_id} resource="app" nameField="name" /> : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="服务">
                {record.service_id ? <RecordNameCell value={record.service_id} resource="service" nameField="name" /> : '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>
      </Row>
    </Show>
  );
};
