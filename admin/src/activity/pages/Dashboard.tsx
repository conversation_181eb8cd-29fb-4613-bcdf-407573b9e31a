import React, { useState, useEffect } from "react";
import { useTable, useSelect } from "@refinedev/antd";
import type { CrudFilters } from "@refinedev/core";
import { useApiUrl, useCustom } from "@refinedev/core";
import { useAuthStore } from "@/admin/stores";
import { Operator } from "@/common/schemas";
import {
  Form,
  Input,
  Table,
  Select,

  Button,
  message,
  Card,
  Row,
  Col,
  DatePicker,
  Progress,
  Tag,
  Tooltip,
  Radio,
  Empty
} from "antd";
import { entityKey } from "../consts";
import { Activity } from "../schemas";
import dayjs from "dayjs";



const { RangePicker } = DatePicker;

interface SearchFormValues {
  app_id?: string;
  user_id?: string;
  service_id?: number | number[];
  currency_type?: string;
  date_range?: [dayjs.Dayjs, dayjs.Dayjs];
  amount_min?: number;
  amount_max?: number;
}

interface AppDistribution {
  appId: string;
  appName: string;
  count: number;
  percentage: number;
  amount: number;
}

interface UserDistribution {
  appId: string;
  appName: string;
  userId: string;
  count: number;
  percentage: number;
  amount: number;
  currencyType?: string;
  totalKeyCredit?: number; // 该应用用户的所有该单位密钥的总额
}

interface ServiceDistribution {
  serviceId: number;
  serviceName: string;
  count: number;
  percentage: number;
  amount: number;
}

interface DailyTrend {
  date: string;
  count: number;
  amount: number;
}

interface GaugeData {
  title: string;
  value: number;
  total: number;
  percent: number;
  color: string;
  valueText?: string;
}

interface BarChartData {
  category: string;
  current: number;
  previous: number;
  target: number;
}

interface DailyConsumptionData {
  date: string;
  [key: string]: string | number; // 动态键，用于存储不同信用货币类型的消耗量
}

interface PieChartData {
  type: string;
  displayName?: string;
  value: number;
  percent: number;
  currencyTypes?: string[];
  currencyValues?: number[];
  appName?: string; // 添加应用名称字段
}

type ChartViewType = 'app' | 'service' | 'currencyType';

export const ActivityDashboard: React.FC = () => {
  const [statistics, setStatistics] = useState({
    totalActivities: 0,
    totalAmount: 0,
    avgAmount: 0
  });
  const [appDistribution, setAppDistribution] = useState<AppDistribution[]>([]);
  const [userDistribution, setUserDistribution] = useState<UserDistribution[]>([]);
  const [serviceDistribution, setServiceDistribution] = useState<ServiceDistribution[]>([]);
  const [dailyTrend, setDailyTrend] = useState<DailyTrend[]>([]);
  const [gaugeData, setGaugeData] = useState<GaugeData[]>([
    { title: "消费次数", value: 0, total: 100, percent: 0, color: "#1890ff" },
    { title: "验证次数", value: 0, total: 100, percent: 0, color: "#1890ff" },
    { title: "平均消耗", value: 0, total: 100, percent: 0, color: "#faad14" }
  ]);
  const [barChartData, setBarChartData] = useState<BarChartData[]>([]);
  const [dailyConsumption, setDailyConsumption] = useState<DailyConsumptionData[]>([]);
  const [pieChartData, setPieChartData] = useState<PieChartData[]>([]);
  const [chartViewType, setChartViewType] = useState<ChartViewType>('currencyType');
  const [loading, setLoading] = useState(false);

  // 获取API URL
  const apiUrl = useApiUrl();

  // 使用 useSelect 钩子获取应用列表
  const { selectProps: appSelectProps } = useSelect({
    resource: "app",
    optionLabel: "name",
    optionValue: "app_id",
  });

  // 使用 useSelect 钩子获取服务列表
  const { selectProps: serviceSelectProps } = useSelect({
    resource: "service",
    optionLabel: "name",
    optionValue: "id",
  });

  const { tableProps, searchFormProps } = useTable<Activity>({
    resource: entityKey,
    syncWithLocation: false,
    defaultSetFilterBehavior: "replace",
    pagination: {
      pageSize: 100000, // 进一步增加页面大小，确保获取所有记录
      mode: "off", // 关闭分页，获取所有数据
    },
    meta: {
      // 添加自定义请求头，确保获取所有记录
      headers: {
        'X-Get-All-Records': 'true'
      }
    },
    onSearch: (values) => {
      const filters: CrudFilters = [];
      const data = values as SearchFormValues;

      // 注意：这里不再默认添加app_id筛选条件
      // 只有当用户明确选择了应用时才添加筛选条件
      if (data.app_id) {
        filters.push({
          field: "app_id",
          operator: "eq",
          value: data.app_id,
        });
      }

      if (data.user_id) {
        filters.push({
          field: "user_id",
          operator: "eq",
          value: data.user_id,
        });
      }

      if (data.service_id) {
        if (Array.isArray(data.service_id)) {
          // 如果是数组，处理多选情况
          if (data.service_id.length > 0) {
            filters.push({
              field: "service_id",
              operator: "in",
              value: data.service_id,
            });
          }
        } else {
          // 单选情况
          filters.push({
            field: "service_id",
            operator: "eq",
            value: data.service_id,
          });
        }
      }

      if (data.currency_type) {
        filters.push({
          field: "currency_type",
          operator: "eq",
          value: data.currency_type,
        });
      }

      // 移除了活动类型筛选

      // 只有当日期范围有值且两个日期都有值时才添加日期筛选条件
      if (data.date_range && data.date_range.length === 2 && data.date_range[0] && data.date_range[1]) {
        const startDate = data.date_range[0];
        const endDate = data.date_range[1];

        // 确保使用完整的日期范围，避免时区问题
        // 使用startOf和endOf方法确保日期范围完整
        startDate.startOf('day');
        endDate.endOf('day');

        // 使用日期字符串进行筛选，而不是ISO字符串
        const startDateString = startDate.format('YYYY-MM-DD');
        const endDateString = endDate.format('YYYY-MM-DD');

        // 使用日期字符串进行筛选
        filters.push({
          field: "created_at",
          operator: "gte",
          value: startDateString,
        });

        filters.push({
          field: "created_at",
          operator: "lte",
          value: endDateString + ' 23:59:59',
        });

        console.log(`日期筛选范围: ${startDateString} 到 ${endDateString}`);
      } else {
        // 如果没有设置日期范围，则不添加日期筛选条件
        console.log("未设置日期范围，将获取所有日期的数据");
      }

      if (data.amount_min !== undefined) {
        filters.push({
          field: "amount",
          operator: "gte",
          value: data.amount_min,
        });
      }

      if (data.amount_max !== undefined) {
        filters.push({
          field: "amount",
          operator: "lte",
          value: data.amount_max,
        });
      }

      return filters;
    },
  });

  // 首次加载页面时触发查询
  useEffect(() => {
    // 触发表单提交，获取所有数据
    // 确保不传递任何筛选条件，以获取所有应用的数据
    console.log("开始加载活动记录数据...");

    // 确保清空所有筛选条件
    searchFormProps.form?.resetFields();

    // 使用空对象作为查询条件，获取所有数据
    // 添加延迟，确保组件完全加载
    setTimeout(() => {
      console.log("提交查询，获取所有活动记录...");
      searchFormProps.onFinish?.({});
    }, 500);
  }, []);

  // 导出用户分布数据
  const exportUserDistributionData = (data: UserDistribution[]) => {
    if (!data || data.length === 0) {
      message.warning('没有数据可导出');
      return;
    }

    try {
      // 先按用户ID排序
      const sortedData = [...data].sort((a, b) => a.userId.localeCompare(b.userId));

      // 准备CSV数据
      const headers = ['应用ID', '应用名称', '用户ID', '活动数量', '消费总量', '限额总量', '单位'];
      const csvContent = sortedData.map(item => {
        // 获取货币类型的显示名称
        const currencyTypeMap: Record<string, string> = {
          'LTC': 'LLM Token Counts',
          'UTS': '体验次数',
          'CNY': '人民币',
          'USD': '美元'
        };
        const currencyDisplay = item.currencyType ? currencyTypeMap[item.currencyType] || item.currencyType : '-';

        return [
          item.appId,
          item.appName || item.appId,
          item.userId,
          item.count.toString(),
          item.amount.toFixed(2),
          (item.totalKeyCredit || 0).toFixed(2),
          currencyDisplay
        ].join(',');
      });

      // 组合CSV内容
      const csv = [headers.join(','), ...csvContent].join('\n');

      // 创建Blob对象
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });

      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      // 设置下载属性
      link.setAttribute('href', url);
      link.setAttribute('download', `用户消耗统计_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';

      // 添加到文档并触发点击
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success('数据导出成功');
    } catch (error) {
      console.error('导出数据失败:', error);
      message.error('导出数据失败');
    }
  };

  // 获取密钥信息的函数
  const fetchKeyLimits = async (appId: string, userId: string, currencyType: string | undefined) => {
    try {
      // 打印API URL，用于调试
      console.log('API URL:', apiUrl);

      // 使用entity API获取密钥信息
      // 构建查询参数
      const queryPayload = {
        filters: [
          {
            property: "app_id",
            operator: Operator.EQUALS, // 使用正确的操作符
            value: appId
          },
          {
            property: "user_id",
            operator: Operator.EQUALS, // 使用正确的操作符
            value: userId
          }
        ],
        pageNo: 1,
        pageSize: 1000 // 设置一个足够大的页面大小
      };

      // 如果有货币类型，添加到过滤条件
      // 注意：我们不再在这里添加货币类型过滤条件，而是在后面处理结果时进行筛选
      // 这样可以获取所有货币类型的密钥，然后只返回特定货币类型的总额

      // 构建URL
      const urlString = `${window.location.origin}/api/entity/key/list`;
      console.log('完整URL:', urlString);

      // 获取认证token
      const token = useAuthStore.getState().token || '';

      // 打印请求信息，用于调试
      console.log(`请求密钥信息: ${urlString}`);
      console.log(`参数:`, queryPayload);

      // 准备请求头
      const headers: Record<string, string> = {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      };

      // 添加认证信息
      if (token) {
        headers['Authorization'] = `Bearer ${token}`;
      }

      // 发送POST请求
      const response = await fetch(urlString, {
        method: 'POST',
        headers,
        body: JSON.stringify(queryPayload)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`API错误 (${response.status}): ${errorText}`);
        throw new Error(`获取密钥信息失败: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // 打印响应数据，用于调试
      console.log('密钥API响应:', data);

      // 获取所有密钥
      const keys = data.data?.data || [];
      console.log(`找到 ${keys.length} 个密钥`);

      // 如果指定了货币类型，只计算该货币类型的密钥限额总量
      const filteredKeys = currencyType
        ? keys.filter((key: any) => key.currency_type === currencyType)
        : keys;

      console.log(`过滤后的密钥数量: ${filteredKeys.length}`);

      const totalLimit = filteredKeys.reduce((sum: number, key: any) => {
        console.log(`密钥 ${key.id}: currency_type = ${key.currency_type}, credit_limit = ${key.credit_limit}`);
        return sum + key.credit_limit;
      }, 0);

      console.log(`总限额 (${currencyType || '所有类型'}): ${totalLimit}`);
      return totalLimit;
    } catch (error) {
      console.error("获取密钥信息失败:", error);
      return 0;
    }
  };

  // 计算统计数据
  useEffect(() => {
    if (tableProps.dataSource) {
      const data = tableProps.dataSource;

      // 确保我们有所有的数据
      console.log(`获取到 ${data.length} 条活动记录`);
      console.log('数据示例:', data.slice(0, 3));

      // 打印所有数据的日期，用于调试
      const dates = data.map(item => new Date(item.created_at).toISOString().split('T')[0]);
      const uniqueDates = [...new Set(dates)];
      console.log('唯一日期列表:', uniqueDates);
      console.log('日期分布:', uniqueDates.map(date => ({
        date,
        count: dates.filter(d => d === date).length
      })));

      const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);

      // 设置基本统计数据
      setStatistics({
        totalActivities: data.length,
        totalAmount: totalAmount,
        avgAmount: data.length > 0 ? totalAmount / data.length : 0
      });

      // 计算应用分布
      const appMap = new Map<string, { count: number, appId: string, appName: string, amount: number }>();
      data.forEach(item => {
        const appId = item.app_id;
        if (!appMap.has(appId)) {
          // 尝试从appSelectProps中获取应用名称
          const appOption = appSelectProps.options?.find(option => option.value === appId);
          const appName = appOption ? String(appOption.label) : appId;
          appMap.set(appId, { count: 0, appId, appName, amount: 0 });
        }
        appMap.get(appId)!.count += 1;
        appMap.get(appId)!.amount += item.amount;
      });

      const appDist = Array.from(appMap.values()).map(item => ({
        ...item,
        percentage: (item.count / data.length) * 100
      })).sort((a, b) => b.amount - a.amount);

      setAppDistribution(appDist);

      // 计算用户分布（按应用ID、用户ID和货币类型分组）
      const userMap = new Map<string, { count: number, appId: string, appName: string, userId: string, amount: number, currencyType?: string }>();
      data.forEach(item => {
        const appId = item.app_id;
        const userId = item.user_id;
        const currencyType = item.currency_type || '未知';
        // 使用应用ID、用户ID和货币类型作为键，以区分不同货币类型的记录
        const key = `${appId}-${userId}-${currencyType}`;

        if (!userMap.has(key)) {
          // 尝试从appSelectProps中获取应用名称
          const appOption = appSelectProps.options?.find(option => option.value === appId);
          const appName = appOption ? String(appOption.label) : appId;
          userMap.set(key, {
            count: 0,
            appId,
            appName,
            userId,
            amount: 0,
            currencyType: currencyType
          });
        }
        userMap.get(key)!.count += 1;
        userMap.get(key)!.amount += item.amount;
      });

      // 创建一个临时数组，用于存储用户分布数据
      const tempUserDist = Array.from(userMap.values()).map(item => ({
        ...item,
        percentage: (item.count / data.length) * 100,
        totalKeyCredit: 0 // 初始化为0，后面会异步更新
      })).sort((a, b) => b.amount - a.amount);

      // 设置用户分布数据
      setUserDistribution(tempUserDist);

      // 异步获取每个用户的密钥限额总量
      const fetchAllKeyLimits = async () => {
        const updatedUserDist = [...tempUserDist];

        // 使用Promise.all并行处理所有请求
        await Promise.all(updatedUserDist.map(async (user, index) => {
          const totalLimit = await fetchKeyLimits(user.appId, user.userId, user.currencyType);
          updatedUserDist[index].totalKeyCredit = totalLimit;
        }));

        // 更新用户分布数据
        setUserDistribution(updatedUserDist);
      };

      // 执行异步函数
      fetchAllKeyLimits();

      // 计算服务分布
      const serviceMap = new Map<number, { count: number, serviceId: number, serviceName: string, amount: number }>();
      data.forEach(item => {
        const serviceId = item.service_id;
        if (!serviceMap.has(serviceId)) {
          // 尝试从serviceSelectProps中获取服务名称
          const serviceOption = serviceSelectProps.options?.find(option => option.value === serviceId);
          const serviceName = serviceOption ? String(serviceOption.label) : String(serviceId);
          serviceMap.set(serviceId, { count: 0, serviceId, serviceName, amount: 0 });
        }
        serviceMap.get(serviceId)!.count += 1;
        serviceMap.get(serviceId)!.amount += item.amount;
      });

      const serviceDist = Array.from(serviceMap.values()).map(item => ({
        ...item,
        percentage: (item.count / data.length) * 100
      })).sort((a, b) => b.amount - a.amount);

      setServiceDistribution(serviceDist);

      // 计算每日趋势
      const dateMap = new Map<string, { count: number, amount: number, date: string }>();
      data.forEach(item => {
        // 使用本地时区处理日期，确保日期分组正确
        const date = new Date(item.created_at);
        const dateString = date.getFullYear() + '-' +
                          String(date.getMonth() + 1).padStart(2, '0') + '-' +
                          String(date.getDate()).padStart(2, '0');

        if (!dateMap.has(dateString)) {
          dateMap.set(dateString, { count: 0, amount: 0, date: dateString });
        }
        dateMap.get(dateString)!.count += 1;
        dateMap.get(dateString)!.amount += item.amount;
      });

      const dailyData = Array.from(dateMap.values())
        .sort((a, b) => a.date.localeCompare(b.date));

      setDailyTrend(dailyData);

      // 获取所有信用货币类型
      const allCurrencyTypes = Array.from(new Set(data.map(item => item.currency_type).filter(Boolean)));

      // 计算每日信用货币类型消耗统计
      const dailyConsumptionMap = new Map<string, DailyConsumptionData>();

      // 确保所有货币类型都有默认值
      const defaultCurrencyTypes = ['CNY', 'LTC', 'UTS', 'USD'];
      const mergedCurrencyTypes = [...new Set([...allCurrencyTypes, ...defaultCurrencyTypes])];

      data.forEach(item => {
        // 使用本地时区处理日期，确保日期分组正确
        const date = new Date(item.created_at);
        const dateString = date.getFullYear() + '-' +
                          String(date.getMonth() + 1).padStart(2, '0') + '-' +
                          String(date.getDate()).padStart(2, '0');

        if (!dailyConsumptionMap.has(dateString)) {
          const newEntry: DailyConsumptionData = { date: dateString };
          // 确保所有货币类型都被初始化为0
          mergedCurrencyTypes.forEach(type => {
            if (type) newEntry[type] = 0;
          });
          dailyConsumptionMap.set(dateString, newEntry);
        }

        const currencyType = item.currency_type || 'CNY'; // 如果没有货币类型，默认为CNY
        dailyConsumptionMap.get(dateString)![currencyType] =
          (dailyConsumptionMap.get(dateString)![currencyType] as number || 0) + item.amount;
      });

      // 调试信息
      console.log('所有货币类型:', mergedCurrencyTypes);
      console.log('每日消耗数据:', dailyConsumptionMap);

      const dailyConsumptionData = Array.from(dailyConsumptionMap.values())
        .sort((a, b) => a.date.localeCompare(b.date));

      setDailyConsumption(dailyConsumptionData);

      // 计算饼图数据 - 默认按信用货币类型
      const generatePieData = (viewType: ChartViewType) => {
        let pieData: PieChartData[] = [];

        switch(viewType) {
          case 'app':
            // 按应用ID分组，收集每个应用的不同货币类型消耗
            const appCurrencyMap = new Map<string, Map<string, number>>();

            // 首先按应用ID分组
            data.forEach(item => {
              const appId = item.app_id;
              const type = item.currency_type || '未知';

              if (!appCurrencyMap.has(appId)) {
                appCurrencyMap.set(appId, new Map<string, number>());
              }

              const currencyMap = appCurrencyMap.get(appId)!;
              currencyMap.set(type, (currencyMap.get(type) || 0) + item.amount);
            });

            // 获取应用名称的函数
            const getAppName = (appId: string): string => {
              const appOption = appSelectProps.options?.find(option => option.value === appId);
              return appOption ? String(appOption.label) : appId;
            };

            // 处理每个应用
            pieData = [];
            appCurrencyMap.forEach((currencyMap, appId) => {
              const appName = getAppName(appId);

              // 收集该应用的所有货币类型和值
              const currencyTypes: string[] = [];
              const currencyValues: number[] = [];
              let totalAppAmount = 0;

              currencyMap.forEach((amount, type) => {
                currencyTypes.push(type);
                currencyValues.push(amount);
                totalAppAmount += amount;
              });

              // 为每种货币类型创建一个饼图数据项
              currencyMap.forEach((amount, type) => {
                const displayName = type === 'LTC' ? 'LLM Token Counts (LTC)' :
                     type === 'UTS' ? '体验次数 (UTS)' :
                     type === 'CNY' ? '人民币 (CNY)' :
                     type === 'USD' ? '美元 (USD)' : type;

                pieData.push({
                  type: `${appName}-${type}`,
                  displayName: `${appName}: ${displayName}`,
                  value: amount,
                  percent: totalAmount > 0 ? (amount / totalAmount) * 100 : 0,
                  currencyTypes: [type],
                  currencyValues: [amount],
                  appName: appName // 添加应用名称，用于分组显示
                });
              });
            });
            break;

          case 'service':
            // 按服务ID分组，收集每个服务的不同货币类型消耗
            const serviceCurrencyMap = new Map<number, Map<string, number>>();

            // 首先按服务ID分组
            data.forEach(item => {
              const serviceId = item.service_id;
              const type = item.currency_type || '未知';

              if (!serviceCurrencyMap.has(serviceId)) {
                serviceCurrencyMap.set(serviceId, new Map<string, number>());
              }

              const currencyMap = serviceCurrencyMap.get(serviceId)!;
              currencyMap.set(type, (currencyMap.get(type) || 0) + item.amount);
            });

            // 获取服务名称的函数
            const getServiceName = (serviceId: number): string => {
              const serviceOption = serviceSelectProps.options?.find(option => option.value === serviceId);
              return serviceOption ? String(serviceOption.label) : String(serviceId);
            };

            // 处理每个服务
            pieData = [];
            serviceCurrencyMap.forEach((currencyMap, serviceId) => {
              const serviceName = getServiceName(serviceId);

              // 收集该服务的所有货币类型和值
              const currencyTypes: string[] = [];
              const currencyValues: number[] = [];
              let totalServiceAmount = 0;

              currencyMap.forEach((amount, type) => {
                currencyTypes.push(type);
                currencyValues.push(amount);
                totalServiceAmount += amount;
              });

              // 为每种货币类型创建一个饼图数据项
              currencyMap.forEach((amount, type) => {
                const displayName = type === 'LTC' ? 'LLM Token Counts (LTC)' :
                     type === 'UTS' ? '体验次数 (UTS)' :
                     type === 'CNY' ? '人民币 (CNY)' :
                     type === 'USD' ? '美元 (USD)' : type;

                pieData.push({
                  type: `${serviceName}-${type}`,
                  displayName: `${serviceName}: ${displayName}`,
                  value: amount,
                  percent: totalAmount > 0 ? (amount / totalAmount) * 100 : 0,
                  currencyTypes: [type],
                  currencyValues: [amount]
                });
              });
            });
            break;

          // 移除了按活动类型分组的逻辑

          case 'currencyType':
          default:
            const currencyTypeMap = new Map<string, number>();
            data.forEach(item => {
              const type = item.currency_type || '未知';
              currencyTypeMap.set(type, (currencyTypeMap.get(type) || 0) + item.amount);
            });

            pieData = Array.from(currencyTypeMap.entries()).map(([type, amount]) => {
              const displayName = type === 'LTC' ? 'LLM Token Counts (LTC)' :
                   type === 'UTS' ? '体验次数 (UTS)' :
                   type === 'CNY' ? '人民币 (CNY)' :
                   type === 'USD' ? '美元 (USD)' : type;

              return {
                type: type,
                displayName: displayName,
                value: amount,
                percent: totalAmount > 0 ? (amount / totalAmount) * 100 : 0,
                currencyTypes: [type],
                currencyValues: [amount]
              };
            });
            break;
        }

        return pieData.sort((a, b) => b.value - a.value);
      };

      setPieChartData(generatePieData(chartViewType));

      // 更新仪表盘数据
      const avgConsumption = data.length > 0 ? (totalAmount / data.length) : 0;
      const maxAvgConsumption = 10; // 假设最大平均消耗为10
      const avgConsumptionPercent = Math.min(avgConsumption / maxAvgConsumption * 100, 100);

      // 使用信用货币类型作为仪表盘数据
      const gauges: GaugeData[] = [];

      // 为每种货币类型创建一个仪表盘
      allCurrencyTypes.slice(0, 4).forEach(currency => {
        const currencyData = data.filter(item => item.currency_type === currency);
        const currencyAmount = currencyData.reduce((sum, item) => sum + item.amount, 0);
        const currencyCount = currencyData.length;
        const currencyPercent = data.length > 0 ? (currencyCount / data.length) * 100 : 0;

        gauges.push({
          title: currency === 'LTC' ? 'LLM Token Counts' :
                 currency === 'UTS' ? '体验次数' :
                 currency === 'CNY' ? '人民币' :
                 currency === 'USD' ? '美元' : `${currency || '未知'}`,
          value: currencyAmount,
          total: data.length,
          percent: currencyPercent,
          color: currency === 'LTC' ? '#1890ff' :
                 currency === 'UTS' ? '#52c41a' :
                 currency === 'CNY' ? '#faad14' :
                 currency === 'USD' ? '#722ed1' : '#eb2f96',
          valueText: currency === 'LTC' ? (currencyAmount / 10000).toFixed(2) + '万' : currencyAmount.toFixed(2)
        });
      });

      // 如果没有足够的货币类型，添加平均消耗指标
      if (gauges.length < 3) {
        gauges.push({
          title: "平均消耗",
          value: avgConsumption,
          total: maxAvgConsumption,
          percent: avgConsumptionPercent,
          color: "#faad14",
          valueText: avgConsumption.toFixed(2)
        });
      }

      setGaugeData(gauges);

      // 更新柱状图数据
      // 按应用分组统计数据
      const topApps = appDist.slice(0, 4); // 取前4个应用

      const barData = topApps.map(app => {
        const appData = data.filter(item => item.app_id === app.appId);
        const currentAmount = appData.reduce((sum, item) => sum + item.amount, 0);

        // 假设目标和历史数据
        const targetAmount = currentAmount * 1.2; // 目标比当前高20%
        const previousAmount = currentAmount * 0.8; // 历史比当前低20%

        return {
          category: app.appName || app.appId,
          current: parseFloat(currentAmount.toFixed(2)),
          previous: parseFloat(previousAmount.toFixed(2)),
          target: parseFloat(targetAmount.toFixed(2))
        };
      });

      setBarChartData(barData);
    }
  }, [tableProps.dataSource, appSelectProps.options, serviceSelectProps.options]);

  // 导出CSV数据
  const exportToCSV = () => {
    if (!tableProps.dataSource || tableProps.dataSource.length === 0) {
      message.warning('没有数据可导出');
      return;
    }

    setLoading(true);

    try {
      // 准备CSV数据
      const headers = [
        '活动ID', '密钥ID', '应用ID', '用户ID', '服务ID',
        '实例ID', '信用货币类型', '活动类型', '消费数量', '创建时间'
      ];

      const csvData = tableProps.dataSource.map(item => [
        item.id,
        item.key_id,
        item.app_id,
        item.user_id,
        item.service_id,
        item.instance_id || '',
        item.currency_type || '',
        item.type,
        item.amount,
        new Date(item.created_at).toLocaleString()
      ]);

      // 添加表头
      csvData.unshift(headers);

      // 转换为CSV格式
      const csvContent = csvData.map(row => row.join(',')).join('\n');

      // 创建Blob对象
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      // 设置下载属性
      link.setAttribute('href', url);
      link.setAttribute('download', `活动记录_${new Date().toISOString().slice(0, 10)}.csv`);
      link.style.visibility = 'hidden';

      // 添加到DOM并触发下载
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      message.success('导出成功');
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出活动记录数据
  const exportActivityData = (data: readonly Activity[]) => {
    if (!data || data.length === 0) {
      message.warning('没有数据可导出');
      return;
    }

    try {
      // 准备CSV数据
      const headers = ['ID', '应用ID', '应用名称', '用户ID', '服务ID', '服务名称', '信用货币类型', '金额', '创建时间'];
      const csvContent = data.map(item => {
        // 获取应用名称
        const appOption = appSelectProps.options?.find(option => option.value === item.app_id);
        const appName = appOption ? String(appOption.label) : item.app_id;

        // 获取服务名称
        const serviceOption = serviceSelectProps.options?.find(option => option.value === item.service_id);
        const serviceName = serviceOption ? String(serviceOption.label) : item.service_id;

        return [
          item.id,
          item.app_id,
          appName,
          item.user_id,
          item.service_id,
          serviceName,
          item.currency_type,
          item.amount,
          new Date(item.created_at).toLocaleString()
        ].join(',');
      });

      // 组合CSV内容
      const csv = [headers.join(','), ...csvContent].join('\n');

      // 创建Blob对象
      const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });

      // 创建下载链接
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);

      // 设置下载属性
      link.setAttribute('href', url);
      link.setAttribute('download', `活动记录_${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';

      // 添加到文档并触发点击
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      message.success('数据导出成功');
    } catch (error) {
      console.error('导出数据失败:', error);
      message.error('导出数据失败');
    }
  };

  // 自定义仪表盘组件
  const renderGaugeChart = (data: GaugeData) => {
    // 获取货币单位
    const getCurrencyUnit = (title: string): string => {
      if (title.includes("消耗次数")) return "次";
      if (title.includes("消费次数")) return "次";
      if (title.includes("验证次数")) return "次";

      // 根据货币类型返回单位
      const currencyMap: Record<string, string> = {
        'LTC': 'Tokens',
        'UTS': '次',
        'CNY': '元',
        'USD': '$'
      };

      // 检查标题中是否包含货币类型
      for (const [key, value] of Object.entries(currencyMap)) {
        if (title.includes(key)) return value;
      }

      return "";
    };

    const unit = getCurrencyUnit(data.title);

    // 统一使用大尺寸，与上方饼图保持一致
    const size = 220;
    const fontSize = '36px';
    const unitSize = '16px';

    return (
      <div style={{
        textAlign: 'center',
        margin: '0 10px',
        minWidth: '240px'
      }}>
        <div style={{ position: 'relative', width: size, height: size, margin: '0 auto' }}>
          <Progress
            type="circle"
            percent={100}
            strokeColor={data.color}
            strokeWidth={10}
            size={size}
            format={() => (
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: fontSize, fontWeight: 'bold', color: data.color }}>
                  {data.valueText || data.value.toFixed(2)}
                </div>
                <div style={{ fontSize: unitSize, color: '#666' }}>
                  {unit}
                </div>
              </div>
            )}
          />
        </div>
        <div style={{
          marginTop: '10px',
          fontSize: '16px',
          fontWeight: 'bold',
          backgroundColor: `${data.color}15`,
          padding: '5px 10px',
          borderRadius: '15px',
          display: 'inline-block'
        }}>
          {data.title}
        </div>
      </div>
    );
  };

  // 渲染消耗次数饼图
  const renderConsumptionCountPieChart = () => {
    if (!tableProps.dataSource || tableProps.dataSource.length === 0) {
      return <Empty description="暂无数据" />;
    }

    const data = tableProps.dataSource;

    // 显示总活动次数
    const totalActivities = data.length;
    console.log(`渲染消耗次数饼图 - 总活动次数: ${totalActivities}`);

    // 打印所有数据的日期，用于调试
    const dates = data.map(item => new Date(item.created_at).toISOString().split('T')[0]);
    const uniqueDates = [...new Set(dates)];
    console.log('消耗次数饼图 - 唯一日期列表:', uniqueDates);

    // 如果有日期筛选，检查是否有符合条件的数据
    const searchForm = searchFormProps.form?.getFieldsValue() as SearchFormValues;
    if (searchForm && searchForm.date_range && searchForm.date_range.length === 2) {
      const startDate = searchForm.date_range[0].format('YYYY-MM-DD');
      const endDate = searchForm.date_range[1].format('YYYY-MM-DD');
      console.log(`当前日期筛选: ${startDate} 到 ${endDate}`);

      // 检查是否有符合日期条件的数据
      const filteredDates = dates.filter(date => date >= startDate && date <= endDate);
      console.log(`符合日期条件的记录数: ${filteredDates.length}`);
    }

    // 按应用ID分组统计消耗次数
    const appCountMap = new Map<string, number>();
    data.forEach(item => {
      const appId = item.app_id;
      appCountMap.set(appId, (appCountMap.get(appId) || 0) + 1);
    });

    // 打印应用分组统计结果
    console.log('应用分组统计结果:');
    appCountMap.forEach((count, appId) => {
      console.log(`应用 ${appId}: ${count} 次`);
    });

    // 获取应用名称的函数
    const getAppName = (appId: string): string => {
      const appOption = appSelectProps.options?.find(option => option.value === appId);
      return appOption ? String(appOption.label) : appId;
    };

    // 转换为饼图数据
    const pieData = Array.from(appCountMap.entries()).map(([appId, count]) => {
      const appName = getAppName(appId);
      return {
        type: appId,
        name: appName,
        value: count,
        percent: totalActivities > 0 ? (count / totalActivities) * 100 : 0
      };
    }).sort((a, b) => b.value - a.value);

    // 为不同应用分配颜色
    const getColor = (index: number) => {
      const colors = ['#1890ff', '#52c41a', '#faad14', '#722ed1', '#eb2f96', '#f5222d', '#fa541c', '#13c2c2'];
      return colors[index % colors.length];
    };

    // 创建一个总活动次数的圆形进度条
    const renderTotalActivitiesCircle = () => {
      // 再次确认总活动次数
      console.log(`渲染总活动次数圆形进度条 - 总活动次数: ${totalActivities}`);

      // 获取当前数据源的实际长度
      const currentDataLength = data.length;
      console.log(`当前数据源长度: ${currentDataLength}`);

      // 使用实际数据长度，而不是计算的总活动次数
      const displayCount = currentDataLength;

      return (
        <div style={{
          textAlign: 'center',
          margin: '0 10px',
          minWidth: '240px'
        }}>
          <div style={{ position: 'relative', width: '220px', height: '220px', margin: '0 auto' }}>
            <Progress
              type="circle"
              percent={100}
              strokeColor="#1890ff"
              strokeWidth={10}
              size={220}
              format={() => (
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '36px', fontWeight: 'bold', color: '#1890ff' }}>
                    {displayCount}
                  </div>
                  <div style={{ fontSize: '16px', color: '#666' }}>
                    次
                  </div>
                </div>
              )}
            />
          </div>
          <div style={{
            marginTop: '10px',
            fontSize: '16px',
            fontWeight: 'bold',
            backgroundColor: '#e6f7ff',
            padding: '5px 10px',
            borderRadius: '15px',
            display: 'inline-block'
          }}>
            总活动次数
          </div>
          <div style={{ fontSize: '14px', color: '#999', marginTop: '5px' }}>
            100%
          </div>
        </div>
      );
    };

    return (
      <div style={{
        padding: '10px 0',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative'
      }}>
        <div style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-around',
          alignItems: 'center',
          flex: 1,
          padding: '0',
          width: '100%',
          overflowX: 'auto',
          overflowY: 'hidden'
        }}>
          {/* 总活动次数圆形进度条 */}
          {renderTotalActivitiesCircle()}

          {/* 各应用活动次数圆形进度条 */}
          {pieData.slice(0, 3).map((item, index) => (
            <div key={index} style={{
              textAlign: 'center',
              margin: '0 10px',
              minWidth: '240px'
            }}>
              <div style={{ position: 'relative', width: '220px', height: '220px', margin: '0 auto' }}>
                <Progress
                  type="circle"
                  percent={100}
                  strokeColor={getColor(index)}
                  strokeWidth={10}
                  size={220}
                  format={() => (
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '36px', fontWeight: 'bold', color: getColor(index) }}>
                        {item.value}
                      </div>
                      <div style={{ fontSize: '16px', color: '#666' }}>
                        次
                      </div>
                    </div>
                  )}
                />
              </div>
              <div style={{ marginTop: '10px', fontSize: '16px', fontWeight: 'bold' }}>
                {item.name}
              </div>
              <div style={{ fontSize: '14px', color: '#999' }}>
                {item.percent.toFixed(2)}%
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };


  // 渲染新的每日信用货币类型消耗统计柱状图
  const renderNewDailyConsumptionChart = () => {
    if (dailyConsumption.length === 0) {
      return <Empty description="暂无数据" />;
    }

    // 获取所有信用货币类型
    const currencyTypes = Object.keys(dailyConsumption[0]).filter(key => key !== 'date');

    // 为每种货币类型分配颜色
    const colorMap: Record<string, string> = {
      'LTC': '#1890ff',  // 蓝色
      'UTS': '#52c41a',  // 绿色
      'CNY': '#faad14',  // 黄色
      'USD': '#722ed1'   // 紫色
    };

    // 获取货币类型的显示名称
    const getCurrencyName = (type: string): string => {
      const currencyNames: Record<string, string> = {
        'LTC': 'LLM Token',
        'UTS': '体验次数',
        'CNY': '人民币',
        'USD': '美元'
      };
      return currencyNames[type] || type;
    };

    // 获取货币单位
    const getCurrencyUnit = (type: string): string => {
      const currencyUnits: Record<string, string> = {
        'LTC': 'Tokens',
        'UTS': '次',
        'CNY': '元',
        'USD': '$'
      };
      return currencyUnits[type] || '';
    };

    // 格式化数值显示
    const formatValue = (value: number, type: string): string => {
      // 对于LTC类型，统一使用"万"为单位
      if (type === 'LTC') {
        return `${(value / 10000).toFixed(2)}万`;
      }

      // 其他类型使用原来的逻辑
      if (value >= 1000000) {
        return `${(value / 1000000).toFixed(1)}M`;
      } else if (value >= 1000) {
        return `${(value / 1000).toFixed(1)}k`;
      } else if (value >= 10) {
        return value.toFixed(0);
      } else {
        return value.toFixed(2);
      }
    };

    return (
      <div style={{
        padding: '10px 0',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        paddingBottom: '30px' // 添加底部内边距，与饼图保持一致
      }}>
        {/* 图例 */}
        <div style={{
          display: 'flex',
          justifyContent: 'center',
          marginBottom: '10px',
          marginTop: '-10px',
          backgroundColor: 'white',
          padding: '8px 15px',
          borderRadius: '20px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
          width: 'fit-content',
          margin: '0 auto'
        }}>
          {currencyTypes.map(type => (
            <div key={type} style={{
              margin: '0 12px',
              display: 'flex',
              alignItems: 'center',
              padding: '3px 8px',
              borderRadius: '15px',
              backgroundColor: `${colorMap[type]}15` || '#f9f9f9'
            }}>
              <div style={{
                width: '14px',
                height: '14px',
                backgroundColor: colorMap[type] || '#999',
                borderRadius: '3px',
                marginRight: '6px',
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
              }}></div>
              <span style={{ fontWeight: 'bold', fontSize: '13px' }}>{getCurrencyName(type)}</span>
            </div>
          ))}
        </div>

        {/* 柱状图容器 */}
        <div style={{
          display: 'flex',
          flexDirection: 'row',
          overflowX: 'auto', /* 自动显示滚动条 */
          overflowY: 'hidden', /* 隐藏垂直滚动条 */
          padding: '0', /* 移除内边距 */
          flex: 1,
          width: '100%',
          minHeight: '350px',
          maxHeight: '350px',
          scrollbarWidth: 'thin', /* 细滚动条 */
          scrollbarColor: '#ddd #f5f5f5', /* 滚动条颜色 */
          whiteSpace: 'nowrap', /* 确保内容不换行，强制显示水平滚动条 */
          marginTop: '0px' /* 移除顶部外边距 */
        }}>
          {dailyConsumption.map((item, index) => (
            <div key={index} style={{
              minWidth: '130px',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              marginRight: '25px'
            }}>
              {/* 日期标签 */}
              <div style={{
                fontSize: '13px',
                fontWeight: 'bold',
                marginBottom: '10px',
                color: '#666',
                textAlign: 'center'
              }}>
                {item.date.split('-')[1] + '/' + item.date.split('-')[2]}
              </div>

              {/* 货币类型柱子组 */}
              <div style={{ display: 'flex', width: '100%', justifyContent: 'space-around' }}>
                {currencyTypes.map(type => {
                  const value = Number(item[type]) || 0;

                  // 计算每个柱子的最大值（为了独立刻度）
                  const maxForType = Math.max(
                    ...dailyConsumption.map(d => Number(d[type]) || 0)
                  );

                  // 计算高度百分比（相对于该类型的最大值）
                  // 确保即使是小值也有一定的高度
                  let heightPercent = 0;
                  if (value > 0) {
                    if (maxForType > 0) {
                      // 最小高度为20%，最大高度为90%
                      heightPercent = 20 + ((value / maxForType) * 70);
                    } else {
                      heightPercent = 20; // 默认最小高度
                    }
                  }

                  return (
                    <div key={type} style={{
                      display: 'flex',
                      flexDirection: 'column',
                      alignItems: 'center',
                      width: `${90 / currencyTypes.length}%`,
                      position: 'relative'
                    }}>
                      {/* 柱子 */}
                      <Tooltip title={`${getCurrencyName(type)}: ${value.toFixed(2)} ${getCurrencyUnit(type)}`}>
                        <div style={{
                          width: '100%',
                          height: '220px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'flex-end',
                          alignItems: 'center'
                        }}>
                          {/* 柱子本体 */}
                          <div style={{
                            width: '28px',
                            height: `${heightPercent}%`,
                            backgroundColor: colorMap[type] || '#999',
                            borderRadius: '6px 6px 0 0',
                            position: 'relative',
                            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                            minHeight: value > 0 ? '15px' : '0',
                            transition: 'height 0.3s ease',
                            border: `1px solid ${colorMap[type] ? colorMap[type].replace(')', ', 0.9)').replace('rgb', 'rgba') : 'rgba(153, 153, 153, 0.9)'}`,
                            borderBottom: 'none',
                            // 添加进度条效果
                            backgroundImage: `linear-gradient(to bottom,
                              ${colorMap[type] ? colorMap[type].replace(')', ', 0.9)').replace('rgb', 'rgba') : 'rgba(153, 153, 153, 0.9)'} 0%,
                              ${colorMap[type] || '#999'} 100%)`
                          }}>
                            {/* 顶部光泽效果 */}
                            <div style={{
                              position: 'absolute',
                              top: 0,
                              left: 0,
                              right: 0,
                              height: '40%',
                              background: 'linear-gradient(to bottom, rgba(255,255,255,0.4) 0%, rgba(255,255,255,0) 100%)',
                              borderRadius: '6px 6px 0 0'
                            }}></div>

                            {/* 数值标签 */}
                            {value > 0 && (
                              <div style={{
                                position: 'absolute',
                                top: '-25px',
                                left: '50%',
                                transform: 'translateX(-50%)',
                                backgroundColor: 'white',
                                padding: '2px 6px',
                                borderRadius: '10px',
                                fontSize: '12px',
                                fontWeight: 'bold',
                                color: colorMap[type] || '#666',
                                boxShadow: '0 1px 4px rgba(0,0,0,0.1)',
                                whiteSpace: 'nowrap',
                                border: `1px solid ${colorMap[type] ? colorMap[type].replace(')', ', 0.3)').replace('rgb', 'rgba') : 'rgba(153, 153, 153, 0.3)'}`
                              }}>
                                {formatValue(value, type)}
                              </div>
                            )}
                          </div>
                        </div>
                      </Tooltip>

                      {/* 货币类型标签 */}
                      <div style={{
                        fontSize: '11px',
                        color: colorMap[type] || '#666',
                        marginTop: '5px',
                        fontWeight: 'bold',
                        textAlign: 'center',
                        position: 'relative',
                        zIndex: 5
                      }}>
                        {type}
                      </div>

                      {/* 刻度线 - 只显示最大值和中间值 */}
                      {maxForType > 0 && (
                        <div style={{
                          position: 'absolute',
                          right: '-15px',
                          top: 0,
                          height: '170px',
                          display: 'flex',
                          flexDirection: 'column',
                          justifyContent: 'space-between',
                          pointerEvents: 'none'
                        }}>
                          {/* 刻度线标记，但不显示数值 */}
                          <div style={{
                            position: 'absolute',
                            top: '0',
                            right: '2px',
                            width: '4px',
                            height: '1px',
                            backgroundColor: '#ddd'
                          }}></div>
                          <div style={{
                            position: 'absolute',
                            top: '50%',
                            right: '2px',
                            width: '4px',
                            height: '1px',
                            backgroundColor: '#ddd'
                          }}></div>
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 渲染饼图
  const renderPieChart = () => {
    if (pieChartData.length === 0) {
      return <Empty description="暂无数据" />;
    }

    // 为不同类型分配颜色
    const getColor = (type: string) => {
      return type === 'LTC' ? '#1890ff' :
             type === 'UTS' ? '#52c41a' :
             type === 'CNY' ? '#faad14' :
             type === 'USD' ? '#722ed1' : '#999';
    };

    // 准备饼图数据 - 按货币类型展开
    const preparePieData = () => {
      // 根据当前视图类型处理数据
      if (chartViewType === 'app' || chartViewType === 'service') {
        // 将数据按货币类型展开
        const expandedData: any[] = [];

        pieChartData.forEach(item => {
          if (item.currencyTypes && item.currencyValues) {
            item.currencyTypes.forEach((type, idx) => {
              const value = item.currencyValues?.[idx] || 0;
              if (value > 0) {
                expandedData.push({
                  type: type,
                  value: value,
                  category: item.displayName || item.type,
                  displayName: type === 'LTC' ? 'LLM Token' :
                               type === 'UTS' ? '体验次数' :
                               type === 'CNY' ? '人民币' :
                               type === 'USD' ? '美元' : type,
                  unit: type === 'LTC' ? 'Tokens' :
                        type === 'UTS' ? '次' :
                        type === 'CNY' ? '元' :
                        type === 'USD' ? '$' : '',
                  appName: item.appName || item.type
                });
              }
            });
          }
        });

        return expandedData.sort((a, b) => b.value - a.value);
      } else {
        // 货币类型视图直接使用数据
        return pieChartData.map(item => ({
          type: item.type,
          value: item.value,
          displayName: item.displayName,
          unit: item.type === 'LTC' ? 'Tokens' :
                item.type === 'UTS' ? '次' :
                item.type === 'CNY' ? '元' :
                item.type === 'USD' ? '$' : ''
        }));
      }
    };



    return (
      <div style={{
        padding: '10px 0',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        position: 'relative',
        paddingBottom: '30px' // 添加底部内边距，与柱状图保持一致
      }}>
        <div style={{ display: 'flex', justifyContent: 'center', marginBottom: '10px', marginTop: '-10px' }}>
          <Radio.Group
            value={chartViewType}
            onChange={(e) => {
              const newType = e.target.value as ChartViewType;
              setChartViewType(newType);

              // 重新计算饼图数据
              if (tableProps.dataSource) {
                const data = tableProps.dataSource;
                const totalAmount = data.reduce((sum, item) => sum + item.amount, 0);

                // 按应用分组
                if (newType === 'app') {
                  // 按应用分组，并按货币类型细分
                  const appCurrencyMap = new Map<string, Map<string, number>>();

                  // 初始化每个应用的货币类型映射
                  appDistribution.forEach(app => {
                    appCurrencyMap.set(app.appId, new Map<string, number>());
                  });

                  // 统计每个应用的每种货币类型消耗
                  data.forEach(item => {
                    const appId = item.app_id;
                    const currencyType = item.currency_type || '未知';
                    const appCurrencies = appCurrencyMap.get(appId);

                    if (appCurrencies) {
                      appCurrencies.set(
                        currencyType,
                        (appCurrencies.get(currencyType) || 0) + item.amount
                      );
                    }
                  });

                  // 转换为饼图数据
                  const pieData = appDistribution.map(item => {
                    const appCurrencies = appCurrencyMap.get(item.appId);
                    const currencyTypes = appCurrencies ? Array.from(appCurrencies.keys()) : [];
                    const currencyValues = appCurrencies ? Array.from(appCurrencies.values()) : [];

                    return {
                      type: item.appName || item.appId,
                      value: item.amount,
                      percent: totalAmount > 0 ? (item.amount / totalAmount) * 100 : 0,
                      currencyTypes: currencyTypes,
                      currencyValues: currencyValues
                    };
                  });

                  setPieChartData(pieData.sort((a, b) => b.value - a.value));
                }
                // 按服务分组
                else if (newType === 'service') {
                  // 按服务分组，并按货币类型细分
                  const serviceCurrencyMap = new Map<string, Map<string, number>>();

                  // 初始化每个服务的货币类型映射
                  serviceDistribution.forEach(service => {
                    serviceCurrencyMap.set(String(service.serviceId), new Map<string, number>());
                  });

                  // 统计每个服务的每种货币类型消耗
                  data.forEach(item => {
                    const serviceId = String(item.service_id);
                    const currencyType = item.currency_type || '未知';
                    const serviceCurrencies = serviceCurrencyMap.get(serviceId);

                    if (serviceCurrencies) {
                      serviceCurrencies.set(
                        currencyType,
                        (serviceCurrencies.get(currencyType) || 0) + item.amount
                      );
                    }
                  });

                  // 转换为饼图数据
                  const pieData = serviceDistribution.map(item => {
                    const serviceCurrencies = serviceCurrencyMap.get(String(item.serviceId));
                    const currencyTypes = serviceCurrencies ? Array.from(serviceCurrencies.keys()) : [];
                    const currencyValues = serviceCurrencies ? Array.from(serviceCurrencies.values()) : [];

                    return {
                      type: item.serviceName,
                      value: item.amount,
                      percent: totalAmount > 0 ? (item.amount / totalAmount) * 100 : 0,
                      currencyTypes: currencyTypes,
                      currencyValues: currencyValues
                    };
                  });

                  setPieChartData(pieData.sort((a, b) => b.value - a.value));
                }
                // 按活动类型分组
                // 移除了按活动类型分组的逻辑
                // 按信用货币类型分组
                else {
                  // 按信用货币类型分组
                  const currencyTypeMap = new Map<string, number>();
                  data.forEach(item => {
                    const type = item.currency_type || '未知';
                    currencyTypeMap.set(type, (currencyTypeMap.get(type) || 0) + item.amount);
                  });

                  const pieData = Array.from(currencyTypeMap.entries()).map(([type, amount]) => {
                    const displayName = type === 'LTC' ? 'LLM Token Counts (LTC)' :
                         type === 'UTS' ? '体验次数 (UTS)' :
                         type === 'CNY' ? '人民币 (CNY)' :
                         type === 'USD' ? '美元 (USD)' : type;

                    return {
                      type: type,
                      displayName: displayName,
                      value: amount,
                      percent: totalAmount > 0 ? (amount / totalAmount) * 100 : 0,
                      currencyTypes: [type],
                      currencyValues: [amount]
                    };
                  });
                  setPieChartData(pieData.sort((a, b) => b.value - a.value));
                }
              }
            }}
            buttonStyle="solid"
            style={{ marginBottom: '10px' }}
          >
            <Radio.Button value="currencyType">按信用货币类型</Radio.Button>
            <Radio.Button value="app">按应用</Radio.Button>
            <Radio.Button value="service">按服务</Radio.Button>
          </Radio.Group>
        </div>

        <div style={{
          display: 'flex',
          flexDirection: 'row',
          justifyContent: 'space-around',
          alignItems: 'flex-start',
          flex: 1,
          padding: '0', /* 移除内边距 */
          minHeight: '350px',
          maxHeight: '350px',
          overflowX: 'auto', /* 自动显示滚动条 */
          overflowY: 'hidden', /* 隐藏垂直滚动条 */
          scrollbarWidth: 'thin', /* 细滚动条 */
          scrollbarColor: '#ddd #f5f5f5', /* 滚动条颜色 */
          whiteSpace: 'nowrap', /* 确保内容不换行，强制显示水平滚动条 */
          width: '100%', /* 确保宽度占满容器 */
          marginTop: '0px' /* 移除顶部外边距 */
        }}>
          {preparePieData().slice(0, 4).map((item, index) => (
            <div key={index} style={{
              textAlign: 'center',
              margin: '0 10px',
              minWidth: '240px'
            }}>
              <div style={{ position: 'relative', width: '220px', height: '220px', margin: '0 auto' }}>
                <Progress
                  type="circle"
                  percent={100}
                  strokeColor={getColor(item.type)}
                  strokeWidth={10}
                  size={220}
                  format={() => (
                    <div>
                      <div style={{ fontSize: '28px', fontWeight: 'bold', color: getColor(item.type) }}>
                        {item.type === 'LTC' ? (item.value / 10000).toFixed(2) + '万' : item.value.toFixed(2)}
                      </div>
                      <div style={{ fontSize: '14px', color: '#666', marginTop: '5px' }}>
                        {item.unit || ''}
                      </div>
                    </div>
                  )}
                />
              </div>
              <div style={{
                marginTop: '10px',
                textAlign: 'center',
                fontSize: '14px',
                fontWeight: 'bold',
                color: '#333',
                backgroundColor: `${getColor(item.type)}15`,
                padding: '6px 12px',
                borderRadius: '15px',
                display: 'inline-block'
              }}>
                {item.displayName || item.type}
              </div>

              {/* 显示应用名称 */}
              {item.appName && (
                <div style={{
                  marginTop: '8px',
                  textAlign: 'center',
                  fontSize: '13px',
                  color: '#666',
                  padding: '4px 10px',
                  borderRadius: '12px',
                  backgroundColor: '#f5f5f5',
                  display: 'inline-block'
                }}>
                  {item.appName}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 这些函数在当前布局中不再使用，但保留以备将来可能需要
  /*
  // 自定义应用消费量柱状图
  const renderBarChart = () => {
    const maxValue = Math.max(...barChartData.flatMap(item => [item.current, item.previous, item.target]));

    return (
      <div style={{ padding: '10px 0' }}>
        <div style={{ display: 'flex', marginBottom: '10px' }}>
          <div style={{ flex: 1, textAlign: 'center' }}>
            <Tag color="blue">当前消费</Tag>
          </div>
          <div style={{ flex: 1, textAlign: 'center' }}>
            <Tag color="cyan">目标消费</Tag>
          </div>
          <div style={{ flex: 1, textAlign: 'center' }}>
            <Tag color="orange">历史消费</Tag>
          </div>
        </div>

        {barChartData.length > 0 ? (
          barChartData.map((item, index) => (
            <div key={index} style={{ marginBottom: '15px' }}>
              <div style={{ marginBottom: '5px', display: 'flex', justifyContent: 'space-between' }}>
                <Tooltip title={`应用ID: ${item.category}`}>
                  <span>{item.category}</span>
                </Tooltip>
                <span style={{ color: '#999', fontSize: '12px' }}>{item.current.toFixed(2)}</span>
              </div>
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: `${(item.current / maxValue) * 100}%`,
                  backgroundColor: '#1890ff',
                  height: '16px',
                  borderRadius: '2px',
                  transition: 'all 0.3s'
                }}></div>
              </div>
            </div>
          ))
        ) : (
          <div style={{ textAlign: 'center', padding: '20px 0', color: '#999' }}>暂无数据</div>
        )}
      </div>
    );
  };

  // 简化版的统计卡片
  const renderStatCard = () => {
    return (
      <div style={{ padding: '10px' }}>
        <Row gutter={[16, 16]}>
          <Col span={8}>
            <div>
              <div style={{ color: '#999', fontSize: '14px' }}>总活动数</div>
              <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{statistics.totalActivities}</div>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <div style={{ color: '#999', fontSize: '14px' }}>总消费量</div>
              <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{statistics.totalAmount.toFixed(2)}</div>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <div style={{ color: '#999', fontSize: '14px' }}>平均消费量</div>
              <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{statistics.avgAmount.toFixed(2)}</div>
            </div>
          </Col>
        </Row>
        <Row gutter={[16, 16]} style={{ marginTop: '16px' }}>
          <Col span={8}>
            <div>
              <div style={{ color: '#999', fontSize: '14px' }}>消费活动数</div>
              <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{statistics.consumeCount}</div>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <div style={{ color: '#999', fontSize: '14px' }}>验证活动数</div>
              <div style={{ fontSize: '24px', fontWeight: 'bold' }}>{statistics.verifyCount}</div>
            </div>
          </Col>
          <Col span={8}>
            <Button
              type="primary"
              onClick={exportToCSV}
              loading={loading}
              style={{ marginTop: '5px' }}
            >
              导出数据
            </Button>
          </Col>
        </Row>
      </div>
    );
  };
  */

  return (
    <div className="p-4">
      <Card className="mb-4">
        <Form
          {...searchFormProps}
          layout="inline"
        >
          <Row gutter={[16, 16]} style={{ width: '100%' }}>
            <Col span={6}>
              <Form.Item label="应用" name="app_id">
                <Select
                  {...appSelectProps}
                  placeholder="请选择应用"
                  allowClear
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="服务" name="service_id">
                <Select
                  {...serviceSelectProps}
                  placeholder="请选择服务"
                  allowClear
                  mode="multiple"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            {/* 移除了活动类型筛选 */}
            <Col span={6}>
              <Form.Item label="信用货币类型" name="currency_type">
                <Select
                  placeholder="请选择信用货币类型"
                  allowClear
                  options={[
                    { label: 'LLM Token Counts (LTC)', value: 'LTC' },
                    { label: '体验次数 (UTS)', value: 'UTS' },
                    { label: '人民币 (CNY)', value: 'CNY' },
                    { label: '美元 (USD)', value: 'USD' }
                  ]}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="用户ID" name="user_id">
                <Input
                  placeholder="请输入精确用户ID"
                  style={{ width: '100%' }}
                  allowClear
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label="日期范围" name="date_range">
                <RangePicker
                  style={{ width: '100%' }}
                  placeholder={['开始日期', '结束日期']}
                  allowEmpty={[true, true]}
                />
              </Form.Item>
            </Col>
            <Col span={6} style={{ display: 'flex', alignItems: 'flex-end' }}>
              <Button type="primary" htmlType="submit">
                查询
              </Button>
              <Button
                style={{ marginLeft: 8 }}
                onClick={() => {
                  searchFormProps.form?.resetFields();
                  // 重置后提交空表单，获取所有应用的数据
                  searchFormProps.onFinish?.({});
                }}
              >
                重置
              </Button>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* 消耗次数饼图 */}
      <Row gutter={[16, 16]} className="mb-4">
        <Col span={24}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '4px',
                  height: '16px',
                  backgroundColor: '#1890ff',
                  marginRight: '8px',
                  borderRadius: '2px'
                }}></div>
                <span style={{ fontWeight: 'bold' }}>活动次数统计</span>
              </div>
            }
            styles={{
              header: { borderBottom: '1px solid #f0f0f0' },
              body: {
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
                overflowX: 'auto',
                overflowY: 'hidden'
              }
            }}
            style={{
              borderRadius: '8px',
              overflowX: 'visible',
              overflowY: 'hidden',
              height: 'auto',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative'
            }}
          >
            {renderConsumptionCountPieChart()}
          </Card>
        </Col>
      </Row>

      {/* 仪表盘指标 */}
      <Row gutter={[16, 16]} className="mb-4">
        <Col span={24}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '4px',
                  height: '16px',
                  backgroundColor: '#1890ff',
                  marginRight: '8px',
                  borderRadius: '2px'
                }}></div>
                <span style={{ fontWeight: 'bold' }}>信用货币消耗统计</span>
              </div>
            }
            styles={{
              header: { borderBottom: '1px solid #f0f0f0' },
              body: {
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
                overflowX: 'auto',
                overflowY: 'hidden'
              }
            }}
            style={{
              borderRadius: '8px',
              overflowX: 'visible',
              overflowY: 'hidden',
              height: 'auto',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative'
            }}
          >
            <div style={{
              display: 'flex',
              flexDirection: 'row',
              justifyContent: 'space-around',
              alignItems: 'center',
              flex: 1,
              padding: '0',
              width: '100%',
              overflowX: 'auto',
              overflowY: 'hidden',
              paddingBottom: '30px' // 添加底部内边距，与其他图表保持一致
            }}>
              {gaugeData.map((item) => renderGaugeChart(item))}
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={16} className="mb-4">
        <Col span={12}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '4px',
                  height: '16px',
                  backgroundColor: '#1890ff',
                  marginRight: '8px',
                  borderRadius: '2px'
                }}></div>
                <span style={{ fontWeight: 'bold' }}>每日信用货币类型消耗统计</span>
              </div>
            }
            styles={{
              header: { borderBottom: '1px solid #f0f0f0' },
              body: {
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
                paddingBottom: '30px', // 增加底部内边距，为滚动条留出空间
                overflowX: 'auto', // 使用 auto，让内容决定是否显示滚动条
                overflowY: 'hidden' // 隐藏垂直滚动条
              }
            }}
            style={{
              borderRadius: '8px',
              overflowX: 'visible', // 水平方向可见
              overflowY: 'hidden', // 垂直方向隐藏
              height: '500px',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative' // 确保定位上下文
            }}
          >
            {renderNewDailyConsumptionChart()}
          </Card>
        </Col>
        <Col span={12}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                  width: '4px',
                  height: '16px',
                  backgroundColor: '#1890ff',
                  marginRight: '8px',
                  borderRadius: '2px'
                }}></div>
                <span style={{ fontWeight: 'bold' }}>信用货币类型消耗占比</span>
              </div>
            }
            styles={{
              header: { borderBottom: '1px solid #f0f0f0' },
              body: {
                padding: '20px',
                display: 'flex',
                flexDirection: 'column',
                flex: 1,
                paddingBottom: '30px', // 增加底部内边距，为滚动条留出空间
                overflowX: 'auto', // 使用 auto，让内容决定是否显示滚动条
                overflowY: 'hidden' // 隐藏垂直滚动条
              }
            }}
            style={{
              borderRadius: '8px',
              overflowX: 'visible', // 水平方向可见
              overflowY: 'hidden', // 垂直方向隐藏
              height: '500px',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative' // 确保定位上下文
            }}
          >
            {renderPieChart()}
          </Card>
        </Col>
      </Row>


      <Card
          title="用户消耗统计列表"
          className="mb-4"
          extra={
            <Button
              type="primary"
              onClick={() => exportUserDistributionData(userDistribution)}
            >
              导出数据
            </Button>
          }
        >
        <Table
          dataSource={userDistribution}
          rowKey={(record) => `${record.appId}-${record.userId}-${record.currencyType || '未知'}`}
          pagination={{ pageSize: 10 }}
        >
          <Table.Column
            title="应用ID"
            dataIndex="appId"
            sorter={(a, b) => a.appId.localeCompare(b.appId)}
          />
          <Table.Column
            title="应用名称"
            dataIndex="appName"
            render={(value, record: UserDistribution) => value || record.appId}
          />
          <Table.Column
            title="用户ID"
            dataIndex="userId"
            sorter={(a, b) => a.userId.localeCompare(b.userId)}
          />
          <Table.Column
            title="活动数量"
            dataIndex="count"
            sorter={(a, b) => a.count - b.count}
          />
          <Table.Column
            title="消费总量"
            dataIndex="amount"
            sorter={(a, b) => a.amount - b.amount}
            render={(value) => value.toFixed(2)}
          />
          <Table.Column
            title="限额总量"
            dataIndex="totalKeyCredit"
            sorter={(a, b) => a.totalKeyCredit - b.totalKeyCredit}
            render={(value) => value.toFixed(2)}
          />
          <Table.Column
            title="单位"
            dataIndex="currencyType"
            render={(value) => {
              const currencyTypeMap: Record<string, string> = {
                'LTC': 'LLM Token Counts',
                'UTS': '体验次数',
                'CNY': '人民币',
                'USD': '美元'
              };
              return value ? currencyTypeMap[value] || value : '-';
            }}
          />
        </Table>
      </Card>
    </div>
  );
};
