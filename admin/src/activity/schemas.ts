// 活动相关类型定义

// 活动类型枚举
export enum ActivityType {
  VERIFY = "verify", // 验证
  CONSUME = "consume" // 消费
}

// 活动实体
export interface Activity {
  id: number; // 活动ID
  key_id: string; // 密钥ID
  app_id: string; // 应用ID
  user_id: string; // 用户ID
  service_id: number; // 服务ID
  service_code: string; // 服务代码
  instance_id?: string; // 实例ID
  currency_type?: string; // 信用货币类型
  type: ActivityType; // 活动类型
  amount: number; // 消费数量
  details?: any; // 详情
  created_at: Date; // 创建时间
}

// 活动详情（包含关联数据）
export interface ActivityDetail extends Activity {
  key?: {
    id: string;
  }; // 关联的密钥信息
  app?: {
    app_id: string;
    name: string;
  }; // 关联的应用信息
  service?: {
    id: number;
    name: string;
    code: string;
  }; // 关联的服务信息
}
