import { create } from 'zustand';
import { createJSONStorage, devtools, persist } from 'zustand/middleware';
import { AdminInfo } from '../schemas';

interface AuthState {
  /** 管理员用户信息 */
  user: AdminInfo | null;
  /** 用户 token */
  token: string | null;
  /** 是否已认证 */
  isAuthenticated: boolean;
  /** 记住登录状态 */
  remember: boolean;

  /**
   * 设置用户信息
   * @param user 用户信息
   * @param token 用户 token
   * @param remember 是否记住登录状态
   */
  setUser: (user: AdminInfo, token: string, remember?: boolean) => void;

  /**
   * 清除用户信息
   */
  clearUser: () => void;
}

export const useAuthStore = create<AuthState>()(
  devtools(
    persist(
      (set) => ({
        user: null,
        token: null,
        isAuthenticated: false,
        remember: false,

        setUser: (user, token, remember = false) => set({
          user,
          token,
          isAuthenticated: true,
          remember
        }, false, 'setUser'),

        clearUser: () => set({
          user: null,
          isAuthenticated: false
        }, false, 'clearUser')
      }),
      {
        name: 'admin-auth-storage',
        storage: createJSONStorage(() => localStorage),
        partialize: (state) => ({
          user: state.user,
          token: state.token,
          isAuthenticated: state.isAuthenticated,
          remember: state.remember
        }),
      }
    ),
    {
      name: 'admin-auth-store',
    }
  )
);

export const getAuthStore = () => useAuthStore.getState(); 