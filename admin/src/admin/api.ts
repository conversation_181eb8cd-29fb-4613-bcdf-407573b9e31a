import { API_BASE_URL } from "@/common/env";
import { ResponsePayloads } from "@/common/schemas";
import { AdminLoginRequest, AdminLoginResult } from "./schemas";
/**
 * 管理员登录API
 * @param data 登录请求数据
 * @returns 登录结果
 */
export const adminLogin = async (data: AdminLoginRequest): Promise<ResponsePayloads<AdminLoginResult>> => {
    const response = await fetch(`${API_BASE_URL}/admin/login`, {
        method: "POST",
        headers: {
            "Content-Type": "application/json",
            "Accept": "application/json"
        },
        body: JSON.stringify(data)
    });

    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "登录失败");
    }

    return await response.json();
};
