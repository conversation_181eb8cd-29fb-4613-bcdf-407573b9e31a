import React from "react";
import { Create, useForm } from "@refinedev/antd";
import { Form, Input, Button, Upload, message } from "antd";
import { FaUpload } from "react-icons/fa";
import { HiaAdmin } from "../schemas";
import type { UploadProps } from "antd";
import { API_BASE_URL } from "@/common/env";
import { entityKey } from "../consts";
export const AdminCreate: React.FC = () => {
  const { formProps, saveButtonProps } = useForm<HiaAdmin>({
    resource: entityKey,
  });

  const uploadProps: UploadProps = {
    name: 'avatar',
    action: `${API_BASE_URL}/upload`,
    headers: {
      // 'Authorization': `Bearer ${token}`,
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        // 设置头像URL到表单
        formProps.form?.setFieldsValue({
          avatar_url: info.file.response.data.url,
        });
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Form.Item
          label="用户名"
          name="username"
          rules={[{ required: true, message: "请输入用户名" }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>

        <Form.Item
          label="密码"
          name="password"
          rules={[{ required: true, message: "请输入密码" }]}
        >
          <Input.Password placeholder="请输入密码" />
        </Form.Item>

        <Form.Item
          label="确认密码"
          name="confirmPassword"
          dependencies={["password"]}
          rules={[
            { required: true, message: "请确认密码" },
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!value || getFieldValue("password") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error("两次输入的密码不一致"));
              },
            }),
          ]}
        >
          <Input.Password placeholder="请确认密码" />
        </Form.Item>

        <Form.Item label="头像" name="avatar_url">
          <Input hidden />
          <Upload {...uploadProps}>
            <Button icon={<FaUpload />}>上传头像</Button>
          </Upload>
        </Form.Item>
      </Form>
    </Create>
  );
}; 