import React from "react";
import { Show } from "@refinedev/antd";
import { Typography, Descriptions, Avatar } from "antd";
import { HiaAdmin } from "../schemas";
import { useShow } from "@refinedev/core";
import { entityKey } from "../consts";
const { Title } = Typography;

export const AdminShow: React.FC = () => {
  const { query } = useShow<HiaAdmin>({
    resource: entityKey,
  });

  const { data, isLoading } = query;
  const record = data?.data;

  return (
    <Show isLoading={isLoading}>
      <Title level={5}>管理员详情</Title>

      {record && (
        <Descriptions bordered column={1}>
          <Descriptions.Item label="ID">{record.id}</Descriptions.Item>
          <Descriptions.Item label="用户名">{record.username}</Descriptions.Item>
          <Descriptions.Item label="头像">
            {record.avatar_url ? (
              <Avatar
                src={record.avatar_url}
                size={64}
                shape="square"
              />
            ) : (
              "-"
            )}
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {record.created_at ? new Date(record.created_at).toLocaleString() : "-"}
          </Descriptions.Item>
          <Descriptions.Item label="更新时间">
            {record.updated_at ? new Date(record.updated_at).toLocaleString() : "-"}
          </Descriptions.Item>
        </Descriptions>
      )}
    </Show>
  );
}; 