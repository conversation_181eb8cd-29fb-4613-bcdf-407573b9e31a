import React from "react";
import { Edit, useForm } from "@refinedev/antd";
import { Form, Input, Button, Upload, message } from "antd";
import { FaUpload } from "react-icons/fa";
import { HiaAdmin } from "../schemas";
import type { UploadProps } from "antd";
import { API_BASE_URL } from "@/common/env";
import { entityKey } from "../consts";
export const AdminEdit: React.FC = () => {
  const { formProps, saveButtonProps, id } = useForm<HiaAdmin>({
    resource: entityKey,
  });

  const uploadProps: UploadProps = {
    name: 'avatar',
    action: `${API_BASE_URL}/upload`,
    headers: {
      // 'Authorization': `Bearer ${token}`,
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        // 设置头像URL到表单
        formProps.form?.setFieldsValue({
          avatar_url: info.file.response.data.url,
        });
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  return (
    <Edit saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Form.Item
          label="用户名"
          name="username"
          rules={[{ required: true, message: "请输入用户名" }]}
        >
          <Input placeholder="请输入用户名" />
        </Form.Item>

        <Form.Item
          label="密码"
          name="password"
          rules={[{ message: "请输入密码" }]}
          help="如不修改密码，请留空"
        >
          <Input.Password placeholder="请输入新密码" />
        </Form.Item>

        <Form.Item
          label="确认密码"
          name="confirmPassword"
          dependencies={["password"]}
          rules={[
            ({ getFieldValue }) => ({
              validator(_, value) {
                if (!getFieldValue("password") || !value || getFieldValue("password") === value) {
                  return Promise.resolve();
                }
                return Promise.reject(new Error("两次输入的密码不一致"));
              },
            }),
          ]}
        >
          <Input.Password placeholder="请确认新密码" />
        </Form.Item>

        <Form.Item label="头像" name="avatar_url">
          <Input hidden />
          <Upload 
            {...uploadProps}
            showUploadList={false}
          >
            <Button icon={<FaUpload />}>上传头像</Button>
          </Upload>
          {formProps.initialValues?.avatar_url && (
            <div className="mt-2">
              <img 
                src={formProps.initialValues.avatar_url} 
                alt="头像预览" 
                style={{ width: 100, height: 100, borderRadius: '50%', objectFit: 'cover' }} 
              />
            </div>
          )}
        </Form.Item>
      </Form>
    </Edit>
  );
}; 