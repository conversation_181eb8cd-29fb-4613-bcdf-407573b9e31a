import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  List,
  ShowButton,
  useTable,
} from "@refinedev/antd";
import type { BaseRecord, CrudFilters } from "@refinedev/core";
import { Button, Divider, Form, Input, Space, Table } from "antd";
import { SearchForm } from "@/common/components";
import { entityKey } from "../consts";
import { HiaAdmin } from "../schemas";
interface SearchFormValues {
  username?: string;
}

export const AdminList = () => {
  const { tableProps, searchFormProps } = useTable<HiaAdmin>({
    resource: entityKey,
    syncWithLocation: false,
    defaultSetFilterBehavior: "replace",
    onSearch: (values) => {
      const filters: CrudFilters = [];
      const data = values as SearchFormValues;
      if (data.username) {
        filters.push({
          field: "username",
          operator: "contains",
          value: data.username,
        });
      }
      return filters;
    },
  });

  return (
    <List>
      <SearchForm
        searchFormProps={searchFormProps}
        useCard={true}
        layout="inline"
      >
        <Form.Item label="用户名" name="username">
          <Input placeholder="请输入用户名" allowClear />
        </Form.Item>
      </SearchForm>

      <Divider className="my-4" />

      <Table {...tableProps} rowKey="id">
        <Table.Column dataIndex="id" title="ID" />
        <Table.Column dataIndex="username" title="用户名" />
        <Table.Column
          dataIndex="avatar_url"
          title="头像"
          render={(value) => (
            value ? <img src={value} alt="头像" style={{ width: 40, height: 40, borderRadius: '50%' }} /> : '-'
          )}
        />
        <Table.Column
          dataIndex="created_at"
          title="创建时间"
          render={(value) => new Date(value).toLocaleString()}
        />
        <Table.Column
          dataIndex="updated_at"
          title="更新时间"
          render={(value) => new Date(value).toLocaleString()}
        />
        <Table.Column
          title="操作"
          dataIndex="actions"
          render={(_, record: BaseRecord) => (
            <Space>
              <EditButton hideText size="small" recordItemId={record.id} />
              <ShowButton hideText size="small" recordItemId={record.id} />
              <DeleteButton hideText size="small" recordItemId={record.id} />
            </Space>
          )}
        />
      </Table>
    </List>
  );
}; 