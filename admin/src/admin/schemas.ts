// 管理员相关类型定义

// 管理员登录请求
export interface AdminLoginRequest {
  username: string;
  password: string;
}

// 管理员信息
export interface AdminInfo {
  id: number;
  username: string;
  avatar_url?: string;
}

// 管理员登录结果
export interface AdminLoginResult {
  token: string;
  admin_info: AdminInfo;
}

// 管理员实体
export interface HiaAdmin {
  id?: number; // 管理员ID
  username: string; // 用户名
  password: string; // 密码
  avatar_url?: string; // 头像URL
  created_at: Date; // 创建时间
  updated_at: Date; // 更新时间
}


