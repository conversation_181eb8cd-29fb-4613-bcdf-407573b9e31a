"use client";
import { APP_TITLE } from "@/common";
import { useLogin } from "@refinedev/core";
import { Button, Card, Form, Input, Typography, Checkbox } from "antd";
import { useState } from "react";

const { Title } = Typography;

export const AuthPage: React.FC = () => {
    const [form] = Form.useForm();
    // 使用refine的useLogin hook
    const { mutate: login, isLoading } = useLogin();

    // 表单提交处理
    const onFinish = (values: { username: string; password: string; remember: boolean }) => {
        login(values);
    };

    return (
        <div
            className="h-screen flex justify-center items-center bg-gradient-to-r from-blue-500 to-blue-400"
        >
            <Card
                className="w-96 p-6 shadow-lg"
            >
                <div className="text-center mb-6">
                    <Title level={3}>{APP_TITLE}</Title>
                </div>

                <Form
                    form={form}
                    layout="vertical"
                    onFinish={onFinish}
                    initialValues={{
                        remember: true
                    }}
                >
                    <Form.Item
                        name="username"
                        label="用户名"
                        rules={[{ required: true, message: "请输入用户名" }]}
                    >
                        <Input size="large" placeholder="请输入用户名" />
                    </Form.Item>

                    <Form.Item
                        name="password"
                        label="密码"
                        rules={[{ required: true, message: "请输入密码" }]}
                    >
                        <Input.Password size="large" placeholder="请输入密码" />
                    </Form.Item>

                    <Form.Item name="remember" valuePropName="checked">
                        <Checkbox>记住我</Checkbox>
                    </Form.Item>

                    <Form.Item>
                        <Button
                            type="primary"
                            size="large"
                            htmlType="submit"
                            loading={isLoading}
                            block
                        >
                            登录
                        </Button>
                    </Form.Item>
                </Form>
            </Card>
        </div>
    );
}; 