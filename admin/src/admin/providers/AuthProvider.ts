"use client";

import type { AuthProvider } from "@refinedev/core";
import { adminLogin } from "../api";
import { getAuthStore } from "../stores";

export const authProvider: AuthProvider = {
  login: async ({ username, password, remember }) => {
    try {
      const response = await adminLogin({ username, password });

      if (response.data) {
        const { token, admin_info } = response.data;

        // 将认证信息存储在Zustand中
        getAuthStore().setUser(admin_info, token, remember);

        return {
          success: true,
          redirectTo: "/",
        };
      }

      return {
        success: false,
        error: {
          name: "登录失败",
          message: response.error?.message || "用户名或密码错误",
        },
      };
    } catch (error: any) {
      return {
        success: false,
        error: {
          name: "登录错误",
          message: error.message || "登录过程中发生错误",
        },
      };
    }
  },

  logout: async () => {
    getAuthStore().clearUser();
    return {
      success: true,
      redirectTo: "/login",
    };
  },

  check: async () => {
    const { isAuthenticated } = getAuthStore();
    if (isAuthenticated) {
      return {
        authenticated: true,
      };
    }

    return {
      authenticated: false,
      logout: true,
      redirectTo: "/login",
    };
  },

  getPermissions: async () => {

    return null;
  },

  getIdentity: async () => {
    const { user } = getAuthStore();
    if (user) {
      return {
        id: user.id,
        name: user.username,
        avatar: user.avatar_url,
      };
    }
    return null;
  },

  onError: async (error) => {
    if (error.response?.status === 401) {
      getAuthStore().clearUser();
      return {
        logout: true,
        redirectTo: "/login",
      };
    }

    return { error };
  },
}; 