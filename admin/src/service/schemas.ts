// 服务相关类型定义

// 服务实体
export interface Service {
  id: number; // 服务ID
  name: string; // 服务名称
  code: string; // 服务编码
  description?: string; // 服务描述
  price: number; // 服务价格
  status: boolean; // 服务状态
  created_at: Date; // 创建时间
  updated_at: Date; // 更新时间
}

// 服务详情（包含关联数据）
export interface ServiceDetail extends Service {
  keys?: any[]; // 关联的密钥列表
}

// 服务创建请求
export interface ServiceCreateRequest {
  name: string; // 服务名称
  code: string; // 服务编码
  description?: string; // 服务描述
  price: number; // 服务价格
  status: boolean; // 服务状态
}

// 服务更新请求
export interface ServiceUpdateRequest {
  name?: string; // 服务名称
  description?: string; // 服务描述
  price?: number; // 服务价格
  status?: boolean; // 服务状态
}
