import React from "react";
import { Edit, useForm } from "@refinedev/antd";
import { Form, Input, InputNumber, Switch } from "antd";
import { Service } from "../schemas";
import { entityKey } from "../consts";

export const ServiceEdit: React.FC = () => {
  const { formProps, saveButtonProps } = useForm<Service>({
    resource: entityKey,
  });

  return (
    <Edit saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Form.Item
          label="服务ID"
          name="id"
        >
          <Input disabled />
        </Form.Item>

        <Form.Item
          label="服务名称"
          name="name"
          rules={[{ required: true, message: "请输入服务名称" }]}
        >
          <Input placeholder="请输入服务名称" />
        </Form.Item>

        <Form.Item
          label="服务编码"
          name="code"
        >
          <Input disabled />
        </Form.Item>

        <Form.Item
          label="服务描述"
          name="description"
        >
          <Input.TextArea rows={4} placeholder="请输入服务描述" />
        </Form.Item>

      </Form>
    </Edit>
  );
};
