import {
  <PERSON>ete<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>on,
  List,
  ShowButton,
  useTable,
} from "@refinedev/antd";
import type { BaseRecord, CrudFilters } from "@refinedev/core";
import { Divider, Form, Input, Space, Table, Tag, InputNumber, Select } from "antd";
import { SearchForm } from "@/common/components";
import { entityKey } from "../consts";
import { Service } from "../schemas";

interface SearchFormValues {
  name?: string;
  code?: string;
  status?: boolean;
}

export const ServiceList = () => {
  const { tableProps, searchFormProps } = useTable<Service>({
    resource: entityKey,
    syncWithLocation: false,
    defaultSetFilterBehavior: "replace",
    onSearch: (values) => {
      const filters: CrudFilters = [];
      const data = values as SearchFormValues;

      // 构建搜索过滤器
      if (data.name) {
        filters.push({
          field: "name",
          operator: "contains",
          value: data.name,
        });
      }

      if (data.code) {
        filters.push({
          field: "code",
          operator: "contains",
          value: data.code,
        });
      } 

      return filters;
    },
  });

  return (
    <List>
      {/* 使用通用搜索表单组件 */}
      <SearchForm
        searchFormProps={searchFormProps}
        useCard={true}
        layout="inline"
      >
        <Form.Item label="服务名称" name="name">
          <Input placeholder="请输入服务名称" allowClear />
        </Form.Item>
        <Form.Item label="服务编码" name="code">
          <Input placeholder="请输入服务编码" allowClear />
        </Form.Item> 
      </SearchForm>

      <Divider className="my-4" />

      <Table {...tableProps} rowKey="id">
        <Table.Column dataIndex="id" title="服务ID" />
        <Table.Column dataIndex="name" title="服务名称" />
        <Table.Column dataIndex="code" title="服务编码" />
        <Table.Column 
          dataIndex="description" 
          title="服务描述" 
          ellipsis
          render={(value) => value || '-'}
        />
        <Table.Column
          dataIndex="created_at"
          title="创建时间"
          render={(value) => new Date(value).toLocaleString()}
        />
        <Table.Column
          title="操作"
          dataIndex="actions"
          render={(_, record: BaseRecord) => (
            <Space>
              <EditButton hideText size="small" recordItemId={record.id} />
              <ShowButton hideText size="small" recordItemId={record.id} />
              <DeleteButton hideText size="small" recordItemId={record.id} />
            </Space>
          )}
        />
      </Table>
    </List>
  );
};
