import React from "react";
import { useShow, useResource, useList } from "@refinedev/core";
import {
  Show,
  DateField,
  TextField,
} from "@refinedev/antd";
import {
  Typography,
  Table,
  Card,
  Descriptions,
  Row,
  Col,
} from "antd";
import { ServiceDetail } from "../schemas";
import { entityKey } from "../consts";
import RecordNameCell from "@/common/components/RecordName";



export const ServiceShow: React.FC = () => {
  const { id } = useResource();
  const { queryResult } = useShow<ServiceDetail>({
    resource: entityKey,
    id,
  });

  // 使用 useList 钩子获取密钥列表
  const { data: keysData, isLoading: keysLoading } = useList({
    resource: "key",
    filters: [
      {
        field: "service_id",
        operator: "eq",
        value: id,
      },
    ],
  });

  const { data, isLoading } = queryResult;
  const record = data?.data;

  // 从 keysData 中提取密钥列表
  const keys = keysData?.data || [];



  if (isLoading) {
    return <div>加载中...</div>;
  }

  if (!record) {
    return <div>未找到服务</div>;
  }

  return (
    <Show
      isLoading={isLoading}
      title={<Typography.Title level={3}>服务详情: {record.name}</Typography.Title>}
    >
      <Row gutter={24}>
        <Col span={24}>
          <Card title="基本信息" className="mb-4">
            <Descriptions column={{ xs: 1, sm: 2, md: 3 }}>
              <Descriptions.Item label="服务ID">
                <TextField value={record.id} />
              </Descriptions.Item>
              <Descriptions.Item label="服务名称">
                <TextField value={record.name} />
              </Descriptions.Item>
              <Descriptions.Item label="服务编码">
                <TextField value={record.code} />
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                <DateField format="YYYY-MM-DD HH:mm:ss" value={record.created_at} />
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                <DateField format="YYYY-MM-DD HH:mm:ss" value={record.updated_at} />
              </Descriptions.Item>
              <Descriptions.Item label="服务描述" span={3}>
                {record.description || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
        </Col>

        <Col span={24}>
          <Card title={`关联密钥 (${keys.length})`} loading={keysLoading}>
            {keys && keys.length > 0 ? (
              <Table
                dataSource={keys}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                columns={[
                  { title: "密钥ID", dataIndex: "id", key: "id" },
                  {
                    title: "应用",
                    dataIndex: "app_id",
                    key: "app_id",
                    render: (value) => <RecordNameCell value={value} resource="app" nameField="name" />
                  },
                  { title: "用户ID", dataIndex: "user_id", key: "user_id" },
                  {
                    title: "密钥",
                    dataIndex: "secret",
                    key: "secret",
                    ellipsis: true,
                    render: (value) => <Typography.Text copyable>{value}</Typography.Text>
                  },
                  {
                    title: "额度",
                    key: "credit",
                    render: (_, record) => `${record.credit_used} / ${record.credit_limit}`
                  },
                  {
                    title: "过期时间",
                    dataIndex: "expires_at",
                    key: "expires_at",
                    render: (text) => text ? new Date(text).toLocaleString() : '永不过期'
                  },
                  {
                    title: "创建时间",
                    dataIndex: "created_at",
                    key: "created_at",
                    render: (text) => new Date(text).toLocaleString()
                  }
                ]}
              />
            ) : (
              <div>暂无关联密钥</div>
            )}
          </Card>
        </Col>
      </Row>
    </Show>
  );
};
