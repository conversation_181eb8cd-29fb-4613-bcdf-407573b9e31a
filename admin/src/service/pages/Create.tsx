import React from "react";
import { Create, useForm } from "@refinedev/antd";
import { Form, Input, InputNumber, Switch } from "antd";
import { Service } from "../schemas";
import { entityKey } from "../consts";

export const ServiceCreate: React.FC = () => {
  const { formProps, saveButtonProps } = useForm<Service>({
    resource: entityKey,
  });

  return (
    <Create saveButtonProps={saveButtonProps}>
      <Form {...formProps} layout="vertical">
        <Form.Item
          label="服务名称"
          name="name"
          rules={[{ required: true, message: "请输入服务名称" }]}
        >
          <Input placeholder="请输入服务名称" />
        </Form.Item>

        <Form.Item
          label="服务编码"
          name="code"
          rules={[{ required: true, message: "请输入服务编码" }]}
        >
          <Input placeholder="请输入服务编码" />
        </Form.Item>

        <Form.Item
          label="服务描述"
          name="description"
        >
          <Input.TextArea rows={4} placeholder="请输入服务描述" />
        </Form.Item> 

      </Form>
    </Create>
  );
};
