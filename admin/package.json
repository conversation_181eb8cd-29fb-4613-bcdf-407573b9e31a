{"name": "admin", "version": "0.1.0", "private": true, "type": "module", "dependencies": {"@refinedev/antd": "^5.45.3", "@refinedev/cli": "^2.16.44", "@refinedev/core": "^4.57.7", "@refinedev/devtools": "^1.2.14", "@refinedev/kbar": "^1.3.16", "@refinedev/react-router": "^1.0.0", "@refinedev/simple-rest": "^5.0.1", "@uiw/react-md-editor": "^3.19.5", "antd": "^5.17.0", "dayjs": "^1.11.13", "react": "^18.0.0", "react-dom": "^18.0.0", "react-icons": "^5.5.0", "react-router": "^7.0.2", "zustand": "^5.0.3"}, "devDependencies": {"@types/node": "^18.16.2", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@unocss/preset-attributify": "66.1.0-beta.2", "@unocss/preset-icons": "66.1.0-beta.2", "@unocss/preset-typography": "66.1.0-beta.2", "@unocss/preset-uno": "66.1.0-beta.2", "@unocss/preset-web-fonts": "66.1.0-beta.2", "@unocss/reset": "66.1.0-beta.2", "@vitejs/plugin-react": "^4.0.0", "typescript": "^5.4.2", "unocss": "66.1.0-beta.2", "vite": "^4.3.1"}, "scripts": {"dev": "vite", "dev:test": "vite --mode test", "dev:prod": "vite --mode production", "build": "vite build", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "preview": "vite preview", "start": "refine start", "refine": "refine", "compile": "tsc --noEmit"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "refine": {"projectId": "RUkgr3-hlbLLB-lUpTgX"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}}