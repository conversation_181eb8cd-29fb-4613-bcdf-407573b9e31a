<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta
      name="description"
      content="refine | Build your React-based CRUD applications, without constraints."
    />
    <meta
      data-rh="true"
      property="og:image"
      content="https://refine.dev/img/refine_social.png"
    />
    <meta
      data-rh="true"
      name="twitter:image"
      content="https://refine.dev/img/refine_social.png"
    />
    <title>
      Refine - Build your React-based CRUD applications, without constraints.
    </title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
    <script type="module" src="/src/index.tsx"></script>
    <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm dev` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  </body>
</html>
