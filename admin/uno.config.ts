import { defineConfig, presetUno, presetAttributify, presetIcons, presetTypography, presetWebFonts } from 'unocss'

export default defineConfig({
  presets: [
    presetUno(),
    presetAttributify(),
    presetIcons({
      scale: 1.2,
      warn: true,
    }),
    presetTypography(),
    presetWebFonts({
      provider: 'google',
      fonts: {
        sans: 'Roboto',
        serif: 'Merriweather',
        mono: 'Fira Code',
        custom: [
          {
            name: 'Open Sans',
            weights: ['400', '700'],
            italic: true,
          },
          {
            name: 'monospace',
            provider: 'none',
          },
        ],
      },
    }),
  ],
  shortcuts: [
    ['btn', 'px-4 py-2 rounded inline-block bg-blue-600 text-white cursor-pointer hover:bg-blue-700'],
    ['icon-btn', 'text-[0.9em] inline-block cursor-pointer select-none opacity-75 transition duration-200 ease-in-out hover:opacity-100 hover:text-blue-600'],
  ],
  rules: [
    // 可以在这里添加自定义规则
  ],
}) 
