---
description: Refine Admin开发最佳实践与规范
---

# Refine Admin开发规范

## 技术栈

- Typescript
- React
- Refine
- Unocss
- Vite
- Zustant
- Antd

## 依赖管理

- 使用`pnpm`管理依赖

## 项目结构

- 项目整体结构如下:

    ```markdown
        $PROJECT_ROOT
        ├── src
        │   ├── $RESOURCE_MODULE1 # 资源模块1
        │   ├── $RESOURCE_MODULE2 # 资源模块2
        ├── App.tsx # 主应用及路由配置
        ├── index.tsx # 入口文件
        ├── uno.d.ts # unocss类型声明
        ├── .env # 环境变量
        ├── .env.development # 开发环境变量
        ├── .env.production # 生产环境变量
        ├── .gitignore # git忽略文件
        ├── .cursorrules # cursor规则
        ├── .clinerules # 项目规范/ClineRules
        ├── .windsurfrules # 项目规范/WindsurfRules
    ```
- 采用模块化设计,每个模块位于`$PROJECT_ROOT/src`目录下,其结构如下:

    ```markdown
        $RESOURCE_MODULE1
        ├── components  # 组件
        │   ├── Cmp1.tsx
        │   ├── index.ts
        ├── contexts  # 上下文
        │   ├── Ctx1.tsx
        │   ├── index.ts
        ├── layouts  # 布局
        │   ├── Layout1.tsx
        │   ├── index.ts
        ├── pages  # 页面
        │   ├── Create.tsx
        │   ├── Edit.tsx
        │   ├── index.ts
        │   └── View.tsx
        ├── stores  # 状态管理
        │   ├── index.ts
        │   └── Store1.ts
        ├── schemas.ts  # 类型定义
        ├── consts.tsx  # 常量
        ├── api.ts  # 接口
        ├── index.ts  # 入口
        └── utils.ts  # 工具函数
    ```


## 样式

- 使用`unocss`作为样式框架

## 图标

- 使用`react-icons`作为图标库

## 代码风格

- 所有`tsx`组件使用大驼峰命名法
- 所有`tsx`组件使用箭头函数定义
- `Refine`的`resource`是实体类的小驼峰命名法，比如`EntityName`，在`Refine`中使用`entityName`


## 路由

- 使用`react-router`作为路由库
- 新增资源管理模块后，需要在`src/App.tsx`中注册资源管理模块


## 枚举

- 在`$RESOURCE_MODULE/consts.tsx`中为枚举定义颜色映射以及转换为`antd`的`Select Option`和`Tag`

## 页面

- 列表页中如果要展示关联的实体，则需要使用`common/components/RecordName.tsx`组件
