# AI智元平台

AI智元是一个综合性的AI应用平台，包含数字人智能体服务、用户管理系统和多端应用界面。

## 项目架构

项目采用微服务架构，主要包含以下组件：

- `api/` - FastAPI后端服务
- `client/` - React客户端应用
- `admin/` - Refine管理后台
- `docs/` - 项目文档
- `docker/` - Docker部署配置

## 技术栈

### 后端 (api/)
- Python 3.12
- FastAPI
- UV (包管理)
- PostgreSQL
- Redis
- Alembic (数据库迁移)

### 客户端 (client/)
- TypeScript
- React
- Vite
- UnoCSS
- Zustant
- Mantine
- Tanstack Query

### 管理后台 (admin/)
- TypeScript
- React
- Refine
- Ant Design
- Vite
- UnoCSS

## 开发环境搭建

### 后端服务

```bash
cd api
# 创建虚拟环境
python -m venv .venv
# 激活虚拟环境
.venv/Scripts/Activate.ps1  # Windows
source .venv/bin/activate   # Linux/Mac
# 安装依赖
uv pip install -e .
# 运行开发服务器
uvicorn app.main:app --host 0.0.0.0 --reload
```

### 客户端

```bash
cd client
# 安装依赖
pnpm install
# 运行开发服务器
pnpm dev
```

### 管理后台

```bash
cd admin
# 安装依赖
pnpm install
# 运行开发服务器
pnpm dev
```

## 部署

### Docker部署

1. 构建镜像

```bash
# 后端API
cd api
docker build -t aq-api .

# 客户端
cd client
docker build -t aq-ui .

# 管理后台
cd admin
docker build -t aq-admin .
```

2. 运行服务

```bash
cd docker
docker-compose up -d
```

## 测试

项目包含完整的测试套件：

```bash
# 运行所有测试
python tests/run_tests.py

# 运行特定测试文件
python tests/run_tests.py tests/integration/users/test_login.py

# 生成测试覆盖率报告
python tests/run_tests.py --cov=app
```

## 主要功能

- 数字人智能体服务
  - 形象定制
  - AI对话
  - 视频生成
- 用户系统
  - 账户管理
  - 充值消费
  - 权限控制
- 商户平台对接
  - 华坤锦绣平台集成
  - 数据上报

## 环境变量配置

项目使用.env文件管理环境变量，主要配置项包括：

- 数据库连接
- Redis配置
- OSS存储
- 第三方平台集成参数

详细配置请参考各组件目录下的`.env.example`文件。

## 项目规范

- 代码规范遵循各语言最佳实践
- 提交前需通过类型检查和代码格式化
- 遵循模块化设计原则
- 完整的测试覆盖

## 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交改动
4. 推送到分支
5. 创建 Pull Request

## 许可证

[许可证类型]

