# 华坤锦绣商户平台

华坤锦绣商户平台是一个全面的商户管理解决方案，用于管理商户账户、交易、产品和服务。系统包含四个主要组件：管理后台(admin)、前端接口(api)、文档(docs)和SDK。

## 项目架构

本项目采用模块化设计，由以下四个主要组件组成：

### 管理后台 (admin)

基于Refine框架构建的React管理界面，提供商户平台的可视化操作界面。

- **技术栈**：TypeScript、React、Refine、Ant Design、UnoCSS、Vite、Zustand
- **功能**：用户管理、管理员管理、产品管理、交易管理等

### 前端接口 (api)

基于FastAPI构建的后端API服务，为管理后台和客户端提供数据接口。

- **技术栈**：Python、FastAPI、SQLModel、Alembic、Redis、JWT
- **功能**：认证授权、数据处理、业务逻辑实现、文件上传等

### 文档 (docs)

系统相关文档，包括API文档、使用说明和开发指南。

- **内容**：API接口文档、系统架构说明、部署指南、开发规范等

### SDK

用于与API交互的客户端开发工具包，简化第三方应用与系统的集成。

- **技术栈**：Python
- **功能**：封装API调用、错误处理、数据模型定义等

## 快速开始

### 管理后台

```bash
# 进入管理后台目录
cd admin

# 安装依赖
pnpm install

# 开发环境运行
pnpm dev

# 构建生产环境
pnpm build

# 预览生产环境
pnpm preview
```

### API服务

```bash
# 进入API目录
cd api

# 安装依赖
uv pip install -e ".[dev]"

# 运行数据库迁移
alembic revision --autogenerate
alembic upgrade head

# 启动开发服务器
uvicorn app.main:app --reload

# Docker构建
docker build -t hkjx-merchant-api:latest .
```

### SDK使用

```bash
# 安装SDK
pip install billing-sdk

# 或从源码安装
cd sdk
pip install -e .
```

SDK使用示例：

```python
from billing import Client

# 初始化客户端
client = Client(api_key="your_api_key")

# 创建交易
invoice = client.create_invoice(
    amount=100.00,
    description="Monthly subscription"
)
```

## 项目结构

```
billing/
├── admin/                      # 管理后台
│   ├── src/                    # 源代码
│   │   ├── admin/              # 管理员模块
│   │   ├── user/               # 商户用户模块
│   │   ├── transaction/        # 交易模块
│   │   ├── product/            # 产品模块
│   │   └── common/             # 公共组件
│   ├── public/                 # 静态资源
│   └── package.json            # 项目配置
├── api/                        # API服务
│   ├── app/                    # 应用代码
│   │   ├── admin/              # 管理模块
│   │   ├── core/               # 核心功能
│   │   ├── entity/             # 实体定义
│   │   ├── files/              # 文件处理
│   │   ├── product/            # 产品模块
│   │   └── transaction/        # 交易模块
│   ├── tests/                  # 测试代码
│   └── pyproject.toml          # 项目配置
├── docs/                       # 文档
│   └── api/                    # API文档
└── sdk/                        # SDK
    ├── billing/                # SDK源码
    ├── examples/               # 使用示例
    ├── tests/                  # 测试代码
    └── pyproject.toml          # 项目配置
```

## 开发规范

本项目遵循严格的开发规范，详情请参考各组件目录下的规范文件：

- `.clinerules` - 项目规范
- `.cursorrules` - 编辑器规范
- `.windsurfrules` - Web开发规范

## 部署

### API服务部署

```bash
# 使用Docker部署
docker build -t hkjx-merchant-api:latest .
docker run -d -p 8000:8000 --name hkjx-merchant-api hkjx-merchant-api:latest
```

### 管理后台部署

```bash
# 构建生产版本
cd admin
pnpm build

# 部署静态文件到Web服务器
cp -r dist/* /path/to/webserver/
```

## 环境变量

### API服务环境变量

在 `.env` 文件中配置：

```
DATABASE_URL=postgresql://user:password@localhost:5432/dbname
JWT_SECRET_KEY=your_secret_key
JWT_ALGORITHM=HS256
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password
REDIS_DB=0
```

### 管理后台环境变量

在 `.env` 文件中配置：

```
VITE_APP_TITLE=华坤锦绣商户平台
VITE_API_BASE_URL=http://localhost:8000
VITE_APP_ENV=development
```

## 许可证

MIT
