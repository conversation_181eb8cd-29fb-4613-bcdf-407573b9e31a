---
description: Python Web开发最佳实践与规范
---

# Python Web开发规范

## 依赖管理

- 使用`uv`管理依赖

## 项目结构

- 项目整体结构如下:

    ```markdown
    $PROJECT_ROOT
    ├── app   # 项目根目录
    ├── tests   # 测试目录
    ├── .env   # 环境变量
    ├── .gitignore   # git忽略文件
    ├── README.md   # 项目说明
    ├── uv.lock   # uv锁文件
    ├── .clinerules   # 项目规范/ClineRules
    ├── .windsurfrules   # 项目规范/WindsurfRules
    ├── .cursorrules   # 项目规范/CursorRules
    ├── Dockerfile   # docker构建文件
    ├── pyproject.toml   # uv项目配置
    ├── main.py   # 主程序
    ```
- 采用模块化设计,每个模块位于`$PROJECT_ROOT/app`目录下,其结构如下:

    ```markdown
    module_name
    ├── admin   # 管理端
    │   ├── __init__.py
    │   ├── routes.py   # 路由
    │   ├── schemas.py  # Pydantic模型
    │   └── crud.py     # 数据库操作
    │   └── utils.py    # 工具函数
    ├── api   # 接口
    │   ├── __init__.py
    │   ├── crud.py     # 数据库操作
    │   ├── routes.py   # 路由
    │   ├── schemas.py  # Pydantic模型
    │   └── utils.py    # 工具函数
    ├── __init__.py
    └── schemas.py      # 模块公共模型
    ```

## 数据库

- 使用`sqlmodel`作为数据库ORM框架


## 示例

- 在完成一个功能时,询问是否需要添加示例代码
- 示例代码应位于`$PROJECT_ROOT/examples`目录下,结构如下:

    ```markdown
    examples
    ├── module_name   # 模块名
    │   ├── __init__.py
    │   ├── feature_name_example.py   # 功能名示例
    │   └── ...
    └── ...
    ```
- 示例和测试不是一个东西，示例代码不要用`test_`开头
- 运行示例代码时,需要先设置`PYTHONPATH`环境变量到`$PROJECT_ROOT`目录,然后使用`uv run examples/module_name/feature_name_example.py`运行

## 日志

- 使用`loguru`作为日志框架
- 日志文件位于`$PROJECT_ROOT/logs`目录下
- 日志文件按天轮转,保留30天,以`zip`格式压缩
- 为每个模块创建单独的日志记录器,并保存到`$PROJECT_ROOT/logs/module_name.log`文件中

## 装饰器

- 使用[wrapt](https://wrapt.readthedocs.io/en/latest/)库创建装饰器
- 装饰器应位于`$PROJECT_ROOT/app/$MODULE_NAME/utils.py`文件中
