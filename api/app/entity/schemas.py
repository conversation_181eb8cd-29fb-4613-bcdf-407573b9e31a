from enum import Enum
from typing import Any, List, Optional

from pydantic import BaseModel, Field


class Filter(BaseModel):
    """
    过滤器模型

    Attributes:
        property: 过滤的属性
        operator: 操作符
        value: 过滤的值
        and_: 与下一个过滤器的关系
        or_: 或下一个过滤器的关系
    """

    class Operator(Enum):
        """
        操作符
        """

        LIKE = "like"  # 模糊匹配
        EQUALS = "="  # 等于
        NOT_EQUALS = "!="  # 不等于
        GREATER_THAN = ">"  # 大于
        GREATER_THAN_OR_EQUAL_TO = ">="  # 大于等于
        LESS_THAN = "<"  # 小于
        LESS_THAN_OR_EQUAL_TO = "<="  # 小于等于
        IN = "in"  # 在...之中
        NOT_IN = "nin"  # 不在...之中
        BETWEEN = "between"  # 在...之间
        IS_NULL = "isNull"  # 为空
        IS_NOT_EMPTY = "isNotEmpty"  # 不为空
        CONTAINS = "contains"  # 包含
        NOT_CONTAINS = "notContains"  # 不包含
        STARTS_WITH = "startsWith"  # 以...开始
        ENDS_WITH = "endsWith"  # 以...结束
        REGEX = "regex"  # 正则表达式
        TAGS_CONTAINS = "containss"  # JSONB数组包含

    property: str = Field(..., description="过滤的属性")
    operator: Operator = Field(..., description="操作符")
    value: Optional[Any] = Field(None, description="过滤的值")
    and_: Optional["Filter"] = Field(
        None, alias="and", description="与下一个过滤器的关系"
    )
    or_: Optional["Filter"] = Field(
        None, alias="or", description="或下一个过滤器的关系"
    )

    class Config:
        # 允许使用字段别名
        allow_population_by_field_name = True


class Sort(BaseModel):
    """
    排序条件模型

    Attributes:
        property: 排序的属性
        direction: 排序的方向
    """

    class Direction(Enum):
        """
        排序方向枚举

        Attributes:
            ASC: 升序，用于表示结果应该从小到大排序
            DESC: 降序，用于表示结果应该从大到小排序
        """

        ASC = "asc"
        DESC = "desc"

    property: str = Field(..., description="排序的属性")
    direction: Direction = Field(..., description="排序的方向")


class QueryPayloads(BaseModel):
    """
    查询载荷模型

    Attributes:
        page_no: 页码
        page_size: 每页大小
        filters: 过滤器列表
        sorts: 排序器列表
    """

    page_no: Optional[int] = Field(None, alias="pageNo", description="页码")
    page_size: Optional[int] = Field(None, alias="pageSize", description="每页大小")
    filters: Optional[List[Filter]] = Field(None, description="过滤器列表")
    sorts: Optional[List[Sort]] = Field(None, description="排序器列表")

    class Config:
        # 允许使用字段别名
        allow_population_by_field_name = True
