"""
实体修改前的处理函数模块

此模块提供了一个注册表，用于存储不同实体类型的修改前处理函数，
以及一个执行修改前逻辑的工具函数。
"""

from typing import Any, Callable, Dict, Type

from loguru import logger
from sqlmodel import Session

# 修改前处理函数类型：接收实体对象、实体类和会话对象，返回修改后的实体对象
PreModifyFunction = Callable[[Any, Type, Session], Any]

# 修改前处理函数注册表：键为实体类，值为处理函数
pre_modify_registry: Dict[Type, PreModifyFunction] = {}


def register_pre_modify_function(entity_class: Type, pre_modify_func: PreModifyFunction) -> None:
    """
    注册实体修改前的处理函数

    Args:
        entity_class: 实体类
        pre_modify_func: 修改前处理函数，接收实体对象、实体类和会话对象
    """
    pre_modify_registry[entity_class] = pre_modify_func
    logger.info(f"已注册 {entity_class.__name__} 的修改前处理函数")


def execute_pre_modify(entity: Any, entity_class: Type, session: Session) -> Any:
    """
    执行实体修改前的处理函数

    Args:
        entity: 实体对象
        entity_class: 实体类
        session: 数据库会话

    Returns:
        处理后的实体对象，如果没有注册处理函数则返回原实体
    """
    pre_modify_func = pre_modify_registry.get(entity_class)
    if pre_modify_func:
        logger.info(f"执行 {entity_class.__name__} 的修改前处理函数")
        try:
            return pre_modify_func(entity, entity_class, session)
        except Exception as e:
            logger.error(f"执行 {entity_class.__name__} 的修改前处理函数时出错: {str(e)}")
            raise
    return entity
