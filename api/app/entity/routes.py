from typing import Any

from fastapi import APIRouter, Depends, HTTPException, Header, Request
from sqlmodel import Session, select

from app.admin.utils import get_current_admin
from app.core.schemas import Pagination, ResponsePayloads
from app.db import get_session
from app.db.models import Admin

from .schemas import QueryPayloads
from .stores import entity_map
from .utils import apply_filter
from .cleanup import execute_cleanup

router = APIRouter(prefix="/entity", tags=["实体"])


@router.post("/{entity_key}/list", response_model=ResponsePayloads[Pagination])
async def list_entity(
    entity_key: str,
    query_payload: QueryPayloads,
    current_admin: Admin = Depends(get_current_admin),
    session: Session = Depends(get_session),
) -> ResponsePayloads[Pagination]:
    """
    通用查询接口

    Args:
        entity_key: 实体类的key（驼峰形式）
        query_payload: 查询参数

    Returns:
        Page: 分页结果
    """
    # 获取实体类
    entity_class = entity_map.get(entity_key)
    if not entity_class:
        raise HTTPException(status_code=404, detail=f"未找到实体类: {entity_key}")

    # 构建查询
    query = select(entity_class)

    # 处理过滤器
    if query_payload.filters:
        for filter_item in query_payload.filters:
            query = apply_filter(query, entity_class, filter_item)

    # 执行查询获取总数
    total_count = len(session.exec(query).all())

    # 处理分页
    page_no = query_payload.page_no or 1
    page_size = query_payload.page_size or 10
    offset = (page_no - 1) * page_size

    # 处理排序
    if query_payload.sorts:
        for sort_item in query_payload.sorts:
            column = getattr(entity_class, sort_item.property)
            if sort_item.direction.value == "desc":
                column = column.desc()
            query = query.order_by(column)

    # 添加分页
    query = query.offset(offset).limit(page_size)

    # 执行查询
    results = session.exec(query).all()

    # 计算总页数
    total_pages = (total_count + page_size - 1) // page_size

    # 构建分页响应
    return ResponsePayloads(
        data=Pagination(
            total=total_count,
            page_no=page_no,
            page_size=page_size,
            data=results,
            has_more=page_no < total_pages,
        )
    )


@router.get("/{entity_key}/{entity_id}", response_model=ResponsePayloads)
async def get_entity_by_id(
    entity_key: str,
    entity_id: Any,
    current_admin: Admin = Depends(get_current_admin),
    session: Session = Depends(get_session),
) -> ResponsePayloads:
    """
    根据ID获取实体

    Args:
        entity_key: 实体类的key（驼峰形式）
        entity_id: 实体ID

    Returns:
        ResponsePayloads: 包含实体数据的响应
    """
    # 获取实体类
    entity_class = entity_map.get(entity_key)
    if not entity_class:
        raise HTTPException(status_code=404, detail=f"未找到实体类: {entity_key}")

    # 查询实体
    entity = session.get(entity_class, entity_id)
    if not entity:
        raise HTTPException(
            status_code=404, detail=f"未找到ID为{entity_id}的{entity_key}实体"
        )

    # 返回实体数据
    return ResponsePayloads(data=entity)


@router.put("/{entity_key}/{entity_id}", response_model=ResponsePayloads)
async def update_entity(
    entity_key: str,
    entity_id: Any,
    entity_data: dict,
    current_admin: Admin = Depends(get_current_admin),
    session: Session = Depends(get_session),
) -> ResponsePayloads:
    """
    更新实体

    Args:
        entity_key: 实体类的key（驼峰形式）
        entity_id: 实体ID
        entity_data: 实体数据

    Returns:
        ResponsePayloads: 包含更新后的实体数据的响应
    """
    # 获取实体类
    entity_class = entity_map.get(entity_key)
    if not entity_class:
        raise HTTPException(status_code=404, detail=f"未找到实体类: {entity_key}")
    # 查询实体
    entity = session.get(entity_class, entity_id)
    if not entity:
        raise HTTPException(
            status_code=404, detail=f"未找到ID为{entity_id}的{entity_key}实体"
        )

    # 更新实体属性
    for key, value in entity_data.items():
        if hasattr(entity, key):
            setattr(entity, key, value)

    # 提交更改
    session.add(entity)
    session.commit()
    session.refresh(entity)

    # 返回更新后的实体
    return ResponsePayloads(data=entity)


@router.post("/{entity_key}", response_model=ResponsePayloads)
async def create_entity(
    entity_key: str,
    entity_data: dict,
    current_admin: Admin = Depends(get_current_admin),
    session: Session = Depends(get_session),
) -> ResponsePayloads:
    """
    创建实体

    Args:
        entity_key: 实体类的key（驼峰形式）
        entity_data: 实体数据

    Returns:
        ResponsePayloads: 包含创建的实体数据的响应
    """
    # 获取实体类
    entity_class = entity_map.get(entity_key)
    if not entity_class:
        raise HTTPException(status_code=404, detail=f"未找到实体类: {entity_key}")

    # 创建实体实例
    entity = entity_class(**entity_data)
    # 添加实体
    session.add(entity)
    session.commit()
    session.refresh(entity)

    # 返回创建的实体
    return ResponsePayloads(data=entity)


@router.delete("/{entity_key}/{entity_id}", response_model=ResponsePayloads)
async def delete_entity(
    entity_key: str,
    entity_id: Any,
    request: Request,
    current_admin: Admin = Depends(get_current_admin),
    session: Session = Depends(get_session)
) -> ResponsePayloads:
    """
    删除实体

    Args:
        entity_key: 实体类的key（驼峰形式）
        entity_id: 实体ID
        request: 请求对象
        current_admin: 当前管理员
        session: 数据库会话
        x_post_delete: 自定义头，指示是否执行删除后的清理函数

    Returns:
        ResponsePayloads: 包含删除结果的响应
    """
    # 获取实体类
    entity_class = entity_map.get(entity_key)
    if not entity_class:
        raise HTTPException(status_code=404, detail=f"未找到实体类: {entity_key}")

    # 查询实体
    entity = session.get(entity_class, entity_id)
    if not entity:
        raise HTTPException(
            status_code=404, detail=f"未找到ID为{entity_id}的{entity_key}实体"
        )

    # 删除实体
    session.delete(entity)
    session.commit()

    # 执行删除后的清理函数
    execute_cleanup(entity_class, entity_id, session)

    # 返回成功响应
    return ResponsePayloads(
        data={
            "success": True,
            "message": f"已成功删除ID为{entity_id}的{entity_key}实体",
        }
    )
