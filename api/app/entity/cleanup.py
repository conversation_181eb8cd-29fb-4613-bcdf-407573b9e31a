"""
实体删除后的清理函数模块

此模块提供了一个注册表，用于存储不同实体类型的清理函数，
以及一个执行清理函数的工具函数。
"""

from typing import Any, Callable, Dict, Type

from loguru import logger
from sqlmodel import Session


# 清理函数类型：接收实体ID和会话对象，返回任意值
CleanupFunction = Callable[[Any, Session], Any]

# 清理函数注册表：键为实体类，值为清理函数
cleanup_registry: Dict[Type, CleanupFunction] = {}


def register_cleanup_function(entity_class: Type, cleanup_func: CleanupFunction) -> None:
    """
    注册实体删除后的清理函数

    Args:
        entity_class: 实体类
        cleanup_func: 清理函数，接收实体ID和会话对象
    """
    cleanup_registry[entity_class] = cleanup_func
    logger.info(f"已注册 {entity_class.__name__} 的清理函数")


def execute_cleanup(entity_class: Type, entity_id: Any, session: Session) -> Any:
    """
    执行实体删除后的清理函数

    Args:
        entity_class: 实体类
        entity_id: 实体ID
        session: 数据库会话

    Returns:
        清理函数的返回值，如果没有注册清理函数则返回None
    """
    cleanup_func = cleanup_registry.get(entity_class)
    if cleanup_func:
        logger.info(f"执行 {entity_class.__name__} 的清理函数，实体ID: {entity_id}")
        try:
            return cleanup_func(entity_id, session)
        except Exception as e:
            logger.error(f"执行 {entity_class.__name__} 的清理函数时出错: {str(e)}")
            raise
    return None