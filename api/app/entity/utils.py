import inspect

from sqlmodel import SQLModel, select

import app.db

from .schemas import Filter


def find_all_sqlmodel_subclasses():
    """找出所有继承自 SQLModel 的子类"""
    sqlmodel_subclasses = []

    print("\n正在搜索 app.db 模块...")

    # 直接检查 app.db 模块中的所有对象
    for _, obj in inspect.getmembers(app.db):
        # 检查是否是类且是 SQLModel 的子类，但不是 SQLModel 本身
        if inspect.isclass(obj):
            try:
                if issubclass(obj, SQLModel) and obj != SQLModel:
                    print(f"找到 SQLModel 子类: {obj.__name__}")
                    sqlmodel_subclasses.append(obj)
            except TypeError:
                # 忽略不能用于 issubclass() 的类型
                continue

    return sqlmodel_subclasses


def apply_filter(query, entity_class, filter_item: Filter):
    """
    应用过滤器到查询

    Args:
        query: 查询对象
        entity_class: 实体类
        filter_item: 过滤器项

    Returns:
        应用过滤器后的查询对象
    """
    property_name = filter_item.property
    operator = filter_item.operator
    value = filter_item.value

    # 处理关联属性 (如 'user.username' 或 'user.profile.nickname')
    if "." in property_name:
        # 分割关联属性 (例如: 'user.profile.nickname' -> ['user', 'profile', 'nickname'])
        parts = property_name.split(".")
        relation_path = parts[:-1]  # 所有关联路径部分
        attr_name = parts[-1]  # 最后一个属性名称

        # 逐步处理关联路径
        current_model = entity_class
        for relation_name in relation_path:
            # 获取关联属性
            relation_attr = getattr(current_model, relation_name)

            # 添加JOIN到查询中
            # 注意：对于每一层关系，我们直接使用属性进行JOIN
            query = query.join(relation_attr)

            # 更新当前模型为关联模型
            current_model = relation_attr.prop.mapper.class_

        # 获取最终模型中要过滤的属性列
        column = getattr(current_model, attr_name)
    else:
        # 获取实体类的属性
        column = getattr(entity_class, property_name)

    # 根据操作符应用不同的过滤条件
    if operator == Filter.Operator.EQUALS:
        query = query.where(column == value)
    elif operator == Filter.Operator.NOT_EQUALS:
        query = query.where(column != value)
    elif operator == Filter.Operator.GREATER_THAN:
        query = query.where(column > value)
    elif operator == Filter.Operator.GREATER_THAN_OR_EQUAL_TO:
        query = query.where(column >= value)
    elif operator == Filter.Operator.LESS_THAN:
        query = query.where(column < value)
    elif operator == Filter.Operator.LESS_THAN_OR_EQUAL_TO:
        query = query.where(column <= value)
    elif operator == Filter.Operator.LIKE:
        query = query.where(column.ilike(f"%{value}%"))  # 使用 ilike 忽略大小写
    elif operator == Filter.Operator.CONTAINS:
        query = query.where(column.ilike(f"%{value}%"))  # 使用 ilike 忽略大小写
    elif operator == Filter.Operator.NOT_CONTAINS:
        query = query.where(~column.ilike(f"%{value}%"))  # 使用 ilike 忽略大小写
    elif operator == Filter.Operator.STARTS_WITH:
        query = query.where(column.ilike(f"{value}%"))  # 使用 ilike 忽略大小写
    elif operator == Filter.Operator.ENDS_WITH:
        query = query.where(column.ilike(f"%{value}"))  # 使用 ilike 忽略大小写
    elif operator == Filter.Operator.IN:
        query = query.where(column.in_(value))
    elif operator == Filter.Operator.NOT_IN:
        query = query.where(~column.in_(value))
    elif operator == Filter.Operator.IS_NULL:
        query = query.where(column.is_(None))
    elif operator == Filter.Operator.IS_NOT_EMPTY:
        query = query.where(column.is_not(None))
    elif operator == Filter.Operator.BETWEEN:
        if isinstance(value, list) and len(value) == 2:
            query = query.where(column.between(value[0], value[1]))
    elif operator == Filter.Operator.TAGS_CONTAINS:
        # 处理JSONB数组包含查询
        # 如果是单个值，转换为列表
        if not isinstance(value, list):
            value = [value]

        # 使用PostgreSQL的JSONB操作符检查数组是否包含指定值

        # 对于每个标签值，检查是否存在于数组中
        # 使用 ? 操作符检查单个值是否存在于JSON数组中
        conditions = [column.contains([tag]) for tag in value]

        # 将所有条件组合为AND条件
        from sqlalchemy import and_
        query = query.where(and_(*conditions))

    # 处理AND条件
    if filter_item.and_:
        query = apply_filter(query, entity_class, filter_item.and_)

    # 处理OR条件
    if filter_item.or_:
        or_query = apply_filter(select(entity_class), entity_class, filter_item.or_)
        query = query.union(or_query)

    return query
