import inspect
from typing import List, Optional, Tuple, Any, Callable, Union, Type

from sqlmodel import SQLModel, select, Session
from sqlalchemy import or_, and_, Column
from fastapi import HTTPException
import app.db
from app.db import User

from app.core import default_password_encoder
from .schemas import Filter


def find_all_sqlmodel_subclasses():
    """找出所有继承自 SQLModel 的子类"""
    sqlmodel_subclasses = []

    print("\n正在搜索 app.db 模块...")

    # 直接检查 app.db 模块中的所有对象
    for _, obj in inspect.getmembers(app.db):
        # 检查是否是类且是 SQLModel 的子类，但不是 SQLModel 本身
        if inspect.isclass(obj):
            try:
                if issubclass(obj, SQLModel) and obj != SQLModel:
                    print(f"找到 SQLModel 子类: {obj.__name__}")
                    sqlmodel_subclasses.append(obj)
            except TypeError:
                # 忽略不能用于 issubclass() 的类型
                continue

    return sqlmodel_subclasses


def resolve_column(entity_class, property_name: str) -> Tuple[Any, Column]:
    """
    解析属性名称，获取对应的列和模型

    Args:
        entity_class: 实体类
        property_name: 属性名称

    Returns:
        包含模型和列的元组 (model, column)
    """
    # 处理关联属性 (如 'user.username' 或 'user.profile.nickname')
    if "." in property_name:
        # 分割关联属性 (例如: 'user.profile.nickname' -> ['user', 'profile', 'nickname'])
        parts = property_name.split(".")
        relation_path = parts[:-1]  # 所有关联路径部分
        attr_name = parts[-1]  # 最后一个属性名称

        # 逐步处理关联路径
        current_model = entity_class
        for relation_name in relation_path:
            # 获取关联属性
            relation_attr = getattr(current_model, relation_name)
            # 更新当前模型为关联模型
            current_model = relation_attr.prop.mapper.class_

        # 获取最终模型中要过滤的属性列
        column = getattr(current_model, attr_name)
        return current_model, column
    else:
        # 获取实体类的属性
        column = getattr(entity_class, property_name)
        return entity_class, column


def apply_operator_condition(column, operator: Filter.Operator, value: Any) -> Any:
    """
    根据操作符应用过滤条件

    Args:
        column: 数据库列
        operator: 操作符
        value: 过滤值

    Returns:
        SQLAlchemy条件表达式
    """
    if operator == Filter.Operator.EQUALS:
        return column == value
    elif operator == Filter.Operator.NOT_EQUALS:
        return column != value
    elif operator == Filter.Operator.GREATER_THAN:
        return column > value
    elif operator == Filter.Operator.GREATER_THAN_OR_EQUAL_TO:
        return column >= value
    elif operator == Filter.Operator.LESS_THAN:
        return column < value
    elif operator == Filter.Operator.LESS_THAN_OR_EQUAL_TO:
        return column <= value
    elif operator == Filter.Operator.LIKE:
        return column.like(f"%{value}%")
    elif operator == Filter.Operator.CONTAINS:
        return column.like(f"%{value}%")
    elif operator == Filter.Operator.NOT_CONTAINS:
        return ~column.like(f"%{value}%")
    elif operator == Filter.Operator.STARTS_WITH:
        return column.like(f"{value}%")
    elif operator == Filter.Operator.ENDS_WITH:
        return column.like(f"%{value}")
    elif operator == Filter.Operator.IN:
        return column.in_(value)
    elif operator == Filter.Operator.NOT_IN:
        return ~column.in_(value)
    elif operator == Filter.Operator.IS_NULL or operator == Filter.Operator.NULL:
        return column.is_(None)
    elif operator == Filter.Operator.IS_NOT_EMPTY:
        return column.is_not(None)
    elif operator == Filter.Operator.BETWEEN:
        if isinstance(value, list) and len(value) == 2:
            return column.between(value[0], value[1])
    elif operator == Filter.Operator.TAGS_CONTAINS:
        # 处理JSONB数组包含查询
        # 如果是单个值，转换为列表
        if not isinstance(value, list):
            value = [value]

        # 对于每个标签值，检查是否存在于数组中
        conditions = [column.contains([tag]) for tag in value]
        # 将所有条件组合为AND条件
        return and_(*conditions)

    return None


def process_logical_operator(entity_class, filter_item: Filter, process_func: Callable) -> Union[Any, None]:
    """
    处理逻辑操作符 (OR, AND)

    Args:
        entity_class: 实体类
        filter_item: 过滤器项
        process_func: 处理子过滤器的函数

    Returns:
        处理结果
    """
    if not isinstance(filter_item.value, list):
        return None

    # 收集所有子过滤器的条件
    conditions = []
    for sub_filter in filter_item.value:
        # 为每个子过滤器创建条件
        sub_filter_obj = sub_filter
        if not isinstance(sub_filter, Filter):
            sub_filter_obj = Filter.model_validate(sub_filter)

        condition = process_func(entity_class, sub_filter_obj)
        if condition is not None:
            conditions.append(condition)

    # 将所有条件组合为逻辑条件
    if conditions:
        if filter_item.operator == Filter.Operator.OR:
            return or_(*conditions)
        else:  # AND
            return and_(*conditions)
    return None


def apply_filter(query, entity_class, filter_item: Filter):
    """
    应用过滤器到查询

    Args:
        query: 查询对象
        entity_class: 实体类
        filter_item: 过滤器项

    Returns:
        应用过滤器后的查询对象
    """
    # 检查是否有逻辑操作符
    if filter_item.operator in [Filter.Operator.OR, Filter.Operator.AND]:
        # 处理逻辑操作符
        condition = process_logical_operator(entity_class, filter_item, create_filter_condition)
        if condition is not None:
            query = query.where(condition)
        return query

    # 处理标准过滤器
    property_name = filter_item.property
    if not property_name:  # 如果属性名为空，返回原查询
        return query

    # 解析列和处理关联
    current_model, column = resolve_column(entity_class, property_name)

    # 如果是关联属性，需要添加JOIN
    if "." in property_name:
        parts = property_name.split(".")
        relation_path = parts[:-1]  # 所有关联路径部分

        # 逐步处理关联路径，添加JOIN
        current_model = entity_class
        for relation_name in relation_path:
            relation_attr = getattr(current_model, relation_name)
            query = query.join(relation_attr)
            current_model = relation_attr.prop.mapper.class_

    # 应用操作符条件
    condition = apply_operator_condition(column, filter_item.operator, filter_item.value)
    if condition is not None:
        query = query.where(condition)

    return query


def create_filter_condition(entity_class, filter_item: Filter):
    """
    创建过滤条件

    Args:
        entity_class: 实体类
        filter_item: 过滤器项

    Returns:
        过滤条件
    """
    # 检查是否有逻辑操作符
    if filter_item.operator in [Filter.Operator.OR, Filter.Operator.AND]:
        return process_logical_operator(entity_class, filter_item, create_filter_condition)

    # 处理标准过滤器
    property_name = filter_item.property
    if not property_name:  # 如果属性名为空，返回None
        return None

    # 解析列
    _, column = resolve_column(entity_class, property_name)

    # 应用操作符条件
    return apply_operator_condition(column, filter_item.operator, filter_item.value)


def modify_user(user: User, entity_class: Type, session: Session):
    """
    对密码进行加密，并且检查用户是否已经存在

    Args:
        user: 一个用户对象
        entity_class: 实体类
        session: 数据库会话
    Returns:
        None
    """

    # 检查用户名是否已存在
    statement = select(User).where(User.username == user.username)
    existing_user = session.exec(statement).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="用户名已存在")
    # 检查手机号是否已注册
    statement = select(User).where(User.phone == user.phone)
    existing_phone = session.exec(statement).first()
    if existing_phone:
        raise HTTPException(status_code=400, detail="手机号已注册")

    # 如果存储的是用户，则需要对密码进行加密之后再存储
    user.password = default_password_encoder.encode(user.password)
