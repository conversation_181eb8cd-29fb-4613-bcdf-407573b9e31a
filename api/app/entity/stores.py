# 存储实体类映射
from typing import Dict, Type

from inflection import camelize
from sqlmodel import SQLModel

from app.entity.utils import find_all_sqlmodel_subclasses

entity_map: Dict[str, Type[SQLModel]] = {}


def init_entity_map():
    """初始化实体类映射"""
    global entity_map
    entity_classes = find_all_sqlmodel_subclasses()

    for entity_class in entity_classes:
        # 使用类名的驼峰形式作为key
        key = camelize(entity_class.__name__, uppercase_first_letter=False)
        entity_map[key] = entity_class


