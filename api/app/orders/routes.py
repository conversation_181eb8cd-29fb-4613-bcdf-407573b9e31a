from datetime import datetime

from fastapi import APIRouter, Depends, HTTPException, Query, Request
from sqlmodel import Session, select

from app.core import ResponsePayloads, Error, payment_logger, settings
from app.core.exceptions import (
    MissingOpenIdException,
    PaymentOrderCreationFailedException,
    UnsupportedPaymentMethodException,
)
from app.db import ProductType, get_session
from app.db.models import Order, PaymentPlan
from app.users.api.utils import get_current_user

from .schemas import OrderCreateRequest, OrderResult, PaymentPlanResponse
from .utils import (
    create_alipay_pc_web_order,
    create_alipay_wap_pay_order,
    create_local_order,
    create_wechat_h5_order,
    create_wechat_jsapi_order,
    create_wechat_native_order,
    get_alipay_client,
    get_order_by_id,
    get_wechat_pay_client,
    process_payment_for_order,
    wechat_response,
)

router = APIRouter(prefix="/orders", tags=["订单"])


@router.post("/", summary="创建订单", response_model=ResponsePayloads[OrderResult])
async def create_order(
    data: OrderCreateRequest,
    request: Request,
    current_user=Depends(get_current_user),
    session: Session = Depends(get_session),
):
    """创建订单并生成支付链接"""
    # 创建本地订单
    local_order = create_local_order(
        payment_method=data.payment_method,
        product_type=data.product_type,
        product_id=data.product_id,
        payment_plan_id=data.payment_plan_id,
        quantity=data.quantity,
        session=session,
        user_id=current_user.id,
    )

    # 创建支付宝订单
    if data.payment_method == Order.PaymentMethod.ALIPAY:
        # 根据设备类型选择支付方式
        user_agent = request.headers.get("User-Agent", "").lower()
        if "mobile" in user_agent:
            payment_url = create_alipay_wap_pay_order(
                order_id=local_order.id,
                amount=local_order.amount,
                subject=f"订单 #{local_order.id}",
            )
        else:
            payment_url = create_alipay_pc_web_order(
                order_id=local_order.id,
                total_amount=local_order.amount,
                subject=f"订单 #{local_order.id}",
            )

        if not payment_url:
            raise PaymentOrderCreationFailedException()
    elif data.payment_method == Order.PaymentMethod.WECHATPAY:
        # 微信支付处理
        user_agent = request.headers.get("User-Agent", "").lower()
        if "micromessenger" in user_agent:  # 微信浏览器
            openid = request.headers.get("X-WX-OPENID")  # 需要前端传递用户openid
            if not openid:
                raise MissingOpenIdException()
            payment_params = create_wechat_jsapi_order(
                order_id=local_order.id,
                total_amount=local_order.amount,
                subject=f"订单 #{local_order.id}",
                openid=openid,
            )
            if not payment_params:
                raise PaymentOrderCreationFailedException()
            return ResponsePayloads(
                data=OrderResult(
                    order_id=local_order.id,
                    payment_params=payment_params,
                    message="订单创建成功",
                )
            )
        elif "mobile" in user_agent:  # 手机浏览器
            payment_url = create_wechat_h5_order(
                order_id=local_order.id,
                total_amount=local_order.amount,
                subject=f"订单 #{local_order.id}",
            )
        else:  # PC端
            payment_url = create_wechat_native_order(
                order_id=local_order.id,
                total_amount=local_order.amount,
                subject=f"订单 #{local_order.id}",
            )
    else:
        raise UnsupportedPaymentMethodException(payment_method=data.payment_method)

    if not payment_url:
        raise PaymentOrderCreationFailedException()

    return ResponsePayloads(
        data=OrderResult(
            order_id=local_order.id, payment_url=payment_url, message="订单创建成功"
        )
    )


@router.get("/detail", summary="获取订单详情", response_model=ResponsePayloads[Order])
async def get_order(
    order_id: str,
    session: Session = Depends(get_session),
):
    """获取订单详情"""
    try:
        order = await get_order_by_id(order_id, session)
        return ResponsePayloads(data=order)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取订单详情失败: {str(e)}")


@router.post("/alipay/notify", summary="支付宝异步通知")
async def alipay_notify(request: Request, session: Session = Depends(get_session)):
    """处理支付宝异步通知

    参考文档：https://opendocs.alipay.com/open/203/105286
    """
    try:
        # 获取通知数据
        data = await request.form()
        payment_logger.info(f"收到支付宝异步通知: {data}")

        # 获取支付宝客户端
        alipay_client = get_alipay_client()
        if not alipay_client:
            payment_logger.error("获取支付宝客户端失败")
            return "fail"

        # 将请求参数转换为字典并过滤空值
        data_dict = {
            k: v for k, v in data.items() if v and k not in ["sign", "sign_type"]
        }
        signature = data.get("sign")

        # 增加调试日志
        payment_logger.debug(f"待验证参数: {data_dict}")
        payment_logger.debug(f"收到签名: {signature}")

        # 验证签名（增加字符集处理）
        success = alipay_client.verify(data_dict, signature)

        if not success:
            # 增加调试信息
            payment_logger.error(
                f"签名验证失败，待签名字符串: {alipay_client._build_sign_string(data_dict)}"
            )
            payment_logger.error(f"本地公钥: {alipay_client.alipay_public_key_string}")
            return "fail"

        # 验证通知中的 app_id 是否为该商户本身
        if data.get("app_id") != settings.alipay_appid:
            payment_logger.error(f"app_id不匹配: {data.get('app_id')}")
            return "fail"

        # 获取订单信息
        order_id = data.get("out_trade_no")
        trade_status = data.get("trade_status")

        if not order_id or not trade_status:
            payment_logger.error("缺少订单号或交易状态")
            return "fail"

        # 在验证订单金额处添加日志
        payment_logger.debug(f"支付宝通知订单号: {order_id} (长度: {len(order_id)})")

        # 处理不同的交易状态
        if trade_status in ["TRADE_SUCCESS", "TRADE_FINISHED"]:
            try:
                # 使用悲观锁查询订单，防止并发问题
                from sqlmodel import select
                statement = select(Order).where(Order.id == order_id).with_for_update()
                order = session.exec(statement).first()

                if not order:
                    payment_logger.error(f"订单不存在: {order_id}")
                    return "fail"
                # 已经支付的订单不重复处理
                if order.status == Order.Status.PAID:
                    payment_logger.info(f"订单 {order_id} 已经支付，不重复处理")
                    return "success"
                # 验证订单金额
                notify_amount = float(data.get("total_amount", 0))
                if notify_amount != order.amount:
                    payment_logger.error(
                        f"订单金额不匹配: 通知金额={notify_amount}, 订单金额={order.amount}"
                    )
                    return "fail"

                # 更新订单状态
                order.status = Order.Status.PAID
                order.updated_at = datetime.now()
                order.pay_time = datetime.now()
                session.add(order)

                # 处理订单支付后的操作
                await process_payment_for_order(order, session)
                session.commit()
                payment_logger.info(f"订单 {order_id} 支付成功")
                return "success"
            except Exception as e:
                payment_logger.exception(f"处理订单 {order_id} 失败: {str(e)}")
                session.rollback()
                return "fail"
        else:
            payment_logger.info(
                f"订单 {order_id} 的交易状态为 {trade_status}，不做处理"
            )
            return "success"

    except Exception as e:
        payment_logger.exception(f"处理支付宝异步通知失败: {str(e)}")
        return "fail"


@router.get(
    "/payment-plans",
    summary="获取付费计划列表",
    response_model=ResponsePayloads[list[PaymentPlanResponse]],
)
async def get_payment_plans(
    product_type: ProductType = Query(..., description="产品类型"),
    product_id: str = Query(..., description="产品ID"),
    session: Session = Depends(get_session),
):
    """获取适用于指定产品的付费计划列表"""
    # 构建查询
    query = select(PaymentPlan).where(
        PaymentPlan.scope_type == product_type,
        PaymentPlan.is_active == True,
    )

    # 查询适用于该产品的付费计划
    # 如果scope_ids为空，表示适用于所有产品
    # 如果scope_ids不为空，则检查产品ID是否在scope_ids中
    plans = session.exec(query).all()

    # 过滤出适用于该产品的付费计划
    filtered_plans = [
        plan for plan in plans if not plan.scope_ids or product_id in plan.scope_ids
    ]

    # 转换为响应模型
    result = [
        PaymentPlanResponse(
            id=plan.id,
            name=plan.name,
            description=plan.description,
            price=plan.price,
            original_price=plan.original_price,
            validity_period=plan.validity_period,
            scope_type=plan.scope_type,
            scope_ids=plan.scope_ids,
            is_active=plan.is_active,
            created_at=plan.created_at.isoformat() if plan.created_at else None,
            updated_at=plan.updated_at.isoformat() if plan.updated_at else None,
        )
        for plan in filtered_plans
    ]

    return ResponsePayloads(data=result)


@router.post("/wechat/notify", summary="微信支付异步通知")
async def wechat_notify(request: Request, session: Session = Depends(get_session)):
    """处理微信支付异步通知

    参考文档：https://pay.weixin.qq.com/wiki/doc/api/jsapi.php?chapter=9_7
    """
    try:
        # 获取原始XML数据
        xml_data = await request.body()
        payment_logger.info(f"收到微信支付异步通知: {xml_data}")

        # 获取微信支付客户端
        wechat_pay = get_wechat_pay_client()
        if not wechat_pay:
            payment_logger.error("获取微信支付客户端失败")
            return wechat_response("FAIL", "签名验证失败")

        try:
            # 解析并验证签名
            data = wechat_pay.parse_payment_result(xml_data)
            payment_logger.debug(f"解析后的通知数据: {data}")
        except Exception as e:
            payment_logger.error(f"解析微信支付通知失败: {str(e)}")
            return wechat_response("FAIL", "签名验证失败")

        # 验证支付结果
        if data["return_code"] != "SUCCESS":
            payment_logger.error(f"通信标识失败: {data.get('return_msg')}")
            return wechat_response("FAIL", data.get("return_msg", ""))

        if data["result_code"] != "SUCCESS":
            payment_logger.error(f"业务结果失败: {data.get('err_code_des')}")
            return wechat_response("FAIL", data.get("err_code_des", ""))

        # 获取订单信息
        order_id = data.get("out_trade_no")
        if not order_id:
            payment_logger.error("缺少订单号")
            return wechat_response("FAIL", "缺少订单号")

        # 在验证订单金额处添加日志
        payment_logger.debug(f"微信通知订单号: {order_id} (长度: {len(order_id)})")

        try:
            # 使用悲观锁查询订单，防止并发问题
            from sqlmodel import select
            statement = select(Order).where(Order.id == order_id).with_for_update()
            order = session.exec(statement).first()

            if not order:
                payment_logger.error(f"订单不存在: {order_id}")
                return wechat_response("FAIL", "订单不存在")

            # 已经支付的订单不重复处理
            if order.status == Order.Status.PAID:
                payment_logger.info(f"订单 {order_id} 已经支付，不重复处理。当前状态: {order.status}, 支付时间: {order.pay_time}")
                return wechat_response("SUCCESS", "OK")

            # 验证订单金额（微信支付金额单位为分）
            notify_amount = float(data.get("total_fee", 0)) / 100
            if notify_amount != order.amount:
                payment_logger.error(
                    f"订单金额不匹配: 通知金额={notify_amount}, 订单金额={order.amount}"
                )
                return wechat_response("FAIL", "订单金额不匹配")

            # 验证商户ID
            if data.get("mch_id") != settings.wechat_mch_id:
                payment_logger.error(f"商户号不匹配: {data.get('mch_id')}")
                return wechat_response("FAIL", "商户号不匹配")

            # 更新订单状态
            order.status = Order.Status.PAID
            order.updated_at = datetime.now()
            order.pay_time = datetime.now()
            session.add(order)

            # 处理订单支付后的操作
            await process_payment_for_order(order, session)
            session.commit()
            payment_logger.info(f"订单 {order_id} 支付成功")
            return wechat_response("SUCCESS", "OK")

        except Exception as e:
            payment_logger.exception(f"处理订单 {order_id} 失败: {str(e)}")
            session.rollback()
            return wechat_response("FAIL", "处理订单失败")

    except Exception as e:
        payment_logger.exception(f"处理微信支付通知失败: {str(e)}")
        return wechat_response("FAIL", "处理失败")

@router.post("/alipay/verify", summary="验证支付宝支付结果", response_model=ResponsePayloads[dict])
async def verify_alipay_payment(request: Request, session: Session = Depends(get_session)):
    """
    验证支付宝支付结果

    前端接收到支付宝回调后，调用此接口验证支付结果
    """
    try:
        # 获取请求数据
        data = await request.json()
        payment_params = data.get("payment_params", {})

        payment_logger.info(f"前端请求验证支付宝支付结果: {payment_params}")

        # 获取支付宝客户端
        alipay_client = get_alipay_client()
        if not alipay_client:
            payment_logger.error("获取支付宝客户端失败")
            return ResponsePayloads(
                error=Error(
                    type="SystemError",
                    message="系统错误，无法验证支付结果",
                    code=500,
                )
            )

        # 验证签名
        data_dict = {k: v for k, v in payment_params.items() if v and k not in ["sign", "sign_type"]}
        signature = payment_params.get("sign")

        if not signature:
            payment_logger.error("缺少支付签名")
            return ResponsePayloads(
                error=Error(
                    type="ValidationError",
                    message="缺少支付签名",
                    code=400,
                )
            )

        success = alipay_client.verify(data_dict, signature)
        if not success:
            payment_logger.error("支付宝签名验证失败")
            return ResponsePayloads(
                error=Error(
                    type="ValidationError",
                    message="支付验证失败",
                    code=400,
                )
            )

        # 获取订单信息
        order_id = payment_params.get("out_trade_no")
        if not order_id:
            payment_logger.error("缺少订单号")
            return ResponsePayloads(
                error=Error(
                    type="ValidationError",
                    message="缺少订单信息",
                    code=400,
                )
            )

        # 查询订单状态
        try:
            order = await get_order_by_id(order_id, session)
            if not order:
                payment_logger.error(f"订单不存在: {order_id}")
                return ResponsePayloads(
                    error=Error(
                        type="NotFound",
                        message="订单不存在",
                        code=404,
                    )
                )

            # 返回验证结果和订单状态
            return ResponsePayloads(data={
                "verification_success": True,
                "order_id": order_id,
                "order_status": order.status,
                "payment_method": "alipay",
                "amount": order.amount,
                "trade_no": payment_params.get("trade_no"),
                "message": "支付验证成功" if order.status == Order.Status.PAID else "支付验证成功，但订单状态未更新"
            })

        except Exception as e:
            payment_logger.exception(f"查询订单失败: {str(e)}")
            return ResponsePayloads(
                error=Error(
                    type="DatabaseError",
                    message="查询订单失败",
                    code=500,
                )
            )

    except Exception as e:
        payment_logger.exception(f"验证支付结果失败: {str(e)}")
        return ResponsePayloads(
            error=Error(
                type="SystemError",
                message="验证支付结果失败",
                code=500,
            )
        )
