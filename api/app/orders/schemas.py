from typing import List, Optional

from pydantic import BaseModel, Field

from app.db import ProductType
from app.db.models import Order, PaymentPlan


class OrderCreateRequest(BaseModel):
    """创建订单请求"""

    payment_method: Order.PaymentMethod = Field(
        default=Order.PaymentMethod.ALIPAY, description="支付方式，默认支付宝"
    )
    product_type: ProductType = Field(..., description="产品类型")
    product_id: str = Field(..., description="产品ID")
    payment_plan_id: int = Field(..., description="付费计划ID")
    quantity: int = Field(default=1, description="数量")


class OrderResult(BaseModel):
    """订单结果"""

    order_id: str
    payment_url: Optional[str] = None
    payment_params: Optional[dict] = None
    message: str


class PaymentPlanResponse(BaseModel):
    """付费计划响应"""

    id: int = Field(..., description="计划ID")
    name: str = Field(..., description="计划名称")
    description: Optional[str] = Field(None, description="计划描述")
    price: float = Field(..., description="价格")
    original_price: float = Field(..., description="原价")
    validity_period: int = Field(..., description="有效期（天）")
    scope_type: PaymentPlan.ScopeType = Field(..., description="适用范围类型")
    scope_ids: List[str] = Field(..., description="适用范围ID列表")
    is_active: bool = Field(..., description="是否激活")
    created_at: Optional[str] = Field(None, description="创建时间")
    updated_at: Optional[str] = Field(None, description="更新时间")
