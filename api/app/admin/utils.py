from typing import Annotated

import jwt
from app.core import settings
from app.db import get_session
from app.db.models import Admin
from fastapi import Depends, HTTPException
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from loguru import logger
from sqlmodel import Session, select
from starlette import status

# HTTP Bearer 认证方案
security = HTTPBearer()


async def get_current_admin(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    session: Session = Depends(get_session),
) -> Admin:
    """
    从 JWT token 中获取当前管理员
    :param session: 数据库会话
    :param credentials: HTTP Bearer 认证凭据
    :return: 当前管理员对象
    :raises HTTPException: 如果 token 无效或管理员不存在
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # 解码 JWT token
        payload = jwt.decode(
            credentials.credentials,
            settings.jwt_secret_key,
            algorithms=[settings.jwt_algorithm],
        )
        admin_id: str = payload.get("sub")
        if admin_id is None:
            raise credentials_exception
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid token: {str(e)}")
        raise credentials_exception

    # 从数据库中获取管理员
    try:
        statement = select(Admin).where(Admin.id == int(admin_id))
        admin = session.exec(statement).first()
        if admin is None:
            logger.warning(f"Admin not found: {admin_id}")
            raise credentials_exception

        return admin
    except ValueError as e:
        logger.exception(f"Database error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="系统错误"
        )
