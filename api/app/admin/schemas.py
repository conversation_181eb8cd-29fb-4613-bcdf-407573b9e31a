from typing import Optional

from pydantic import BaseModel, Field


class AdminLoginRequest(BaseModel):
    """管理员登录请求"""

    username: str = Field(..., example="admin")
    password: str = Field(..., example="admin123")


class AdminInfo(BaseModel):
    """管理员信息"""

    id: int
    username: str
    avatar_url: Optional[str] = None


class AdminLoginResult(BaseModel):
    """管理员登录结果"""

    token: str
    admin_info: AdminInfo
