from fastapi import APIRouter, HTTPException, Body, Depends
from sqlmodel import select, Session

from app.core import default_password_encoder, create_jwt_token
from app.core.schemas import ResponsePayloads
from app.db import get_session
from app.db.models import Admin
from .schemas import (
    AdminLoginRequest,
    AdminLoginResult,
    AdminInfo,
)
from .utils import get_current_admin

router = APIRouter(prefix="/admin", tags=["管理员"])


@router.post(
    "/login", summary="管理员登录", response_model=ResponsePayloads[AdminLoginResult]
)
async def admin_login(
    data: AdminLoginRequest = Body(...), session: Session = Depends(get_session)
):
    """管理员登录"""
    # 查找管理员
    statement = select(Admin).where(Admin.username == data.username)
    admin = session.exec(statement).first()

    # 管理员不存在或密码错误
    if not admin or not default_password_encoder.matches(data.password, admin.password):
        raise HTTPException(status_code=401, detail="用户名或密码错误")

    # 生成token
    token = create_jwt_token({"sub": str(admin.id)})

    return ResponsePayloads(
        data=AdminLoginResult(
            token=token,
            admin_info=AdminInfo(
                id=admin.id,
                username=admin.username,
                avatar_url=admin.avatar_url,
            ),
        )
    )


@router.get(
    "/me", summary="获取当前管理员信息", response_model=ResponsePayloads[AdminInfo]
)
async def get_admin_info(current_admin: Admin = Depends(get_current_admin)):
    """获取当前管理员信息"""
    return ResponsePayloads(
        data=AdminInfo(
            id=current_admin.id,
            username=current_admin.username,
            avatar_url=current_admin.avatar_url,
        )
    )
