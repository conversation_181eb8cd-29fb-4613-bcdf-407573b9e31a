from typing import List, Literal, TypeVar

from pydantic import BaseModel, Field

from app.db import Agent

T = TypeVar("T")
AgentType = Agent.Type


class AppImportStatus(BaseModel):
    """应用导入状态"""

    id: str = Field(..., description="应用ID")
    status: Literal["imported", "already_exists"] = Field(..., description="导入状态")


class ImportAppsResponse(BaseModel):
    """导入应用响应"""

    imported_apps: List[AppImportStatus] = Field(..., description="已导入应用列表")
