import asyncio
import os
import uuid
from typing import Dict, List, Optional

import aiofiles
from billing import HkJingXiuBilling
from dify.app import AppMode, AppParameters, ChatPayloads, RunWorkflowPayloads
from dify.app.conversation import (
    Conversation,
    ConversationList,
    ConversationListQueryPayloads,
    ConversationRenamePayloads,
    MessageFeedbackPayloads,
    MessageList,
    MessageListQueryPayloads,
)
from dify.app.schemas import MessageEndEvent, OperationResult
from dify.file import FileUploadResponse
from dify.llm import LLMList, ModelParameterRuleList
from dify.tool.schemas import ToolProvider, ToolType
from fastapi import APIRouter, Depends, File, HTTPException, Path, Query, UploadFile
from fastapi.responses import StreamingResponse
from loguru import logger
from sqlalchemy import and_, or_
from sqlmodel import Session, select

from app.core import Pagination, ResponsePayloads, chat_logger, settings
from app.core import redis_client
from app.core.exceptions import (
    <PERSON><PERSON><PERSON><PERSON>ailedException,
    AgentPermissionDeniedException,
    AgentUpdateFailedException,
)
from app.db import Agent, get_session
from app.db.models import User
from app.users.api import get_current_user
from .schemas import (
    AgentBase,
    AgentDetail,
    AgentQuery,
    SaveOrUpdateAgentPayloads,
    AgentSimple,
    AgentsByTag,
    AgentStats,
    RatingDistribution,
    ShareToFeishuRequest,
    ShareToFeishuResponse,
)
from ..crud import find_by_id
from ..utils import (
    consume_key,
    dify,
    transcribe_audio,
    require_chat_permission,
    get_agent_features,
    check_user_has_purchased_agent,
    FeishuDocument,
)
from ...users.api.utils import (
    require_create_agent_privilege,
)

AgentType = Agent.Type
router = APIRouter(prefix="/agents", tags=["智能体"])


@router.get(
    "/by_tags",
    summary="获取按标签分组的智能体列表",
    response_model=ResponsePayloads[AgentsByTag],
    description="获取按标签分组的智能体列表，返回格式为 {标签1: [智能体1, 智能体2], 标签2: [智能体3, 智能体4]}",
)
async def get_agents_by_tags(
    types: List[str] = Query(None, description="智能体类型列表，用于过滤特定类型的智能体"),
    current_user: User = Depends(get_current_user),
    session: Session = Depends(get_session),
):
    """获取按标签分组的智能体列表

    特殊规则：
    1. "精选推荐"标签默认返回4个智能体
    2. 其它标签默认返回6个智能体
    3. 没有标签的智能体归类到"其他"标签中

    性能优化：
    1. 使用缓存减少数据库查询
    2. 针对每个标签单独查询，限制数量
    3. 使用数据库排序而非内存排序
    """
    # 构建基础查询条件
    base_conditions = [or_(Agent.is_public == True, Agent.owner_id.is_(None))]

    # 如果指定了类型，添加类型过滤条件
    if types:
        # 将字符串类型转换为Agent.Type枚举
        agent_types = []
        for type_str in types:
            try:
                agent_types.append(AgentType(type_str.lower()))
            except ValueError:
                # 忽略无效的类型
                continue
        if agent_types:
            base_conditions.append(Agent.type.in_(agent_types))

    # 获取所有标签
    all_tags_query = select(Agent.tags).where(and_(*base_conditions))
    all_tags_result = session.exec(all_tags_query).all()

    # 提取所有唯一标签
    unique_tags = set()
    for tags_list in all_tags_result:
        # 确保 tags_list 不为 None 且不为空数组
        if tags_list and len(tags_list) > 0:
            unique_tags.update(tags_list)

    result: Dict[str, List[AgentSimple]] = {}

    # 处理"精选推荐"标签 - 限制为4个
    if "精选推荐" in unique_tags:
        recommended_conditions = base_conditions + [Agent.tags.contains(["精选推荐"])]
        recommended_query = (
            select(Agent)
            .where(and_(*recommended_conditions))
            .order_by(Agent.sort_order.asc())
            .limit(4)
        )

        recommended_agents = session.exec(recommended_query).all()
        result["精选推荐"] = [
            AgentSimple.from_agent(agent) for agent in recommended_agents
        ]

        # 从唯一标签中移除已处理的标签
        unique_tags.remove("精选推荐")

    # 处理其他标签 - 每个限制为6个
    for tag in unique_tags:
        tag_conditions = base_conditions + [Agent.tags.contains([tag])]
        tag_query = (
            select(Agent)
            .where(and_(*tag_conditions))
            .order_by(Agent.sort_order.asc())
            .limit(6)
        )

        tag_agents = session.exec(tag_query).all()
        if tag_agents:
            result[tag] = [AgentSimple.from_agent(agent) for agent in tag_agents]

    # 处理无标签智能体 - 归类到"其他"标签，限制为6个
    # 包括 tags 为 None 或空数组 [] 的情况
    no_tag_conditions = base_conditions + [or_(Agent.tags == None, Agent.tags == [])]
    no_tag_query = (
        select(Agent)
        .where(and_(*no_tag_conditions))
        .order_by(Agent.sort_order.asc())
        .limit(6)
    )

    no_tag_agents = session.exec(no_tag_query).all()
    if no_tag_agents:
        if "其他" not in result:
            result["其他"] = []
        result["其他"].extend(
            [AgentSimple.from_agent(agent) for agent in no_tag_agents]
        )

    # 创建结果对象
    agents_by_tag_result = AgentsByTag(data=result)
    return ResponsePayloads(data=agents_by_tag_result)


@router.get(
    "/search",
    summary="搜索应用",
    response_model=ResponsePayloads[Pagination[AgentBase]],
)
async def search_agents(
        query: AgentQuery = Depends(),
        tags: List[str] = Query(None, description="标签列表，为空时返回所有应用"),
        exclude_ids: List[str] = Query(None, description="排除的智能体ID列表"),
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """搜索智能体"""
    # 构建基础查询
    statement = select(Agent)

    # 构建基础可见性条件
    base_visibility_condition = None

    # 如果指定了创建者ID，添加创建者过滤条件
    if query.owner_id:
        # 如果查询的是当前用户的智能体，可以查看所有（包括私有的）
        if query.owner_id == current_user.id:
            base_visibility_condition = Agent.owner_id == query.owner_id
        else:
            # 如果查询的是其他用户的智能体，只能查看公开的
            base_visibility_condition = and_(
                Agent.owner_id == query.owner_id,
                Agent.is_public == True
            )
    else:
        # 未指定创建者ID时，查询公开的智能体或者平台智能体（owner_id为None）
        base_visibility_condition = or_(Agent.is_public == True, Agent.owner_id.is_(None))

    # 应用基础可见性条件
    statement = statement.where(base_visibility_condition)

    # 如果指定了标签，添加标签过滤条件
    if tags:
        # 检查是否包含"其他"标签
        if "其他" in tags:
            # 如果包含"其他"，需要特殊处理
            other_tags = [tag for tag in tags if tag != "其他"]

            if other_tags:
                # 如果除了"其他"还有其他标签，搜索包含这些标签的智能体 OR 无标签的智能体
                statement = statement.where(
                    or_(
                        Agent.tags.contains(other_tags),
                        Agent.tags == None,
                        Agent.tags == []
                    )
                )
            else:
                # 如果只有"其他"标签，只搜索无标签的智能体
                statement = statement.where(
                    or_(Agent.tags == None, Agent.tags == [])
                )
        else:
            # 普通标签搜索，PostgreSQL的 @> 操作符用于检查数组是否包含另一个数组
            statement = statement.where(Agent.tags.contains(tags))

    # 如果指定了排除ID列表，添加排除条件
    if exclude_ids or query.exclude_ids:
        # 合并两个排除ID列表
        all_exclude_ids = set(exclude_ids or []) | set(query.exclude_ids or [])
        if all_exclude_ids:
            statement = statement.where(Agent.id.not_in(list(all_exclude_ids)))

    # 如果指定了模式，添加模式过滤条件
    if query.mode:
        if query.mode == AgentType.CHAT:
            statement = statement.where(
                Agent.type.in_([AgentType.CHAT, AgentType.AGENT_CHAT])
            )
        elif query.mode == AgentType.WORKFLOW:
            statement = statement.where(
                Agent.type.in_([AgentType.WORKFLOW, AgentType.COMPLETION])
            )
        else:
            statement = statement.where(Agent.type == query.mode)

    # 如果指定了关键词，添加关键词搜索条件（忽略大小写）
    if query.keyword:
        # 使用 ilike 进行大小写不敏感的搜索
        keyword_pattern = f"%{query.keyword}%"
        statement = statement.where(
            or_(
                Agent.name.ilike(keyword_pattern),
                Agent.description.ilike(keyword_pattern),
            )
        )
    # 添加排序
    statement = statement.order_by(Agent.sort_order.asc())
    # 计算总数
    total = len(session.exec(statement).all())

    # 添加分页
    statement = statement.offset((query.page - 1) * query.page_size).limit(
        query.page_size
    )

    # 执行查询
    apps = session.exec(statement).all()
    # 构造分页响应
    return ResponsePayloads(
        data=Pagination(
            data=[AgentBase.from_agent(app) for app in apps],
            total=total,
            has_more=(query.page * query.page_size) < total,
        )
    )


@router.get(
    "/detail", summary="获取智能体详情", response_model=ResponsePayloads[AgentDetail]
)
async def get_agent_details(
        agent_id: str,
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """根据ID获取智能体详情"""
    # 缓存未命中，从数据库获取
    local_agent = await find_by_id(session, agent_id)
    if not local_agent:
        raise HTTPException(status_code=404, detail="智能体不存在")
    dify_app = await dify.app.find_by_id(local_agent.id)
    agent_detail = AgentDetail(**dify_app.model_dump())

    # 将字符串标签转换为Tag对象
    from dify.app.schemas import Tag

    if local_agent.tags:
        agent_detail.tags = [
            Tag(id=tag, name=tag, type="app") for tag in local_agent.tags
        ]
    else:
        agent_detail.tags = []

    # 设置本地数据库中的字段
    agent_detail.is_public = local_agent.is_public
    agent_detail.owner_id = local_agent.owner_id

    agent_detail.description = local_agent.description
    agent_detail.icon_url = local_agent.icon_url
    agent_detail.icon = local_agent.icon_url

    # 设置创建者信息
    if local_agent.owner_id:
        # 查询用户信息
        user = session.exec(select(User).where(User.id == local_agent.owner_id)).first()
        if user:
            agent_detail.created_by = user.username
        else:
            agent_detail.created_by = "华坤智元"
        # 设置创建者ID
        agent_detail.owner_id = local_agent.owner_id
    else:
        agent_detail.created_by = "华坤智元"
        agent_detail.owner_id = None

    # 生成模拟的统计数据 - 使用随机数
    import random

    # 生成随机评分 (3.5-4.9之间)
    rating = round(random.uniform(3.5, 4.9), 1)

    # 生成随机的评级分布数据 (总和为100%)
    five_star = round(random.uniform(50, 75), 2)
    four_star = round(random.uniform(10, 25), 2)
    three_star = round(random.uniform(5, 15), 2)
    two_star = round(random.uniform(2, 8), 2)

    # 计算一星评分，确保非负
    one_star = max(0, round(100 - five_star - four_star - three_star - two_star, 2))

    # 如果总和不等于100%，调整五星评分使总和为100%
    total = five_star + four_star + three_star + two_star + one_star
    if total != 100:
        five_star = round(five_star + (100 - total), 2)

    rating_distribution = [
        RatingDistribution(stars=5, percentage=five_star),
        RatingDistribution(stars=4, percentage=four_star),
        RatingDistribution(stars=3, percentage=three_star),
        RatingDistribution(stars=2, percentage=two_star),
        RatingDistribution(stars=1, percentage=one_star),
    ]

    # 随机生成评级数量
    rating_counts = ["10K+", "25K+", "50K+", "100K+", "250K+", "500K+"]
    rating_count = random.choice(rating_counts)

    # 随机生成排名
    rank = f"#{random.randint(1, 50)}"

    # 随机生成对话数量
    conversation_counts = ["500K+", "1M+", "2M+", "3M+", "5M+", "10M+"]
    conversation_count = random.choice(conversation_counts)

    # 获取智能体的第一个标签作为类别，如果没有标签则使用"其他"
    category = (
        local_agent.tags[0]
        if local_agent.tags and len(local_agent.tags) > 0
        else "其他"
    )

    # 创建统计信息
    agent_detail.stats = AgentStats(
        rating=rating,
        rating_count=rating_count,
        rank=rank,
        category=category,
        conversation_count=conversation_count,
        rating_distribution=rating_distribution,
    )

    # 获取智能体支持的功能列表
    agent_detail.features = get_agent_features(agent_detail)

    # 检查用户是否已购买该智能体
    agent_detail.has_purchased = await check_user_has_purchased_agent(
        current_user, local_agent, session
    )

    return ResponsePayloads(data=agent_detail)


@router.post("/chat/{agent_id}", summary="与智能体聊天")
@require_chat_permission()
async def chat(
        agent_id: str,
        payloads: ChatPayloads,
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """与智能体进行聊天，使用SSE返回响应"""
    # FIXME 数字人智能体用独立页面提供服务之后这个参数就不需要了
    payloads.inputs["user_id"] = f"{current_user.id}-{agent_id}"
    payloads.inputs["project_id"] = settings.project_id

    async def event_stream():
        app = await find_by_id(session, agent_id)
        async for event in dify.app.chat(app.api_key, payloads):
            # 检查是否为消息结束事件，并上报token用量
            if isinstance(event, MessageEndEvent):
                asyncio.create_task(consume_key(current_user.id, agent_id, event))
            yield f"data: {event.model_dump_json()}\n\n"

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        },
    )


@router.post("/completion/{app_id}", summary="文本补全交互")
@require_chat_permission()
async def completion(
        app_id: str,
        payloads: RunWorkflowPayloads,
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """文本生成型应用交互，使用SSE返回响应"""

    async def event_stream():
        app = await find_by_id(session, app_id)
        async for event in dify.app.completion(app.api_key, payloads):
            # 检查是否为消息结束事件，并上报token用量
            if isinstance(event, MessageEndEvent):
                asyncio.create_task(consume_key(current_user.id, app_id, event))
            yield f"data: {event.model_dump_json(ensure_ascii=False)}\n\n"

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        },
    )


@router.post("/run/{agent_id}", summary="运行工作流")
@require_chat_permission()
async def run(
        agent_id: str,
        payloads: RunWorkflowPayloads,
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """运行工作流，使用SSE返回响应"""

    async def event_stream():
        app = await find_by_id(session, agent_id)
        async for event in dify.app.run(app.api_key, payloads):
            # 检查是否为消息结束事件，并上报token用量
            if isinstance(event, MessageEndEvent):
                asyncio.create_task(consume_key(current_user.id, agent_id, event))
            yield f"data: {event.model_dump_json()}\n\n"

    return StreamingResponse(
        event_stream(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no",
        },
    )


@router.delete("/delete/{agent_id}", summary="删除智能体")
async def delete(
        agent_id: str,
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """删除智能体"""
    local_agent = await find_by_id(session, agent_id)
    if not local_agent:
        raise HTTPException(status_code=404, detail="智能体不存在")
    await dify.app.delete(local_agent.id)
    session.delete(local_agent)
    session.commit()

    # 删除缓存
    try:
        cache_hash_key = "agent_details"
        redis_client.hdel(cache_hash_key, agent_id)
        logger.debug(f"删除智能体详情缓存: {agent_id}")
    except Exception as e:
        logger.error(f"删除智能体详情缓存失败: {str(e)}")

    # 创建billing客户端
    billing_client = HkJingXiuBilling(
        base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
    )

    # 获取用户所有密钥
    keys_response = await billing_client.keys.list_available_keys(user_id=str(current_user.id), service_code="AGC")
    # 从密钥中删除该智能体
    for key in keys_response.keys:
        if key.scope and agent_id in key.scope:
            key.scope.remove(agent_id)
            await billing_client.keys.update_key(key)

    return ResponsePayloads(data=OperationResult(result="success"))


@router.get(
    "/conversations",
    summary="获取应用对话列表",
    response_model=ResponsePayloads[ConversationList],
)
async def get_app_conversations(
        app_id: str,
        last_id: Optional[str] = Query(None, description="分页游标，最后一条记录ID"),
        limit: int = Query(20, ge=1, le=100, description="每页数量"),
        sort_by: str = Query("-updated_at", description="排序字段"),
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """获取指定应用的对话列表"""
    # 获取本地应用记录
    app = session.exec(select(Agent).where(Agent.id == app_id)).first()
    if not app or not app.id:
        raise HTTPException(status_code=404, detail="应用不存在")
    if not app.api_key:
        raise HTTPException(status_code=404, detail="应用未配置API密钥")

    # 获取对话列表

    result = await dify.app.conversation.find_list(
        app.api_key,
        ConversationListQueryPayloads(
            user=str(current_user.id),
            last_id=last_id,
            limit=limit,
            sort_by=sort_by,
        ),
    )

    return ResponsePayloads(data=result)


@router.get(
    "/messages",
    summary="获取会话历史消息",
    response_model=ResponsePayloads[MessageList],
)
async def get_conversation_messages(
        app_id: str = Query(..., description="应用ID"),
        conversation_id: str = Query(..., description="会话ID"),
        first_id: Optional[str] = Query(None, description="分页游标，第一条消息ID"),
        limit: int = Query(20, ge=1, le=100, description="每页数量"),
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """获取指定会话的历史消息"""
    # 获取本地应用记录
    app = session.exec(select(Agent).where(Agent.id == app_id)).first()
    if not app or not app.id:
        raise HTTPException(status_code=404, detail="关联应用不存在")
    if not app.api_key:
        raise HTTPException(status_code=404, detail="应用未配置API密钥")

    # 获取消息列表
    result = await dify.app.conversation.get_messages(
        app.api_key,
        MessageListQueryPayloads(
            user=str(current_user.id),
            conversation_id=conversation_id,
            first_id=first_id,
            limit=limit,
        ),
    )
    # result.data = result.data[::-1]  # 将消息列表顺序反转，最新的消息在前
    result.data = sorted(result.data, key=lambda x: x.created_at if x.created_at else 0)
    # for message in result.data:
    #     if message.message_files:
    #         for file in message.message_files:
    #             # file  = await process_message_file_url(file )
    #             file.url = settings.dify_url + file.url
    return ResponsePayloads(data=result)


@router.delete(
    "/conversation",
    summary="删除会话",
    response_model=ResponsePayloads[OperationResult],
)
async def delete_conversation(
        conversation_id: str,
        app_id: str = Query(..., description="应用ID"),
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """删除指定会话"""
    # 获取本地应用记录
    app: Agent = session.exec(select(Agent).where(Agent.id == app_id)).first()
    if not app or not app.id:
        raise HTTPException(status_code=404, detail="应用不存在")
    if not app.api_key:
        raise HTTPException(status_code=404, detail="应用未配置API密钥")

    # 执行删除操作
    result = await dify.app.conversation.delete(
        app.api_key, conversation_id, current_user.id
    )
    return ResponsePayloads(data=result)


@router.put(
    "/conversation/rename",
    summary="重命名会话",
    response_model=ResponsePayloads[Conversation],
)
async def rename_conversation(
        conversation_id: str,
        new_name: str,
        app_id: str = Query(..., description="应用ID"),
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """重命名指定会话"""
    # 获取本地应用记录
    app = session.exec(select(Agent).where(Agent.id == app_id)).first()
    if not app or not app.id:
        raise HTTPException(status_code=404, detail="应用不存在")
    if not app.api_key:
        raise HTTPException(status_code=404, detail="应用未配置API密钥")

    # 执行重命名操作
    result = await dify.app.conversation.rename(
        app.api_key,
        conversation_id,
        ConversationRenamePayloads(
            name=new_name, auto_generate=False, user=str(current_user.id)
        ),
    )

    return ResponsePayloads(data=result)


@router.post(
    "/messages/feedback",
    summary="提交消息反馈",
    response_model=ResponsePayloads[OperationResult],
)
async def message_feedback(
        message_id: str,
        feedback: MessageFeedbackPayloads,
        app_id: str = Query(..., description="应用ID"),
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """提交消息反馈（点赞/点踩）"""
    # 获取本地应用记录
    app = session.exec(select(Agent).where(Agent.id == app_id)).first()
    if not app or not app.id:
        raise HTTPException(status_code=404, detail="应用不存在")
    if not app.api_key:
        raise HTTPException(status_code=404, detail="应用未配置API密钥")

    # 设置用户标识
    feedback.user = str(current_user.id)

    # 提交反馈
    result = await dify.app.conversation.submit_feedback(
        app.api_key, message_id, feedback
    )
    return ResponsePayloads(data=result)


@router.post(
    "/tasks/stop",
    summary="停止消息响应",
    response_model=ResponsePayloads[OperationResult],
)
async def stop_chat_task(
        task_id: str,
        app_id: str = Query(..., description="应用ID"),
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """停止正在进行的消息响应"""
    # 获取本地应用记录
    app = session.exec(select(Agent).where(Agent.id == app_id)).first()
    if not app or not app.id:
        raise HTTPException(status_code=404, detail="应用不存在")
    if not app.api_key:
        raise HTTPException(status_code=404, detail="应用未配置API密钥")

    # 执行停止操作
    result = await dify.app.conversation.stop_message(
        app.api_key, task_id, str(current_user.id)
    )
    return ResponsePayloads(data=result)


@router.get(
    "/parameters",
    summary="获取应用参数配置",
    response_model=ResponsePayloads[AppParameters],
)
async def get_app_parameters(
        app_id: str = Query(..., description="应用ID"),
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """获取应用的功能开关、输入参数配置等信息"""
    # 获取本地应用记录
    app = session.exec(select(Agent).where(Agent.id == app_id)).first()
    if not app or not app.id:
        raise HTTPException(status_code=404, detail="应用不存在")
    if not app.api_key:
        raise HTTPException(status_code=404, detail="应用未配置API密钥")

    try:
        # 调用工具函数获取参数
        params = await dify.app.get_parameters(app.api_key)
        return ResponsePayloads(data=params)
    except Exception as e:
        logger.error(f"获取应用参数失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取应用参数失败")


@router.get(
    "/workflow/parameters/{agent_id}",
    summary="获取工作流参数配置",
    response_model=ResponsePayloads[dict],
    description="获取工作流的参数配置信息",
)
async def get_workflow_parameters(
        agent_id: str = Path(..., description="工作流ID"),
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """获取工作流的参数配置信息"""
    # 获取本地应用记录
    app = session.exec(select(Agent).where(Agent.id == agent_id)).first()
    if not app or not app.id:
        raise HTTPException(status_code=404, detail="工作流不存在")
    if not app.api_key:
        raise HTTPException(status_code=404, detail="工作流未配置API密钥")

    try:
        # 调用工具函数获取参数
        params = await dify.app.get_parameters(app.api_key)
        workflow_publish = await dify.app.workflow.get_publish(app.id)
        return ResponsePayloads(
            data=dict(
                parameters=params,
                workflow_publish=workflow_publish,
            )
        )
    except Exception as e:
        logger.error(f"获取工作流参数失败: {str(e)}")
        raise HTTPException(status_code=500, detail="获取工作流参数失败")


@router.get(
    "/check_chat_permission/{agent_id}",
    summary="检查聊天权限",
    response_model=ResponsePayloads[bool],
)
@require_chat_permission()
async def check_chat_permission(
        agent_id: str,
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """检查用户是否有权限与指定智能体聊天

    此接口会检查用户是否在商户平台上拥有该智能体的使用权限。
    通过查询用户资产表来确认用户是否有权限使用该智能体。

    返回:
        bool: 如果用户有权限，返回True；否则会抛出403错误
    """
    # 如果代码执行到这里，说明装饰器已经验证了用户有权限
    # 装饰器会在用户没有权限时抛出异常，所以这里直接返回True
    return ResponsePayloads(data=True)


@router.post(
    "/save",
    summary="创建或更新智能体",
    response_model=ResponsePayloads[AgentDetail],
    description="如果payload中的ID不存在则创建，存在则更新",
)
@require_create_agent_privilege()
async def save_agent(
        payloads: SaveOrUpdateAgentPayloads,
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """创建或更新智能体接口"""

    # 如果提供了ID，尝试更新
    if payloads.id:
        # 查找现有智能体
        local_agent = await find_by_id(session, payloads.id)
        if local_agent:
            # 检查权限（只有拥有者可以更新）
            if local_agent.owner_id != current_user.id:
                raise AgentPermissionDeniedException(payloads.id)

            # 更新Dify应用配置
            result = await dify.app.update_model_config(local_agent.id, payloads.config)

            if result.result == "success":
                # 更新本地数据库中的智能体信息
                local_agent.name = payloads.name
                local_agent.description = payloads.description
                local_agent.icon_url = payloads.icon_url
                local_agent.tags = payloads.tags
                local_agent.is_public = payloads.is_public
                session.commit()
                session.refresh(local_agent)

                # 获取更新后的应用详情
                dify_app = await dify.app.find_by_id(local_agent.id)
                agent_detail = AgentDetail(**dify_app.model_dump())

                # 将字符串标签转换为Tag对象
                from dify.app.schemas import Tag

                if local_agent.tags:
                    agent_detail.tags = [
                        Tag(id=tag, name=tag, type="app") for tag in local_agent.tags
                    ]
                else:
                    agent_detail.tags = []

                # 设置本地数据库中的字段
                agent_detail.is_public = local_agent.is_public
                agent_detail.owner_id = local_agent.owner_id

                agent_detail.description = local_agent.description
                agent_detail.icon_url = local_agent.icon_url

                # 生成模拟的统计数据 - 使用随机数
                import random

                # 生成随机评分 (3.5-4.9之间)
                rating = round(random.uniform(3.5, 4.9), 1)

                # 生成随机的评级分布数据 (总和为100%)
                five_star = round(random.uniform(50, 75), 2)
                four_star = round(random.uniform(10, 25), 2)
                three_star = round(random.uniform(5, 15), 2)
                two_star = round(random.uniform(2, 8), 2)

                # 计算一星评分，确保非负
                one_star = max(
                    0, round(100 - five_star - four_star - three_star - two_star, 2)
                )

                # 如果总和不等于100%，调整五星评分使总和为100%
                total = five_star + four_star + three_star + two_star + one_star
                if total != 100:
                    five_star = round(five_star + (100 - total), 2)

                rating_distribution = [
                    RatingDistribution(stars=5, percentage=five_star),
                    RatingDistribution(stars=4, percentage=four_star),
                    RatingDistribution(stars=3, percentage=three_star),
                    RatingDistribution(stars=2, percentage=two_star),
                    RatingDistribution(stars=1, percentage=one_star),
                ]

                # 随机生成评级数量
                rating_counts = ["10K+", "25K+", "50K+", "100K+", "250K+", "500K+"]
                rating_count = random.choice(rating_counts)

                # 随机生成排名
                rank = f"#{random.randint(1, 50)}"

                # 随机生成对话数量
                conversation_counts = ["500K+", "1M+", "2M+", "3M+", "5M+", "10M+"]
                conversation_count = random.choice(conversation_counts)

                # 获取智能体的第一个标签作为类别，如果没有标签则使用"其他"
                category = (
                    local_agent.tags[0]
                    if local_agent.tags and len(local_agent.tags) > 0
                    else "其他"
                )

                # 创建统计信息
                agent_detail.stats = AgentStats(
                    rating=rating,
                    rating_count=rating_count,
                    rank=rank,
                    category=category,
                    conversation_count=conversation_count,
                    rating_distribution=rating_distribution,
                )
                return ResponsePayloads(data=agent_detail)
            else:
                raise AgentUpdateFailedException()

    # 如果没有提供agent_id或智能体不存在，创建新的智能体
    dify_app = await dify.app.create(payloads.name, AppMode.AGENT_CHAT)
    result = await dify.app.update_model_config(dify_app.id, payloads.config)
    api_key = await dify.app.create_api_key(dify_app.id)
    if result.result == "success":
        local_agent = Agent(
            id=dify_app.id,
            name=payloads.name,
            description=payloads.description,
            icon_url=payloads.icon_url,
            type=AgentType.AGENT_CHAT,
            tags=payloads.tags,
            owner_id=current_user.id,
            api_key=api_key.token,
            is_public=payloads.is_public,
        )
        session.add(local_agent)
        session.commit()
        session.refresh(local_agent)
        dify_app = await dify.app.find_by_id(local_agent.id)
        agent_detail = AgentDetail(**dify_app.model_dump())

        # 设置本地数据库中的字段
        agent_detail.is_public = local_agent.is_public
        agent_detail.owner_id = local_agent.owner_id

        # 创建billing客户端
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )

        # 获取用户所有密钥
        keys_response = await billing_client.keys.list_available_keys(user_id=str(current_user.id), service_code="AGC")
        # 添加新的智能体到密钥中
        for key in keys_response.keys:
            if key.scope and local_agent.id not in key.scope:
                key.scope.append(local_agent.id)
                await billing_client.keys.update_key(key)

        return ResponsePayloads(data=agent_detail)
    else:
        raise AgentCreateFailedException()


@router.get(
    "/llms",
    summary="获取LLM列表",
    response_model=ResponsePayloads[LLMList],
)
async def llm_list(current_user: User = Depends(get_current_user)):
    """获取LLM列表"""
    llms = await dify.llm.find_list()
    return ResponsePayloads(data=llms)


@router.post(
    "/transcribe",
    summary="语音转文本",
    response_model=ResponsePayloads[str],
    description="将上传的音频文件转换为文本",
)
async def audio_to_text(
        audio_file: UploadFile = File(..., description="音频文件（支持MP3、WAV等格式）"),
        current_user: User = Depends(get_current_user),
):
    """语音转文本接口"""
    try:
        # 校验文件类型
        if not audio_file.filename.lower().endswith((".mp3", ".wav", ".ogg")):
            raise HTTPException(status_code=400, detail="仅支持MP3/WAV/OGG格式")

        # 创建临时文件
        temp_dir = "tmp_audio"
        os.makedirs(temp_dir, exist_ok=True)
        file_path = os.path.join(temp_dir, f"{uuid.uuid4()}_{audio_file.filename}")

        # 保存上传文件
        async with aiofiles.open(file_path, "wb") as f:
            await f.write(await audio_file.read())

        # 调用转写服务
        text = await transcribe_audio(file_path)

        # 清理临时文件
        try:
            os.remove(file_path)
        except Exception as e:
            chat_logger.warning(f"删除临时文件失败: {str(e)}")

        return ResponsePayloads(data=text)

    except HTTPException as he:
        raise he
    except Exception as e:
        chat_logger.exception(f"语音转文本失败: {str(e)}")
        raise HTTPException(status_code=500, detail="语音转文本处理失败")


@router.post(
    "/upload_file",
    summary="上传文件",
    response_model=ResponsePayloads[FileUploadResponse],
    description="上传文件到Dify平台",
)
async def upload_file(
        file: UploadFile = File(..., description="要上传的文件"),
        current_user: User = Depends(get_current_user),
):
    """上传文件到Dify平台接口"""
    try:
        # 记录接收到的文件信息
        chat_logger.info(
            f"接收到文件: {file.filename}, 大小: {file.size}, 类型: {file.content_type}"
        )

        # 创建临时目录
        temp_dir = "tmp_files"
        os.makedirs(temp_dir, exist_ok=True)
        temp_file_path = os.path.join(temp_dir, file.filename)

        # 保存上传的文件内容到临时文件
        async with aiofiles.open(temp_file_path, "wb") as f:
            await f.write(await file.read())

        # 使用临时文件路径上传到Dify
        dify_file: FileUploadResponse = await dify.file.upload(file_path=temp_file_path)

        # 删除临时文件
        try:
            os.remove(temp_file_path)
        except Exception as e:
            chat_logger.warning(f"删除临时文件失败: {str(e)}")

        return ResponsePayloads(data=dify_file)
    except Exception as e:
        chat_logger.exception(f"上传文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"上传文件失败: {str(e)}")


@router.get(
    "/owned",
    summary="获取用户拥有的智能体列表",
    response_model=ResponsePayloads[List[AgentBase]],
    description="获取当前用户通过密钥拥有权限的智能体列表",
)
async def get_owned_agents(
        current_user: User = Depends(get_current_user),
        session: Session = Depends(get_session),
):
    """获取用户拥有的智能体列表

    此接口通过查询用户在商户平台上拥有的密钥，获取用户有权限使用的智能体列表。

    返回:
        List[AgentBase]: 用户拥有的智能体列表
    """
    try:
        # 创建billing客户端
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )

        # 获取用户所有可用的智能体密钥
        keys = await billing_client.keys.list_available_keys(
            user_id=str(current_user.id),
            service_code="AGC",
        )

        # 提取所有密钥的scope中的智能体ID
        agent_ids = set()
        for key in keys.keys:
            if key.scope:
                agent_ids.update(key.scope)

        agents = []
        # 如果没有找到任何智能体ID，返回所有公共智能体和自己创建的智能体
        if not agent_ids:
            statement = select(Agent).where(
                or_(Agent.is_public == True, Agent.owner_id.is_(None), Agent.owner_id == current_user.id)
            )
            agents = session.exec(statement).all()
        else:
            # 查询这些智能体的详细信息
            for agent_id in agent_ids:
                agent = session.exec(select(Agent).where(Agent.id == agent_id)).first()
                if agent:
                    agents.append(AgentBase.from_agent(agent))

        return ResponsePayloads(data=agents)
    except Exception as e:
        logger.exception(f"获取用户拥有的智能体失败: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"获取用户拥有的智能体失败: {str(e)}"
        )


@router.get(
    "/model-parameter-rules",
    summary="获取模型参数规则",
    response_model=ResponsePayloads[ModelParameterRuleList],
    description="获取指定模型的参数规则配置",
)
async def get_model_parameter_rules(
        provider: str = Query(..., description="模型提供者，例如 'langgenius'"),
        model: str = Query(..., description="模型名称，例如 'openai/gpt-4.1'"),
        current_user: User = Depends(get_current_user),
):
    """获取模型参数规则"""
    # 调用 Dify SDK 获取模型参数规则
    parameter_rules = await dify.llm.get_model_parameter_rules(
        provider=provider, model=model
    )
    return ResponsePayloads(data=parameter_rules)


@router.get(
    "/tools",
    summary="获取工具列表",
    response_model=ResponsePayloads[List[ToolProvider]],
    description="获取工具列表，支持按类型过滤",
)
async def get_tools_list(
        tool_type: str = Query("builtin", description="工具类型：builtin(内置)、api(API)、workflow(工作流)、all(所有类型)"),
        current_user: User = Depends(get_current_user),
):
    """获取工具列表"""
    # 获取并合并工具列表
    if tool_type == "all":
        # 如果指定为 'all'，返回所有类型的工具
        tool_types = [ToolType.API, ToolType.BUILTIN, ToolType.WORKFLOW]
    else:
        tool_type_enum = ToolType(tool_type)
        tool_types = [tool_type_enum]

    all_tools = []
    for t in tool_types:
        tools = await dify.tool.list_tools(t)
        all_tools.extend(tools)

    # 返回合并后的工具列表
    return ResponsePayloads(data=all_tools)


@router.post(
    "/share/feishu",
    summary="分享到飞书文档",
    response_model=ResponsePayloads[ShareToFeishuResponse],
    description="将聊天内容分享到飞书云文档",
)
async def share_to_feishu(
        request: ShareToFeishuRequest,
        current_user: User = Depends(get_current_user),
):
    """分享聊天内容到飞书云文档"""
    try:
        # 从环境变量或配置中获取飞书应用凭证
        app_id = settings.feishu_app_id
        app_secret = settings.feishu_app_secret

        if not app_id or not app_secret or app_id == '' or app_secret == '':
            raise HTTPException(
                status_code=500,
                detail="飞书应用配置未设置，请联系管理员配置FEISHU_APP_ID和FEISHU_APP_SECRET"
            )

        # 创建飞书文档客户端
        feishu_client = FeishuDocument(app_id=app_id, app_secret=app_secret)

        # 创建飞书文档
        document_info = await feishu_client.create_document(
            title=request.title,
            content=request.content
        )

        # 返回文档信息
        return ResponsePayloads(
            data=ShareToFeishuResponse(
                document_id=document_info["document_id"],
                url=document_info["url"],
                title=document_info["title"]
            )
        )

    except Exception as e:
        chat_logger.exception(f"分享到飞书失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"分享到飞书失败: {str(e)}")
