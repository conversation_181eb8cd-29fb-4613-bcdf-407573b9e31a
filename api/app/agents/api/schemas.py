from typing import List, TypeVar, Optional, Dict

from dify.app.schemas import App, ModelConfig
from dify.app.workflow.schemas import WorkflowPublish
from pydantic import BaseModel, Field

from app.db import Agent

T = TypeVar("T")
AgentType = Agent.Type


class RatingDistribution(BaseModel):
    """智能体评级分布项"""

    stars: int = Field(..., description="星级数量", ge=1, le=5)
    percentage: float = Field(..., description="该星级的百分比", ge=0, le=100)


class AgentStats(BaseModel):
    """智能体统计信息模型"""

    rating: float = Field(..., description="智能体平均评级", ge=0, le=5)
    rating_count: str = Field(..., description="评级数量，例如：'50K+'")
    rank: str = Field(..., description="排名，例如：'#13'")
    category: str = Field(..., description="所属类别")
    conversation_count: str = Field(..., description="对话数量，例如：'3M+'")
    rating_distribution: List[RatingDistribution] = Field(
        default_factory=list, description="各星级评分的分布情况"
    )


class AgentQuery(BaseModel):
    """应用搜索参数"""

    keyword: Optional[str] = Field(default=None, description="关键词")
    page: int = Field(1, description="页码，从1开始")
    page_size: int = Field(10, description="每页数量")
    mode: Optional[AgentType] = Field(None, description="应用模式，为空时返回所有应用")
    exclude_ids: Optional[List[str]] = Field(
        default=None, description="排除的智能体ID列表"
    )
    owner_id: Optional[int] = Field(default=None, description="创建者ID")


class AgentBase(BaseModel):
    id: str = Field(..., description="应用ID")
    name: str = Field(..., description="应用名称")
    type: AgentType = Field(..., description="应用类型")
    owner_id: Optional[int] = Field(default=None, description="创建者ID")
    description: Optional[str] = Field(default=None, description="应用描述")
    icon_url: Optional[str] = Field(default=None, description="应用图标URL")
    icon: Optional[str] = Field(default=None, description="应用图标URL")
    bundle_agent_ids: Optional[List[str]] = Field(
        default=None, description="捆绑包包含的智能体ID列表"
    )
    is_public: Optional[bool] = Field(default=False, description="是否公开")

    @classmethod
    def from_agent(cls, agent: Agent):
        return cls(
            id=agent.id,
            name=agent.name,
            type=agent.type,
            owner_id=agent.owner_id,
            description=agent.description,
            icon_url=agent.icon_url,
            icon=agent.icon_url,
            bundle_agent_ids=agent.bundle_agent_ids,
            is_public=agent.is_public,
        )

    @classmethod
    def from_dify_app(cls, app: App):
        return cls(
            id=app.id,
            name=app.name,
            type=AgentType(app.mode),
            description=app.description,
        )


class AgentDetail(App):
    bundle_agent_ids: List[str] = Field(
        default_factory=list, description="捆绑包包含的智能体ID列表"
    )
    owner_id: Optional[int] = Field(default=None, description="创建者ID")
    is_public: bool = Field(default=False, description="是否公开")
    workflow_publish: Optional[WorkflowPublish] = Field(
        default=None, description="工作流发布配置"
    )
    stats: Optional[AgentStats] = Field(default=None, description="智能体统计信息")
    features: List[str] = Field(
        default_factory=list, description="智能体支持的功能列表"
    )
    has_purchased: bool = Field(default=False, description="用户是否已购买该智能体")


class AgentSimple(BaseModel):
    """简化的智能体信息，用于标签分组展示"""

    id: str = Field(..., description="应用ID")
    name: str = Field(..., description="应用名称")
    icon: Optional[str] = Field(default=None, description="应用图标URL")
    description: Optional[str] = Field(default=None, description="应用描述")

    @classmethod
    def from_agent(cls, agent: Agent):
        return cls(
            id=agent.id,
            name=agent.name,
            icon=agent.icon_url,
            description=agent.description,
        )

    class Config:
        """Pydantic配置"""

        json_encoders = {
            # 自定义JSON编码器
        }
        # 允许从ORM模型创建
        orm_mode = True


class AgentsByTag(BaseModel):
    """按标签分组的智能体列表"""

    data: Dict[str, List[AgentSimple]] = Field(
        ..., description="按标签分组的智能体列表"
    )

    class Config:
        """Pydantic配置"""

        json_encoders = {
            # 自定义JSON编码器
        }
        # 允许从ORM模型创建
        orm_mode = True


class SaveOrUpdateAgentPayloads(BaseModel):
    """创建智能体请求参数"""

    id: Optional[str] = Field(
        default=None, description="智能体ID，如果提供且存在则更新，否则创建"
    )
    name: str = Field(..., description="智能体名称")
    description: str = Field(..., description="智能体描述")
    icon_url: Optional[str] = Field(default=None, description="智能体图标URL")
    tags: Optional[List[str]] = Field(default=None, description="智能体标签")
    is_public: bool = Field(default=False, description="是否公开")
    bundle_agent_ids: List[str] = Field(
        default_factory=list, description="捆绑包包含的智能体ID列表"
    )
    config: ModelConfig = Field(
        default=ModelConfig(),
        description="模型配置",
    )


class ShareToFeishuRequest(BaseModel):
    """分享到飞书请求参数"""

    title: str = Field(..., description="文档标题")
    content: str = Field(..., description="文档内容")


class ShareToFeishuResponse(BaseModel):
    """分享到飞书响应"""

    document_id: str = Field(..., description="飞书文档ID")
    url: str = Field(..., description="飞书文档URL")
    title: str = Field(..., description="文档标题")
