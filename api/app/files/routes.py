import asyncio
from datetime import datetime

from app.core.configs import settings
from app.core.schemas import ResponsePayloads
from fastapi import APIRouter, File, UploadFile

from .schemas import FileUpload
from .utils import CHUNK_SIZE, MAX_WORKERS, get_oss_bucket, upload_part_task

router = APIRouter(prefix="/files", tags=["文件"])


@router.post("/upload", summary="上传文件", response_model=ResponsePayloads[FileUpload])
async def upload_file(file: UploadFile = File(...)):
    """并行分片上传文件"""
    bucket = get_oss_bucket()
    file_name = f"{int(datetime.now().timestamp())}-{file.filename}"

    upload_id = bucket.init_multipart_upload(file_name).upload_id
    parts = []
    upload_tasks = []
    part_number = 1

    try:
        while True:
            chunk = await file.read(CHUNK_SIZE)
            if not chunk:
                break

            task = asyncio.create_task(
                upload_part_task(bucket, file_name, upload_id, part_number, chunk)
            )
            upload_tasks.append(task)

            # 控制并发数
            if len(upload_tasks) >= MAX_WORKERS:
                completed_parts = await asyncio.gather(*upload_tasks)
                parts.extend(completed_parts)
                upload_tasks = []

            part_number += 1

        # 处理剩余的任务
        if upload_tasks:
            completed_parts = await asyncio.gather(*upload_tasks)
            parts.extend(completed_parts)

        # 按分片号排序
        parts.sort(key=lambda x: x.part_number)

        # 完成上传
        bucket.complete_multipart_upload(
            key=file_name, upload_id=upload_id, parts=parts
        )

        file_url = (
            f"https://{settings.oss_bucket}.oss-cn-hangzhou.aliyuncs.com/{file_name}"
        )
        return ResponsePayloads(data=FileUpload(url=file_url, file_name=file_name))

    except Exception as e:
        bucket.abort_multipart_upload(file_name, upload_id)
        raise e
