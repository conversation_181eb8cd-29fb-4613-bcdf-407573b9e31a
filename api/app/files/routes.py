from datetime import datetime
import os
import uuid

import oss2
from fastapi import APIRouter, File, Form, UploadFile
from minio import Minio
from minio.error import S3Error

from app.core.configs import settings
from app.core.schemas import ResponsePayloads

from .schemas import FileUpload, DocumentExtract
from .utils import office2markdown_minio, FileProcessingError, UnsupportedFileTypeError

router = APIRouter(prefix="/files", tags=["文件"])


@router.post("/upload", response_model=ResponsePayloads[FileUpload])
async def upload_file(file: UploadFile = File(...)):
    # 读取文件内容
    contents = await file.read()

    # 生成文件名
    file_name = f"{int(datetime.now().timestamp())}-{file.filename}"

    # 初始化 OSS 客户端
    auth = oss2.Auth(settings.aliyun_access_key_id, settings.aliyun_access_key_secret)

    # 正确构建 endpoint
    endpoint = "https://oss-cn-hangzhou.aliyuncs.com"
    bucket = oss2.Bucket(
        auth,
        endpoint,  # 使用不带 bucket 的 endpoint
        settings.oss_bucket,
        connect_timeout=30,  # 增加超时时间
    )

    # 上传到OSS
    bucket.put_object(file_name, contents)

    # 构建文件URL
    file_url = f"https://{settings.oss_bucket}.oss-cn-hangzhou.aliyuncs.com/{file_name}"

    return ResponsePayloads(data=FileUpload(url=file_url, file_name=file_name))


@router.post("/upload_to_minio", response_model=ResponsePayloads[FileUpload])
async def upload_file_to_minio(
    file: UploadFile = File(...),
    path: str = Form(None, description="文件在MinIO中的存储路径，例如 'images/'"),
):
    """
    上传文件到MinIO存储服务

    Args:
        file: 要上传的文件
        path: 可选的存储路径，例如 'images/'、'documents/2023/'

    Returns:
        上传成功后的文件URL和文件名
    """
    try:
        # 读取文件内容
        contents = await file.read()

        # 生成唯一文件名
        file_id = uuid.uuid4().hex
        file_name = f"{file_id}-{file.filename}"

        # 构建对象名称，包含可选的路径
        object_name = file_name
        if path:
            # 确保路径以斜杠结尾
            normalized_path = path if path.endswith("/") else f"{path}/"
            object_name = f"{normalized_path}{file_name}"

        # 创建临时文件
        temp_file_path = f"temp_{file_id}"
        with open(temp_file_path, "wb") as f:
            f.write(contents)

        # 初始化 MinIO 客户端
        minio_client = Minio(
            endpoint=settings.minio_endpoint,
            access_key=settings.minio_access_key,
            secret_key=settings.minio_secret_key,
            secure=True,  # 使用HTTPS
        )

        # 检查存储桶是否存在，不存在则创建
        if not minio_client.bucket_exists(settings.minio_bucket):
            minio_client.make_bucket(settings.minio_bucket)

        # 上传文件到MinIO
        minio_client.fput_object(
            bucket_name=settings.minio_bucket,
            object_name=object_name,
            file_path=temp_file_path,
            content_type=file.content_type,
        )

        # 删除临时文件
        os.remove(temp_file_path)

        # 构建文件URL
        file_url = (
            f"https://{settings.minio_endpoint}/{settings.minio_bucket}/{object_name}"
        )

        return ResponsePayloads(data=FileUpload(url=file_url, file_name=file_name))

    finally:
        # 删除临时文件（如果存在）
        if "temp_file_path" in locals() and os.path.exists(temp_file_path):
            os.remove(temp_file_path)


@router.post("/extract_document", response_model=ResponsePayloads[DocumentExtract])
async def extract_document(
    file: UploadFile = File(...),
    path: str = Form("documents/images/", description="文档图片在MinIO中的存储路径"),
):
    """
    提取PDF或Word文档中的文本和图片，并将图片上传到MinIO

    Args:
        file: 要处理的文档文件（支持PDF和DOCX格式）
        path: 图片在MinIO中的存储路径，默认为 'documents/images/'

    Returns:
        提取的文本内容和图片URL列表
    """
    try:
        # 检查文件类型
        file_extension = os.path.splitext(file.filename)[1].lower()
        if file_extension not in ['.pdf', '.docx', '.xlsx', '.xls', '.csv']:
            return ResponsePayloads(
                code=400,
                message=f"不支持的文件类型: {file_extension}，仅支持PDF、DOCX、XLSX、XLS和CSV格式",
                data=None,
            )

        # 读取文件内容
        contents = await file.read()

        # 生成唯一文件名
        file_id = uuid.uuid4().hex
        temp_file_path = f"temp_{file_id}{file_extension}"

        try:
            # 保存临时文件
            with open(temp_file_path, "wb") as f:
                f.write(contents)

            # 处理文档
            data = office2markdown_minio(temp_file_path, path)

            # 构建响应
            return ResponsePayloads(
                data=DocumentExtract(
                    text=data.text,
                    images=data.imgs,
                    file_name=file.filename,
                )
            )

        except UnsupportedFileTypeError as e:
            return ResponsePayloads(
                code=400,
                message=str(e),
                data=None,
            )
        except FileProcessingError as e:
            return ResponsePayloads(
                code=500,
                message=str(e),
                data=None,
            )
        finally:
            # 删除临时文件
            if os.path.exists(temp_file_path):
                os.remove(temp_file_path)

    except Exception as e:
        return ResponsePayloads(
            code=500,
            message=f"处理文档时发生错误: {str(e)}",
            data=None,
        )
