import os
import tempfile
from datetime import datetime
from urllib.parse import unquote, urlparse

import oss2
import requests
from app.core.configs import settings

def get_oss_bucket():
    """获取OSS bucket实例"""
    auth = oss2.Auth(
        settings.aliyun_access_key_id,
        settings.aliyun_access_key_secret
    )
    
    endpoint = "https://oss-cn-hangzhou.aliyuncs.com"
    return oss2.Bucket(
        auth,
        endpoint,
        settings.oss_bucket,
        connect_timeout=30
    )

def upload_to_oss(
    file_content: bytes, file_name: str, link_type: str = "default"
) -> str:
    """上传文件到阿里云OSS

    Args:
        file_content: 文件内容(bytes)
        file_name: 原始文件名
        link_type: 链接类型，用于区分不同类型的文件，如预览视频、最终生成视频等

    Returns:
        文件访问URL
    """
    # 初始化OSS客户端
    auth = oss2.Auth(
        settings.aliyun_access_key_id,
        settings.aliyun_access_key_secret,
    )

    # 构建endpoint
    endpoint = "https://oss-cn-hangzhou.aliyuncs.com"
    bucket = oss2.Bucket(auth, endpoint, settings.oss_bucket, connect_timeout=30)

    # 根据链接类型确定存储目录
    directory = ""
    if link_type == "avatar_preview":
        directory = "avatar/preview/"
    elif link_type == "final_video":
        directory = "video/final/"
    elif link_type == "voice":
        directory = "voice/"

    # 生成唯一文件名
    unique_name = f"{directory}{int(datetime.now().timestamp())}-{file_name}"

    # 上传文件
    bucket.put_object(unique_name, file_content)

    # 返回文件URL
    return f"https://{settings.oss_bucket}.oss-cn-hangzhou.aliyuncs.com/{unique_name}"


def copy_to_oss(file_url: str, link_type: str = "default") -> str:
    """复制文件到阿里云OSS

    Args:
        file_url: 文件URL
        link_type: 链接类型，用于区分不同类型的文件，如预览视频、最终生成视频等

    Returns:
        文件访问URL
    """
    try:
        # 下载文件
        response = requests.get(file_url, stream=True)
        response.raise_for_status()
        # 提取文件名和扩展名
        parsed_url = urlparse(file_url)
        file_path = unquote(parsed_url.path)  # 解码URL编码的字符
        file_name = os.path.basename(file_path)
        ext = os.path.splitext(file_name)[1]

        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix=ext, delete=False) as tmp_file:
            for chunk in response.iter_content(chunk_size=8192):
                if chunk:
                    tmp_file.write(chunk)
            tmp_path = tmp_file.name

        # 读取临时文件内容并上传
        with open(tmp_path, "rb") as f:
            file_content = f.read()
            oss_url = upload_to_oss(file_content, file_name, link_type)

        # 删除临时文件
        os.unlink(tmp_path)

        return oss_url

    except Exception as e:
        # 清理临时文件
        if "tmp_path" in locals() and os.path.exists(tmp_path):
            os.unlink(tmp_path)
        raise e
CHUNK_SIZE = 5 * 1024 * 1024  # 增大到10MB
MAX_WORKERS = 4  # 并行上传数


async def upload_part_task(bucket, file_name, upload_id, part_number, chunk):
    """单个分片上传任务"""
    result = bucket.upload_part(
        key=file_name, upload_id=upload_id, part_number=part_number, data=chunk
    )
    return oss2.models.PartInfo(part_number=part_number, etag=result.etag)