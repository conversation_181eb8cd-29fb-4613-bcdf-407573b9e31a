from typing import Generator

from sqlalchemy import create_engine
from sqlmodel import Session
from sshtunnel import SSHTunnelForwarder

from app.core import settings

from .models import *

database_url = settings.database_url
if settings.database_use_tunnel:
    # SSH隧道配置
    ssh_host = "************"  # SSH服务器地址
    ssh_port = 22  # SSH端口
    ssh_username = "root"
    ssh_password = "dongjak@2015"  # 或设置为None使用SSH密钥

    # 远程数据库配置
    remote_db_host = "localhost"  # 数据库服务器地址，如果在SSH同一服务器上通常是"localhost"或"127.0.0.1"
    remote_db_port = 5432  # 数据库端口，例如PostgreSQL是5432，MySQL是3306
    db_name = "aq"
    db_username = "postgres"
    db_password = "postgres"

    # 建立SSH隧道
    tunnel = SSHTunnelForwarder(
        (ssh_host, ssh_port),
        ssh_username=ssh_username,
        ssh_password=ssh_password,
        # 如果使用SSH密钥认证:
        # ssh_private_key_password=None,
        # ssh_pkey=os.path.expanduser(ssh_pkey),
        remote_bind_address=(remote_db_host, remote_db_port),
        local_bind_address=("127.0.0.1", 6543),  # 本地转发端口
    )

    # 启动隧道
    tunnel.start()
    # 创建数据库连接
    database_url = f"postgresql://{db_username}:{db_password}@127.0.0.1:{tunnel.local_bind_port}/{db_name}"

    # 注册应用退出时关闭隧道
    import atexit

    atexit.register(tunnel.stop)

engine = create_engine(
    database_url,
    pool_size=5,  # 增加连接池大小
    max_overflow=10,  # 增加溢出连接数
    pool_timeout=30,
    pool_recycle=600,  # 减少为10分钟
    pool_pre_ping=True,
)


def get_session() -> Generator[Session, None, None]:
    """
    获取数据库会话的依赖函数
    """
    with Session(engine) as session:
        try:
            yield session
        finally:
            session.close()


__all__ = ["User", "Order", "Agent",  "engine", "get_session"]
