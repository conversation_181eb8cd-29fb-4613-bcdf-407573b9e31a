from datetime import datetime
from enum import Enum
from typing import List, Optional

from pydantic import BaseModel
from sqlalchemy import Boolean, Column, DateTime
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import Float, ForeignKey, Integer, String, Text, UniqueConstraint
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Field, Relationship, SQLModel


class User(SQLModel, table=True):
    """用户信息表"""

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(Integer, primary_key=True, autoincrement=True),
        description="用户ID",
    )
    username: str = Field(
        sa_column=Column(String(50), comment="用户名"), description="用户名"
    )
    nickname: str = Field(
        default="", sa_column=Column(String(50), comment="昵称"), description="昵称"
    )
    comment: str = Field(
        default="", sa_column=Column(String(255), comment="备注"), description="备注"
    )
    phone: str = Field(
        sa_column=Column(String(11), comment="手机号"), description="手机号"
    )
    password: str = Field(
        sa_column=Column(String(100), comment="密码(加密存储)"), description="密码"
    )
    can_create_agent: bool = Field(
        default=False,
        sa_column=Column(Boolean, comment="是否允许创建智能体"),
        description="是否允许创建智能体",
    )
    is_allow_public_agent: bool = Field(
        default=False,
        sa_column=Column(Boolean, comment="是否允许公开智能体"),
        description="是否允许公开智能体",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )
    tags: List[str] = Field(
        default=[], sa_column=Column(JSONB, comment="标签列表"), description="标签列表"
    )
    orders: List["Order"] = Relationship(
        back_populates="user", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )
    agents: List["Agent"] = Relationship(
        back_populates="owner", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )
    __table_args__ = (
        UniqueConstraint("phone", name="uq_user_phone"),
        {"comment": "用户表"},
    )

    __tablename__ = "qu_users"


class Agent(SQLModel, table=True):
    """智能体"""

    class Type(str, Enum):
        CHAT = "chat"
        AGENT_CHAT = "agent-chat"
        WORKFLOW = "workflow"
        COMPLETION = "completion"
        BUNDLE = "bundle"  # 智能体捆绑包类型

    id: str = Field(
        default=None,
        sa_column=Column(String, primary_key=True, comment="应用ID"),
        description="应用ID",
    )
    name: str = Field(
        sa_column=Column(String, index=True, comment="名称"), description="名称"
    )
    sort_order: int = Field(
        default=0,
        sa_column=Column(Integer, index=True, comment="排序顺序，数字越小越靠前"),
        description="排序顺序",
    )
    bundle_agent_ids:Optional[List[str]] = Field(
        default=None,
        sa_column=Column(JSONB, comment="捆绑包包含的智能体ID列表"),
        description="捆绑包包含的智能体ID列表",
    )
    icon_url: Optional[str] = Field(
        default=None, sa_column=Column(String, comment="图标URL"), description="图标URL"
    )
    description: str = Field(
        sa_column=Column(String, comment="功能描述"), description="功能描述"
    )
    type: Type = Field(
        sa_column=Column(
            SQLAlchemyEnum(Type, name="agent_type_enum"),
            comment="应用模式",
        ),
        description="应用模式",
    )
    api_key: Optional[str] = Field(
        default=None,
        sa_column=Column(String, index=True, comment="API密钥"),
        description="最后同步的可用API密钥",
    )
    owner_id: Optional[int] = Field(
        default=None,
        sa_column=Column(Integer, ForeignKey("qu_users.id"), comment="创建者ID"),
        description="创建者ID",
    )
    tags: List[str] = Field(
        default=[], sa_column=Column(JSONB, comment="标签列表"), description="标签列表"
    )
    is_public: bool = Field(
        default=False,
        sa_column=Column(Boolean, comment="是否公开"),
        description="是否公开",
    )

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )
    owner: Optional["User"] = Relationship(back_populates="agents")

    __table_args__ = {"comment": "Dify应用表"}
    __tablename__ = "qu_agents"


class Course(SQLModel, table=True):
    """
    课程表
    """

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(Integer, primary_key=True, comment="课程ID"),
        description="ID",
    )

    title: str = Field(
        sa_column=Column(String, index=True, comment="课程标题"), description="课程标题"
    )

    description: str = Field(
        sa_column=Column(String, comment="课程描述"), description="课程描述"
    )
    tags: List[str] = Field(
        default=[], sa_column=Column(JSONB, comment="标签列表"), description="标签列表"
    )
    cover_image: Optional[str] = Field(
        default=None,
        sa_column=Column(String, comment="课程封面图片URL"),
        description="课程封面",
    )
    poster_url: Optional[str] = Field(
        default=None,
        sa_column=Column(String, comment="海报图片URL"),
        description="海报图片",
    )

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )

    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )
    instructor: Optional[str] = Field(
        default=None, sa_column=Column(String, comment="导师"), description="导师"
    )

    # 添加这一行，建立与章节的关系
    sections: List["CourseSection"] = Relationship(back_populates="course")
    __table_args__ = {"comment": "课程信息表"}
    __tablename__ = "qu_courses"


class CourseSection(SQLModel, table=True):
    """
    课程章节表
    """

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(Integer, primary_key=True, comment="章节ID"),
        description="ID",
    )

    title: str = Field(
        sa_column=Column(String, index=True, comment="章节标题"), description="章节标题"
    )

    duration: int = Field(
        default=0, sa_column=Column(Integer, comment="时长(秒)"), description="时长"
    )

    sort_order: int = Field(
        default=0, sa_column=Column(Integer, comment="排序"), description="排序"
    )

    is_free: bool = Field(
        default=False,
        sa_column=Column(Boolean, comment="是否免费"),
        description="是否免费",
    )

    video_url: Optional[str] = Field(
        default=None, sa_column=Column(String, comment="视频URL"), description="视频URL"
    )

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )

    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )

    is_published: bool = Field(
        default=False,
        sa_column=Column(Boolean, comment="是否发布"),
        description="是否发布",
    )

    course_id: int = Field(
        sa_column=Column(Integer, ForeignKey("qu_courses.id"), comment="关联课程ID"),
        description="课程ID",
    )

    course: "Course" = Relationship(back_populates="sections")

    __table_args__ = {"comment": "课程章节表"}
    __tablename__ = "qu_course_sections"


class PaymentPlan(SQLModel, table=True):
    """付费计划表"""


    class ScopeType(str, Enum):
        """适用范围类型枚举"""
        APP = "app"  # 应用
        COURSE = "course"  # 课程
        BUNDLE = "bundle"  # 捆绑包

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(Integer, primary_key=True, autoincrement=True),
        description="计划ID",
    )
    name: str = Field(
        sa_column=Column(String, comment="计划名称"), description="计划名称"
    )
    description: Optional[str] = Field(
        default=None,
        sa_column=Column(String, comment="计划描述"),
        description="计划描述",
    )
    price: float = Field(
        sa_column=Column(Float, comment="价格"), description="价格"
    )
    original_price: float = Field(
        default=0.0,
        sa_column=Column(Float, comment="原价"),
        description="原价",
    )
    validity_period: int = Field(
        default=0,
        sa_column=Column(Integer, comment="有效期（天）"),
        description="有效期（天）",
    )
    scope_type: ScopeType = Field(
        sa_column=Column(
            SQLAlchemyEnum(ScopeType, name="payment_plan_scope_type_enum"),
            comment="适用范围类型",
        ),
        description="适用范围类型",
    )
    scope_ids: List[str] = Field(
        default=[],
        sa_column=Column(JSONB, comment="适用范围ID列表"),
        description="适用范围ID列表",
    )
    is_active: bool = Field(
        default=True,
        sa_column=Column(Boolean, comment="是否激活"),
        description="是否激活",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )

    __table_args__ = {"comment": "付费计划表"}
    __tablename__ = "qu_payment_plans"

class ProductType(str, Enum):
        APP = "app"
        COURSE = "course"
class Order(SQLModel, table=True):
    """订单表"""

    class PaymentMethod(str, Enum):
        """支付方式枚举"""

        ALIPAY = "alipay"  # 支付宝
        WECHATPAY = "wechatpay"  # 微信支付

    class Status(str, Enum):
        """订单状态枚举"""

        PENDING = "PENDING"  # 待支付
        PAID = "PAID"  # 已支付
        CANCELLED = "CANCELLED"  # 已取消
        REFUNDED = "REFUNDED"  # 已退款

    id: Optional[str] = Field(
        default=None,
        sa_column=Column(String, primary_key=True, comment="订单ID"),
        description="ID",
    )
    user_id: int = Field(
        sa_column=Column(Integer, ForeignKey("qu_users.id"), comment="用户ID"),
        description="用户ID",
    )

    amount: float = Field(
        sa_column=Column(Float, comment="订单金额"), description="订单金额"
    )

    status: str = Field(
        default=Status.PENDING,
        sa_column=Column(
            SQLAlchemyEnum(Status, name="order_status_enum"),
            comment="订单状态",
        ),
        description="订单状态",
    )
    payment_method: PaymentMethod = Field(
        default=PaymentMethod.ALIPAY,
        sa_column=Column(
            SQLAlchemyEnum(PaymentMethod, name="payment_method_enum"),
            comment="支付方式",
        ),
        description="支付方式",
    )
    pay_time: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(), comment="支付时间"),
        description="支付时间",
    )

    # 新增字段
    product_type: ProductType = Field(
        sa_column=Column(
            SQLAlchemyEnum(ProductType, name="product_type_enum"),
            comment="产品类型",
        ),
        description="产品类型",
    )
    product_id: str = Field(
        sa_column=Column(String, comment="产品ID"), description="产品ID"
    )
    payment_plan_id: int = Field(
        sa_column=Column(Integer, ForeignKey("qu_payment_plans.id"), comment="付费计划ID"),
        description="付费计划ID",
    )
    quantity: int = Field(
        default=1,
        sa_column=Column(Integer, comment="数量"),
        description="数量",
    )

    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )

    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )
    user: "User" = Relationship(back_populates="orders")
    payment_plan: "PaymentPlan" = Relationship()

    __table_args__ = {"comment": "订单信息表"}
    __tablename__ = "qu_orders"


class Admin(SQLModel, table=True):
    """管理员表"""

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(
            Integer, primary_key=True, autoincrement=True, comment="管理员ID"
        ),
        description="管理员ID",
    )
    username: str = Field(
        sa_column=Column(String(50), unique=True, comment="用户名"),
        description="用户名",
    )
    password: str = Field(
        sa_column=Column(String(100), comment="密码"),
        description="密码",
    )
    avatar_url: Optional[str] = Field(
        default=None,
        sa_column=Column(Text, comment="头像URL"),
        description="头像URL",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )

    __table_args__ = {"comment": "管理员表"}
    __tablename__ = "qu_admins"
