from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional
from uuid import uuid4

from billing.keys import CurrencyType
from sqlalchemy import Column, DateTime
from sqlalchemy import Enum as SQLAlchemyEnum
from sqlalchemy import Float, Foreign<PERSON>ey, Integer, String, Text
from sqlalchemy.dialects.postgresql import JSONB
from sqlmodel import Field, Relationship, SQLModel


class Admin(SQLModel, table=True):
    """管理员表"""

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(
            Integer, primary_key=True, autoincrement=True, comment="管理员ID"
        ),
        description="管理员ID",
    )
    username: str = Field(
        sa_column=Column(String(50), unique=True, comment="用户名"),
        description="用户名",
    )
    password: str = Field(
        sa_column=Column(String(100), comment="密码"),
        description="密码",
    )
    avatar_url: Optional[str] = Field(
        default=None,
        sa_column=Column(Text, comment="头像URL"),
        description="头像URL",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )

    __table_args__ = {"comment": "管理员表"}
    __tablename__ = "hb_admins"


class App(SQLModel, table=True):
    """应用表"""

    app_id: str = Field(
        default_factory=lambda: str(uuid4()).replace("-", "")[:6],
        sa_column=Column(String(6), primary_key=True, comment="应用ID"),
        description="应用ID",
    )
    name: str = Field(
        sa_column=Column(String(100), index=True, comment="应用名称"),
        description="应用名称",
    )
    description: Optional[str] = Field(
        default=None,
        sa_column=Column(Text, comment="应用描述"),
        description="应用描述",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )

    # 关联关系
    keys: List["Key"] = Relationship(
        back_populates="app", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    __table_args__ = {"comment": "应用表"}
    __tablename__ = "hb_apps"


class Service(SQLModel, table=True):
    """服务表"""

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(
            Integer, primary_key=True, autoincrement=True, comment="服务ID"
        ),
        description="服务ID",
    )
    name: str = Field(
        sa_column=Column(String(100), index=True, comment="服务名称"),
        description="服务名称",
    )
    code: str = Field(
        sa_column=Column(String(50), unique=True, index=True, comment="服务代码"),
        description="服务代码",
    )
    description: Optional[str] = Field(
        default=None,
        sa_column=Column(Text, comment="服务描述"),
        description="服务描述",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )

    # 关联关系
    keys: List["Key"] = Relationship(
        back_populates="service",
        sa_relationship_kwargs={"cascade": "all, delete-orphan"},
    )

    __table_args__ = {"comment": "服务表"}
    __tablename__ = "hb_services"


class Key(SQLModel, table=True):
    """密钥表"""

    id: str = Field(
        default_factory=lambda: str(uuid4()).replace("-", ""),
        sa_column=Column(String(32), primary_key=True, unique=True, comment="密钥ID"),
        description="密钥ID",
    )
    app_id: str = Field(
        sa_column=Column(
            String(6), ForeignKey("hb_apps.app_id"), index=True, comment="应用ID"
        ),
        description="应用ID",
    )
    user_id: str = Field(
        sa_column=Column(String(50), index=True, comment="用户ID"),
        description="用户ID",
    )
    service_id: int = Field(
        sa_column=Column(
            Integer, ForeignKey("hb_services.id"), index=True, comment="服务ID"
        ),
        description="服务ID",
    )
    scope: Optional[List[str]] = Field(
        default=None,
        sa_column=Column(JSONB, index=True, comment="作用域，JSON数组格式"),
        description="作用域，JSON数组格式",
    )
    currency_type: Optional[CurrencyType] = Field(
        default=None,
        sa_column=Column(
            SQLAlchemyEnum(CurrencyType, name="currency_type_enum"),
            comment="信用货币类型",
        ),
        description="信用货币类型",
    )
    credit_limit: float = Field(
        default=0.0,
        sa_column=Column(Float, comment="额度限制"),
        description="额度限制",
    )
    credit_used: float = Field(
        default=0.0,
        sa_column=Column(Float, comment="已使用额度"),
        description="已使用额度",
    )
    expires_at: Optional[datetime] = Field(
        default=None,
        sa_column=Column(DateTime(), comment="过期时间"),
        description="过期时间",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="更新时间", onupdate=datetime.now()),
        description="更新时间",
    )

    # 关联关系
    app: App = Relationship(back_populates="keys")
    service: Service = Relationship(back_populates="keys")
    activities: List["Activity"] = Relationship(
        back_populates="key", sa_relationship_kwargs={"cascade": "all, delete-orphan"}
    )

    __table_args__ = {"comment": "密钥表"}
    __tablename__ = "hb_keys"


class Activity(SQLModel, table=True):
    """活动表"""

    class Type(str, Enum):
        """活动类型"""

        VERIFY = "verify"  # 验证
        CONSUME = "consume"  # 消费

    id: Optional[int] = Field(
        default=None,
        sa_column=Column(
            Integer, primary_key=True, autoincrement=True, comment="活动ID"
        ),
        description="活动ID",
    )
    key_id: str = Field(
        sa_column=Column(
            String(32), ForeignKey("hb_keys.id"), index=True, comment="密钥ID"
        ),
        description="密钥ID",
    )
    # 添加与密钥表相同的详细字段
    app_id: str = Field(
        sa_column=Column(
            String(6), ForeignKey("hb_apps.app_id"), index=True, comment="应用ID"
        ),
        description="应用ID",
    )
    user_id: str = Field(
        sa_column=Column(String(50), index=True, comment="用户ID"),
        description="用户ID",
    )
    service_id: int = Field(
        sa_column=Column(
            Integer, ForeignKey("hb_services.id"), index=True, comment="服务ID"
        ),
        description="服务ID",
    )
    scope: Optional[List[str]] = Field(
        default=None,
        sa_column=Column(JSONB, index=True, comment="作用域，JSON数组格式"),
        description="作用域，JSON数组格式",
    )
    currency_type: Optional[CurrencyType] = Field(
        default=None,
        sa_column=Column(
            SQLAlchemyEnum(CurrencyType, name="currency_type_enum"),
            comment="信用货币类型",
        ),
        description="信用货币类型",
    )
    type: Type = Field(
        sa_column=Column(
            SQLAlchemyEnum(Type, name="activity_type_enum"),
            comment="活动类型",
        ),
        description="活动类型",
    )
    amount: float = Field(
        default=0.0,
        sa_column=Column(Float, comment="消费数量"),
        description="消费数量",
    )
    details: Optional[Dict[str, Any]] = Field(
        default=None,
        sa_column=Column(JSONB, comment="详情"),
        description="详情",
    )
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(),
        sa_column=Column(DateTime(), comment="创建时间"),
        description="创建时间",
    )

    # 关联关系
    key: Key = Relationship(back_populates="activities")
    app: App = Relationship()
    service: Service = Relationship()

    __table_args__ = {"comment": "活动表"}
    __tablename__ = "hb_activities"
