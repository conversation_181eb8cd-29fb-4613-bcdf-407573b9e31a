"""empty message

Revision ID: 44b994bb4a0e
Revises: 30650b245a91
Create Date: 2025-05-16 21:23:28.932460

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '44b994bb4a0e'
down_revision: Union[str, None] = '30650b245a91'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('APP', 'COURSE', name='product_type_enum').create(op.get_bind())
    sa.Enum('APP', 'COURSE', 'BUNDLE', name='payment_plan_scope_type_enum').create(op.get_bind())
    op.create_table('qu_payment_plans',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('name', sa.String(), nullable=True, comment='计划名称'),
    sa.Column('description', sa.String(), nullable=True, comment='计划描述'),
    sa.Column('price', sa.Float(), nullable=True, comment='价格'),
    sa.Column('original_price', sa.Float(), nullable=True, comment='原价'),
    sa.Column('validity_period', sa.Integer(), nullable=True, comment='有效期（天）'),
    sa.Column('scope_type', postgresql.ENUM('APP', 'COURSE', 'BUNDLE', name='payment_plan_scope_type_enum', create_type=False), nullable=True, comment='适用范围类型'),
    sa.Column('scope_ids', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='适用范围ID列表'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否激活'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='付费计划表'
    )
    op.drop_table('qu_user_assets')
    op.add_column('qu_orders', sa.Column('product_type', postgresql.ENUM('APP', 'COURSE', name='product_type_enum', create_type=False), nullable=True, comment='产品类型'))
    op.add_column('qu_orders', sa.Column('product_id', sa.String(), nullable=True, comment='产品ID'))
    op.add_column('qu_orders', sa.Column('payment_plan_id', sa.Integer(), nullable=True, comment='付费计划ID'))
    op.add_column('qu_orders', sa.Column('quantity', sa.Integer(), nullable=True, comment='数量'))
    op.create_foreign_key(None, 'qu_orders', 'qu_payment_plans', ['payment_plan_id'], ['id'])
    op.drop_column('qu_orders', 'items')
    sa.Enum('APP', 'COURSE', name='asset_type_enum').drop(op.get_bind())
    sa.Enum('CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION', 'BUNDLE', name='app_mode_enum').drop(op.get_bind())
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION', 'BUNDLE', name='app_mode_enum').create(op.get_bind())
    sa.Enum('APP', 'COURSE', name='asset_type_enum').create(op.get_bind())
    op.add_column('qu_orders', sa.Column('items', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='订单项'))
    op.drop_constraint(None, 'qu_orders', type_='foreignkey')
    op.drop_column('qu_orders', 'quantity')
    op.drop_column('qu_orders', 'payment_plan_id')
    op.drop_column('qu_orders', 'product_id')
    op.drop_column('qu_orders', 'product_type')
    op.create_table('qu_user_assets',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='用户ID'),
    sa.Column('asset_type', postgresql.ENUM('APP', 'COURSE', name='asset_type_enum', create_type=False), autoincrement=False, nullable=True, comment='资产类型'),
    sa.Column('app_mode', postgresql.ENUM('CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION', 'BUNDLE', name='app_mode_enum', create_type=False), autoincrement=False, nullable=True, comment='应用模式,当资产类型为APP时有效'),
    sa.Column('asset_id', sa.VARCHAR(), autoincrement=False, nullable=True, comment='资产ID'),
    sa.Column('asset_name', sa.VARCHAR(), autoincrement=False, nullable=True, comment='资产名称'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['user_id'], ['qu_users.id'], name='qu_user_assets_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='qu_user_assets_pkey'),
    comment='用户资产表'
    )
    op.drop_table('qu_payment_plans')
    sa.Enum('APP', 'COURSE', 'BUNDLE', name='payment_plan_scope_type_enum').drop(op.get_bind())
    sa.Enum('APP', 'COURSE', name='product_type_enum').drop(op.get_bind())
    # ### end Alembic commands ###
