"""empty message

Revision ID: 8e17e9278438
Revises: 8c02f3a3cb46
Create Date: 2025-04-21 05:32:55.623562

"""

from typing import Sequence, Union

# revision identifiers, used by Alembic.
revision: str = "8e17e9278438"
down_revision: Union[str, None] = "8c02f3a3cb46"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    pass
    # ### end Alembic commands ###
