"""empty message

Revision ID: 9aac5f774f67
Revises: 7e1aaeaa95a9
Create Date: 2025-03-31 21:27:36.015884

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "9aac5f774f67"
down_revision: Union[str, None] = "7e1aaeaa95a9"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "qu_user_accounts",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "owner_type",
            sa.Enum("USER", "PLATFORM", name="account_owner_type_enum"),
            nullable=True,
            comment="账户归属类型",
        ),
        sa.Column("user_id", sa.Integer(), nullable=True, comment="用户ID"),
        sa.Column("balance", sa.Float(), nullable=True, comment="账户余额"),
        sa.Column("currency", sa.String(length=3), nullable=True, comment="货币类型"),
        sa.Column("is_active", sa.Boolean(), nullable=True, comment="是否激活"),
        sa.Column("created_at", sa.DateTime(), nullable=True, comment="创建时间"),
        sa.Column("updated_at", sa.DateTime(), nullable=True, comment="更新时间"),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["qu_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("owner_type", "user_id", name="uq_account_owner"),
        comment="账户表",
    )
    op.create_index(
        op.f("ix_qu_user_accounts_user_id"),
        "qu_user_accounts",
        ["user_id"],
        unique=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_qu_user_accounts_user_id"), table_name="qu_user_accounts")
    op.drop_table("qu_user_accounts")
    # ### end Alembic commands ###
