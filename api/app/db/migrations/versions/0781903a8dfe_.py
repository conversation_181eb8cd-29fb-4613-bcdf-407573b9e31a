"""empty message

Revision ID: 0781903a8dfe
Revises: 59a10c030f8d
Create Date: 2025-04-06 22:16:02.568630

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "0781903a8dfe"
down_revision: Union[str, None] = "59a10c030f8d"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "qu_accounts",
        "asset_id",
        existing_type=sa.INTEGER(),
        type_=sa.String(),
        existing_comment="资产ID",
        existing_nullable=True,
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column(
        "qu_accounts",
        "asset_id",
        existing_type=sa.String(),
        type_=sa.INTEGER(),
        existing_comment="资产ID",
        existing_nullable=True,
    )
    # ### end Alembic commands ###
