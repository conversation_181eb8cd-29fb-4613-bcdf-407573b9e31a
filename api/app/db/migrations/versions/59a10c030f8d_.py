"""empty message

Revision ID: 59a10c030f8d
Revises: 92a367a93d49
Create Date: 2025-04-06 21:21:27.671032

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "59a10c030f8d"
down_revision: Union[str, None] = "92a367a93d49"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_qu_user_accounts_user_id", table_name="qu_user_accounts")
    op.drop_table("qu_user_accounts")
    op.execute("DROP TYPE IF EXISTS account_owner_type_enum")
    op.create_table(
        "qu_accounts",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column(
            "owner_type",
            sa.Enum("USER", "USER_ASSET", "PLATFORM", name="account_owner_type_enum"),
            nullable=True,
            comment="账户归属类型",
        ),
        sa.Column(
            "asset_type",
            postgresql.ENUM("APP", "COURSE", name="asset_type_enum", create_type=False),
            nullable=True,
            comment="资产类型",
        ),
        sa.Column("asset_id", sa.Integer(), nullable=True, comment="资产ID"),
        sa.Column("user_id", sa.Integer(), nullable=True, comment="用户ID"),
        sa.Column("balance", sa.Float(), nullable=True, comment="账户余额"),
        sa.Column("currency", sa.String(length=3), nullable=True, comment="货币类型"),
        sa.Column("is_active", sa.Boolean(), nullable=True, comment="是否激活"),
        sa.Column("created_at", sa.DateTime(), nullable=True, comment="创建时间"),
        sa.Column("updated_at", sa.DateTime(), nullable=True, comment="更新时间"),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["qu_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        sa.UniqueConstraint("owner_type", "user_id", "asset_type", "asset_id"),
        comment="账户表",
    )
    op.create_index(
        op.f("ix_qu_accounts_user_id"), "qu_accounts", ["user_id"], unique=True
    )
    op.add_column(
        "qu_users",
        sa.Column(
            "can_create_agent",
            sa.Boolean(),
            nullable=True,
            comment="是否允许创建智能体",
        ),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("qu_users", "can_create_agent")
    op.create_table(
        "qu_user_accounts",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column(
            "owner_type",
            postgresql.ENUM("USER", "PLATFORM", name="account_owner_type_enum"),
            autoincrement=False,
            nullable=True,
            comment="账户归属类型",
        ),
        sa.Column(
            "user_id",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="用户ID",
        ),
        sa.Column(
            "balance",
            sa.DOUBLE_PRECISION(precision=53),
            autoincrement=False,
            nullable=True,
            comment="账户余额",
        ),
        sa.Column(
            "currency",
            sa.VARCHAR(length=3),
            autoincrement=False,
            nullable=True,
            comment="货币类型",
        ),
        sa.Column(
            "is_active",
            sa.BOOLEAN(),
            autoincrement=False,
            nullable=True,
            comment="是否激活",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(),
            autoincrement=False,
            nullable=True,
            comment="创建时间",
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(),
            autoincrement=False,
            nullable=True,
            comment="更新时间",
        ),
        sa.Column(
            "can_create_agent",
            sa.BOOLEAN(),
            autoincrement=False,
            nullable=True,
            comment="是否允许创建智能体",
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["qu_users.id"], name="qu_user_accounts_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="qu_user_accounts_pkey"),
        sa.UniqueConstraint("owner_type", "user_id", name="uq_account_owner"),
        comment="账户表",
    )
    op.create_index(
        "ix_qu_user_accounts_user_id", "qu_user_accounts", ["user_id"], unique=True
    )
    op.drop_index(op.f("ix_qu_accounts_user_id"), table_name="qu_accounts")
    op.drop_table("qu_accounts")
    # ### end Alembic commands ###
