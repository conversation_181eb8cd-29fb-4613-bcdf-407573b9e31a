"""empty message

Revision ID: 7e1aaeaa95a9
Revises: f02fb9db8e59
Create Date: 2025-03-31 18:13:51.775412

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "7e1aaeaa95a9"
down_revision: Union[str, None] = "f02fb9db8e59"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "qu_user_usages",
        sa.Column("total_price", sa.Float(), nullable=True, comment="总价格"),
    )
    op.add_column(
        "qu_user_usages",
        sa.Column("currency", sa.String(), nullable=True, comment="货币"),
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column("qu_user_usages", "currency")
    op.drop_column("qu_user_usages", "total_price")
    # ### end Alembic commands ###
