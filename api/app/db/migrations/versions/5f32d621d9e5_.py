"""empty message

Revision ID: 5f32d621d9e5
Revises: 
Create Date: 2025-04-19 06:49:22.211135

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '5f32d621d9e5'
down_revision: Union[str, None] = None
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('hb_admins',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='管理员ID'),
    sa.Column('username', sa.String(length=50), nullable=True, comment='用户名'),
    sa.Column('password', sa.String(length=100), nullable=True, comment='密码'),
    sa.Column('avatar_url', sa.Text(), nullable=True, comment='头像URL'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('username'),
    comment='管理员表'
    )
    op.create_table('hb_apps',
    sa.Column('app_id', sa.String(length=6), nullable=False, comment='应用ID'),
    sa.Column('name', sa.String(length=100), nullable=True, comment='应用名称'),
    sa.Column('description', sa.Text(), nullable=True, comment='应用描述'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('app_id'),
    comment='应用表'
    )
    op.create_index(op.f('ix_hb_apps_name'), 'hb_apps', ['name'], unique=False)
    op.create_table('hb_services',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='服务ID'),
    sa.Column('name', sa.String(length=100), nullable=True, comment='服务名称'),
    sa.Column('code', sa.String(length=50), nullable=True, comment='服务代码'),
    sa.Column('description', sa.Text(), nullable=True, comment='服务描述'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    comment='服务表'
    )
    op.create_index(op.f('ix_hb_services_code'), 'hb_services', ['code'], unique=True)
    op.create_index(op.f('ix_hb_services_name'), 'hb_services', ['name'], unique=False)
    op.create_table('hb_keys',
    sa.Column('id', sa.String(length=32), nullable=False, comment='密钥ID'),
    sa.Column('app_id', sa.String(length=6), nullable=True, comment='应用ID'),
    sa.Column('user_id', sa.String(length=50), nullable=True, comment='用户ID'),
    sa.Column('service_id', sa.Integer(), nullable=True, comment='服务ID'),
    sa.Column('credit_limit', sa.Float(), nullable=True, comment='额度限制'),
    sa.Column('credit_used', sa.Float(), nullable=True, comment='已使用额度'),
    sa.Column('expires_at', sa.DateTime(), nullable=True, comment='过期时间'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.ForeignKeyConstraint(['app_id'], ['hb_apps.app_id'], ),
    sa.ForeignKeyConstraint(['service_id'], ['hb_services.id'], ),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('id'),
    comment='密钥表'
    )
    op.create_index(op.f('ix_hb_keys_app_id'), 'hb_keys', ['app_id'], unique=False)
    op.create_index(op.f('ix_hb_keys_service_id'), 'hb_keys', ['service_id'], unique=False)
    op.create_index(op.f('ix_hb_keys_user_id'), 'hb_keys', ['user_id'], unique=False)
    op.create_table('hb_activities',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False, comment='活动ID'),
    sa.Column('key_id', sa.String(length=32), nullable=True, comment='密钥ID'),
    sa.Column('type', sa.Enum('VERIFY', 'CONSUME', name='activity_type_enum'), nullable=True, comment='活动类型'),
    sa.Column('amount', sa.Float(), nullable=True, comment='消费数量'),
    sa.Column('details', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='详情'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.ForeignKeyConstraint(['key_id'], ['hb_keys.id'], ),
    sa.PrimaryKeyConstraint('id'),
    comment='活动表'
    )
    op.create_index(op.f('ix_hb_activities_key_id'), 'hb_activities', ['key_id'], unique=False)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f('ix_hb_activities_key_id'), table_name='hb_activities')
    op.drop_table('hb_activities')
    op.drop_index(op.f('ix_hb_keys_user_id'), table_name='hb_keys')
    op.drop_index(op.f('ix_hb_keys_service_id'), table_name='hb_keys')
    op.drop_index(op.f('ix_hb_keys_app_id'), table_name='hb_keys')
    op.drop_table('hb_keys')
    op.drop_index(op.f('ix_hb_services_name'), table_name='hb_services')
    op.drop_index(op.f('ix_hb_services_code'), table_name='hb_services')
    op.drop_table('hb_services')
    op.drop_index(op.f('ix_hb_apps_name'), table_name='hb_apps')
    op.drop_table('hb_apps')
    op.drop_table('hb_admins')
    # ### end Alembic commands ###
