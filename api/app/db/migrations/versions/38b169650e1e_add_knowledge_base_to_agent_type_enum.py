"""add knowledge-base to agent type enum

Revision ID: 38b169650e1e
Revises: bc056203e691
Create Date: 2025-05-16 19:31:11.931210

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision: str = '38b169650e1e'
down_revision: Union[str, None] = 'bc056203e691'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    sa.Enum('CREATE_AGENT', 'SINGLE_CHAT', name='privilege_type_enum').drop(op.get_bind())
    sa.Enum('AGENT', 'COURSE', 'COURSE_SECTION', name='resource_type_enum').drop(op.get_bind())
    sa.Enum('VIEW', 'USE', 'DOWNLOAD', 'SHARE', 'COMPLETE', 'CHAT', name='action_type_enum').drop(op.get_bind())
    sa.Enum('USER', 'USER_ASSET', 'PLATFORM', name='account_owner_type_enum').drop(op.get_bind())
    sa.Enum('CJS', 'CNY', 'USD', name='currency_type_enum').drop(op.get_bind())
    op.sync_enum_values(  # type: ignore[attr-defined]
        enum_schema='public',
        enum_name='agent_type_enum',
        new_values=['CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION', 'BUNDLE', 'KNOWLEDGE_BASE'],
        affected_columns=[TableReference(table_schema='public', table_name='qu_agents', column_name='type')],
        enum_values_to_rename=[],
    )
    op.sync_enum_values(  # type: ignore[attr-defined]
        enum_schema='public',
        enum_name='app_mode_enum',
        new_values=['CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION', 'BUNDLE', 'KNOWLEDGE_BASE'],
        affected_columns=[TableReference(table_schema='public', table_name='qu_user_assets', column_name='app_mode')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(  # type: ignore[attr-defined]
        enum_schema='public',
        enum_name='app_mode_enum',
        new_values=['CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION'],
        affected_columns=[TableReference(table_schema='public', table_name='qu_user_assets', column_name='app_mode')],
        enum_values_to_rename=[],
    )
    op.sync_enum_values(  # type: ignore[attr-defined]
        enum_schema='public',
        enum_name='agent_type_enum',
        new_values=['CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION'],
        affected_columns=[TableReference(table_schema='public', table_name='qu_agents', column_name='type')],
        enum_values_to_rename=[],
    )
    sa.Enum('CJS', 'CNY', 'USD', name='currency_type_enum').create(op.get_bind())
    sa.Enum('USER', 'USER_ASSET', 'PLATFORM', name='account_owner_type_enum').create(op.get_bind())
    sa.Enum('VIEW', 'USE', 'DOWNLOAD', 'SHARE', 'COMPLETE', 'CHAT', name='action_type_enum').create(op.get_bind())
    sa.Enum('AGENT', 'COURSE', 'COURSE_SECTION', name='resource_type_enum').create(op.get_bind())
    sa.Enum('CREATE_AGENT', 'SINGLE_CHAT', name='privilege_type_enum').create(op.get_bind())
    # ### end Alembic commands ###
