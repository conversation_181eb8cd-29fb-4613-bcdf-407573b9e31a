"""empty message

Revision ID: d3be87745020
Revises: 6aafd9216fce
Create Date: 2025-03-21 11:10:14.296607

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'd3be87745020'
down_revision: Union[str, None] = '6aafd9216fce'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('qu_users', sa.Column('nickname', sa.String(length=50), nullable=True, comment='昵称'))
    op.add_column('qu_users', sa.Column('comment', sa.String(length=255), nullable=True, comment='备注'))
    op.add_column('qu_users', sa.Column('tags', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='标签列表'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('qu_users', 'tags')
    op.drop_column('qu_users', 'comment')
    op.drop_column('qu_users', 'nickname')
    # ### end Alembic commands ###
