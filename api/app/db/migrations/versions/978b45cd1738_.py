"""empty message

Revision ID: 978b45cd1738
Revises: 741d0773fe5c
Create Date: 2025-04-11 11:29:33.548427

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '978b45cd1738'
down_revision: Union[str, None] = '741d0773fe5c'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('qu_rates',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('src_currency', sa.Enum('CJS', 'CNY', 'USD', name='currency_type_enum'), nullable=True, comment='源货币类型'),
    sa.Column('target_currency', sa.Enum('CJS', 'CNY', 'USD', name='currency_type_enum'), nullable=True, comment='目标货币类型'),
    sa.Column('multiple', sa.Float(), nullable=True, comment='倍数'),
    sa.Column('description', sa.String(length=255), nullable=True, comment='说明'),
    sa.Column('is_active', sa.Boolean(), nullable=True, comment='是否激活'),
    sa.Column('created_at', sa.DateTime(), nullable=True, comment='创建时间'),
    sa.Column('updated_at', sa.DateTime(), nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('src_currency', 'target_currency', name='uq_rate_currency_pair'),
    comment='费率设置表'
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('qu_rates')
    # ### end Alembic commands ###
