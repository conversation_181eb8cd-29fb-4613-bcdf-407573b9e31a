"""empty message

Revision ID: 055b4d41c119
Revises: 44b994bb4a0e
Create Date: 2025-05-16 23:49:07.745459

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = '055b4d41c119'
down_revision: Union[str, None] = '44b994bb4a0e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('qu_agents', sa.Column('bundle_agent_ids', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='捆绑包包含的智能体ID列表'))
    op.drop_column('qu_agents', 'yearly_price')
    op.drop_column('qu_agents', 'original_monthly_price')
    op.drop_column('qu_agents', 'original_yearly_price')
    op.drop_column('qu_agents', 'monthly_price')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('qu_agents', sa.Column('monthly_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='月付价格'))
    op.add_column('qu_agents', sa.Column('original_yearly_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='年付原价'))
    op.add_column('qu_agents', sa.Column('original_monthly_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='月付原价'))
    op.add_column('qu_agents', sa.Column('yearly_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='年付价格'))
    op.drop_column('qu_agents', 'bundle_agent_ids')
    # ### end Alembic commands ###
