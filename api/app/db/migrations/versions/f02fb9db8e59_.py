"""empty message

Revision ID: f02fb9db8e59
Revises: d3be87745020
Create Date: 2025-03-31 13:06:33.770259

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "f02fb9db8e59"
down_revision: Union[str, None] = "d3be87745020"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "qu_user_usages",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("user_id", sa.Integer(), nullable=True, comment="用户ID"),
        sa.Column(
            "resource_type",
            sa.Enum("AGENT", "COURSE", "COURSE_SECTION", name="resource_type_enum"),
            nullable=True,
            comment="资源类型",
        ),
        sa.Column("resource_id", sa.String(), nullable=True, comment="资源ID"),
        sa.Column(
            "action_type",
            sa.Enum(
                "VIEW",
                "USE",
                "DOWNLOAD",
                "SHARE",
                "COMPLETE",
                "CHAT",
                name="action_type_enum",
            ),
            nullable=True,
            comment="动作类型",
        ),
        sa.Column("duration", sa.Integer(), nullable=True, comment="使用时长(秒)"),
        sa.Column("session_id", sa.String(), nullable=True, comment="会话ID"),
        sa.Column("tokens_used", sa.Integer(), nullable=True, comment="使用的令牌数"),
        sa.Column("progress", sa.Float(), nullable=True, comment="进度百分比"),
        sa.Column(
            "meta_data",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="元数据",
        ),
        sa.Column("created_at", sa.DateTime(), nullable=True, comment="创建时间"),
        sa.Column("updated_at", sa.DateTime(), nullable=True, comment="更新时间"),
        sa.Column("ip_address", sa.String(), nullable=True, comment="IP地址"),
        sa.Column("device_info", sa.String(), nullable=True, comment="设备信息"),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["qu_users.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
        comment="用户用量表",
    )
    op.create_index(
        op.f("ix_qu_user_usages_resource_id"),
        "qu_user_usages",
        ["resource_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_qu_user_usages_user_id"), "qu_user_usages", ["user_id"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_qu_user_usages_user_id"), table_name="qu_user_usages")
    op.drop_index(op.f("ix_qu_user_usages_resource_id"), table_name="qu_user_usages")
    op.drop_table("qu_user_usages")
    # ### end Alembic commands ###
