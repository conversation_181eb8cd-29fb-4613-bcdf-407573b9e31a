"""empty message

Revision ID: 8c02f3a3cb46
Revises: beccf723b850
Create Date: 2025-04-21 05:00:29.750759

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "8c02f3a3cb46"
down_revision: Union[str, None] = "beccf723b850"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # 先创建枚举类型
    op.execute("CREATE TYPE currency_type_enum AS ENUM ('CNY', 'USD', 'LTC', 'UTS')")

    # 然后添加使用该枚举类型的列
    op.add_column(
        "hb_keys",
        sa.Column(
            "currency_type",
            sa.Enum("CNY", "USD", "LTC", "UTS", name="currency_type_enum"),
            nullable=True,
            comment="信用货币类型",
        ),
    )
    op.alter_column(
        "hb_keys",
        "instance_id",
        existing_type=sa.VARCHAR(length=50),
        type_=sa.Text(),
        comment="实例ID，可能是多个ID以逗号隔开",
        existing_comment="实例ID",
        existing_nullable=True,
    )


def downgrade() -> None:
    # 先删除使用该枚举类型的列
    op.alter_column(
        "hb_keys",
        "instance_id",
        existing_type=sa.Text(),
        type_=sa.VARCHAR(length=50),
        comment="实例ID",
        existing_comment="实例ID，可能是多个ID以逗号隔开",
        existing_nullable=True,
    )
    op.drop_column("hb_keys", "currency_type")

    # 然后删除枚举类型
    op.execute("DROP TYPE currency_type_enum")
