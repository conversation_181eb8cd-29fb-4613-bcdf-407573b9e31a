"""empty message

Revision ID: 1e9cfc25b94f
Revises: 8e17e9278438
Create Date: 2025-04-22 03:20:58.931642

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1e9cfc25b94f"
down_revision: Union[str, None] = "8e17e9278438"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "hb_activities",
        sa.Column("app_id", sa.String(length=6), nullable=True, comment="应用ID"),
    )
    op.add_column(
        "hb_activities",
        sa.Column("user_id", sa.String(length=50), nullable=True, comment="用户ID"),
    )
    op.add_column(
        "hb_activities",
        sa.Column("service_id", sa.Integer(), nullable=True, comment="服务ID"),
    )
    op.add_column(
        "hb_activities",
        sa.Column(
            "instance_id",
            sa.Text(),
            nullable=True,
            comment="实例ID，可能是多个ID以逗号隔开",
        ),
    )
    op.add_column(
        "hb_activities",
        sa.Column(
            "currency_type",
            sa.Enum("CNY", "USD", "LTC", "UTS", name="currency_type_enum"),
            nullable=True,
            comment="信用货币类型",
        ),
    )
    op.create_index(
        op.f("ix_hb_activities_app_id"), "hb_activities", ["app_id"], unique=False
    )
    op.create_index(
        op.f("ix_hb_activities_instance_id"),
        "hb_activities",
        ["instance_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_hb_activities_service_id"),
        "hb_activities",
        ["service_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_hb_activities_user_id"), "hb_activities", ["user_id"], unique=False
    )
    op.create_foreign_key(None, "hb_activities", "hb_apps", ["app_id"], ["app_id"])
    op.create_foreign_key(None, "hb_activities", "hb_services", ["service_id"], ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "hb_activities", type_="foreignkey")
    op.drop_constraint(None, "hb_activities", type_="foreignkey")
    op.drop_index(op.f("ix_hb_activities_user_id"), table_name="hb_activities")
    op.drop_index(op.f("ix_hb_activities_service_id"), table_name="hb_activities")
    op.drop_index(op.f("ix_hb_activities_instance_id"), table_name="hb_activities")
    op.drop_index(op.f("ix_hb_activities_app_id"), table_name="hb_activities")
    op.drop_column("hb_activities", "currency_type")
    op.drop_column("hb_activities", "instance_id")
    op.drop_column("hb_activities", "service_id")
    op.drop_column("hb_activities", "user_id")
    op.drop_column("hb_activities", "app_id")
    # ### end Alembic commands ###
