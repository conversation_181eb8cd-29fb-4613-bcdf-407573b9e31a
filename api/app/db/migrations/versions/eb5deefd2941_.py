"""empty message

Revision ID: eb5deefd2941
Revises: 1e9cfc25b94f
Create Date: 2025-05-16 11:02:48.014866

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "eb5deefd2941"
down_revision: Union[str, None] = "1e9cfc25b94f"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "hb_activities",
        sa.Column(
            "scope",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="作用域，JSON数组格式",
        ),
    )
    op.drop_index("ix_hb_activities_instance_id", table_name="hb_activities")
    op.create_index(
        op.f("ix_hb_activities_scope"), "hb_activities", ["scope"], unique=False
    )
    op.drop_column("hb_activities", "instance_id")
    op.add_column(
        "hb_keys",
        sa.Column(
            "scope",
            postgresql.JSONB(astext_type=sa.Text()),
            nullable=True,
            comment="作用域，JSON数组格式",
        ),
    )
    op.drop_index("ix_hb_keys_instance_id", table_name="hb_keys")
    op.create_index(op.f("ix_hb_keys_scope"), "hb_keys", ["scope"], unique=False)
    op.drop_column("hb_keys", "instance_id")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "hb_keys",
        sa.Column(
            "instance_id",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="实例ID，可能是多个ID以逗号隔开",
        ),
    )
    op.drop_index(op.f("ix_hb_keys_scope"), table_name="hb_keys")
    op.create_index("ix_hb_keys_instance_id", "hb_keys", ["instance_id"], unique=False)
    op.drop_column("hb_keys", "scope")
    op.add_column(
        "hb_activities",
        sa.Column(
            "instance_id",
            sa.TEXT(),
            autoincrement=False,
            nullable=True,
            comment="实例ID，可能是多个ID以逗号隔开",
        ),
    )
    op.drop_index(op.f("ix_hb_activities_scope"), table_name="hb_activities")
    op.create_index(
        "ix_hb_activities_instance_id", "hb_activities", ["instance_id"], unique=False
    )
    op.drop_column("hb_activities", "scope")
    # ### end Alembic commands ###
