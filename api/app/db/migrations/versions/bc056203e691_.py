"""empty message

Revision ID: bc056203e691
Revises: 978b45cd1738
Create Date: 2025-04-21 07:15:37.707244

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = 'bc056203e691'
down_revision: Union[str, None] = '978b45cd1738'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('qu_rates')
    op.drop_table('qu_accounts')
    op.drop_index('ix_qu_user_usages_resource_id', table_name='qu_user_usages')
    op.drop_index('ix_qu_user_usages_user_id', table_name='qu_user_usages')
    op.drop_table('qu_user_usages')
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('qu_user_usages',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='用户ID'),
    sa.Column('resource_type', postgresql.ENUM('AGENT', 'COURSE', 'COURSE_SECTION', name='resource_type_enum'), autoincrement=False, nullable=True, comment='资源类型'),
    sa.Column('resource_id', sa.VARCHAR(), autoincrement=False, nullable=True, comment='资源ID'),
    sa.Column('action_type', postgresql.ENUM('VIEW', 'USE', 'DOWNLOAD', 'SHARE', 'COMPLETE', 'CHAT', name='action_type_enum'), autoincrement=False, nullable=True, comment='动作类型'),
    sa.Column('duration', sa.INTEGER(), autoincrement=False, nullable=True, comment='使用时长(秒)'),
    sa.Column('session_id', sa.VARCHAR(), autoincrement=False, nullable=True, comment='会话ID'),
    sa.Column('tokens_used', sa.INTEGER(), autoincrement=False, nullable=True, comment='使用的令牌数'),
    sa.Column('progress', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='进度百分比'),
    sa.Column('meta_data', postgresql.JSONB(astext_type=sa.Text()), autoincrement=False, nullable=True, comment='元数据'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.Column('ip_address', sa.VARCHAR(), autoincrement=False, nullable=True, comment='IP地址'),
    sa.Column('device_info', sa.VARCHAR(), autoincrement=False, nullable=True, comment='设备信息'),
    sa.Column('total_price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='总价格'),
    sa.Column('currency', sa.VARCHAR(), autoincrement=False, nullable=True, comment='货币'),
    sa.ForeignKeyConstraint(['user_id'], ['qu_users.id'], name='qu_user_usages_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='qu_user_usages_pkey'),
    comment='用户用量表'
    )
    op.create_index('ix_qu_user_usages_user_id', 'qu_user_usages', ['user_id'], unique=False)
    op.create_index('ix_qu_user_usages_resource_id', 'qu_user_usages', ['resource_id'], unique=False)
    op.create_table('qu_accounts',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('owner_type', postgresql.ENUM('USER', 'USER_ASSET', 'PLATFORM', name='account_owner_type_enum'), autoincrement=False, nullable=True, comment='账户归属类型'),
    sa.Column('asset_type', postgresql.ENUM('APP', 'COURSE', name='asset_type_enum'), autoincrement=False, nullable=True, comment='资产类型'),
    sa.Column('asset_id', sa.VARCHAR(), autoincrement=False, nullable=True, comment='资产ID'),
    sa.Column('user_id', sa.INTEGER(), autoincrement=False, nullable=True, comment='用户ID'),
    sa.Column('balance', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='账户余额'),
    sa.Column('currency', sa.VARCHAR(length=3), autoincrement=False, nullable=True, comment='货币类型'),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否激活'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.Column('remaining_experience_count', sa.INTEGER(), autoincrement=False, nullable=True, comment='剩余体验次数'),
    sa.Column('remaining_tokens', sa.INTEGER(), autoincrement=False, nullable=True, comment='剩余token数量'),
    sa.ForeignKeyConstraint(['user_id'], ['qu_users.id'], name='qu_accounts_user_id_fkey'),
    sa.PrimaryKeyConstraint('id', name='qu_accounts_pkey'),
    sa.UniqueConstraint('owner_type', 'user_id', 'asset_type', 'asset_id', name='qu_accounts_owner_type_user_id_asset_type_asset_id_key'),
    comment='账户表'
    )
    op.create_table('qu_rates',
    sa.Column('id', sa.INTEGER(), autoincrement=True, nullable=False),
    sa.Column('src_currency', postgresql.ENUM('CJS', 'CNY', 'USD', name='currency_type_enum'), autoincrement=False, nullable=True, comment='源货币类型'),
    sa.Column('target_currency', postgresql.ENUM('CJS', 'CNY', 'USD', name='currency_type_enum'), autoincrement=False, nullable=True, comment='目标货币类型'),
    sa.Column('multiple', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='倍数'),
    sa.Column('description', sa.VARCHAR(length=255), autoincrement=False, nullable=True, comment='说明'),
    sa.Column('is_active', sa.BOOLEAN(), autoincrement=False, nullable=True, comment='是否激活'),
    sa.Column('created_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='创建时间'),
    sa.Column('updated_at', postgresql.TIMESTAMP(), autoincrement=False, nullable=True, comment='更新时间'),
    sa.PrimaryKeyConstraint('id', name='qu_rates_pkey'),
    sa.UniqueConstraint('src_currency', 'target_currency', name='uq_rate_currency_pair'),
    comment='费率设置表'
    )
    # ### end Alembic commands ###
