"""empty message

Revision ID: 30650b245a91
Revises: 38b169650e1e
Create Date: 2025-05-16 19:36:45.184275

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from alembic_postgresql_enum import TableReference

# revision identifiers, used by Alembic.
revision: str = '30650b245a91'
down_revision: Union[str, None] = '38b169650e1e'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(  # type: ignore[attr-defined]
        enum_schema='public',
        enum_name='agent_type_enum',
        new_values=['CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION', 'BUNDLE'],
        affected_columns=[TableReference(table_schema='public', table_name='qu_agents', column_name='type')],
        enum_values_to_rename=[],
    )
    op.sync_enum_values(  # type: ignore[attr-defined]
        enum_schema='public',
        enum_name='app_mode_enum',
        new_values=['CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION', 'BUNDLE'],
        affected_columns=[TableReference(table_schema='public', table_name='qu_user_assets', column_name='app_mode')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.sync_enum_values(  # type: ignore[attr-defined]
        enum_schema='public',
        enum_name='app_mode_enum',
        new_values=['CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION', 'BUNDLE', 'KNOWLEDGE_BASE'],
        affected_columns=[TableReference(table_schema='public', table_name='qu_user_assets', column_name='app_mode')],
        enum_values_to_rename=[],
    )
    op.sync_enum_values(  # type: ignore[attr-defined]
        enum_schema='public',
        enum_name='agent_type_enum',
        new_values=['CHAT', 'AGENT_CHAT', 'WORKFLOW', 'COMPLETION', 'BUNDLE', 'KNOWLEDGE_BASE'],
        affected_columns=[TableReference(table_schema='public', table_name='qu_agents', column_name='type')],
        enum_values_to_rename=[],
    )
    # ### end Alembic commands ###
