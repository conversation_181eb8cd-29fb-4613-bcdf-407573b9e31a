"""empty message

Revision ID: 1752d4267263
Revises: 0781903a8dfe
Create Date: 2025-04-06 22:31:37.125891

"""

from typing import Sequence, Union

from alembic import op

# revision identifiers, used by Alembic.
revision: str = "1752d4267263"
down_revision: Union[str, None] = "0781903a8dfe"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_qu_accounts_user_id", table_name="qu_accounts")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_index("ix_qu_accounts_user_id", "qu_accounts", ["user_id"], unique=True)
    # ### end Alembic commands ###
