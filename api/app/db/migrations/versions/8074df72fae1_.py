"""empty message

Revision ID: 8074df72fae1
Revises: 1752d4267263
Create Date: 2025-04-11 05:55:38.789983

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '8074df72fae1'
down_revision: Union[str, None] = '1752d4267263'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('qu_agents', sa.Column('is_public', sa.<PERSON>(), nullable=True, comment='是否公开'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('qu_agents', 'is_public')
    # ### end Alembic commands ###
