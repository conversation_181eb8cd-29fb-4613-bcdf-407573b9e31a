"""empty message

Revision ID: feead4de3fea
Revises: 1a8647bf32c6
Create Date: 2025-04-01 01:00:36.648212

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "feead4de3fea"
down_revision: Union[str, None] = "1a8647bf32c6"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index("ix_qu_user_privileges_user_id", table_name="qu_user_privileges")
    op.drop_table("qu_user_privileges")
    op.drop_column("qu_user_accounts", "remaining_chats")
    op.drop_column("qu_user_accounts", "remaining_tokens")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "qu_user_accounts",
        sa.Column(
            "remaining_tokens",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="剩余token数量",
        ),
    )
    op.add_column(
        "qu_user_accounts",
        sa.Column(
            "remaining_chats",
            sa.INTEGER(),
            autoincrement=False,
            nullable=True,
            comment="剩余对话次数",
        ),
    )
    op.create_table(
        "qu_user_privileges",
        sa.Column("id", sa.INTEGER(), autoincrement=True, nullable=False),
        sa.Column("user_id", sa.INTEGER(), autoincrement=False, nullable=True),
        sa.Column(
            "privilege_type",
            postgresql.ENUM("CREATE_AGENT", "SINGLE_CHAT", name="privilege_type_enum"),
            autoincrement=False,
            nullable=True,
            comment="权益类型",
        ),
        sa.Column(
            "value", sa.INTEGER(), autoincrement=False, nullable=True, comment="权益值"
        ),
        sa.Column(
            "expires_at",
            postgresql.TIMESTAMP(),
            autoincrement=False,
            nullable=True,
            comment="过期时间",
        ),
        sa.Column(
            "created_at",
            postgresql.TIMESTAMP(),
            autoincrement=False,
            nullable=True,
            comment="创建时间",
        ),
        sa.Column(
            "updated_at",
            postgresql.TIMESTAMP(),
            autoincrement=False,
            nullable=True,
            comment="更新时间",
        ),
        sa.ForeignKeyConstraint(
            ["user_id"], ["qu_users.id"], name="qu_user_privileges_user_id_fkey"
        ),
        sa.PrimaryKeyConstraint("id", name="qu_user_privileges_pkey"),
        sa.UniqueConstraint(
            "user_id", "privilege_type", name="uix_user_privilege_type"
        ),
        comment="用户权益表",
    )
    op.create_index(
        "ix_qu_user_privileges_user_id", "qu_user_privileges", ["user_id"], unique=False
    )
    # ### end Alembic commands ###
