"""empty message

Revision ID: 1a8647bf32c6
Revises: 9aac5f774f67
Create Date: 2025-04-01 00:43:23.063903

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision: str = "1a8647bf32c6"
down_revision: Union[str, None] = "9aac5f774f67"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "qu_user_accounts",
        sa.Column(
            "remaining_chats", sa.Integer(), nullable=True, comment="剩余对话次数"
        ),
    )
    op.add_column(
        "qu_user_accounts",
        sa.<PERSON>umn(
            "can_create_agent",
            sa.<PERSON>(),
            nullable=True,
            comment="是否允许创建智能体",
        ),
    )
    op.add_column(
        "qu_user_accounts",
        sa.Column(
            "remaining_tokens", sa.Integer(), nullable=True, comment="剩余token数量"
        ),
    )
    op.drop_column("qu_user_assets", "quantity")
    op.drop_column("qu_user_assets", "expire_at")
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "qu_user_assets",
        sa.Column(
            "expire_at",
            postgresql.TIMESTAMP(),
            autoincrement=False,
            nullable=True,
            comment="有效期至",
        ),
    )
    op.add_column(
        "qu_user_assets",
        sa.Column(
            "quantity", sa.INTEGER(), autoincrement=False, nullable=True, comment="数量"
        ),
    )
    op.drop_column("qu_user_accounts", "remaining_tokens")
    op.drop_column("qu_user_accounts", "can_create_agent")
    op.drop_column("qu_user_accounts", "remaining_chats")
    # ### end Alembic commands ###
