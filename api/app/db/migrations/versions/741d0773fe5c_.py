"""empty message

Revision ID: 741d0773fe5c
Revises: 8074df72fae1
Create Date: 2025-04-11 08:39:04.595248

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = '741d0773fe5c'
down_revision: Union[str, None] = '8074df72fae1'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('qu_accounts', sa.Column('remaining_experience_count', sa.Integer(), nullable=True, comment='剩余体验次数'))
    op.add_column('qu_accounts', sa.Column('remaining_tokens', sa.Integer(), nullable=True, comment='剩余token数量'))
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('qu_accounts', 'remaining_tokens')
    op.drop_column('qu_accounts', 'remaining_experience_count')
    # ### end Alembic commands ###
