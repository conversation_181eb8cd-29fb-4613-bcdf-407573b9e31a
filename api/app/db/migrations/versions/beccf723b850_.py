"""empty message

Revision ID: beccf723b850
Revises: 5f32d621d9e5
Create Date: 2025-04-21 01:52:20.826717

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "beccf723b850"
down_revision: Union[str, None] = "5f32d621d9e5"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "hb_keys",
        sa.Column("instance_id", sa.String(length=50), nullable=True, comment="实例ID"),
    )
    op.create_index(
        op.f("ix_hb_keys_instance_id"), "hb_keys", ["instance_id"], unique=False
    )
    op.create_unique_constraint(None, "hb_keys", ["id"])
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, "hb_keys", type_="unique")
    op.drop_index(op.f("ix_hb_keys_instance_id"), table_name="hb_keys")
    op.drop_column("hb_keys", "instance_id")
    # ### end Alembic commands ###
