"""empty message

Revision ID: 92a367a93d49
Revises: feead4de3fea
Create Date: 2025-04-01 22:40:44.542025

"""

from typing import Sequence, Union

import sqlalchemy as sa
from alembic import op

# revision identifiers, used by Alembic.
revision: str = "92a367a93d49"
down_revision: Union[str, None] = "feead4de3fea"
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column(
        "qu_agents",
        sa.Column(
            "sort_order",
            sa.Integer(),
            nullable=True,
            comment="排序顺序，数字越小越靠前",
        ),
    )
    op.create_index(
        op.f("ix_qu_agents_sort_order"), "qu_agents", ["sort_order"], unique=False
    )
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_index(op.f("ix_qu_agents_sort_order"), table_name="qu_agents")
    op.drop_column("qu_agents", "sort_order")
    # ### end Alembic commands ###
