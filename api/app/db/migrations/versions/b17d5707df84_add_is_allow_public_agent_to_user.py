"""add_is_allow_public_agent_to_user

Revision ID: b17d5707df84
Revises: 055b4d41c119
Create Date: 2025-05-27 16:15:21.125227

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision: str = 'b17d5707df84'
down_revision: Union[str, None] = '055b4d41c119'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('qu_courses', 'price')

    # 检查字段是否已存在，如果不存在才添加
    connection = op.get_bind()
    result = connection.execute(sa.text("""
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name='qu_users' AND column_name='is_allow_public_agent'
    """))

    if not result.fetchone():
        op.add_column('qu_users', sa.Column('is_allow_public_agent', sa.BOOLEAN(), nullable=True, comment='是否允许公开智能体'))
    # ### end Alembic commands ###

def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('qu_users', 'is_allow_public_agent')
    op.add_column('qu_courses', sa.Column('price', sa.DOUBLE_PRECISION(precision=53), autoincrement=False, nullable=True, comment='价格'))
    # ### end Alembic commands ###
