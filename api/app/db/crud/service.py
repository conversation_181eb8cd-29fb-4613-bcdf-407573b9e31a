from typing import Optional

from sqlmodel import Session, select

from app.db.models import Service
from .base import BaseCrud


class ServiceCrud(BaseCrud[Service]):
    """服务CRUD操作类"""

    def __init__(self, session: Session):
        super().__init__(Service, session)

    def get_by_code(self, code: str) -> Optional[Service]:
        """通过服务代码获取服务"""
        statement = select(Service).where(Service.code == code)
        return self.session.exec(statement).first()
