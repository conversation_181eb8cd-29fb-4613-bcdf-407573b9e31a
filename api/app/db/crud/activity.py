from typing import Dict, List, Optional, Any

from sqlmodel import Session, select

from ..models import Activity
from .base import BaseCrud


class ActivityCrud(BaseCrud[Activity]):
    """活动CRUD操作类"""

    def __init__(self, session: Session):
        super().__init__(Activity, session)

    def create_activity(
        self,
        key_id: str,
        activity_type: Activity.Type,
        amount: float,
        details: Optional[Dict[str, Any]] = None
    ) -> Activity:
        """创建活动"""
        activity = Activity(
            key_id=key_id,
            type=activity_type,
            amount=amount,
            details=details
        )
        return self.save(activity)

    def get_activities_by_key_id(
        self,
        key_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[Activity]:
        """获取密钥的活动"""
        statement = select(Activity).where(Activity.key_id == key_id).offset(skip).limit(limit)
        return self.session.exec(statement).all()
