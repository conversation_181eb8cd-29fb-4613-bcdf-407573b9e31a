import json
from datetime import datetime
from typing import Any, Dict, List, Optional

from sqlalchemy import text
from sqlmodel import Session, select

from ..models import Activity, Key
from .activity import ActivityCrud
from .base import BaseCrud


class KeyCrud(BaseCrud[Key]):
    """密钥CRUD操作类"""

    def __init__(self, session: Session):
        super().__init__(Key, session)

    def get_by_app_user_service(
        self, app_id: str, user_id: str, service_id: int
    ) -> Optional[Key]:
        """通过应用ID、用户ID和服务ID获取密钥"""
        statement = select(Key).where(
            Key.app_id == app_id, Key.user_id == user_id, Key.service_id == service_id
        )
        return self.session.exec(statement).first()

    def get_available_keys(
        self,
        app_id: str,
        user_id: Optional[str] = None,
        service_code: Optional[str] = None,
        scope: Optional[List[str]] = None,
        currency_type: Optional[str] = None,
    ) -> List[Key]:
        """获取可用密钥列表

        Args:
            app_id: 应用ID
            user_id: 用户ID（可选）
            service_code: 服务代码（可选）
            scope: 作用域（可选）
            currency_type: 信用货币类型（可选）

        Returns:
            可用密钥列表
        """
        # 构建查询条件
        from ..models import Service  # Moved import here

        conditions = [Key.app_id == app_id]

        # 添加可选条件
        if user_id is not None:
            conditions.append(Key.user_id == user_id)
        if service_code is not None:
            # 如果提供了服务代码，需要连接Service表进行查询

            conditions.append(Key.service_id == Service.id)
            conditions.append(Service.code == service_code)
        if scope is not None:
            # 使用 PostgreSQL 的 <@ 操作符（子集）
            # 检查查询作用域是否是密钥作用域的子集
            # 这样，当密钥作用域是[1,2,3]，查询作用域是[1,2]时，会返回这个密钥
            # 但当密钥作用域是[1,2,3]，查询作用域是[2,4]时，不会返回这个密钥，因为4不在密钥作用域中

            # 使用 PostgreSQL 的 <@ 操作符检查查询作用域是否是密钥作用域的子集
            # 对查询作用域进行排序，确保元素顺序不会影响结果
            sorted_scope = sorted(scope)
            scope_json = json.dumps(sorted_scope)
            # 使用命名参数，确保参数名在 SQL 文本和 bindparams 中匹配
            conditions.append(
                text("cast(:scope_param as jsonb) <@ hb_keys.scope").bindparams(
                    scope_param=scope_json
                )
            )
        if currency_type is not None:
            conditions.append(Key.currency_type == currency_type)

        # 添加过期时间条件：未过期或无过期时间
        now = datetime.now()
        conditions.append((Key.expires_at.is_(None)) | (Key.expires_at > now))

        # 添加额度条件：可用额度大于0
        conditions.append(Key.credit_limit > Key.credit_used)

        # 连接Service表，以便返回关联的Service对象
        statement = (
            select(Key).join(Service, Key.service_id == Service.id).where(*conditions)
        )

        # 执行查询
        return self.session.exec(statement).all()

    def get_key_by_id(self, key_id: str) -> Optional[Key]:
        """通过ID获取密钥"""
        return self.find_by_id(key_id)

    def update_key_credit_used(self, key_id: str, amount: float) -> Key:
        """更新密钥已使用额度"""
        key = self.find_by_id(key_id)
        if key:
            key.credit_used += amount
            self.save(key)
        return key

    def record_consume_activity(
        self, key_id: str, amount: float, details: Optional[Dict[str, Any]] = None
    ) -> None:
        """记录消费活动"""
        # 获取密钥信息
        key = self.find_by_id(key_id)
        if not key:
            return

        activity_crud = ActivityCrud(self.session)

        # 创建活动记录，包含与密钥相同的详细字段
        activity = Activity(
            key_id=key_id,
            type=Activity.Type.CONSUME,
            amount=amount,
            details=details,
            app_id=key.app_id,
            user_id=key.user_id,
            service_id=key.service_id,
            scope=key.scope,
            currency_type=key.currency_type,
        )
        activity_crud.save(activity)

    def delete_key(self, key_id: str) -> bool:
        """删除密钥

        Args:
            key_id: 密钥ID

        Returns:
            bool: 是否成功删除
        """
        key = self.find_by_id(key_id)
        if not key:
            return False

        self.session.delete(key)
        self.session.commit()
        return True
