from typing import Generator

from app.core import settings
from sqlalchemy import create_engine
from sqlmodel import Session

database_url = settings.database_url
engine = create_engine(
    database_url,
    pool_size=30,  # 连接池保持的最大连接数
    max_overflow=30,  # 超出pool_size后允许创建的最大连接数
    pool_timeout=60,  # 获取连接的超时时间(秒)
    pool_recycle=600,  # 600秒(10分钟)后回收连接
    pool_pre_ping=True,  # 启用连接健康检查
    echo=settings.sql_echo,  # 启用SQL语句打印，根据配置决定是否开启
)


def get_session() -> Generator[Session, None, None]:
    """
    获取数据库会话的依赖函数
    """
    with Session(engine) as session:
        try:
            yield session
        finally:
            session.close()
