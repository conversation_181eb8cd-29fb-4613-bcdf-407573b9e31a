from datetime import datetime

from app.core.exceptions import InvalidKeyException, KeyExpiredException
from app.db import get_session
from app.db.models import App, Key, Service
from fastapi import Depends, Header
from sqlmodel import Session, select


async def get_admin_app(
    x_admin_key: str = Header(..., description="管理密钥"),
    session: Session = Depends(get_session),
) -> App:
    """
    从HTTP header中获取管理密钥，验证其有效性，并返回关联的应用

    Args:
        x_admin_key: HTTP header中的管理密钥
        session: 数据库会话

    Returns:
        App: 管理密钥关联的应用

    Raises:
        HTTPException: 当密钥无效、过期或不是管理密钥时
    """
    # 查询密钥
    statement = (
        select(Key)
        .join(Service)
        .where(
            Key.id == x_admin_key,
            Service.code == "HKM",  # 服务代码为HKM的是管理密钥
        )
    )
    key = session.exec(statement).first()

    # 验证密钥是否存在
    if not key:
        raise InvalidKeyException(key_id=x_admin_key)

    # 验证密钥是否过期
    if key.expires_at and key.expires_at < datetime.now():
        raise KeyExpiredException(key_id=x_admin_key)

    # 获取关联的应用
    app = key.app
    if not app:
        raise InvalidKeyException(key_id=x_admin_key)

    return app
