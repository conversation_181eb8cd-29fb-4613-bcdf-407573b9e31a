from datetime import datetime
from typing import List

from app.core.exceptions import (
    KeyNotFoundException,
    PermissionDeniedException,
    ServiceNotFoundException,
)
from app.core.schemas import ResponsePayloads
from app.db import get_session
from app.db.crud import KeyCrud
from app.db.crud.service import ServiceCrud
from app.db.models import App, Key
from billing.keys import (
    KeyConsumeRequest,
    KeyCreate,
    KeyDeleteResponse,
    KeyListResponse,
    KeyResponse,
)
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session

from ..billing import BillingStrategyFactory
from .dependencies import get_admin_app

router = APIRouter(prefix="/keys", tags=["密钥管理"])


@router.post("", summary="创建密钥", response_model=ResponsePayloads[KeyResponse])
async def create_key(
    key_create: KeyCreate,
    admin_app: App = Depends(get_admin_app),
    session: Session = Depends(get_session),
):
    """创建密钥"""
    # 创建密钥
    key_crud = KeyCrud(session)
    service_crud = ServiceCrud(session)
    if key_create.service_code == "HKM":
        raise PermissionDeniedException(status_code=400, detail="不能创建管理密钥")
    service = service_crud.get_by_code(key_create.service_code)
    if not service:
        raise ServiceNotFoundException(service_code=key_create.service_code)
    key = Key(
        app_id=admin_app.app_id,
        user_id=key_create.user_id,
        service_id=service.id,
        scope=key_create.scope,
        currency_type=key_create.currency_type,
        credit_limit=key_create.credit_limit,
        expires_at=key_create.expires_at,
    )
    key = key_crud.save(key)

    # 将 Key 模型实例转换为 KeyResponse 实例
    key_response = KeyResponse(
        id=key.id,
        app_id=key.app_id,
        user_id=key.user_id,
        service_code=service.code,  # 服务代码
        currency_type=key.currency_type,  # 信用货币类型
        scope=key.scope,
        credit_limit=key.credit_limit,
        credit_used=key.credit_used,
        expires_at=key.expires_at,
        created_at=key.created_at,
        updated_at=key.updated_at,
    )

    return ResponsePayloads(data=key_response)


@router.get(
    "/available",
    summary="获取可用密钥列表",
    response_model=ResponsePayloads[KeyListResponse],
)
async def list_available_keys(
    user_id: str = None,
    service_code: str = None,
    scope: List[str] = Query(None),  # 使用 Query 参数接收数组
    currency_type: str = None,
    admin_app: App = Depends(get_admin_app),
    session: Session = Depends(get_session),
):
    """获取可用密钥列表

    Args:
        user_id: 用户ID（可选）
        service_code: 服务代码（可选）
        scope: 作用域（可选）
        currency_type: 信用货币类型（可选）

    Returns:
        可用密钥列表
    """
    key_crud = KeyCrud(session)

    # 获取可用密钥列表
    keys = key_crud.get_available_keys(
        app_id=admin_app.app_id,
        user_id=user_id,
        service_code=service_code,
        scope=scope,  # 直接使用数组参数
        currency_type=currency_type,
    )

    # 将 Key 模型实例转换为 KeyResponse 实例
    key_responses = [
        KeyResponse(
            id=key.id,
            app_id=key.app_id,
            user_id=key.user_id,
            service_code=key.service.code,  # 服务代码
            currency_type=key.currency_type,  # 信用货币类型
            scope=key.scope,
            credit_limit=key.credit_limit,
            credit_used=key.credit_used,
            expires_at=key.expires_at,
            created_at=key.created_at,
            updated_at=key.updated_at,
        )
        for key in keys
    ]

    # 返回结果
    return ResponsePayloads(data=KeyListResponse(keys=key_responses))


@router.post("/consume", summary="消费密钥", response_model=ResponsePayloads[str])
async def consume_key(
    consume_request: KeyConsumeRequest,
    admin_app: App = Depends(get_admin_app),
    session: Session = Depends(get_session),
):
    """消费密钥

    根据密钥的货币类型和服务代码动态计算扣费金额：
    1. 使用次数扣费：适用于货币类型为UTS的密钥，每次消费减少1个单位
    2. 智能体聊天人民币扣费：适用于服务代码为AGC且货币类型为CNY的密钥，按token数量和单价计算
    3. 智能体聊天LLM Token Counts扣费：适用于服务代码为AGC且货币类型为LTC的密钥，直接使用token数量
    """
    key_crud = KeyCrud(session)
    key_id = consume_request.key_id
    amount = consume_request.amount
    details = consume_request.details

    # 获取密钥
    key = key_crud.get_key_by_id(key_id)
    if not key:
        return

    # 检查密钥是否过期
    if key.expires_at and key.expires_at < datetime.now():
        return

    # 使用策略模式计算实际扣费金额
    calculated_amount = BillingStrategyFactory.calculate_fee(key, details)

    # 如果策略计算结果为0，则使用请求中的amount
    # 这确保了向后兼容性，同时支持新的动态计费逻辑
    final_amount = calculated_amount if calculated_amount > 0 else (amount or 0.0)
    # 更新已使用额度
    key = key_crud.update_key_credit_used(key_id, final_amount)

    # 记录消费活动
    key_crud.record_consume_activity(key_id, final_amount, details)
    return ResponsePayloads(data="ok")


@router.delete(
    "/{key_id}", summary="删除密钥", response_model=ResponsePayloads[KeyDeleteResponse]
)
async def delete_key(
    key_id: str,
    admin_app: App = Depends(get_admin_app),
    session: Session = Depends(get_session),
):
    """删除密钥

    Args:
        key_id: 密钥ID

    Returns:
        删除结果
    """
    key_crud = KeyCrud(session)
    service_crud = ServiceCrud(session)

    # 获取密钥
    key = key_crud.get_key_by_id(key_id)
    if not key:
        raise KeyNotFoundException(key_id=key_id)

    # 检查是否为管理密钥
    service = service_crud.find_by_id(key.service_id)
    if service and service.code == "HKM":
        raise PermissionDeniedException(status_code=400, detail="不能删除管理密钥")

    # 密钥必须得是自己的
    if key.app_id != admin_app.app_id:
        raise PermissionDeniedException(
            status_code=400, detail="不能删除其他应用的密钥"
        )

    # 删除密钥
    success = key_crud.delete_key(key_id)

    if not success:
        raise KeyNotFoundException(key_id=key_id)

    # 返回结果
    return ResponsePayloads(data=KeyDeleteResponse(success=True, key_id=key_id))
