"""
密钥扣费策略模块

该模块实现了不同的密钥扣费策略，用于根据密钥的货币类型和服务代码动态计算扣费金额。
使用策略模式避免在consume_key函数中累积大量if-else逻辑。
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional

from app.db.models import Key


class BillingStrategy(ABC):
    """扣费策略基类"""

    @abstractmethod
    def calculate_fee(
        self, key: Key, details: Optional[Dict[str, Any]] = None
    ) -> float:
        """计算扣费金额

        Args:
            key: 密钥对象
            details: 消费详情

        Returns:
            float: 计算得到的扣费金额
        """
        pass


class UsageTimesStrategy(BillingStrategy):
    """使用次数扣费策略

    适用于货币类型为UTS(Usage Times)的密钥，每次消费减少1个单位。
    """

    def calculate_fee(
        self, key: Key, details: Optional[Dict[str, Any]] = None
    ) -> float:
        """计算扣费金额，固定为1个单位

        Args:
            key: 密钥对象
            details: 消费详情

        Returns:
            float: 固定返回1.0
        """
        return 1.0


class AgentChatCnyStrategy(BillingStrategy):
    """智能体聊天人民币扣费策略

    适用于服务代码为AGC(智能体聊天)且货币类型为CNY的密钥。
    根据token数量和单价计算扣费金额。
    """

    # 每token的单价（人民币）
    TOKEN_PRICE = 0.00012

    def calculate_fee(
        self, key: Key, details: Optional[Dict[str, Any]] = None
    ) -> float:
        """根据token数量和单价计算扣费金额

        Args:
            key: 密钥对象
            details: 消费详情，需要包含token数量信息

        Returns:
            float: 计算得到的扣费金额
        """
        if not details:
            raise ValueError("消费详情不能为空")

        # 获取token数量
        token_count = self._get_token_count(details)

        # 计算费用
        return token_count * self.TOKEN_PRICE

    def _get_token_count(self, details: Dict[str, Any]) -> int:
        """从详情中获取token数量

        Args:
            details: 消费详情

        Returns:
            int: token数量
        """
        # 优先使用total_tokens
        if "total_tokens" in details:
            return details["total_tokens"]

        # 如果没有total_tokens，则使用input_tokens和output_tokens之和
        input_tokens = details.get("input_tokens", 0)
        output_tokens = details.get("output_tokens", 0)

        return input_tokens + output_tokens


class AgentChatLtcStrategy(BillingStrategy):
    """智能体聊天LLM Token Counts扣费策略

    适用于服务代码为AGC(智能体聊天)且货币类型为LTC的密钥。
    直接使用token数量作为扣费金额。
    """

    def calculate_fee(
        self, key: Key, details: Optional[Dict[str, Any]] = None
    ) -> float:
        """直接使用token数量作为扣费金额

        Args:
            key: 密钥对象
            details: 消费详情，需要包含token数量信息

        Returns:
            float: 计算得到的扣费金额
        """
        if not details:
            raise ValueError("消费详情不能为空")

        # 获取token数量
        token_count = self._get_token_count(details)

        # 直接返回token数量作为扣费金额
        return float(token_count)

    def _get_token_count(self, details: Dict[str, Any]) -> int:
        """从详情中获取token数量

        Args:
            details: 消费详情

        Returns:
            int: token数量
        """
        # 优先使用total_tokens
        if "total_tokens" in details:
            return details["total_tokens"]

        # 如果没有total_tokens，则使用input_tokens和output_tokens之和
        input_tokens = details.get("input_tokens", 0)
        output_tokens = details.get("output_tokens", 0)

        return input_tokens + output_tokens


class DigitalHumanVideoServiceCnyStrategy(BillingStrategy):
    """数字人视频生成服务人民币扣费策略

    适用于服务代码为DGV(数字人视频生成)且货币类型为CNY的密钥。
    根据视频时长和单价计算扣费金额，按每秒0.067元人民币计费。
    """

    # 每秒的单价（人民币）
    SECOND_PRICE = 0.067

    def calculate_fee(
        self, key: Key, details: Optional[Dict[str, Any]] = None
    ) -> float:
        """根据视频时长和单价计算扣费金额

        Args:
            key: 密钥对象
            details: 消费详情，需要包含视频时长信息

        Returns:
            float: 计算得到的扣费金额
        """
        if not details:
            raise ValueError("消费详情不能为空")

        # 获取视频时长（秒）
        duration_seconds = self._get_duration(details)

        # 计算费用（按秒计费，更加精确）
        fee = duration_seconds * self.SECOND_PRICE

        # 保留两位小数
        return round(fee, 2)

    def _get_duration(self, details: Dict[str, Any]) -> int:
        """从详情中获取视频时长

        Args:
            details: 消费详情

        Returns:
            int: 视频时长（秒）
        """
        # 优先使用duration字段
        if "duration" in details:
            return details["duration"]

        raise ValueError("消费详情中缺少视频时长信息")
