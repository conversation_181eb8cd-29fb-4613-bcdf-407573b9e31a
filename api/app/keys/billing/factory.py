"""
扣费策略工厂模块

该模块实现了扣费策略的工厂类，用于根据密钥的属性选择合适的扣费策略。
"""

from typing import Any, Dict, Optional

from app.core.exceptions import BillingStrategyNotFoundException
from app.db.models import Key

from .strategies import (
    AgentChatCnyStrategy,
    AgentChatLtcStrategy,
    BillingStrategy,
    DigitalHumanVideoServiceCnyStrategy,
    UsageTimesStrategy,
)


class BillingStrategyFactory:
    """扣费策略工厂类"""

    @staticmethod
    def get_strategy(key: Key) -> BillingStrategy:
        """根据密钥属性获取合适的扣费策略

        Args:
            key: 密钥对象

        Returns:
            BillingStrategy: 扣费策略对象
        """
        # 获取服务代码
        service_code = key.service.code if key.service else None

        # 获取货币类型
        currency_type = key.currency_type

        # 根据货币类型和服务代码选择策略

        # 策略1: 使用次数扣费 - 适用于货币类型为UTS的密钥
        if currency_type == "UTS":
            return UsageTimesStrategy()

        # 策略2: 智能体聊天人民币扣费 - 适用于服务代码为AGC且货币类型为CNY的密钥
        if service_code == "AGC" and currency_type == "CNY":
            return AgentChatCnyStrategy()

        # 策略3: 智能体聊天LLM Token Counts扣费 - 适用于服务代码为AGC且货币类型为LTC的密钥
        if service_code == "AGC" and currency_type == "LTC":
            return AgentChatLtcStrategy()

        # 策略4: 数字人视频服务人民币扣费 - 适用于服务代码为DGV且货币类型为CNY的密钥
        if service_code == "DGV" and currency_type == "CNY":
            return DigitalHumanVideoServiceCnyStrategy()

        raise BillingStrategyNotFoundException(key_id=key.id)

    @staticmethod
    def calculate_fee(key: Key, details: Optional[Dict[str, Any]] = None) -> float:
        """计算扣费金额

        Args:
            key: 密钥对象
            details: 消费详情

        Returns:
            float: 计算得到的扣费金额
        """
        strategy = BillingStrategyFactory.get_strategy(key)
        return strategy.calculate_fee(key, details)
