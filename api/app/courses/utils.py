from sqlmodel import Session

from app.core import   settings
from app.db.models import User
from billing import HkJingXiuBilling
from loguru import logger


async def check_user_has_purchased_course(user: User, course_id: int, session: Session) -> bool:
    """
    检查用户是否已购买课程

    此函数通过查询用户可用密钥来确认用户是否已购买指定课程。
    如果用户有针对该课程的可用密钥，则表示已购买。

    Args:
        user: 用户对象
        course_id: 课程ID
        session: 数据库会话

    Returns:
        bool: 如果用户已购买该课程，返回True；否则返回False
    """
    logger.debug(f"检查用户 {user.id} 是否已购买课程 {course_id}")

    # 创建billing客户端
    billing_client = HkJingXiuBilling(
        base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
    )

    # 查询用户是否有针对该课程的密钥
    keys = await billing_client.keys.list_available_keys(
        user_id=str(user.id),
        service_code="COU",  # 使用COU服务代码表示课程
        scope=[str(course_id)],
    )

    if len(keys.keys) > 0:
        logger.debug(f"用户 {user.id} 有针对课程 {course_id} 的密钥")
        return True

    logger.debug(f"用户 {user.id} 未购买课程 {course_id}")
    return False
