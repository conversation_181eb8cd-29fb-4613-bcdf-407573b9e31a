from typing import Dict, List, Optional

from pydantic import BaseModel, Field


class RatingDistribution(BaseModel):
    """评级分布项"""
    stars: int = Field(..., description="星级数量", ge=1, le=5)
    percentage: float = Field(..., description="该星级的百分比", ge=0, le=100)


class CourseStats(BaseModel):
    """课程统计信息模型"""
    rating: float = Field(..., description="课程平均评级", ge=0, le=5)
    rating_count: str = Field(..., description="评级数量，例如：'50K+'")
    rank: str = Field(..., description="排名，例如：'#13'")
    category: str = Field(..., description="所属类别")
    student_count: str = Field(..., description="学生数量，例如：'3M+'")
    rating_distribution: List[RatingDistribution] = Field(
        default_factory=list,
        description="各星级评分的分布情况"
    )


class CourseOutline(BaseModel):
    """课程概要

    Attributes:
        title: 课程标题
        cover_image: 课程封面图片URL
        tags: 课程标签列表
    """
    id: int = Field(description="课程ID")
    title: str = Field(description="课程标题")
    name: Optional[str] = Field(default=None, description="课程名称")
    description: Optional[str] = Field(default=None, description="课程描述")
    instructor: Optional[str] = Field(default=None, description="导师")
    icon: Optional[str] = Field(default=None, description="课程封面图片URL")
    cover_image: Optional[str] = Field(default=None, description="课程封面图片URL")
    poster_url: Optional[str] = Field(default=None, description="海报图片URL")
    tags: Optional[List[str]] = Field(default=None, description="课程标签列表")
    sections_count: Optional[int] = Field(default=None, description="课程章节数量")


class CourseSectionOutline(BaseModel):
    """课程章节概要"""
    id: int = Field(description="章节ID")
    title: str = Field(description="章节标题")
    duration: Optional[int] = Field(default=None, description="章节时长")
    is_free: Optional[bool] = Field(default=None, description="是否免费")
    video_url: Optional[str] = Field(default=None, description="视频URL")


class CourseDetail(BaseModel):
    """课程详情"""
    id: int = Field(description="课程ID")
    title: str = Field(description="课程标题")
    name: Optional[str] = Field(default=None, description="课程名称")
    cover_image: Optional[str] = Field(default=None, description="课程封面图片URL")
    poster_url: Optional[str] = Field(default=None, description="海报图片URL")
    instructor: Optional[str] = Field(default=None, description="导师")
    description: str = Field(description="课程描述")
    tags: List[str] = Field(default_factory=list, description="课程标签列表")
    sections: List[CourseSectionOutline] = Field(default_factory=list, description="课程章节列表")
    has_purchased: bool = Field(default=False, description="是否已购买")
    stats: Optional[CourseStats] = Field(default=None, description="课程统计信息")


class CoursesByTag(BaseModel):
    """按标签分组的课程列表"""
    data: Dict[str, List[CourseOutline]] = Field(description="按标签分组的课程列表")
