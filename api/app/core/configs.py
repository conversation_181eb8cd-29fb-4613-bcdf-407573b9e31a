from pathlib import Path

from pydantic import Field
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    database_url: str = Field(default=None, description="数据库URL")
    mode: str = Field(default="dev", description="运行模式")
    jwt_secret_key: str = Field(default=None, description="JWT密钥")
    jwt_algorithm: str = Field(default=None, description="JWT算法")
    sms_sign_name: str = Field(default=None, description="短信签名")
    sms_template_code: str = Field(default=None, description="短信模板代码")

    # 数据库配置
    sql_echo: bool = Field(default=False, description="是否打印SQL语句")

    # 阿里云访问密钥配置
    aliyun_access_key_id: str = Field(default=None, description="阿里云访问密钥ID")
    aliyun_access_key_secret: str = Field(
        default=None, description="阿里云访问密钥密钥"
    )
    oss_region: str = Field(default="oss-cn-hangzhou", description="阿里云OSS地域")
    oss_bucket: str = Field(default=None, description="阿里云OSS Bucket名称")
    redis_host: str = Field(default=None, description="Redis Host")
    redis_port: int = Field(default=None, description="Redis Port")
    redis_password: str = Field(default=None, description="Redis Password")
    redis_db: int = Field(default=None, description="Redis DB")
    model_config = SettingsConfigDict(
        env_file=Path(__file__).parent.parent.parent / ".env"
    )


settings = Settings()
