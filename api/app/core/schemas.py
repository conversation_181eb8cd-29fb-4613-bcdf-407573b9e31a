from pathlib import Path
from typing import List, TypeVar, Optional, Generic

from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings, SettingsConfigDict

T = TypeVar("T")


class Error(BaseModel):
    """表示一个错误

    Attributes:
        type: 错误类型
        message: 错误信息
        code: 错误码

    Author: dongjak
    Created: 2024/11/08
    Version: 1.0
    Since: 1.0
    """

    type: Optional[str] = Field(default=None, description="错误类型")
    message: Optional[str] = Field(default=None, description="错误信息")
    code: Optional[int] = Field(default=None, description="错误码")

class Pagination(BaseModel, Generic[T]):
    """分页请求模型

    Attributes:
        page: 当前页码
        limit: 每页条数
        total: 总条数
        has_more: 是否有更多数据
        data: 数据列表
    """

    page_no: Optional[int] = Field(default=1, description="当前页码")
    page_size: Optional[int] = Field(default=10, description="每页条数")
    total: Optional[int] = Field(default=None, description="总条数")
    has_more: Optional[bool] = Field(default=None, description="是否有更多数据")
    data: Optional[List[T]] = Field(default=None, description="数据列表")


class ResponsePayloads(BaseModel, Generic[T]):
    """响应载荷"""

    data: Optional[T] = Field(default=None, description="数据")
    error: Optional[Error] = Field(default=None, description="错误信息")