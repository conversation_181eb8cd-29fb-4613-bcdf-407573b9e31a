import logging
from functools import wraps
from typing import Callable, Optional

from apscheduler.executors.pool import ThreadPoolExecutor
from apscheduler.jobstores.redis import RedisJobStore
from apscheduler.schedulers.background import BackgroundScheduler

from app.core.log import scheduler_logger
from app.core.redis import redis_client


def handle_exceptions(func: Callable) -> Callable:
    """错误处理装饰器"""

    @wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            scheduler_logger.error(
                f"执行 {func.__name__} 时出错: {str(e)}", exc_info=True
            )

    return wrapper


class SchedulerManager:
    """调度器管理器"""

    _instance: Optional["SchedulerManager"] = None
    _scheduler: Optional[BackgroundScheduler] = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if self._scheduler is None:
            # 配置Redis作为任务存储
            jobstores = {
                "default": RedisJobStore(
                    jobs_key="apscheduler.jobs",
                    run_times_key="apscheduler.run_times",
                    host=redis_client.connection_pool.connection_kwargs.get("host"),
                    port=redis_client.connection_pool.connection_kwargs.get("port"),
                    db=redis_client.connection_pool.connection_kwargs.get("db"),
                    password=redis_client.connection_pool.connection_kwargs.get(
                        "password"
                    ),
                )
            }

            # 配置执行器
            executors = {"default": ThreadPoolExecutor(20)}

            # 作业默认设置
            job_defaults = {
                "coalesce": False,  # 错过的任务不合并执行
                "max_instances": 3,  # 同一个任务最多同时运行的实例数
                "misfire_grace_time": 60 * 60,  # 错过执行时间的宽限期（秒）
            }

            # 创建调度器
            self._scheduler = BackgroundScheduler(
                jobstores=jobstores,
                executors=executors,
                job_defaults=job_defaults,
                timezone="Asia/Shanghai",  # 使用中国时区
            )

            # 设置APScheduler的日志处理
            logging.getLogger("apscheduler").setLevel(logging.ERROR)

    @property
    def scheduler(self) -> BackgroundScheduler:
        return self._scheduler

    def start(self):
        """启动调度器"""
        if not self._scheduler.running:
            self._scheduler.start()
            scheduler_logger.info("调度器已启动")

    def shutdown(self):
        """关闭调度器"""
        if self._scheduler.running:
            self._scheduler.shutdown()
            scheduler_logger.info("调度器已关闭")

    def add_job(self, *args, **kwargs):
        """添加任务"""
        return self._scheduler.add_job(*args, **kwargs)

    def remove_job(self, job_id, jobstore=None):
        """移除任务"""
        self._scheduler.remove_job(job_id, jobstore)

    def get_job(self, job_id, jobstore=None):
        """获取任务"""
        return self._scheduler.get_job(job_id, jobstore)

    def reschedule_job(self, job_id, jobstore=None, **kwargs):
        """重新调度任务"""
        return self._scheduler.reschedule_job(job_id, jobstore, **kwargs)


# 全局调度器实例
scheduler_manager = SchedulerManager()


def setup_scheduler():
    """设置并启动调度器"""
    scheduler_manager.start()
    return scheduler_manager
