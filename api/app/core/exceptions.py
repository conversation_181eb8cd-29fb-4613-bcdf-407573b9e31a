class ServiceException(Exception):
    """
    自定义基础异常类，支持错误消息、错误代码和参考URL
    """

    def __init__(
        self, msg="An error occurred", code="E000", reference_url=None, *args, **kwargs
    ):
        self.msg = msg
        self.code = code
        self.reference_url = reference_url
        self.details = kwargs.get("details", {})

        # 构建完整错误消息
        message = f"[{self.code}] {self.msg}"
        if self.reference_url:
            message += f" (参考: {self.reference_url})"

        super().__init__(message, *args)

    def to_dict(self):
        """返回异常的字典表示，方便转换为JSON"""
        result = {
            "error": {
                "code": self.code,
                "message": self.msg,
            }
        }

        if self.reference_url:
            result["error"]["reference_url"] = self.reference_url

        if self.details:
            result["error"]["details"] = self.details

        return result


class ServiceNotFoundException(ServiceException):
    """服务不存在异常"""

    def __init__(self, service_code=None, *args, **kwargs):
        msg = f"服务不存在: {service_code}" if service_code else "服务不存在"
        super().__init__(msg=msg, code="E1000", *args, **kwargs)


class PermissionDeniedException(ServiceException):
    """权限不足异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="权限不足", code="E1006", *args, **kwargs)


class AppNotFoundException(ServiceException):
    """应用不存在异常"""

    def __init__(self, app_id=None, *args, **kwargs):
        msg = f"应用不存在: {app_id}" if app_id else "应用不存在"
        super().__init__(msg=msg, code="E1001", *args, **kwargs)


class KeyNotFoundException(ServiceException):
    """密钥不存在异常"""

    def __init__(self, key_id=None, *args, **kwargs):
        msg = f"密钥不存在: {key_id}" if key_id else "密钥不存在"
        super().__init__(msg=msg, code="E1002", *args, **kwargs)


class KeyExpiredException(ServiceException):
    """密钥过期异常"""

    def __init__(self, key_id=None, *args, **kwargs):
        msg = f"密钥已过期: {key_id}" if key_id else "密钥已过期"
        super().__init__(msg=msg, code="E1003", *args, **kwargs)


class InvalidKeyException(ServiceException):
    """无效密钥异常"""

    def __init__(self, key_id=None, *args, **kwargs):
        msg = f"无效密钥: {key_id}" if key_id else "无效密钥"
        super().__init__(msg=msg, code="E1005", *args, **kwargs)


class InsufficientCreditException(ServiceException):
    """额度不足异常"""

    def __init__(self, key_id=None, available=None, required=None, *args, **kwargs):
        if key_id and available is not None and required is not None:
            msg = f"密钥 {key_id} 额度不足: 可用 {available}, 需要 {required}"
        else:
            msg = "额度不足"
        super().__init__(msg=msg, code="E1004", *args, **kwargs)


class BillingStrategyNotFoundException(ServiceException):
    """未找到合适的扣费策略异常"""

    def __init__(self, key_id=None, *args, **kwargs):
        msg = f"未找到合适的扣费策略: {key_id}" if key_id else "未找到合适的扣费策略"
        super().__init__(msg=msg, code="E1007", *args, **kwargs)
