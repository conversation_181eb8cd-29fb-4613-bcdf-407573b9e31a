class ServiceException(Exception):
    """
    自定义基础异常类，支持错误消息、错误代码和参考URL
    """

    def __init__(
        self, msg="An error occurred", code="E000", reference_url=None, *args, **kwargs
    ):
        self.msg = msg
        self.code = code
        self.reference_url = reference_url
        self.details = kwargs.get("details", {})

        # 构建完整错误消息
        message = f"[{self.code}] {self.msg}"
        if self.reference_url:
            message += f" (参考: {self.reference_url})"

        super().__init__(message, *args)

    def to_dict(self):
        """返回异常的字典表示，方便转换为JSON"""
        result = {
            "error": {
                "code": self.code,
                "message": self.msg,
            }
        }

        if self.reference_url:
            result["error"]["reference_url"] = self.reference_url

        if self.details:
            result["error"]["details"] = self.details

        return result


class NoCreateAgentPrivilegeError(ServiceException):
    """没有创建智能体权限异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="您没有创建智能体的权限", code="USER003", *args, **kwargs)


class UserNotFoundException(ServiceException):
    """用户不存在异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="用户不存在", code="USER001", *args, **kwargs)


class InvalidVerificationCodeException(ServiceException):
    """验证码错误或已过期异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="验证码错误或已过期", code="USER002", *args, **kwargs)


class NoChatPermissionException(ServiceException):
    """没有聊天权限异常"""

    def __init__(self, agent_id=None, *args, **kwargs):
        msg = "没有权限使用该智能体"
        if agent_id:
            msg = f"没有权限使用智能体 {agent_id}"
        super().__init__(msg=msg, code="AGENT001", *args, **kwargs)


class UnauthorizedAccessException(ServiceException):
    """未授权访问异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="未授权访问", code="AUTH001", *args, **kwargs)


class DatabaseSessionUnavailableException(ServiceException):
    """数据库会话不可用异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="数据库会话不可用", code="DB001", *args, **kwargs)


class MissingAgentIdException(ServiceException):
    """缺少智能体ID参数异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="缺少智能体ID参数", code="AGENT002", *args, **kwargs)


class AgentNotFoundException(ServiceException):
    """智能体不存在异常"""

    def __init__(self, agent_id=None, *args, **kwargs):
        msg = "智能体不存在"
        if agent_id:
            msg = f"智能体不存在: {agent_id}"
        super().__init__(msg=msg, code="AGENT003", *args, **kwargs)


class AgentCreateFailedException(ServiceException):
    """智能体创建失败异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="创建智能体失败", code="AGENT004", *args, **kwargs)


class AgentUpdateFailedException(ServiceException):
    """智能体更新失败异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="更新智能体失败", code="AGENT005", *args, **kwargs)


class AgentPermissionDeniedException(ServiceException):
    """智能体权限被拒绝异常"""

    def __init__(self, agent_id=None, *args, **kwargs):
        msg = "无权限操作此智能体"
        if agent_id:
            msg = f"无权限操作智能体: {agent_id}"
        super().__init__(msg=msg, code="AGENT006", *args, **kwargs)


# 订单相关异常
class OrderNotFoundException(ServiceException):
    """订单不存在异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="订单不存在", code="ORDER001", *args, **kwargs)


class AppNotFoundException(ServiceException):
    """应用不存在异常"""

    def __init__(self, app_id=None, *args, **kwargs):
        msg = "应用不存在"
        if app_id:
            msg = f"应用不存在: {app_id}"
        super().__init__(msg=msg, code="ORDER002", *args, **kwargs)


class InvalidPriceTypeException(ServiceException):
    """无效的价格类型异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="无效的价格类型，只支持 monthly/yearly", code="ORDER003", *args, **kwargs)


class AppPriceNotConfiguredException(ServiceException):
    """应用价格未配置异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="应用价格未配置", code="ORDER004", *args, **kwargs)


class CourseNotFoundException(ServiceException):
    """课程不存在异常"""

    def __init__(self, course_id=None, *args, **kwargs):
        msg = "课程不存在"
        if course_id:
            msg = f"课程不存在: {course_id}"
        super().__init__(msg=msg, code="ORDER005", *args, **kwargs)


class CoursePriceNotConfiguredException(ServiceException):
    """课程价格未配置异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="课程价格未配置", code="ORDER006", *args, **kwargs)


class PriceFormatException(ServiceException):
    """价格格式错误异常"""

    def __init__(self, error_msg=None, *args, **kwargs):
        msg = "价格格式错误"
        if error_msg:
            msg = f"价格格式错误: {error_msg}"
        super().__init__(msg=msg, code="ORDER007", *args, **kwargs)


class OrderCreationException(ServiceException):
    """订单创建失败异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="订单创建失败", code="ORDER008", *args, **kwargs)


class PaymentPlanNotFoundException(ServiceException):
    """付费计划不存在异常"""

    def __init__(self, plan_id=None, *args, **kwargs):
        msg = "付费计划不存在"
        if plan_id:
            msg = f"付费计划不存在: {plan_id}"
        super().__init__(msg=msg, code="ORDER009", *args, **kwargs)


class PaymentPlanNotActiveException(ServiceException):
    """付费计划未激活异常"""

    def __init__(self, plan_id=None, *args, **kwargs):
        msg = "付费计划未激活"
        if plan_id:
            msg = f"付费计划未激活: {plan_id}"
        super().__init__(msg=msg, code="ORDER010", *args, **kwargs)


class ProductTypeMismatchException(ServiceException):
    """产品类型不匹配异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="产品类型与付费计划的适用范围类型不匹配", code="ORDER011", *args, **kwargs)


class ProductNotInPlanScopeException(ServiceException):
    """产品不在付费计划适用范围内异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="产品不在付费计划的适用范围内", code="ORDER012", *args, **kwargs)


class MissingOpenIdException(ServiceException):
    """缺少OpenID参数异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="缺少OpenID参数", code="ORDER013", *args, **kwargs)


class UnsupportedPaymentMethodException(ServiceException):
    """不支持的支付方式异常"""

    def __init__(self, payment_method=None, *args, **kwargs):
        msg = "不支持的支付方式"
        if payment_method:
            msg = f"不支持的支付方式: {payment_method}"
        super().__init__(msg=msg, code="ORDER014", *args, **kwargs)


class PaymentOrderCreationFailedException(ServiceException):
    """创建支付订单失败异常"""

    def __init__(self, *args, **kwargs):
        super().__init__(msg="创建支付订单失败", code="ORDER015", *args, **kwargs)
