from billing import HkJingXiuBilling
from billing.keys import KeyListResponse, KeyCreate
from fastapi import APIRouter, Body, Depends, HTTPException, Path
from sqlmodel import Session

from app.db import get_session
from app.admin.utils import get_current_admin
from app.core import ResponsePayloads
from app.core import settings
from app.db.models import Admin
from app.users.utils import populate_key_resource_names

router = APIRouter(prefix="/users", tags=["用户"])





@router.get(
    "/{user_id}/keys",
    summary="获取用户密钥列表",
    response_model=ResponsePayloads[KeyListResponse],
)
async def get_user_keys(
        user_id: int = Path(..., description="用户ID"),
        current_admin: Admin = Depends(get_current_admin),
        session: Session = Depends(get_session),
):
    """获取用户密钥列表"""
    try:
        # 创建billing客户端
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )        # 获取用户所有密钥
        keys_response = await billing_client.keys.list_available_keys(user_id=str(user_id))

        # 为每个密钥获取对应的资源名称
        populate_key_resource_names(keys_response.keys, session)

        return ResponsePayloads(data=keys_response)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取用户密钥失败: {str(e)}")


@router.delete(
    "/{user_id}/keys/{key_id}",
    summary="删除用户密钥",
    response_model=ResponsePayloads[dict],
)
async def delete_user_key(
        user_id: int = Path(..., description="用户ID"),
        key_id: str = Path(..., description="密钥ID"),
        current_admin: Admin = Depends(get_current_admin),
):
    """删除用户密钥"""
    try:
        # 创建billing客户端
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )

        # 删除密钥
        await billing_client.keys.delete_key(key_id=key_id)

        return ResponsePayloads(data={"success": True})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除密钥失败: {str(e)}")


@router.post(
    "/{user_id}/keys",
    summary="创建用户密钥",
    response_model=ResponsePayloads[dict],
)
async def create_user_key(
        user_id: int = Path(..., description="用户ID"),
        key_data: KeyCreate = Body(..., description="密钥创建数据"),
        current_admin: Admin = Depends(get_current_admin),
):
    """创建用户密钥"""
    try:
        # 创建billing客户端
        billing_client = HkJingXiuBilling(
            base_url=settings.hkjx_base_url, admin_key=settings.hkjx_appsecret
        )

        # 设置用户ID
        key_data.user_id = str(user_id)

        # 创建密钥
        key = await billing_client.keys.create_key(key_data)

        return ResponsePayloads(data={"id": key.id, "success": True})
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建密钥失败: {str(e)}")
