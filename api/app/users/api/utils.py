from typing import Annotated, Optional

import jwt
import wrapt
from fastapi import Depends, HTTPException
from fastapi.security import HTT<PERSON>uthorizationCredentials, HTTPBearer
from loguru import logger
from sqlmodel import Session, select
from starlette import status

from app.core import settings
from app.core.exceptions import (
    NoCreateAgentPrivilegeError,
)
from app.db import User, get_session

# HTTP Bearer 认证方案
security = HTTPBearer()


async def get_current_user(
    credentials: Annotated[HTTPAuthorizationCredentials, Depends(security)],
    session: Session = Depends(get_session),
) -> User:
    """
    从 JWT token 中获取当前用户
    :param session: 数据库会话
    :param credentials: HTTP Bearer 认证凭据
    :return: 当前用户对象
    :raises HTTPException: 如果 token 无效或用户不存在
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # 解码 JWT token
        payload = jwt.decode(
            credentials.credentials,
            settings.jwt_secret_key,
            algorithms=[settings.jwt_algorithm],
        )
        user_id: str = payload.get("sub")
        if user_id is None:
            raise credentials_exception
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid token: {str(e)}")
        raise credentials_exception

    # 从数据库中获取用户
    try:
        statement = select(User).where(User.id == int(user_id))
        user = session.exec(statement).first()
        if user is None:
            logger.warning(f"User not found: {user_id}")
            raise credentials_exception

        return user
    except ValueError as e:
        logger.exception(f"Database error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="系统错误"
        )


async def get_current_user_from_token(token: str, session: Session) -> Optional[User]:
    """
    从 token 获取当前用户（用于 WebSocket 认证）
    :param session: 数据库会话
    :param token: JWT token
    :return: 用户对象，如果 token 无效则返回 None
    """
    try:
        # 解码 JWT token
        payload = jwt.decode(
            token, settings.jwt_secret_key, algorithms=[settings.jwt_algorithm]
        )
        user_id = int(payload.get("sub"))
        if not user_id:
            return None
        # 从数据库获取用户
        statement = select(User).where(User.id == user_id)
        user = session.exec(statement).first()
        if not user:
            logger.warning(f"User not found: {user_id}")
            return None
        return user
    except jwt.InvalidTokenError as e:
        logger.warning(f"Invalid token: {str(e)}")
        return None
    except Exception as e:
        logger.exception(f"Error getting user from token: {str(e)}")
        return None

def require_create_agent_privilege():
    """
    要求创建智能体权限的装饰器

    :raises:
        NoCreateAgentPrivilegeError: 如果用户没有创建智能体的权限
    """

    @wrapt.decorator
    async def wrapper(wrapped, instance, args, kwargs):
        # 从依赖注入获取当前用户和会话
        current_user: User = kwargs.get("current_user")
        if not current_user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED, detail="未授权"
            )

        session = kwargs.get("session")
        if not session:
            # 如果没有在kwargs中找到，获取新会话
            session = next(get_session())
        # 直接从用户对象检查创建智能体权限
        if not current_user.can_create_agent:
            raise NoCreateAgentPrivilegeError()

        # 将当前用户和会话添加到kwargs中
        kwargs["current_user"] = current_user
        kwargs["session"] = session

        # 调用原始函数
        return await wrapped(*args, **kwargs)

    return wrapper
