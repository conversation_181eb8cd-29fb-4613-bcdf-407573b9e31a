import os
import tempfile
from typing import List, Optional, Literal

from dify.dataset import DataSetCreatePayloads, DataSetCreateResponse
from dify.dataset.schemas import (
    DataSource,
    FileInfoList,
    InfoList,
    DataSetList,
    MetadataCreatePayload,
    MetadataUpdatePayload,
    MetadataField,
    MetadataListResponse,
    DocumentMetadataItem,
    DocumentMetadataOperation,
    DocumentMetadataUpdatePayload,
    DocumentCreateByFileIdsPayload,
    DocumentCreateByFileIdsResponse,
    EmptyDataSetCreatePayload,
    DataSetInCreate
)
from dify.file import FileUploadResponse
from dify.tag import BindingPayloads, TagType
from fastapi import APIRouter, Depends, File, UploadFile, Query, Body, Path
from loguru import logger

from app.agents.utils import dify
from app.core.schemas import Error, ResponsePayloads, Pagination
from app.db.models import User
from app.users.api.utils import get_current_user

router = APIRouter(prefix="/knowledge", tags=["知识库"])


@router.post(
    "/create",
    summary="创建知识库",
    response_model=ResponsePayloads[DataSetCreateResponse],
    description="创建知识库",
)
async def create_knowledge_dataset(
    files: List[UploadFile] = File(..., description="知识库文件列表"),
    current_user: User = Depends(get_current_user),
):
    """上传多个文件创建知识库接口"""
    # 1. 先看下有没有当前用户对应的知识库标签
    tags = await dify.tag.list(type=TagType.KNOWLEDGE)
    tag_names = [tag.name for tag in tags]
    # 2. 如果没有，则创建一个
    knowledge_tag = None
    if current_user.phone not in tag_names:
        knowledge_tag = await dify.tag.create(
            name=current_user.phone,
            type=TagType.KNOWLEDGE,
        )
    else:
        knowledge_tag = tags[tag_names.index(current_user.phone)]
    # 3. 上传文件
    dify_files: List[FileUploadResponse] = []
    for file in files:
        # 记录接收到的文件信息
        logger.info(
            f"接收到文件: {file.filename}, 大小: {file.size}, 类型: {file.content_type}"
        )

        # 将文件内容保存到临时文件
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, file.filename)

        # 保存上传的文件内容到临时文件
        with open(temp_file_path, "wb") as f:
            f.write(await file.read())

        # 使用临时文件路径上传到Dify
        dify_file: FileUploadResponse = await dify.file.upload(
            file_path=temp_file_path
        )
        dify_files.append(dify_file)

        # 删除临时文件
        os.remove(temp_file_path)
    # 4. 创建知识库
    dataset = await dify.dataset.create(
        payload=DataSetCreatePayloads(
            data_source=DataSource(
                type="upload_file",
                info_list=InfoList(
                    file_info_list=FileInfoList(
                        file_ids=[dify_file.id for dify_file in dify_files]
                    )
                ),
            )
        )
    )
    # 5. 绑定标签
    await dify.tag.bind(
        payload=BindingPayloads(
            tag_ids=[knowledge_tag.id],
            target_id=dataset.dataset.id,
            type=TagType.KNOWLEDGE,
        )
    )
    return ResponsePayloads(data=dataset)


@router.post(
    "/create-empty",
    summary="创建空知识库",
    response_model=ResponsePayloads[DataSetCreateResponse],
    description="创建空知识库",
)
async def create_empty_knowledge(
    name: str = Body(..., description="知识库名称"),
    description: str = Body(None, description="知识库描述"),
    current_user: User = Depends(get_current_user),
):
    """创建空知识库接口"""
    # 1. 先看下有没有当前用户对应的知识库标签
    tags = await dify.tag.list(type=TagType.KNOWLEDGE)
    tag_names = [tag.name for tag in tags]
    # 2. 如果没有，则创建一个
    knowledge_tag = None
    if current_user.phone not in tag_names:
        knowledge_tag = await dify.tag.create(
            name=current_user.phone,
            type=TagType.KNOWLEDGE,
        )
    else:
        knowledge_tag = tags[tag_names.index(current_user.phone)]

    # 3. 创建空知识库
    # 使用SDK创建空知识库
    empty_dataset = await dify.dataset.create_empty(
        payload=EmptyDataSetCreatePayload(
            name=name,
            description=description,
            permission="only_me",
            provider="vendor"
        )
    )
    logger.info(f"使用create_empty创建空知识库成功: {empty_dataset}")

    # 将EmptyDataSetCreateResponse转换为DataSetCreateResponse格式
    dataset = DataSetCreateResponse(
        dataset=DataSetInCreate(
            id=empty_dataset.id,
            name=empty_dataset.name,
            description=empty_dataset.description,
            permission=empty_dataset.permission,
            data_source_type=empty_dataset.data_source_type,
            indexing_technique=empty_dataset.indexing_technique,
            created_by=empty_dataset.created_by,
            created_at=empty_dataset.created_at
        ),
        documents=[]
    )

    # 4. 绑定标签到知识库
    logger.info(f"尝试绑定标签 {knowledge_tag.name}({knowledge_tag.id}) 到知识库 {dataset.dataset.id}")
    await dify.tag.bind(
        payload=BindingPayloads(
            tag_ids=[knowledge_tag.id],
            target_id=dataset.dataset.id,
            type=TagType.KNOWLEDGE,
        )
    )
    logger.info(f"成功绑定标签 {knowledge_tag.name} 到知识库 {dataset.dataset.id}")
    return ResponsePayloads(data=dataset)


@router.get(
    "/list",
    summary="获取知识库列表",
    response_model=ResponsePayloads[DataSetList],
    description="获取知识库列表",
)
async def get_knowledge_list(
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(get_current_user),
):
    """获取知识库列表接口"""
    # 1. 先获取当前用户手机号对应的标签ID
    user_tag_id = None
    # 获取所有知识库标签
    tags = await dify.tag.list(type=TagType.KNOWLEDGE)
    # 查找用户手机号对应的标签
    for tag in tags:
        if tag.name == current_user.phone:
            user_tag_id = tag.id
            logger.info(f"找到用户 {current_user.phone} 对应的标签ID: {user_tag_id}")
            break

    if not user_tag_id:
        return ResponsePayloads(data=DataSetList(data=[], total=0, has_more=False))

    # 使用 Dify SDK 的 dataset.find_list 方法
    # 如果有用户标签ID，则按标签过滤
    tag_ids = [user_tag_id] if user_tag_id else []
    datasets = await dify.dataset.find_list(
        page=page,
        limit=page_size,
        include_all=False,
        tag_ids=tag_ids,  # 使用用户手机号对应的标签ID进行过滤
    )

    # 直接返回DataSetList对象
    logger.info(f"获取到知识库列表: {len(datasets.data)}条数据")

    # 如果有关键词，在客户端进行过滤
    if keyword:
        # 过滤数据
        filtered_data = [
            item for item in datasets.data
            if keyword.lower() in item.name.lower() or
            (item.description and keyword.lower() in item.description.lower())
        ]

        # 创建新的分页结果
        return ResponsePayloads(
            data=Pagination(
                data=filtered_data,
                total=len(filtered_data),
                has_more=False,
            )
        )

    # 返回完整的DataSetList对象
    return ResponsePayloads(
        data=datasets
    )


@router.put(
    "/update/{dataset_id}",
    summary="更新知识库",
    response_model=ResponsePayloads[dict],
    description="更新知识库名称和描述",
)
async def update_knowledge(
    dataset_id: str,
    name: str = Body(..., description="知识库名称"),
    description: str = Body(None, description="知识库描述"),
    current_user: User = Depends(get_current_user),
):
    """更新知识库接口"""
    # 准备更新数据
    payload = {
        "name": name
    }
    if description:
        payload["description"] = description

    # 使用SDK更新知识库
    result = await dify.dataset.update(dataset_id, payload)
    return ResponsePayloads(data={"success": True, "result": result})



@router.delete(
    "/delete/{dataset_id}",
    summary="删除知识库",
    response_model=ResponsePayloads[dict],
    description="删除知识库",
)
async def delete_knowledge(
    dataset_id: str,
    current_user: User = Depends(get_current_user),
):
    """删除知识库接口"""
    # 使用 SDK 方法删除知识库
    result = await dify.dataset.delete(dataset_id)
    return ResponsePayloads(data={"success": True, "result": result})



@router.get(
    "/{dataset_id}/metadata",
    summary="获取知识库元数据列表",
    response_model=ResponsePayloads[MetadataListResponse],
    description="获取知识库元数据字段列表",
)
async def get_metadata_list(
    dataset_id: str,
    current_user: User = Depends(get_current_user),
):
    """获取知识库元数据列表接口"""
    # 调用 Dify SDK 获取元数据列表
    metadata_list = await dify.dataset.get_metadata_list(dataset_id)
    return ResponsePayloads(data=metadata_list)



@router.post(
    "/{dataset_id}/metadata",
    summary="创建知识库元数据字段",
    response_model=ResponsePayloads[MetadataField],
    description="创建知识库元数据字段",
)
async def create_metadata(
    dataset_id: str,
    type: str = Body(..., description="元数据字段类型"),
    name: str = Body(..., description="元数据字段名称"),
    current_user: User = Depends(get_current_user),
):
    """创建知识库元数据字段接口"""
    # 创建元数据字段
    payload = MetadataCreatePayload(type=type, name=name)
    metadata = await dify.dataset.create_metadata(dataset_id, payload)
    return ResponsePayloads(data=metadata)



@router.put(
    "/{dataset_id}/metadata/{metadata_id}",
    summary="更新知识库元数据字段",
    response_model=ResponsePayloads[MetadataField],
    description="更新知识库元数据字段名称",
)
async def update_metadata(
    dataset_id: str,
    metadata_id: str,
    name: str = Body(..., description="元数据字段名称"),
    current_user: User = Depends(get_current_user),
):
    """更新知识库元数据字段接口"""
    # 更新元数据字段
    payload = MetadataUpdatePayload(name=name)
    metadata = await dify.dataset.update_metadata(dataset_id, metadata_id, payload)
    return ResponsePayloads(data=metadata)


@router.delete(
    "/{dataset_id}/metadata/{metadata_id}",
    summary="删除知识库元数据字段",
    response_model=ResponsePayloads[dict],
    description="删除知识库元数据字段",
)
async def delete_metadata(
    dataset_id: str,
    metadata_id: str,
    current_user: User = Depends(get_current_user),
):
    """删除知识库元数据字段接口"""
    # 删除元数据字段
    result = await dify.dataset.delete_metadata(dataset_id, metadata_id)
    return ResponsePayloads(
        data={
            "success": result,
            "message": "删除知识库元数据字段成功" if result else "删除知识库元数据字段失败",
        }
    )


@router.post(
    "/{dataset_id}/metadata/built-in-field",
    summary="启用/禁用知识库元数据内置字段",
    response_model=ResponsePayloads[dict],
    description="启用或禁用知识库元数据内置字段",
)
async def toggle_built_in_metadata(
    dataset_id: str,
    action: Literal["enable", "disable"] = Body(..., description="操作类型，enable表示启用，disable表示禁用"),
    current_user: User = Depends(get_current_user),
):
    """启用/禁用知识库元数据内置字段接口"""
    # 启用/禁用内置字段
    result = await dify.dataset.toggle_built_in_metadata(dataset_id, action)
    return ResponsePayloads(
        data={
            "success": result,
            "message": f"{action}知识库元数据内置字段成功" if result else f"{action}知识库元数据内置字段失败",
        }
    )


@router.post(
    "/{dataset_id}/documents/metadata",
    summary="更新文档元数据",
    response_model=ResponsePayloads[dict],
    description="更新文档的元数据（赋值）",
)
async def update_document_metadata(
    dataset_id: str,
    operation_data: List[dict] = Body(..., description="操作数据，包含文档ID和元数据列表"),
    current_user: User = Depends(get_current_user),
):
    """更新文档元数据接口"""
    # 构建操作数据
    operations = []
    for op in operation_data:
        document_id = op.get("document_id")
        metadata_list = op.get("metadata_list", [])

        if not document_id:
            continue

        metadata_items = []
        for meta in metadata_list:
            metadata_items.append(
                DocumentMetadataItem(
                    id=meta.get("id"),
                    value=meta.get("value"),
                    name=meta.get("name", "")
                )
            )

        operations.append(
            DocumentMetadataOperation(
                document_id=document_id,
                metadata_list=metadata_items
            )
        )

    # 更新文档元数据
    payload = DocumentMetadataUpdatePayload(operation_data=operations)
    result = await dify.dataset.update_document_metadata(dataset_id, payload)

    return ResponsePayloads(
        data={
            "success": result,
            "message": "更新文档元数据成功" if result else "更新文档元数据失败",
        }
    )


@router.get(
    "/detail/{dataset_id}",
    summary="获取知识库详情",
    response_model=ResponsePayloads,
    description="获取知识库详情信息",
)
async def get_knowledge_detail(
    dataset_id: str = Path(..., description="知识库ID"),
    current_user: User = Depends(get_current_user),
):
    """获取知识库详情接口"""
    # 使用SDK获取知识库详情
    dataset = await dify.dataset.get_detail(dataset_id)
    return ResponsePayloads(data=dataset)



@router.get(
    "/{dataset_id}/documents",
    summary="获取知识库文档列表",
    response_model=ResponsePayloads,
    description="获取知识库文档列表",
)
async def get_knowledge_documents(
    dataset_id: str = Path(..., description="知识库ID"),
    page: int = Query(1, description="页码"),
    page_size: int = Query(20, description="每页数量"),
    keyword: Optional[str] = Query(None, description="搜索关键词"),
    current_user: User = Depends(get_current_user),
):
    """获取知识库文档列表接口"""
    # 使用SDK获取知识库文档列表
    # 获取文档列表，SDK方法不支持keyword参数
    documents = await dify.dataset.get_document_list(
        dataset_id,
        page=page,
        limit=page_size
    )


    return ResponsePayloads(data=documents)


@router.delete(
    "/{dataset_id}/documents/{document_id}",
    summary="删除知识库文档",
    response_model=ResponsePayloads,
    description="删除知识库中的文档",
)
async def delete_knowledge_document(
    dataset_id: str = Path(..., description="知识库ID"),
    document_id: str = Path(..., description="文档ID"),
    current_user: User = Depends(get_current_user),
):
    """删除知识库文档接口"""
    # 使用SDK删除知识库文档
    result = await dify.dataset.delete_document(dataset_id, document_id)
    logger.info(f"SDK删除文档结果: {type(result)}")
    return ResponsePayloads(data={"success": True, "result": result})


@router.delete(
    "/{dataset_id}/documents",
    summary="批量删除知识库文档",
    response_model=ResponsePayloads,
    description="批量删除知识库中的文档",
)
async def batch_delete_knowledge_documents(
    dataset_id: str = Path(..., description="知识库ID"),
    document_ids: List[str] = Query(..., alias="document_ids", description="文档ID列表"),
    current_user: User = Depends(get_current_user),
):
    """批量删除知识库文档接口"""
    logger.info(f"批量删除文档，知识库ID: {dataset_id}, 文档ID列表: {document_ids}")

    # 使用SDK批量删除知识库文档
    result = await dify.dataset.batch_delete_documents(dataset_id, document_ids)
    logger.info(f"SDK批量删除文档结果: {result}")
    return ResponsePayloads(data={"success": True, "result": result})


@router.post(
    "/{dataset_id}/documents/{document_id}/rename",
    summary="重命名知识库文档",
    response_model=ResponsePayloads,
    description="重命名知识库文档",
)
async def rename_knowledge_document(
    dataset_id: str = Path(..., description="知识库ID"),
    document_id: str = Path(..., description="文档ID"),
    name: str = Body(..., description="新文档名称", embed=True),
    current_user: User = Depends(get_current_user),
):
    """重命名知识库文档接口"""
    # 使用SDK重命名知识库文档
    # 由于SDK可能没有直接的rename_document方法，我们使用admin_client直接发送请求
    result = await dify.dataset.admin_client.post(
        f"/datasets/{dataset_id}/documents/{document_id}/rename",
        json={"name": name}
    )

    return ResponsePayloads(data={"success": True, "result": result})


@router.post(
    "/{dataset_id}/documents/upload",
    summary="上传文件到知识库",
    response_model=ResponsePayloads,
    description="上传文件到知识库",
)
async def upload_documents_to_knowledge(
    dataset_id: str = Path(..., description="知识库ID"),
    files: List[UploadFile] = File(..., description="要上传的文件列表"),
    current_user: User = Depends(get_current_user),
):
    """上传文件到知识库接口"""
    # 上传文件到Dify
    dify_files: List[FileUploadResponse] = []
    for file in files:
        # 记录接收到的文件信息
        logger.info(
            f"接收到文件: {file.filename}, 大小: {file.size}, 类型: {file.content_type}"
        )

        # 将文件内容保存到临时文件
        temp_dir = tempfile.gettempdir()
        temp_file_path = os.path.join(temp_dir, file.filename)

        # 保存上传的文件内容到临时文件
        with open(temp_file_path, "wb") as f:
            f.write(await file.read())

        # 使用临时文件路径上传到Dify
        dify_file: FileUploadResponse = await dify.file.upload(
            file_path=temp_file_path
        )
        dify_files.append(dify_file)

        # 删除临时文件
        os.remove(temp_file_path)

    # 使用SDK的create_documents_by_file_ids方法将文件添加到知识库
    # 准备请求数据
    payload = DocumentCreateByFileIdsPayload(
        data_source={
            "type": "upload_file",
            "info_list": {
                "data_source_type": "upload_file",
                "file_info_list": {
                    "file_ids": [dify_file.id for dify_file in dify_files]
                }
            }
        },
        indexing_technique="high_quality",
        process_rule={
            "rules": {
                "pre_processing_rules": [
                    {"id": "remove_extra_spaces", "enabled": True},
                    {"id": "remove_urls_emails", "enabled": False}
                ],
                "segmentation": {
                    "separator": "\n\n",
                    "max_tokens": 1024,
                    "chunk_overlap": 50
                }
            },
            "mode": "custom"
        },
        doc_form="text_model",
        doc_language="zh-Hans",
        embedding_model="text-embedding-3-large",
        embedding_model_provider="langgenius/openai/openai"
    )

    # 调用SDK方法
    result = await dify.dataset.create_documents_by_file_ids(
        dataset_id=dataset_id,
        payload=payload
    )

    logger.info(f"SDK上传文件到知识库结果: {result}")
    return ResponsePayloads(data={"success": True, "result": result})


# 下载功能已移除


@router.post(
    "/{dataset_id}/documents/status/enable",
    summary="启用知识库文档",
    response_model=ResponsePayloads,
    description="启用知识库文档",
)
async def enable_knowledge_document(
    dataset_id: str = Path(..., description="知识库ID"),
    document_id: str = Path(..., description="文档ID"),
    current_user: User = Depends(get_current_user),
):
    """启用知识库文档接口"""
    # 使用SDK启用知识库文档
    result = await dify.dataset.enable_documents(dataset_id, [document_id])
    logger.info(f"SDK启用文档结果: {result}")
    return ResponsePayloads(data={"success": True, "result": result})


@router.post(
    "/{dataset_id}/documents/status/disable",
    summary="禁用知识库文档",
    response_model=ResponsePayloads,
    description="禁用知识库文档",
)
async def disable_knowledge_document(
    dataset_id: str = Path(..., description="知识库ID"),
    document_id: str = Path(..., description="文档ID"),
    current_user: User = Depends(get_current_user),
):
    """禁用知识库文档接口"""

        # 使用SDK禁用知识库文档
    result = await dify.dataset.disable_documents(dataset_id, [document_id])
    logger.info(f"SDK禁用文档结果: {result}")
    return ResponsePayloads(data={"success": True, "result": result})



@router.post(
    "/{dataset_id}/documents/status/enable/batch",
    summary="批量启用知识库文档",
    response_model=ResponsePayloads,
    description="批量启用知识库文档",
)
async def batch_enable_knowledge_documents(
    dataset_id: str = Path(..., description="知识库ID"),
    document_ids: List[str] = Query(..., alias="document_ids", description="文档ID列表"),
    current_user: User = Depends(get_current_user),
):
    """批量启用知识库文档接口"""
    # 使用SDK批量启用知识库文档
    result = await dify.dataset.enable_documents(dataset_id, document_ids)
    logger.info(f"SDK批量启用文档结果: {result}")
    return ResponsePayloads(data={"success": True, "result": result})


@router.post(
    "/{dataset_id}/documents/status/disable/batch",
    summary="批量禁用知识库文档",
    response_model=ResponsePayloads,
    description="批量禁用知识库文档",
)
async def batch_disable_knowledge_documents(
    dataset_id: str = Path(..., description="知识库ID"),
    document_ids: List[str] = Query(..., alias="document_ids", description="文档ID列表"),
    current_user: User = Depends(get_current_user),
):
    """批量禁用知识库文档接口"""
    # 使用SDK批量禁用知识库文档
    result = await dify.dataset.disable_documents(dataset_id, document_ids)
    logger.info(f"SDK批量禁用文档结果: {result}")
    return ResponsePayloads(data={"success": True, "result": result})
