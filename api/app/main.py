
import os
from contextlib import asynccontextmanager

import sentry_sdk
import uvicorn
from fastapi import FastAPI, HTTPException
from starlette import status
from starlette.exceptions import HTTPException as StarletteHTTPException
from starlette.middleware.cors import CORSMiddleware
from starlette.requests import Request
from starlette.responses import JSONResponse

from app.core import settings
from app.core.scheduler import scheduler_manager, setup_scheduler
from app.core.exceptions import ServiceException

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用程序生命周期管理"""
    # 启动时执行的操作
    setup_scheduler()
    yield
    # 关闭时执行的操作
    scheduler_manager.shutdown()


app = FastAPI(
    title="AI智元接口",
    version="1.0.0",
    servers=[{"url": "http://localhost:8000", "description": "本地开发环境"}],
    lifespan=lifespan,
)


@app.exception_handler(ServiceException)
async def service_exception_handler(request: Request, exc: ServiceException):
    """处理自定义基础异常"""
    status_code = 500
    # 如果异常有to_dict方法，使用它来获取错误信息
    if hasattr(exc, 'to_dict'):
        error_dict = exc.to_dict()
        return JSONResponse(
            status_code=status_code,
            content=error_dict
        )
    # 否则使用默认处理
    return JSONResponse(
        status_code=status_code,
        content=ResponsePayloads(
            error=Error(type=type(exc).__name__, message=str(exc), code=status_code)
        ).model_dump(),
    )


# 1. 自定义未授权异常处理器
@app.exception_handler(HTTPException)
async def http_exception_handler(request, exc):
    if exc.status_code == status.HTTP_401_UNAUTHORIZED:
        return JSONResponse(
            status_code=exc.status_code,
            content=ResponsePayloads(
                data=None,
                error=Error(
                    type="Unauthorized", message=exc.detail, code=exc.status_code
                ),
            ).model_dump(),
        )
    return JSONResponse(
        status_code=exc.status_code,
        content=ResponsePayloads(
            data=None,
            error=Error(type="HTTPException", message=exc.detail, code=exc.status_code),
        ).model_dump(),
    )


@app.exception_handler(StarletteHTTPException)
async def not_found_exception_handler(request: Request, exc: StarletteHTTPException):
    """处理404等HTTP异常"""
    return JSONResponse(
        status_code=exc.status_code,
        content=ResponsePayloads(
            error=Error(
                type="NotFound" if exc.status_code == 404 else type(exc).__name__,
                message=str(exc.detail),
                code=exc.status_code,
            )
        ).model_dump(),
    )


if settings.mode == "prod":
    sentry_sdk.init(
        "https://<EMAIL>/1",
        send_default_pii=True,
        max_request_body_size="always",
        traces_sample_rate=0,
    )


if os.getenv("TYPE") == "client":
    from app import core, courses, files, knowledge, orders
    from app.agents import agents_api_router
    from app.core import Error, ResponsePayloads
    from app.users import users_api_router
    app.include_router(users_api_router)
    app.include_router(orders.router)
    app.include_router(agents_api_router)
    app.include_router(courses.router)
    app.include_router(core.router)
    app.include_router(files.router)
    app.include_router(knowledge.router)
elif os.getenv("TYPE") == "admin":
    from app import admin, entity
    from app.agents.admin import router as agents_admin_router
    from app.core import Error, ResponsePayloads
    from app.entity.stores import init_entity_map
    from app.users.admin import router as users_admin_router

    init_entity_map()
    app.include_router(admin.router)
    app.include_router(entity.router)
    app.include_router(agents_admin_router)
    app.include_router(users_admin_router)

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0")
