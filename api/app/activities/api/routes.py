from typing import List, Optional

from app.core.schemas import ResponsePayloads
from app.db import get_session
from app.db.crud.activity import ActivityCrud
from app.db.crud.service import ServiceCrud
from app.db.models import App
from app.keys.api.dependencies import get_admin_app
from billing.activities import ActivityListResponse, ActivityResponse
from fastapi import APIRouter, Depends, Query
from sqlmodel import Session

router = APIRouter(prefix="/activities", tags=["活动管理"])


@router.get("", response_model=ResponsePayloads[ActivityListResponse])
async def list_activities(
    key_id: str,
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(10, ge=1, le=100, description="每页数量"),
    admin_app: App = Depends(get_admin_app),
    session: Session = Depends(get_session),
):
    """获取活动列表

    Args:
       key_id: 密钥ID

    Returns:
        活动列表
    """
    activity_crud = ActivityCrud(session)
    service_crud = ServiceCrud(session)

 

    # 获取活动列表
    activities = activity_crud.get_activities_by_key_id(
       key_id=key_id,
       skip=page-1,
       limit=limit,
    )

    # 将 Activity 模型实例转换为 ActivityResponse 实例
    activity_responses = []
    for activity in activities:
        # 获取服务代码
        service = (
            service_crud.find_by_id(activity.service_id)
            if activity.service_id
            else None
        )
        service_code = service.code if service else None

        activity_response = ActivityResponse(
            id=activity.id,
            key_id=activity.key_id,
            app_id=activity.app_id,
            user_id=activity.user_id,
            service_code=service_code,
            scope=activity.scope,
            currency_type=activity.currency_type,
            type=activity.type,
            amount=activity.amount,
            details=activity.details,
            created_at=activity.created_at,
        )
        activity_responses.append(activity_response)

    # 计算总数（这里简化处理，实际应该使用COUNT查询）
    total = len(activity_responses)

    # 返回结果
    return ResponsePayloads(
        data=ActivityListResponse(
            activities=activity_responses,
            total=total,
            page=page,
            limit=limit,
        )
    )
