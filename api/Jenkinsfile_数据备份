pipeline {
    agent any
    environment {
        // 远程服务器信息
        REMOTE_HOST = '************'
        REMOTE_USER = 'root'
        // 数据库信息
        DB_CONTAINER = 'db'
        DB_USER = 'postgres'
        DB_PASSWORD = 'postgres'
        DB_NAME = 'aq'
        // 远程备份目录
        REMOTE_BACKUP_DIR = '/app/db/backup'
        // 本地备份目录
        LOCAL_BACKUP_DIR = "${WORKSPACE}/backup"
        // 时间戳
        TIMESTAMP = new Date().format('yyyy-MM-dd_HH-mm-ss')
    }

    stages {
        stage('打印环境信息') {
            steps {
                echo "开始数据库备份流程..."
                echo "备份类型: 完整备份 + 仅数据备份"
                echo "远程服务器: ${REMOTE_USER}@${REMOTE_HOST}"
                echo "数据库容器: ${DB_CONTAINER}"
                echo "数据库名称: ${DB_NAME}"
                echo "远程备份目录: ${REMOTE_BACKUP_DIR}"
                echo "本地备份目录: ${LOCAL_BACKUP_DIR}"
                echo "时间戳: ${TIMESTAMP}"

                // 创建本地备份目录
                sh "mkdir -p ${LOCAL_BACKUP_DIR}"
            }
        }

        stage('检查远程服务器状态') {
            steps {
                echo "检查远程服务器和数据库容器状态..."

                withCredentials([sshUserPrivateKey(credentialsId: 'ssh-private-key', keyFileVariable: 'SSH_KEY')]) {
                    sh """
                        ssh -i ${SSH_KEY} -o StrictHostKeyChecking=no ${REMOTE_USER}@${REMOTE_HOST} 'bash -c "
                            echo \\"检查数据库容器状态...\\"
                            if ! docker ps | grep -q ${DB_CONTAINER}; then
                                echo \\"数据库容器未运行!\\"
                                exit 1
                            fi

                            echo \\"创建远程备份目录...\\"
                            mkdir -p ${REMOTE_BACKUP_DIR}

                            echo \\"检查磁盘空间...\\"
                            df -h ${REMOTE_BACKUP_DIR}
                        "'
                    """
                }
            }
        }

        stage('执行数据库备份') {
            steps {
                echo "执行完整数据库备份..."

                withCredentials([sshUserPrivateKey(credentialsId: 'ssh-private-key', keyFileVariable: 'SSH_KEY')]) {
                    sh """
                        ssh -i ${SSH_KEY} -o StrictHostKeyChecking=no ${REMOTE_USER}@${REMOTE_HOST} 'bash -c "
                            echo \\"开始完整备份...\\"
                            docker exec ${DB_CONTAINER} bash -c \\"PGPASSWORD='\\''${DB_PASSWORD}'\\'' pg_dump -U ${DB_USER} ${DB_NAME} > /tmp/backup.sql && cat /tmp/backup.sql\\" > \\"${REMOTE_BACKUP_DIR}/full_backup_${TIMESTAMP}.sql\\"

                            echo \\"完整备份完成，检查文件大小...\\"
                            ls -lh \\"${REMOTE_BACKUP_DIR}/full_backup_${TIMESTAMP}.sql\\"
                        "'
                    """
                }

                echo "执行仅数据备份..."

                withCredentials([sshUserPrivateKey(credentialsId: 'ssh-private-key', keyFileVariable: 'SSH_KEY')]) {
                    sh """
                        ssh -i ${SSH_KEY} -o StrictHostKeyChecking=no ${REMOTE_USER}@${REMOTE_HOST} 'bash -c "
                            echo \\"开始仅数据备份...\\"
                            docker exec ${DB_CONTAINER} bash -c \\"PGPASSWORD='\\''${DB_PASSWORD}'\\'' pg_dump -U ${DB_USER} ${DB_NAME} --data-only --column-inserts > /tmp/data_only.sql && cat /tmp/data_only.sql\\" > \\"${REMOTE_BACKUP_DIR}/data_only_${TIMESTAMP}.sql\\"

                            echo \\"仅数据备份完成，检查文件大小...\\"
                            ls -lh \\"${REMOTE_BACKUP_DIR}/data_only_${TIMESTAMP}.sql\\"
                        "'
                    """
                }
            }
        }

        stage('下载备份文件到本地') {
            steps {
                echo "下载备份文件到本地..."

                withCredentials([sshUserPrivateKey(credentialsId: 'ssh-private-key', keyFileVariable: 'SSH_KEY')]) {
                    echo "下载完整备份文件..."
                    sh """
                        scp -i ${SSH_KEY} -o StrictHostKeyChecking=no ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_BACKUP_DIR}/full_backup_${TIMESTAMP}.sql ${LOCAL_BACKUP_DIR}/
                    """

                    echo "下载仅数据备份文件..."
                    sh """
                        scp -i ${SSH_KEY} -o StrictHostKeyChecking=no ${REMOTE_USER}@${REMOTE_HOST}:${REMOTE_BACKUP_DIR}/data_only_${TIMESTAMP}.sql ${LOCAL_BACKUP_DIR}/
                    """
                }

                echo "检查本地下载的备份文件..."
                sh "ls -lh ${LOCAL_BACKUP_DIR}/"
            }
        }

        stage('验证备份文件') {
            steps {
                echo "验证备份文件完整性..."

                sh """
                    echo "验证完整备份文件..."
                    if [ -f "${LOCAL_BACKUP_DIR}/full_backup_${TIMESTAMP}.sql" ]; then
                        file_size=\$(stat -c%s "${LOCAL_BACKUP_DIR}/full_backup_${TIMESTAMP}.sql")
                        if [ \$file_size -gt 1000 ]; then
                            echo "完整备份文件验证成功，大小: \$file_size 字节"
                        else
                            echo "警告: 完整备份文件太小，可能备份失败"
                            exit 1
                        fi
                    else
                        echo "错误: 完整备份文件不存在"
                        exit 1
                    fi
                """

                sh """
                    echo "验证仅数据备份文件..."
                    if [ -f "${LOCAL_BACKUP_DIR}/data_only_${TIMESTAMP}.sql" ]; then
                        file_size=\$(stat -c%s "${LOCAL_BACKUP_DIR}/data_only_${TIMESTAMP}.sql")
                        if [ \$file_size -gt 100 ]; then
                            echo "仅数据备份文件验证成功，大小: \$file_size 字节"
                        else
                            echo "警告: 仅数据备份文件太小，可能备份失败"
                            exit 1
                        fi
                    else
                        echo "错误: 仅数据备份文件不存在"
                        exit 1
                    fi
                """
            }
        }

        stage('清理远程临时文件') {
            steps {
                echo "清理远程服务器上的临时文件..."

                withCredentials([sshUserPrivateKey(credentialsId: 'ssh-private-key', keyFileVariable: 'SSH_KEY')]) {
                    sh """
                        ssh -i ${SSH_KEY} -o StrictHostKeyChecking=no ${REMOTE_USER}@${REMOTE_HOST} 'bash -c "
                            echo \\"清理数据库容器中的临时文件...\\"
                            docker exec ${DB_CONTAINER} rm -f /tmp/backup.sql /tmp/data_only.sql || true

                            echo \\"显示远程备份目录的文件列表...\\"
                            ls -lah ${REMOTE_BACKUP_DIR}/
                        "'
                    """
                }
            }
        }
    }

    post {
        success {
            script {
                def message = "数据库备份成功完成！\\n"
                message += "备份类型: 完整备份 + 仅数据备份\\n"
                message += "时间戳: ${TIMESTAMP}\\n"
                message += "远程备份目录: ${REMOTE_BACKUP_DIR}\\n"
                message += "本地备份目录: ${LOCAL_BACKUP_DIR}\\n"
                message += "完整备份文件: full_backup_${TIMESTAMP}.sql\\n"
                message += "仅数据备份文件: data_only_${TIMESTAMP}.sql\\n"

                echo message
            }
        }
        failure {
            echo "数据库备份失败，请检查日志获取详细信息"

            // 尝试清理可能残留的临时文件
            script {
                try {
                    withCredentials([sshUserPrivateKey(credentialsId: 'ssh-private-key', keyFileVariable: 'SSH_KEY')]) {
                        sh """
                            ssh -i ${SSH_KEY} -o StrictHostKeyChecking=no ${REMOTE_USER}@${REMOTE_HOST} 'bash -c "
                                docker exec ${DB_CONTAINER} rm -f /tmp/backup.sql /tmp/data_only.sql || true
                            "' || true
                        """
                    }
                } catch (Exception e) {
                    echo "清理临时文件时发生错误: ${e.getMessage()}"
                }
            }
        }
        always {
            echo "备份流程结束"

            // 归档备份文件
            script {
                try {
                    archiveArtifacts artifacts: 'backup/**/*.sql', fingerprint: true, allowEmptyArchive: true
                    echo "备份文件已归档到 Jenkins"
                } catch (Exception e) {
                    echo "归档备份文件时发生错误: ${e.getMessage()}"
                }
            }
        }
    }
}
