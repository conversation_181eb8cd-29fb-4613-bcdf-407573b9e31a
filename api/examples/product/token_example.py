"""
产品访问令牌示例

该示例演示如何使用appid和密钥生成访问令牌，以及如何验证令牌
"""
import json
import requests
import time

# API基础URL
BASE_URL = "http://localhost:8000"

def get_token(appid: str, secret: str, expires: int = 1):
    """获取访问令牌
    
    Args:
        appid: 应用ID
        secret: 应用密钥
        expires: 有效期(天)
    
    Returns:
        dict: 响应数据
    """
    url = f"{BASE_URL}/product/token"
    payload = {
        "appid": appid,
        "secret": secret,
        "expires": expires
    }
    
    response = requests.post(url, json=payload)
    result = response.json()
    
    print(f"获取令牌响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
    
    return result.get("data")


def main():
    """主函数"""
    print("===== 产品访问令牌示例 =====")
    
    # 示例appid和secret (实际应用中应从配置或命令行参数获取)
    appid = "test_app_001"
    secret = "test_secret_key"
    
    # 获取令牌
    print("\n1. 获取访问令牌")
    token_data = get_token(appid, secret)
    
    if not token_data:
        print("获取令牌失败")
        return
    
    token = token_data.get("access_token")
    expires_in = token_data.get("expires_in")
    
    print(f"访问令牌: {token}")
    print(f"有效期: {expires_in}秒 ({expires_in/3600:.2f}小时)")
    
    # 可以添加验证令牌的示例代码
    # 例如发送一个带有令牌的请求到需要认证的API端点
    
    print("\n示例完成")


if __name__ == "__main__":
    main() 