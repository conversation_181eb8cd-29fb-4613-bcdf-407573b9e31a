from app.db.models import HiaSoftwareProduct

def test_hia_software_product_auto_generate():
    """测试HiaSoftwareProduct自动生成app_id和secret_key"""
    product = HiaSoftwareProduct(name="测试产品")
    
    print("产品名称:", product.name)
    print("自动生成的app_id:", product.app_id)
    print("自动生成的secret_key:", product.secret_key)
    
    assert len(product.app_id) == 16, "app_id长度应为16"
    assert len(product.secret_key) == 32, "secret_key长度应为32"
    print("测试通过 - app_id和secret_key自动生成且长度正确")

if __name__ == "__main__":
    test_hia_software_product_auto_generate()
