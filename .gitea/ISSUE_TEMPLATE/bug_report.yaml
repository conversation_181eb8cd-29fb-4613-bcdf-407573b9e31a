name: "🐛 缺陷报告"
about: "提交一个缺陷报告以帮助我们改进"
labels:
  - bug
body:
  - type: markdown
    attributes:
      value: |
        ## 🔍 感谢您花时间填写这份缺陷报告！
        请尽可能详细地填写以下信息，这将帮助我们更快地解决问题。

  - type: input
    id: version
    attributes:
      label: 📌 版本信息
      description: 您使用的是哪个版本？
      placeholder: "例如：v1.0.0"
    validations:
      required: true

  - type: dropdown
    id: component
    attributes:
      label: 🧩 组件
      description: 这个问题出现在哪个组件中？
      options:
        - 接口 (api/)
        - 客户端 (client/)
        - 管理后台 (admin/)
        - 运维
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: 📝 问题描述
      description: 请详细描述您遇到的问题
      placeholder: 清晰简洁地描述问题是什么
    validations:
      required: true

  - type: textarea
    id: reproduction
    attributes:
      label: 🔄 复现步骤
      description: 如何复现这个问题？
      placeholder: |
        1. 进入 '...'
        2. 点击 '....'
        3. 滚动到 '....'
        4. 看到错误
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: ✅ 预期行为
      description: 清晰简洁地描述您期望发生的事情
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: 📋 相关日志
      description: 请复制并粘贴任何相关的日志输出
      render: shell

  - type: textarea
    id: environment
    attributes:
      label: 🖥️ 环境信息
      description: |
        例如：
         - 操作系统: [例如 Windows 11]
         - 浏览器: [例如 chrome 119]
         - Python版本: [例如 3.12]
         - Node版本: [例如 18.17.0]
      render: markdown
    validations:
      required: true