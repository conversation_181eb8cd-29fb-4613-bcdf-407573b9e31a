name: "✨ 功能建议"
about: "对这个项目的想法或建议"
labels:
  - enhancement
body:
  - type: markdown
    attributes:
      value: |
        ## 💡 感谢您的功能建议！
        请填写以下信息帮助我们更好地理解您的需求。

  - type: dropdown
    id: component
    attributes:
      label: 🧩 相关组件
      description: 这个功能建议与哪个组件相关？
      options:
        - 接口 (api/)
        - 客户端 (client/)
        - 管理后台 (admin/)
        - 运维
    validations:
      required: true

  - type: textarea
    id: problem
    attributes:
      label: 🔎 相关问题
      description: 这个功能建议是否与某个问题相关？请描述。
      placeholder: |
        例如：我经常感到困扰，因为[...]
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: 💡 建议的解决方案
      description: 描述您想要的解决方案
      placeholder: |
        清晰简洁地描述您希望发生的事情
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: 🔍 考虑过的替代方案
      description: 描述您考虑过的任何替代解决方案或功能
      placeholder: |
        清晰简洁地描述您考虑过的任何替代解决方案或功能

  - type: textarea
    id: additional
    attributes:
      label: 📄 其他信息
      description: 添加任何其他上下文或截图