@startuml Payment Flow

|用户|
start
:选择充值金额;
:选择支付方式;

|支付系统|
:创建充值订单;
if (支付方式) then (支付宝)
  :生成支付宝支付链接;
  |用户|
  :跳转支付宝页面;
else (微信支付)
  |支付系统|
  if (设备类型) then (微信内部)
    :生成JSAPI支付参数;
  elseif (手机浏览器) then (H5)
    :生成H5支付链接;
  else (PC)
    :生成Native支付二维码;
  endif
endif

|用户|
:完成支付;

|支付回调系统|
:接收支付成功通知;
:验证签名和订单信息;
:更新订单状态为已支付;

|账户系统|
:增加用户主账户余额;
:增加用户资产账户余额;
:可能增加用户体验次数或token余额;
:上报充值交易到华坤锦绣平台;

|用户|
:请求使用服务;

|权限检查系统|
if (检查创建智能体权限) then (无权限)
  :抛出NoCreateAgentPrivilegeError;
  stop
endif
if (检查剩余体验次数) then (有体验次数)
  :允许聊天;
else (无体验次数)
  if (检查聊天权限) then (无权限)
    :返回false;
    stop
  endif
  if (检查用户主账户余额) then (余额不足)
    :抛出InsufficientBalanceError;
    stop
  endif
  if (检查用户资产账户余额) then (余额不足)
    :抛出InsufficientBalanceError;
    stop
  endif
endif

|消费系统|
fork
  :AI对话消费;
  :计算token消耗;
  :按token定价计费;
fork again
  :视频生成消费;
  :计算视频时长;
  :按时长定价计费;
end fork

|账户系统|
if (用户有剩余体验次数) then (是)
  :扣减用户体验次数;
else (否)
  :更新用户token统计;
  :扣减用户资产账户余额;
  :扣减用户主账户余额;
endif
:上报消费交易到华坤锦绣平台;

|用户|
:查看账户余额、剩余体验次数、剩余token和消费记录;

stop

@enduml