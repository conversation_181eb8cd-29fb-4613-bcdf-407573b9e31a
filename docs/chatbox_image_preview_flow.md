**图例核心步骤概览：**

1.  **图片拖放与处理**：
    *   用户将图片拖放到 `Desktop.tsx` 组件中的聊天框区域。
    *   `Desktop.tsx` 的 `handleDrop` 事件捕获文件，并通过 `setAttachments` 更新 `ChatboxContext` 中的 `attachments` 状态（一个包含文件对象的数组）。

2.  **`TextArea.tsx` 响应附件变化**：
    *   `TextArea.tsx` 通过 `useChatbox()` Hook 监听到 `attachments` 的变化。
    *   它将这些附件文件（图片）转换为内部的 `ContentItem` 对象（类型为 'image'），并更新其自身的 `message` 状态（这是一个 `ContentItem` 对象的数组，代表编辑器的完整内容）。这个更新也会通过 `setMessage` 同步到 `ChatboxContext`。

3.  **渲染到 `contentEditable` div**：
    *   当 `TextArea.tsx` 的 `message` 状态（`ContentItem[]`）发生变化时，`contentToHtml` 函数被调用。
    *   `contentToHtml` 遍历 `ContentItem` 数组：
        *   对于文本类型的 `ContentItem`，直接输出文本内容（经过 HTML 转义）。
        *   对于图片类型的 `ContentItem`，生成一段简单的 HTML 字符串来代表图片预览卡片（包含 `<img>` 标签、文件名和一个移除按钮，并带有必要的 `data-`属性用于后续操作）。
    *   最终拼接成的完整 HTML 字符串通过 `editorRef.current.innerHTML = generatedHtml` 设置到 `contentEditable` div 中，用户从而看到图文混排的效果。

4.  **用户输入文本**：
    *   当用户在 `contentEditable` div 中输入文本时，`onInput` 事件触发。
    *   `htmlToContentItems` 函数被调用，它会解析 `contentEditable` div 的 `innerHTML`，将其转换回 `ContentItem[]` 的结构。
    *   解析后的 `ContentItem[]` 通过 `setMessage` 更新到 `ChatboxContext`。

5.  **移除图片预览**：
    *   用户点击图片预览卡片上的 "×" (移除) 按钮。
    *   由于事件委托，`TextArea.tsx` 中附加到 `contentEditable` div 上的 `click` 事件监听器被触发。
    *   监听器检查被点击的是否为移除按钮，如果是，则获取对应的图片 `id`。
    *   `handleRemoveImage` 函数被调用，它从当前的 `message` (`ContentItem[]`) 中过滤掉具有该 `id` 的图片项。
    *   更新后的 `ContentItem[]` 通过 `setMessage` 更新到 `ChatboxContext`，导致 `TextArea.tsx` 重新渲染，移除的图片预览从视图中消失。

这个序列图应该能帮助您更好地理解各个部分是如何协同工作以实现所需效果的。核心思想是将编辑器的内容抽象为一个结构化数据 (`ContentItem[]`)，在 React 中管理这个数据，然后将这个数据渲染为 HTML 以在 `contentEditable` div 中显示，并能从 `contentEditable` div 的 HTML 解析回结构化数据。
