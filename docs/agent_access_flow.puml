@startuml 智能体访问流程

actor "用户" as user
participant "应用平台" as app
participant "商户平台" as merchant
participant "智能体服务" as agent
database "密钥存储" as keystore

title 智能体访问流程

' 初始访问
user -> app: 访问智能体
activate app

' 权限验证
app -> merchant: 验证用户权限\n(appId=xxx, userId=xxx, type=agent)
activate merchant

' 密钥查询
merchant -> keystore: 查询密钥记录
activate keystore
keystore --> merchant: 返回密钥信息
deactivate keystore

' 验证逻辑
merchant -> merchant: 验证密钥有效性\n1. 检查是否过期\n2. 验证token余额是否充足

alt 密钥有效
    merchant --> app: 返回 true
    
    app -> agent: 调用智能体服务
    activate agent
    agent --> app: 返回响应
    deactivate agent
    
    app --> user: 显示智能体响应
else 密钥无效或不存在
    merchant --> app: 返回 false
    app --> user: 提示开通服务
end

deactivate merchant
deactivate app

@enduml