@startuml 聊天框图片预览流程

actor 用户
participant "Desktop.tsx" as Desktop组件
participant "ChatboxContext上下文" as 上下文
participant "TextArea.tsx" as TextArea组件
participant "contentEditable div编辑器" as 编辑器Div

skinparam sequenceMessageAlign center
skinparam actorStyle awesome

title 聊天输入框图文混排实现流程

box "聊天框组件系统" #LightSkyBlue
  participant Desktop组件
  participant 上下文
  participant TextArea组件
  participant 编辑器Div
end box

== 用户拖放图片 ==
用户 -> Desktop组件: 拖放图片文件
activate Desktop组件
Desktop组件 -> Desktop组件: handleDrop(事件)
Desktop组件 -> 上下文: setAttachments([新的附件对象数组])
deactivate Desktop组件
activate 上下文
上下文 -> 上下文: 更新 'attachments' 状态
上下文 -> TextArea组件: 通知 'attachments' 状态变更 (通过 useChatbox Hook)
deactivate 上下文

activate TextArea组件
TextArea组件 -> TextArea组件: 'attachments' 相关的 useEffect 触发
TextArea组件 -> TextArea组件: 将附件文件转换为 ContentItem[] (类型: 'image')
TextArea组件 -> 上下文: setMessage([更新后的ContentItem数组])
deactivate TextArea组件

activate 上下文
上下文 -> 上下文: 更新 'message' 状态 (ContentItem[])
上下文 -> TextArea组件: 通知 'message' 状态变更
deactivate 上下文

activate TextArea组件
TextArea组件 -> TextArea组件: 'message' 相关的 useEffect 触发
TextArea组件 -> TextArea组件: contentToHtml(message: ContentItem[])
note right: 生成包含图片预览 (<img> 标签)\n和文本的 HTML 字符串
TextArea组件 -> 编辑器Div: editorRef.current.innerHTML = 生成的Html
deactivate TextArea组件

== 用户输入文本 ==
用户 -> 编辑器Div: 输入文本
activate 编辑器Div
编辑器Div -> TextArea组件: onInput 事件
deactivate 编辑器Div
activate TextArea组件
TextArea组件 -> TextArea组件: htmlToContentItems(editorRef.current.innerHTML)
note right: 将编辑器内的 HTML 解析回 ContentItem[]
TextArea组件 -> 上下文: setMessage([解析后的ContentItem数组])
deactivate TextArea组件

activate 上下文
上下文 -> 上下文: 更新 'message' 状态
上下文 -> TextArea组件: 通知 'message' 状态变更 (可能触发重新渲染)
deactivate 上下文

== 用户移除图片 ==
用户 -> 编辑器Div: 点击图片预览上的 '移除' 按钮
activate 编辑器Div
编辑器Div -> TextArea组件: (通过事件委托) 编辑器上的 'click' 事件
deactivate 编辑器Div
activate TextArea组件
TextArea组件 -> TextArea组件: handleRemoveImage(图片ID)
TextArea组件 -> TextArea组件: htmlToContentItems(editorRef.current.innerHTML)
TextArea组件 -> TextArea组件: 根据 ID 过滤掉对应的图片 ContentItem
TextArea组件 -> 上下文: setMessage([移除图片后的ContentItem数组])
deactivate TextArea组件

activate 上下文
上下文 -> 上下文: 更新 'message' 状态
上下文 -> TextArea组件: 通知 'message' 状态变更 (触发重新渲染)
deactivate 上下文

@enduml
