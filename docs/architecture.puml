@startuml Architecture

!define RECTANGLE class

skinparam rectangle {
    BackgroundColor<<用户>> #FFE4B5
    BackgroundColor<<应用平台>> #A9DCDF
    BackgroundColor<<商户平台>> #87CEEB
    BackgroundColor<<智能体>> #DDA0DD
    BackgroundColor<<代理层>> #F0E68C
    BackgroundColor<<基础设施>> #FFB6C1
    BackgroundColor<<模型服务>> #98FB98
    BorderColor Black
    ArrowColor Black
}

' 用户层
rectangle "企业员工" as staff <<用户>>
rectangle "创作者" as creator <<用户>>
rectangle "国学爱好者" as guoxue <<用户>>
rectangle "普通用户" as normal <<用户>>

' 应用平台层
rectangle "智元AI\n应用平台" as zhiyuan <<应用平台>>
rectangle "创建大师AI\n应用平台" as chuangshi <<应用平台>>
rectangle "澜沁国学AI\n应用平台" as lanqin <<应用平台>>
rectangle "全员AI\n应用平台" as quanyuan <<应用平台>>

' 商户平台层
rectangle "华坤锦绣商户平台" as merchant_platform <<商户平台>> {
    rectangle "密钥管理" as key_management
    rectangle "资源计量" as resource_metering
    rectangle "访问控制" as access_control_merchant
    rectangle "计费系统" as billing_system
}

' 智能体层
rectangle "IP专家\n智能体" as ip_expert <<智能体>>
rectangle "文案大师\n智能体" as copywriter <<智能体>>
rectangle "国学专家\n智能体" as guoxue_expert <<智能体>>
rectangle "通用助手\n智能体" as general_assistant <<智能体>>

' 代理层
rectangle "蝉镜代理服务\n(多租户支持)" as chanjing_proxy <<代理层>> {
    rectangle "租户隔离" as tenant_isolation
    rectangle "资源管理" as resource_management
    rectangle "访问控制" as access_control
}

' 基础设施层
rectangle "Dify\n(AI服务基础设施)" as dify <<基础设施>>
rectangle "蝉镜\n(数字人服务基础设施)" as chanjing <<基础设施>>

' 模型服务层
rectangle "OpenAI\n(GPT-3.5/4)" as openai <<模型服务>>
rectangle "Anthropic\n(Claude)" as anthropic <<模型服务>>
rectangle "OpenRouter\n(模型路由服务)" as openrouter <<模型服务>>
rectangle "Deepseek\n(Deepseek-Chat)" as deepseek <<模型服务>>

' 用户到应用平台的使用关系
staff -down-> zhiyuan : 使用
creator -down-> chuangshi : 使用
guoxue -down-> lanqin : 使用
normal -down-> quanyuan : 使用

' 应用平台到商户平台的认证关系
zhiyuan -down-> merchant_platform : 认证和授权
chuangshi -down-> merchant_platform : 认证和授权
lanqin -down-> merchant_platform : 认证和授权
quanyuan -down-> merchant_platform : 认证和授权

' 商户平台到智能体的调用关系
merchant_platform -down-> ip_expert : 授权访问
merchant_platform -down-> copywriter : 授权访问
merchant_platform -down-> guoxue_expert : 授权访问
merchant_platform -down-> general_assistant : 授权访问

' 智能体到基础设施的调用关系
ip_expert -down-> dify : 使用
copywriter -down-> dify : 使用
guoxue_expert -down-> dify : 使用
general_assistant -down-> dify : 使用

' 商户平台到代理层的授权关系
merchant_platform -down-> chanjing_proxy : 授权访问

' 代理层到蝉镜的调用关系
chanjing_proxy -down-> chanjing : 代理访问

' 基础设施到模型服务的调用关系
dify -down-> openai : 使用
dify -down-> anthropic : 使用
dify -down-> openrouter : 使用
dify -down-> deepseek : 使用

' 添加标题和图例
title 系统架构图

legend right
  |= 类型 |= 说明 |
  |<#FFE4B5>| 用户 |
  |<#A9DCDF>| 应用平台 |
  |<#87CEEB>| 商户平台 |
  |<#DDA0DD>| 智能体 |
  |<#F0E68C>| 代理层 |
  |<#FFB6C1>| 基础设施 |
  |<#98FB98>| 模型服务 |
endlegend

@enduml