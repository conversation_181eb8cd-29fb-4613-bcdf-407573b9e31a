@startuml 数字人访问流程

actor "国学用户" as user
participant "国学后台\n(澜沁国学AI)" as lanqin
participant "商户平台" as merchant
database "密钥存储" as keystore

title 数字人访问流程

' 初始点击
user -> lanqin: 点击数字人智能体
activate lanqin

' 权限验证
lanqin -> merchant: 验证用户权限\n(appId=lqgx, userId=1, type=chanjing)
activate merchant

' 密钥查询
merchant -> keystore: 查询密钥记录
activate keystore
keystore --> merchant: 返回密钥信息
deactivate keystore

' 验证逻辑
merchant -> merchant: 验证密钥有效性\n1. 检查是否过期\n2. 验证余额是否充足

alt 密钥有效
    merchant --> lanqin: 返回 true
    lanqin --> user: 进入数字人页面
else 密钥无效或不存在
    merchant --> lanqin: 返回 false
    lanqin --> user: 提示开通服务
end

deactivate merchant
deactivate lanqin

@enduml