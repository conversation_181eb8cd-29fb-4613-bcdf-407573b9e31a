@startuml 数字人购买流程

actor "国学用户" as user
participant "国学后台\n(澜沁国学AI)" as lanqin
participant "支付系统" as payment
participant "商户平台" as merchant
database "密钥存储" as keystore

title 数字人购买流程

' 用户支付
user -> lanqin: 支付499元购买数字人服务
activate lanqin

lanqin -> payment: 发起支付请求
activate payment

alt 支付成功
    payment --> lanqin: 支付成功回调
    
    ' 计算使用时长
    lanqin -> lanqin: 计算可用分钟数\n499元 ÷ 0.067元/秒 = 7447秒\n= 124分钟
    
    ' 创建密钥
    lanqin -> merchant: 创建密钥请求\n(\
    appId=lqgx,\n\
    userId=1,\n\
    type=chanjing,\n\
    creditLimit=124)
    activate merchant
    
    merchant -> keystore: 存储密钥信息
    activate keystore
    keystore --> merchant: 存储成功
    deactivate keystore
    
    merchant --> lanqin: 返回密钥信息
    deactivate merchant
    
    lanqin --> user: 显示开通成功
else 支付失败
    payment --> lanqin: 支付失败回调
    lanqin --> user: 显示支付失败
end

deactivate payment
deactivate lanqin

@enduml