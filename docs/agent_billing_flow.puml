@startuml 智能体结算流程

actor "用户" as user
participant "应用平台" as app
participant "商户平台" as merchant
participant "智能体服务" as agent
database "密钥存储" as keystore
database "计费记录" as billing_records

title 智能体结算流程

' 用户对话
user -> app: 发送消息到智能体
activate app

' 验证密钥
app -> merchant: 验证密钥有效性
activate merchant
merchant --> app: 返回验证结果
deactivate merchant

alt 密钥有效
    ' 调用智能体
    app -> agent: 发送用户消息
    activate agent
    
    ' 智能体处理
    agent -> agent: 处理用户消息
    
    ' 计算token消耗
    agent -> agent: 计算token使用量\n1. 输入tokens\n2. 输出tokens
    
    agent --> app: 返回响应和token使用量
    deactivate agent
    
    ' 记录消费
    app -> merchant: 扣减密钥额度\n(token使用量)
    activate merchant
    
    merchant -> billing_records: 记录token消费
    activate billing_records
    billing_records --> merchant: 记录成功
    deactivate billing_records
    
    merchant -> keystore: 更新密钥余额
    activate keystore
    keystore --> merchant: 更新成功
    deactivate keystore
    
    merchant --> app: 扣减成功
    deactivate merchant
    
    app --> user: 显示智能体响应
else 密钥无效
    app --> user: 提示开通服务
end

deactivate app

@enduml