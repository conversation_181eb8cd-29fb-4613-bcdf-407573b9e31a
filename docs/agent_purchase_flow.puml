@startuml 智能体购买流程

actor "用户" as user
participant "应用平台" as app
participant "支付系统" as payment
participant "商户平台" as merchant
database "密钥存储" as keystore

title 智能体购买流程

' 用户支付
user -> app: 支付购买智能体服务
activate app

app -> payment: 发起支付请求
activate payment

alt 支付成功
    payment --> app: 支付成功回调
    
    ' 计算token额度
    app -> app: 计算可用token数\n例：499元 = 50万tokens
    
    ' 创建密钥
    app -> merchant: 创建密钥请求\n(\
    appId=xxx,\n\
    userId=xxx,\n\
    type=agent,\n\
    creditLimit=500000)
    activate merchant
    
    merchant -> keystore: 存储密钥信息
    activate keystore
    keystore --> merchant: 存储成功
    deactivate keystore
    
    merchant --> app: 返回密钥信息
    deactivate merchant
    
    app --> user: 显示开通成功
else 支付失败
    payment --> app: 支付失败回调
    app --> user: 显示支付失败
end

deactivate payment
deactivate app

@enduml