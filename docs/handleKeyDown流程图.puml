@startuml handleKeyDown流程图

title ChatboxInput handleKeyDown 事件处理流程

start

:接收键盘事件;

if (按下Enter键?) then (是)
    if (同时按下Ctrl?) then (是)
        :处理换行逻辑;
        :在光标位置插入\n换行符;
        :更新光标位置;
    else (否)
        if (命令菜单显示中?) then (否)
            :触发发送消息事件;
            :阻止默认行为;
        endif
    endif
endif

if (命令菜单显示中?) then (是)
    switch (按键类型)
        case (Escape)
            :关闭命令菜单;
            if (输入框内容为"/") then (是)
                :清空输入框;
            endif
            
        case (ArrowUp)
            :选择上一个命令;
            :如果是第一个则\n循环到最后一个;
            
        case (ArrowDown)
            :选择下一个命令;
            :如果是最后一个则\n循环到第一个;
            
        case (Enter)
            if (有建议命令?) then (是)
                :选择当前命令;
                :发送选中的命令;
            endif
    endswitch
endif

stop

@enduml