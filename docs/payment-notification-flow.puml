@startuml 支付通知流程图

title 支付通知流程 - 客户端中转方案

actor 用户 as User
participant 客户端 as Client
participant 支付网关 as Gateway
participant API服务器 as API

== 支付流程 ==
User -> Client: 发起支付请求
Client -> API: 创建支付订单
API -> Client: 返回支付订单信息
Client -> Gateway: 调用支付网关
Gateway -> User: 显示支付页面
User -> Gateway: 完成支付

== 支付通知流程 ==
Gateway -> Client: 发送支付成功通知
note right of Client
  客户端接收支付网关的通知
  包含支付状态、订单号等信息
end note

Client -> API: 转发支付通知
note right of Client
  将支付网关的通知
  原封不动地转发给API
end note

API -> API: 验证通知签名
API -> API: 处理支付结算
note right of API
  - 验证订单状态
  - 更新用户余额/权限
  - 记录支付日志
end note

alt 结算成功
    API -> Client: 返回结算成功响应
    Client -> User: 显示支付成功页面
    note right of Client
      客户端立即知道支付结果
      无需轮询或等待推送
    end note
else 结算失败
    API -> Client: 返回结算失败响应
    Client -> User: 显示支付失败页面
    note right of Client
      可以提示用户重试
      或联系客服
    end note
end

== 优势说明 ==
note over Client, API
优势：
1. 客户端实时获得支付结果
2. 无需轮询支付状态
3. 无需服务器推送机制
4. 流程简单直接
5. 减少服务器负载
end note

@enduml
