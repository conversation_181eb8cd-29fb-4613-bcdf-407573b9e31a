@startuml 创建智能体流程

title 创建智能体流程

actor User as "用户"
participant Client as "客户端"
participant API as "API服务"
participant DifyAPI as "Dify API"
participant DB as "数据库"

autonumber

group 权限检查
    User -> Client: 请求创建智能体
    Client -> API: POST /agents/create
    activate API
    API -> API: 检查创建智能体权限\n(@require_create_agent_privilege)
end

group 创建Dify应用
    API -> DifyAPI: 创建应用\n(dify.app.create)
    DifyAPI --> API: 返回应用ID
    
    API -> DifyAPI: 更新模型配置\n(dify.app.update_model_config)
    DifyAPI --> API: 返回配置更新结果
    
    API -> DifyAPI: 创建API密钥\n(dify.app.create_api_key)
    DifyAPI --> API: 返回API密钥
end

group 本地数据存储
    API -> DB: 创建Agent记录
    note right
        - id (Dify应用ID)
        - name
        - description
        - icon_url
        - type (AGENT_CHAT)
        - tags
        - owner_id
        - api_key
    end note
    
    API -> DB: 创建UserAsset记录
    note right
        - user_id
        - asset_id
        - asset_type (APP)
        - asset_name
        - app_mode
    end note
    
    DB --> API: 确认数据保存
end

group 返回结果
    API -> DifyAPI: 获取完整应用信息\n(dify.app.find_by_id)
    DifyAPI --> API: 返回应用详情
    
    API --> Client: 返回智能体详情
    deactivate API
    Client --> User: 显示创建成功
end

@enduml