@startuml 支付通知流程图

title 支付通知流程 - 服务端推送方案

actor 用户 as User
participant 客户端 as Client
participant 支付网关 as Gateway
participant API服务器 as API
participant Redis队列 as Redis
participant SSE接口 as SSE

== 支付流程 ==
User -> Client: 发起支付请求
Client -> API: 创建支付订单

API -> Redis: 放入等待支付结果的消息
note right of Redis
  消息包含：
  - 订单ID
  - 客户端标识
  - 创建时间
  - 状态：waiting
end note

API -> Client: 返回支付订单信息
Client -> Gateway: 调用支付网关
Gateway -> User: 显示支付页面

== SSE订阅 ==
Client -> SSE: 建立SSE连接订阅支付结果
note right of SSE
  客户端通过SSE接口
  订阅对应订单的支付结果
  传递订单ID作为订阅标识
end note

SSE -> Redis: 持续监听队列中的订单状态
note right of SSE
  SSE接口持续关注
  Redis队列中对应订单的状态变化
  使用订单ID作为key进行监听
end note

== 支付完成 ==
User -> Gateway: 完成支付

== 支付通知处理 ==
Gateway -> API: 发送支付成功通知
note right of Gateway
  支付网关向API发送
  支付结果通知
end note

API -> API: 验证通知签名
API -> API: 处理支付结算
note right of API
  - 验证订单状态
  - 更新用户余额/权限
  - 记录支付日志
end note

API -> Redis: 更新队列中消息状态
note right of Redis
  更新消息状态：
  - waiting -> success/failed
  - 添加结算结果信息
  - 添加处理时间戳
end note

== 结果推送 ==
Redis -> SSE: 通知状态变化
note right of Redis
  Redis通知SSE接口
  对应订单状态已更新
end note

SSE -> Client: 推送支付结果
note right of Client
  客户端通过SSE连接
  实时接收到支付结果
  包含成功/失败状态和详细信息
end note

Client -> User: 显示支付结果

== 清理工作 ==
SSE -> Redis: 删除已处理的消息
note right of SSE
  处理完成后将这条消息
  从Redis队列中删除
  避免内存泄漏
end note

== 异常处理 ==
note over Client, SSE
连接断开处理：
- 客户端重连时重新订阅
- SSE检查订单状态并补发结果
- 超时订单自动清理
end note

== 优势说明 ==
note over Redis, SSE
优势：
1. 标准的服务端推送架构
2. 支持多客户端同时等待
3. 可扩展性好，易于集群部署
4. 状态管理清晰
5. 支持断线重连
end note

== 技术考虑 ==
note over Redis, SSE
需要考虑：
1. Redis队列的维护和清理
2. SSE连接的管理复杂度
3. 连接断开重连机制
4. 服务器资源消耗
5. 消息过期和清理策略
end note

@enduml
