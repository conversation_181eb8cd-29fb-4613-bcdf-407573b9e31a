@startuml 网络架构图

!define RECTANGLE class

' 设置样式
skinparam rectangle {
    BackgroundColor<<ECS>> #FFFFFF
    BackgroundColor<<Ubuntu>> #FFFFFF
    BackgroundColor<<Component>> #FFFFFF
    BorderColor Black
    ArrowColor White
}

' 用户图标
actor "Users" as users

' 阿里云ECS服务器
rectangle "阿里云 ECS" as aliyun_ecs <<ECS>> {
    rectangle "frp server" as frp_server <<Component>> #2E8B57
    
    rectangle "caddy" as caddy <<Component>> #8B4513 {
        rectangle "dify.ailoveworld.cn" as domain <<Component>> #4682B4
    }
}

' Ubuntu服务器
rectangle "192.168.1.6 ubuntu" as ubuntu_server <<Ubuntu>> {
    rectangle "dify" as dify <<Component>> #8B3626
    rectangle "frp client" as frp_client <<Component>> #696969
}

' 连接关系
users -up-> domain : "1"
domain <-up-> frp_server : "2"
frp_server <--> frp_client : "3\nping 16ms"
frp_client <-up-> dify : "4"

' 添加说明
legend right
  服务器间通信延迟: 16ms
endlegend

@enduml