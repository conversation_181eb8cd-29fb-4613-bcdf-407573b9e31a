@startuml 数字人视频生成流程

actor "用户" as user
participant "蝉镜代理" as proxy
participant "商户平台" as merchant
participant "蝉镜官方API" as chanjing
database "代理任务存储" as task_store

title 数字人视频生成流程

' 发起请求
user -> proxy: 发送生成视频请求\n(携带密钥)
activate proxy

' 验证密钥
proxy -> merchant: 验证密钥有效性
activate merchant
merchant --> proxy: 返回验证结果
deactivate merchant

alt 密钥有效

    ' 存储任务信息
    proxy -> task_store: 存储任务信息\n(关联密钥和请求ID)
    activate task_store
    task_store --> proxy: 存储成功
    deactivate task_store

    ' 调用官方API
    proxy -> chanjing: 发送视频生成请求
    activate chanjing
    chanjing --> proxy: 返回任务已接收
    deactivate chanjing

    proxy --> user: 返回任务已接收

    ' 异步回调处理
    chanjing -> proxy: 视频生成完成回调\n(携带视频ID)
    activate proxy

    ' 查询关联密钥
    proxy -> task_store: 查询任务关联的密钥
    activate task_store
    task_store --> proxy: 返回密钥信息
    deactivate task_store

    ' 扣减余额
    proxy -> merchant: 扣减密钥余额\n(计算实际使用时长)
    activate merchant
    merchant --> proxy: 扣减成功
    deactivate merchant

    proxy -> user: 通知视频生成完成
    deactivate proxy

else 密钥无效
    proxy --> user: 返回错误信息
end

deactivate proxy

@enduml