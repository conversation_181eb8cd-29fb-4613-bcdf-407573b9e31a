@startuml 数字人智能体对接流程

title 数字人智能体对接流程

actor User as "用户"
participant Agent as "数字人智能体"
participant Plugin as "蝉镜插件"
participant Callback as "回调服务"
participant Merchant as "商户平台"
participant Account as "用户账户系统"

User -> Agent: 请求：定制一个形象
Agent -> User: 要求提供视频链接和形象名称
User -> Agent: 提供视频链接和形象名称
Agent -> Plugin: 调用蝉镜插件(视频链接,形象名称,回调地址)
Plugin -> Agent: 返回任务ID
Agent -> User: 返回任务ID并告知处理中
Plugin -> Plugin: 生成形象(处理中...)
Plugin -> Callback: 调用回调地址(形象数据)
Callback -> Callback: 根据时长计算费用
Callback -> Account: 更新用户账户余额
Callback -> Merchant: 上报形象生成数据
Callback -> User: 通知形象生成完成
@enduml